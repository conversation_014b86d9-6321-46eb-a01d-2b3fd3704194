codecov:
  bot: codecov

coverage:
  # Anything bellow red, in between is yellow and above is green
  range: 60..80
  round: down
  precision: 2
  status:
    project:
      default:
        target: auto # the required coverage value
        threshold: 2% # the leniency in hitting the target
    patch:
      default:
        target: auto
ignore:
  - "integration-test/**/*"
  - "deploy/**/*"
  - "node_modules/**/*"
  - "coverage/**/*"
  - "dist/**/*"
  - "public/**/*"
  - "build/**/*"
  - "vite.config.ts"
  - "tsconfig.json"
  - "globalSetup.ts"
  - "eslintrc.cjs"
