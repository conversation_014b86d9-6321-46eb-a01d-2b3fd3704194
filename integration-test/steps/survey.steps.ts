import { expect } from '@playwright/test';
import { DEFAULT_DATA, page } from '../setup';
import { Given, When, Then, After } from '@cucumber/cucumber';
import { Company, PageUtils, Survey } from '../pages';
import { COOKIE_KEYS, <PERSON>ie } from '../utils/cookie';
import { GenerateData } from '../utils/generate-data';
import { MESSAGES, TESTID } from '../utils/constants';
import { getApi, HTTP, transformResponse } from '../utils/api';
import { generateUrl } from '../utils/path';

Given('I am logged in as an administrator user within a specific organization', async () => {
  await Company.selectByName(DEFAULT_DATA.company.name);
});

// --- Create a new survey --- //

When('I create a new survey by inputting details into a form', async () => {
  await page.goto(generateUrl().survey.create);
  await expect(page.getByTestId(TESTID.SURVEY.SURVEY_NAME_INPUT)).toBeVisible();

  let surveyData = GenerateData.surveyData();
  await Survey.add({ ...surveyData, placeholder: DEFAULT_DATA.company.name });

  if (await page.getByText(MESSAGES.INVALID_SURVEY_INFORMATION).isVisible()) {
    // update month year when current one already existed
    const updateSurvey = GenerateData.surveyData();
    surveyData = { ...surveyData, month: updateSurvey.month, year: updateSurvey.year };
    await Survey.fillMonthYear(updateSurvey);
  }
  const promise = page.waitForResponse(getApi().survey.list);
  await page.getByTestId(TESTID.SURVEY.SURVEY_CONFIGS_SUBMIT_BUTTON).click();

  const { data: surveys = [] } = await transformResponse(await promise);
  const createdSurvey = surveys.find((s) => s.name === surveyData.name);
  if (!createdSurvey) {
    return;
  }
  const { _id, name, period, initiativeId, effectiveDate, type } = createdSurvey;
  await Cookie.addCookies(COOKIE_KEYS.SURVEY, { _id, name, period, initiativeId, effectiveDate, type });
});

Then('the new survey should be created successfully', async () => {
  const currentSurvey = await Survey.getCurrentSurvey();
  expect(currentSurvey._id).toBeDefined();
});

When('I mark a survey as complete', async () => {
  await PageUtils.selectAppNavItem('Reporting');
  const { status, method } = await Survey.completeSurvey();
  expect(status).toEqual(200);
  expect(method).toEqual(HTTP.METHOD.PATCH);
});

Then('the survey is changed to completed status', async () => {
  expect(await page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON).textContent()).toEqual('Report completed');
});

When('I mark a survey as uncomplete', async () => {
  const promise = page.waitForResponse(getApi().survey.complete);
  await page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON).click();
  const { status, method } = await transformResponse(await promise);
  expect(status).toEqual(200);
  expect(method).toEqual(HTTP.METHOD.DELETE);
});

Then('the survey status should change to not completed', async () => {
  expect(await page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON).textContent()).toEqual('Mark as complete');
});

After({ tags: '@create-and-complete-survey' }, async () => {
  await PageUtils.selectAppNavItem('All reports');
  await Survey.deleteCurrentSurvey();
});
