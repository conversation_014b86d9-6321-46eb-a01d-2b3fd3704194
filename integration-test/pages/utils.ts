import { Locator } from 'playwright';
import { DEFAULT_DATA, page } from '../setup';
import { generateUrl } from '../utils/path';
import { TESTID } from '../utils/constants';

export class PageUtils {

  static async triggerButton(content: string) {
    await page.getByRole('button', { name: content }).click();
  }

  static async openDropdownMenu(triggerLocator: Locator) {
    const showedMenu = page.locator('.dropdown.show');
    while (await showedMenu.isHidden()) {
      await triggerLocator.click({ delay: 300 });
      if (await showedMenu.isVisible()) {
        break;
      }
    }
  }

  static async selectDropdownItem(selectLocator: Locator, item: string) {
    const control = selectLocator.locator('div[class*="-control"]');
    const menu = selectLocator.locator('div[class*="-menu"]');
    await control.click();
    await menu.getByText(item, { exact: true }).click();
  }

  static async goToReportPage() {
    await page.goto(generateUrl({ initiativeId: DEFAULT_DATA.company.id }).survey.list, { waitUntil: 'domcontentloaded' });
  }

  static async selectAppNavItem(item: string) {
    await page.getByTestId(TESTID.APP.APP_NAVIGATION_DESKTOP).getByRole('link', { name: item }).click();
  }

  static async selectBreadcrumbItem(item: string) {
    await page.getByTestId(TESTID.APP.APP_BREADCRUMB).getByText(item, { exact: true }).click();
  }
}
