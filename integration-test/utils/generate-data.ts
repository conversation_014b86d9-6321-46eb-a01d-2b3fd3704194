import { faker } from '@faker-js/faker';
import { METRICS_TYPE, METRICS_UNIT, SURVEY_TYPES, SURVEY_VERSION, SURVEY_YEARS } from './constants';

export interface Survey {
  reportingType: string;
  name: string;
  year: string;
  month: string;
  version: string;
}

export type SurveyTemplate = Omit<Survey, 'version'>;

export interface CustomMetric {
  name: string;
  description: string;
  preferredStandard?: string;
}

export interface CustomQuestion {
  title: string;
  text: string;
  code: string;
  instruction: string;
  type: string;
  unit: string;
}

export interface User {
  title: string;
  email: string;
  firstName: string;
  lastName: string;
  jobTitle: string;
  tel: string;
}

export class GenerateData {
  static tag() {
    return faker.lorem.word({ length: { min: 5, max: 10 } });
  }

  static code(code: number = 14) {
    return faker.string.alphanumeric({ length: { min: 5, max: code } });
  }

  static number(params?: {min: number, max: number}) {
    return params ? faker.number.int(params) : faker.number.int();
  }

  static text(min = 1, numOfWords: number = 3) {
    return faker.lorem.sentence({ min, max: numOfWords });
  }

  static longText(numOfWords: number = 10) {
    return this.text(5, numOfWords);
  }

  static surveyReportingType(surveyTypes: string[] = SURVEY_TYPES) {
    return faker.string.fromCharacters(surveyTypes);
  }

  static year(years: string[] = SURVEY_YEARS) {
    return faker.string.fromCharacters(years);
  }

  static version(versions: string[] = SURVEY_VERSION) {
    return faker.string.fromCharacters(versions);
  }

  static type(types: string[] = METRICS_TYPE) {
    return faker.string.fromCharacters(types);
  }

  static unit(units: string[] = METRICS_UNIT) {
    return faker.string.fromCharacters(units);
  }

  static email(custom?: { firstName?: string; lastName?: string; provider?: string }) {
    return custom ? faker.internet.email(custom) : faker.internet.email();
  }

  static surveyTemplateData(data: Partial<SurveyTemplate> = {}): SurveyTemplate {
    return {
      name: this.longText(6),
      month: faker.date.month(),
      year: this.year(),
      reportingType: this.surveyReportingType(),
      ...data
    };
  }

  static surveyData(data: Partial<Survey> = {}): Survey {
    return {
      name: this.longText(6),
      month: faker.date.month(),
      year: this.year(),
      version: this.version(),
      reportingType: this.surveyReportingType(),
      ...data
    };
  }

  static metricGroupData(data: Partial<CustomMetric> = {}): CustomMetric {
    return {
      name: this.longText(6),
      description: this.longText(20),
      ...data
    };
  }

  static customQuestionData(data: Partial<CustomQuestion> = {}): CustomQuestion {
    return {
      title: this.longText(),
      text: this.longText(),
      code: this.code(),
      instruction: this.longText(20),
      type: this.type(),
      unit: this.unit(),
      ...data
    };
  }
  
  static userData(data: Partial<User> = {}): User {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    return {
      title: this.longText(),
      email: this.email({ firstName, lastName }),
      firstName,
      lastName,
      jobTitle: faker.person.jobTitle(),
      tel: faker.phone.number(),
      ...data
    };
  }
}
