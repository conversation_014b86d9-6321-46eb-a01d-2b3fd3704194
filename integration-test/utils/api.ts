import { Response } from 'playwright';
import { DEFAULT_DATA } from '../setup';

export const API_BASE_URL = process.env.API_BASE_URL;

export const HTTP = {
  METHOD: {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    PATCH: 'PATCH',
    DELETE: 'DELETE'
  }
};

interface Params {
  baseUrl?: string;
  initiativeId?: string;
  surveyTemplateId?: string;
}

export const getApi = (params: Params = {}) => {
  const { baseUrl = '**', initiativeId = DEFAULT_DATA.company.id, surveyTemplateId = '' } = params;

  return {
    company: {
      list: `${baseUrl}/api/initiative-tree/companies`,
      bankingSetting: `${baseUrl}/api/banking-settings/**`,
    },
    customMetricGroup: {
      list: `${baseUrl}/api/initiatives/${initiativeId}/metric-groups`,
      addMetric: `${baseUrl}/api/initiatives/${initiativeId}/metric-groups/**/universal-tracker/**`,
      tag: `${baseUrl}/api/initiatives/${initiativeId}/metric-groups/tag`
    },
    customMetric: {
      list: `${baseUrl}/api/initiatives/${initiativeId}/custom-metrics`,
      usage: `${baseUrl}/api/initiatives/${initiativeId}/custom-metrics/usage`,
      addCustomMetric: `${baseUrl}/api/initiatives/${initiativeId}/metric`,
      updateCustomMetric: `${baseUrl}/api/initiatives/${initiativeId}/metric/**`,
    },
    survey: {
      list: `${baseUrl}/api/initiatives/${initiativeId}/surveys`,
      createCombinedSurvey: `${baseUrl}/api/initiatives/${initiativeId}/aggregated-survey/aggregate/`,
      updateScope: `${baseUrl}/api/surveys/**/scope`,
      aggregatedSurvey: `${baseUrl}/api/initiatives/${initiativeId}/aggregated-survey/auto-aggregate`,
      complete: `${baseUrl}/api/surveys/**/complete`
    },
    surveyTemplate: {
      common: `${baseUrl}/api/survey-templates/`,
      list: `${baseUrl}/api/initiatives/${initiativeId}/survey-templates/`,
      update: `${baseUrl}/api/survey-templates/**`,
      useTemplate: `${baseUrl}/api/survey-templates/${surveyTemplateId}/bulk-surveys-create-job`
    },
    utrv: {
      updateOne: `${baseUrl}/api/universal-tracker-values/**/update`,
      bookmark: `${baseUrl}/api/bookmarks/universal-tracker-value`
    },
    user: {
      currentUser: `${baseUrl}/api/users/current`,
      onboard: `${baseUrl}/api/initiatives/${initiativeId}/users/onboard`,
      onboardingDetail: `${baseUrl}/api/initiatives/${initiativeId}/onboarding/**`
    }
  };
};

export const transformResponse = async (response: Response) => {
  return {
    headers: response.headers(),
    status: response.status(),
    method: response.request().method(),
    data: (await response.json()).data
  };
};
