import { context, config } from '../setup';

export const COOKIE_KEYS = {
  INITIATIVE: 'initiative',
  SURVEY_TEMPLATE: 'surveyTemplate',
  COMBINED_SURVEY_TEMPLATE: 'combinedSurveyTemplate',
  SURVEY: 'survey',
  COMBINED_SURVEY: 'combinedSurvey',
  PACKS: 'surveyScopes',
  TAGS: 'tags',
  METRIC_GROUP: 'metricGroup',
  CUSTOM_METRIC: 'customMetric',
  BANK: 'bank'
};

export class Cookie {
  static async addCookies(key: string, value: unknown) {
    if (!value) {
      return value;
    }

    const convertToString = JSON.stringify(value);
    await context.addCookies([{ name: key, value: convertToString, url: config.baseUrl }]);
  }

  static async getCookies(key: string) {
    const cookies = await context.cookies();
    const foundItem = cookies.find((item) => item.name === key);
    return foundItem ? JSON.parse(foundItem.value) : undefined;
  }

  static async clearCookies() {
    await context.clearCookies();
  }
}
