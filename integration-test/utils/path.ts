import { DEFAULT_DATA } from '../setup';

interface Params {
  initiativeId?: string;
  surveyId?: string;
}

export const generateUrl = (params: Params = {}) => {
  const { initiativeId = DEFAULT_DATA.company.id, surveyId = '' } = params;

  return {
    homepage: '/marketplace',
    companyTracker: '/company-tracker',
    admin: {
      settings: `/company-tracker/admin/${initiativeId}`,
      customMetric: {
        dashboard: `/company-tracker/admin/${initiativeId}/custom-metrics/dashboard`,
        manage: `/company-tracker/admin/${initiativeId}/custom-metrics/manage`,
        create: `/company-tracker/admin/${initiativeId}/custom-metrics/create`
      },
      users: {
        manage: `/company-tracker/admin/${initiativeId}/manage-users`,
      },
      company: {
        settings: `/company-tracker/admin/${initiativeId}/account-settings/details`
      }
    },
    survey: {
      surveyTemplateList: `/company-tracker/survey-templates/${initiativeId}`,
      surveyTemplateCreate: `/company-tracker/survey-templates/${initiativeId}/template/create`,
      combinedSurveyTemplateCreate: `/company-tracker/survey-templates/${initiativeId}/aggregated/create`,
      create: `/company-tracker/reports/${initiativeId}/create`,
      list: `/company-tracker/reports/${initiativeId}`,
      overview: `/company-tracker/reports/${initiativeId}/${surveyId}/overview`
    },
    user: {
      profile: '/user-profile'
    },
    insight: {
      summary: `/company-tracker/summary/${initiativeId}`
    },
    downloads: {
      overview: `/company-tracker/downloads/${initiativeId}`
    }
  };
};
