{"$schema": "./node_modules/oxlint/configuration_schema.json", "plugins": ["import"], "categories": {}, "rules": {"require-yield": "warn", "no-func-assign": "warn", "no-unused-private-class-members": "warn", "no-debugger": "warn", "for-direction": "warn", "no-dupe-class-members": "warn", "no-setter-return": "warn", "no-useless-rename": "warn", "no-duplicate-case": "warn", "use-isnan": "warn", "no-with": "warn", "no-class-assign": "warn", "no-ex-assign": "warn", "no-unsafe-negation": "warn", "no-constant-condition": "warn", "no-unused-labels": "warn", "no-control-regex": "warn", "no-import-assign": "warn", "no-useless-catch": "warn", "no-irregular-whitespace": "warn", "no-useless-escape": "warn", "no-loss-of-precision": "warn", "no-dupe-keys": "warn", "no-sparse-arrays": "warn", "no-empty-character-class": "warn", "no-this-before-super": "warn", "no-empty-static-block": "warn", "no-cond-assign": "warn", "no-eval": "warn", "no-obj-calls": "warn", "no-constant-binary-expression": "warn", "no-extra-boolean-cast": "warn", "no-unsafe-finally": "warn", "import/namespace": "warn", "no-global-assign": "warn", "no-unused-vars": "warn", "no-delete-var": "warn", "no-invalid-regexp": "warn", "no-self-assign": "warn", "no-dupe-else-if": "warn", "no-shadow-restricted-names": "warn", "valid-typeof": "warn", "no-async-promise-executor": "warn", "import/default": "warn", "import/no-cycle": "warn", "no-caller": "warn", "no-empty-pattern": "warn", "no-new-native-nonconstructor": "warn", "no-compare-neg-zero": "warn", "no-nonoctal-decimal-escape": "warn", "no-const-assign": "warn", "no-unsafe-optional-chaining": "warn"}, "settings": {"jsx-a11y": {"polymorphicPropName": null, "components": {}}, "next": {"rootDir": []}, "react": {"formComponents": [], "linkComponents": []}, "jsdoc": {"ignorePrivate": false, "ignoreInternal": false, "ignoreReplacesDocs": true, "overrideReplacesDocs": true, "augmentsExtendsReplacesDocs": false, "implementsReplacesDocs": false, "exemptDestructuredRootsFromChecks": false, "tagNamePreference": {}}}, "env": {"builtin": true}, "globals": {}, "ignorePatterns": []}