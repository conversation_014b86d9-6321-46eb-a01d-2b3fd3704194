import { defineWorkspace } from 'vitest/config'
import react from '@vitejs/plugin-react-swc';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import { resolveConfig } from './vite.config';
import { ViteEjsPlugin } from 'vite-plugin-ejs';

export default defineWorkspace([
  {
    extends: './vite.config.ts',
    test: {
      name: 'unit',
      environment: 'jsdom',
      include: [
        'src/**/*.{test,spec}.?(c|m)[jt]s?(x)',
      ],
    },
  },
  {
    plugins: [
      react(),
      viteTsconfigPaths(),
      ViteEjsPlugin({ robots: 'noindex', internationalization: '0' }),
    ],
    optimizeDeps: {
      include: ['@g17eco/convert-units'],
    },
    resolve: resolveConfig,
    test: {
      globalSetup: './globalSetup.ts',
      setupFiles: ['./tests/setupTests.ts'],
      testTimeout: 5000,
      restoreMocks: true,
      // an example of file based convention,
      // you don't have to follow it
      include: [
        'tests/**/*.browser.{test,spec}.ts[x]',
        'src/**/*.browser.{test,spec}.ts[x]',
      ],
      name: 'browser',
      browser: {
        provider: 'playwright',
        enabled: true,
        headless: true,
        screenshotDirectory: '__screenshots__',
        viewport: {
          width: 1280,
          height: 720,
        },
        instances: [
          {
            browser: 'chromium',
            launch: {
              devtools: false,
            },
            context: {},
          },
        ],
      },
    },
  },
])
