<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="robots" content="<%= robots %>" />
    <meta
      name="description"
      content="Comprehensive solutions and tools for your sustainability reporting or disclosure needs with support for ISSB, CSRD, TCFD and CDP."
    />
    <meta name="keywords" content="sustainability report, esg, climate change, ISSB, CSRD, CDP, TCFD" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta http-equiv="Cache-control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta name="insight-app-sec-validation" content="00914d5b-6440-47eb-afab-dbd4c6cb9a40" />
    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="stylesheet" type="text/css" href="/style.css" />
    <title>G17Eco Sustainability Reporting</title>
    <!-- Ensure global is defined to legacy libs, to be removed   -->
    <script>
      window.global = window;
    </script>
  </head>

  <body style="background-color: #f9fafc">
    <noscript> You need to enable JavaScript to run this app. Please. </noscript>
    <div id="root" class="weglot-translate">
      <div id="loader-wrapper">
        <div id="loader" style="box-sizing: border-box"></div>
        <img id="loader-img" src="/assets/logo-icon.png" alt="logo-icon" />
      </div>
    </div>
    <script type="module" src="/src/index.tsx"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->

    <% if (process.env.NODE_ENV === 'production') { %>
    <!-- Start of wwghelp Zendesk Widget script -->
    <script
      id="ze-snippet"
      src="https://static.zdassets.com/ekr/snippet.js?key=3547d132-090b-40d7-a0d3-fd2d59897656"
    ></script>
    <!-- End of wwghelp Zendesk Widget script -->
    <% } %> <% if (process.env.NODE_ENV === 'production') { %>
    <script>
      (function (apiKey) {
        (function (p, e, n, d, o) {
          var v, w, x, y, z;
          o = p[d] = p[d] || {};
          o._q = o._q || [];
          v = ['initialize', 'identify', 'updateOptions', 'pageLoad', 'track'];
          for (w = 0, x = v.length; w < x; ++w)
            (function (m) {
              o[m] =
                o[m] ||
                function () {
                  o._q[m === v[0] ? 'unshift' : 'push']([m].concat([].slice.call(arguments, 0)));
                };
            })(v[w]);
          y = e.createElement(n);
          y.async = !0;
          y.src = 'https://cdn.pendo.io/agent/static/' + apiKey + '/pendo.js';
          z = e.getElementsByTagName(n)[0];
          z.parentNode.insertBefore(y, z);
        })(window, document, 'script', 'pendo');
      })('a119e4fe-ec28-414e-7df4-1d8ce9c7a7af');
    </script>
    <% } %>

    <script>
      if (!!window.MSInputMethodContext && !!document.documentMode) {
        document.body.classList.add('ie-11');
      }
    </script>

    <% if (internationalization === '1') { %>
    <!-- Start of WeGlot script to do internationalization -->
    <script type="text/javascript" src="https://cdn.weglot.com/weglot.min.js"></script>
    <script>
      Weglot.initialize({
        api_key: 'wg_5883c6a22db1716d6e3478bcf7168f1b1',
      });
    </script>
    <!-- End of WeGlot script to do internationalization -->
    <% } %>
  </body>
</html>
