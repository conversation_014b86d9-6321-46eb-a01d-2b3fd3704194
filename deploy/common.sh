#
# Copyright (c) 2024. World Wide Generation Ltd
#

gray="\\e[37m"
blue="\\e[36m"
red="\\e[31m"
green="\\e[32m"
reset="\\e[0m"

info() { echo "${blue}INFO: $*${reset}"; }
error() { echo "${red}ERROR: $*${reset}"; }
debug() {
    if [[ "${DEBUG}" == "true" ]]; then
        echo "${gray}DEBUG: $*${reset}";
    fi
}
success() { echo "${green}✔ $*${reset}"; }
fail() {
  echo "${red}✖ $*${reset}"
  exit 1
}

# Execute a command, saving its output and exit status code, and echoing its output upon completion.
# Globals set:
#   status: Exit status of the command that was executed.
#
run() {
  echo "$@"
  set +e
  eval "$@" 2>&1
  status=$?
  set -e
}
