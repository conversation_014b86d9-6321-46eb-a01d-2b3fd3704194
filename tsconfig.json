{
  "compilerOptions": {
    "target": "es6",
    "lib": [
      "dom",
      "dom.iterable",
      "ES2022"
    ],
    "types": [
      "vitest/globals",
      "vite/client",
      "vite-plugin-svgr/client",
      "@vitest/browser/providers/playwright"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "strictNullChecks": true,
    "useDefineForClassFields": true,
    "useUnknownInCatchVariables": false,
    "noFallthroughCasesInSwitch": true,
    "noEmit": true,
    // "traceResolution": true,
    "jsx": "react-jsx",

    "paths": {
      "@g17eco/atoms/*": ["./src/atoms/*"],
      "@g17eco/molecules/*": ["./src/molecules/*"],
      "@g17eco/images": ["./src/images"],
      "@g17eco/types/*": ["./src/types/*"],
      "@g17eco/slices/*": ["./src/slice/*"],
      "@components/*": ["./src/components/*"],
      "@features/*": ["./src/features/*"],
      "@constants/*": ["./src/constants/*"],
      "@services/*": ["./src/services/*"],
      "@utils/*": ["./src/utils/*"],
      "@fixtures/*": ["./src/__fixtures__/*"],
      "@browser/*": ["./src/browser/*"],
      "@routes/*": ["./src/routes/*"],
      "@api/*": ["./src/api/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@actions/*": ["./src/actions/*"],
      "@models/*": ["./src/model/*"],
      "@reducers/*": ["./src/reducers/*"],
      "@selectors/*": ["./src/selectors/*"],
      "@apps/*": ["./src/apps/*"]
    }
  },
  "include": ["src"]
}
