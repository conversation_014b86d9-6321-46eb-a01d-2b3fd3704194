/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { Page, Locator } from '@playwright/test';
import { BasePage } from './base.page';

export class MarketplacePage extends BasePage {

  constructor(page: Page) {
    super(page);
  }

  async goto() {
    await this.page.goto('/marketplace');
  }

  getKeyByName(name: string): Locator {
    return this.page.getByRole('listitem').filter({ hasText: name });
  }

  getPageTitle(): Locator {
    return this.page.getByText('THE G17ECO MARKETPLACE', { exact: true });
  }


}
