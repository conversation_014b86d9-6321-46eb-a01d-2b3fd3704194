/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { beforeAll, afterAll } from 'vitest';

const originalError = console.error;
const reactstrapWarnings = [
  'prop `timeout` is marked',
  'prop `transition.timeout` is marked',
  'Support for defaultProps'
]

const shouldSkipArgs = (args: unknown[]) => {
  return args.some(arg => {
    if (typeof arg !== 'string') {
      return false
    }
    return reactstrapWarnings.some(text => arg.includes(text));
  })
}
beforeAll(() => {
  console.error = (...args) => {
    // Silence the transition timeout warnings until Reactstrap fixes them
    if (shouldSkipArgs(args)) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
