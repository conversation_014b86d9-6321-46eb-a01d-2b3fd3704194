/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { test, expect } from '@playwright/test';
import { MarketplacePage } from '../pages/marketplace.page';

test.describe('Marketplace', () => {
  let marketPlacePage: MarketplacePage;

  test.beforeEach(async ({ page }) => {
    marketPlacePage = new MarketplacePage(page);
    await marketPlacePage.goto();
  });

  test('Login button for Company Tracker should be visible', async ({ page }) => {

    const loginButton = page.locator('.product-cards.row')
      .getByTestId('Company Tracker')
      .getByRole('button', { name: `Log in` });

    await expect(loginButton).toBeVisible();
  });
});
