enum Permission {
  User = 'user',
}

interface User {
  _id: string;
  permissions: Permission[];
}

export type UserWithInfo = User & {
  name: string;
  email: string;
};

export interface Workgroup<U extends User = User> {
  _id: string;
  initiativeId: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  users: U[];
  creatorId: string;
  created: string;
  updated: string;
}
