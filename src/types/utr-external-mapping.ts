/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

type ReferenceItem = [label: string, value: string];
/**
 * @example
 *  [
 *    ["Name", "ESRS"],
 *    ["Number", "ESRS 2"],
 *    ["Paragraph", "48"],
 *    ["Subparagraph", "a"],
 *    ["Section", "SBM-3"]
 *  ]
 */
type ReferenceRow = ReferenceItem[]

export interface ExternalMappingItem {
  /** The code of the mapping, usually the factName **/
  mappingCode: string;
  /** standard type like gri, csrd, issb etc. **/
  type: string;
  name: string;
  utrCode: string;
  valueListCode?: string;
  references?: ReferenceRow[];
}

export type UtrMappingResponse = {
  utrCode: string;
  mappings: ExternalMappingItem[];
};

interface UtrMapping {
  utrCode: string,
  valueListCode?: string
}

export type ExternalDefinitionItem = Pick<ExternalMappingItem, 'mappingCode' | 'name' | 'references' | 'type'> & {
  utrs: UtrMapping[];
}

export interface MappingListResponse {
  mappings: ExternalDefinitionItem[];
}
