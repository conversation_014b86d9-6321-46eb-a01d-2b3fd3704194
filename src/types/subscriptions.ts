/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import { FeatureCode } from '@g17eco/core';
import { Subscription } from './initiative';

export interface UpgradeRequestBase {
  initiativeId: string,
  featureCode: FeatureCode
}

export type PriceInterval = 'day' | 'month' | 'week' | 'year';

interface ProductDetails {
  interval?: PriceInterval;
  price: number;
  currencySymbol: string;
  currency: string
}

interface UsageDetails {
  featureCode: FeatureCode;
  currentUsage: number;
  limit: number;
}

export interface CanUpgradeDetails {
  canUpgrade: true;
  usageDetails: UsageDetails;
  productDetails: ProductDetails;
}

export type UpgradeDetails = CanUpgradeDetails | { canUpgrade: false, productDetails?: undefined };

export interface PlanUpgrade extends UpgradeRequestBase {
  additionalUnits: number;
  interval: PriceInterval | undefined;
}

interface InvoiceLineItem {
  id: string;
  amount: number;
  currency: string;
  currencySymbol?: string;
  description: string | null;
  periodStart: string;
  periodEnd: string;
  itemId: string | undefined;
  proration: boolean;
  quantity: number | null;
  price?: number;
}

export interface PreviewUpgradeResult {
  subscriptionId: string;
  status: Subscription['status'],
  periodStart: string;
  periodEnd: string;
  currency: string;
  currencySymbol?: string;
  lines: InvoiceLineItem[];
  total: number;
}

export interface UpgradeResult {
  details: {
    subscriptionId: string;
    itemId: string;
    quantity: number;
    total: number;
  };
}


