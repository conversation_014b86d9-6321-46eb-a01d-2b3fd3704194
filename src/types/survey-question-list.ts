/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ScopeQuestionOptionalValue } from './surveyScope';

export interface ColumnCommonProps {
  question: ScopeQuestionOptionalValue;
  surveyId: string;
  initiativeId: string;
  alternativeCode?: string;
  preferredAltCodes?: string[];
  handleGoToQuestion: () => void;
  isBookmarked: boolean;
  toggleBookmark: () => void;
}

export type QuestionListColumn = (props: ColumnCommonProps) => JSX.Element | null;