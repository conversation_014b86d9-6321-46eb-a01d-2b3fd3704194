import { BackgroundJob, JobStatus, JobType, Task, TaskType } from './background-jobs';
import { SurveyInitiative } from './survey';

type SetupContext = {
  utrvIds: string[];
  survey: SurveyInitiative;
};
interface TaskAIAutoAnswerSetup extends Task<SetupContext> {
  type: TaskType.AIAutoAnswerSetup;
}

interface ProcessedAnswerResult {
  isSuccess: boolean;
  errorMessage?: string;
}

type ProcessContext = {
  utrvId: string;
  processedResult?: ProcessedAnswerResult;
};
interface TaskAIAutoAnswerProcess extends Task<ProcessContext> {
  type: TaskType.AIAutoAnswerProcess;
}

type CompleteContext = {
  completedUtrvs: string[];
  errorUtrvs: string[];
};
interface TaskAIAutoAnswerComplete extends Task<CompleteContext> {
  type: TaskType.AIAutoAnswerComplete;
}
export type AIAutoAnswerTask = TaskAIAutoAnswerSetup | TaskAIAutoAnswerProcess | TaskAIAutoAnswerComplete;
export type AIAutoAnswerSurveyJobPlain = Pick<BackgroundJob, '_id' | 'status' | 'updated'> & {
  type: JobType.AIAutoAnswerSurvey;
  tasks: Pick<AIAutoAnswerTask, 'id' | 'type' | 'status'>[];
};

export interface CreatedJob {
  jobId: string;
  status: JobStatus;
}
