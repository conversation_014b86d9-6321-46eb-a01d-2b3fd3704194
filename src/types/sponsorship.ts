/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { CustomScope } from '@g17eco/types/initiative';
import { AgreementConfig } from './app';
import { DataShare, RestrictionType } from './dataShare';
import { FeatureDetails } from '@g17eco/core';

interface SponsorshipAgreement {
  code: string;
  date: string;
  userId: string;
}

export interface SponsorshipSurveyConfig {
  customScope: CustomScope[];
}

export interface Sponsorship {
  _id: string;
  sponsorshipConfigId: string;
  initiativeId: string;
  referralCode: string;
  dataShareIds: string[];

  surveyConfig?: SponsorshipSurveyConfig;

  cancelDate?: string;

  periodEndDate: string;
  updateRequired?: string;
  lastUpdated: string;
  created: string;

  agreements?: SponsorshipAgreement[];
  subscriptions?: { subscriptionId: string }[];
}

enum SponsorType {
  Portfolio = 'portfolio',
}

interface SponsorshipReferral {
  code: string;
}

type DataShareMapper = Pick<DataShare, 'title' | 'content' | 'requesterType' | 'dataScope'> & {
  requesterCode: string;
  restrictionType: RestrictionType;
};

interface SubscriptionConfigItem {
  productCode: string;
}

interface ConfigSubscription {
  items: SubscriptionConfigItem[];
}

interface SubscriptionConfiguration {
  couponId: string;
  subscriptions: ConfigSubscription[];
}

export interface SponsorshipConfig<D extends string | Date = string | Date> {
  _id: string;
  title: string;
  sponsorCode: string;
  sponsorType: SponsorType;
  /** Referral codes used to lookup sponsorship */
  referrals?: SponsorshipReferral[];
  agreements?: AgreementConfig[];
  surveyConfig?: SponsorshipSurveyConfig;
  dataShareConfigs?: DataShareMapper[];
  features?: FeatureDetails[];

  /**
   * Allow to add custom subscription items that should be provided
   * as part of sponsorship
   */
  subscriptionConfig?: SubscriptionConfiguration;

  registrationConfig: {
    /** @deprecated **/
    enableCustomCompany: boolean;
  };
  autoRenew?: boolean;
  endDate: D;
  lastUpdated: D;
  created: D;
}
