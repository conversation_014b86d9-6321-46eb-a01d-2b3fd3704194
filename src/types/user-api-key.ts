/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { InitiativePlain } from '@g17eco/types/initiative';

export enum ScopePermission {
  InitiativeRead = 'initiative.read',
  SurveyRead = 'survey.read',
  UniversalTrackerValueRead = 'universal_tracker_value.read',
  UniversalTrackerValueWrite = 'universal_tracker_value.write',
  ValueListRead = 'value_list.read',
}

export enum ConnectionRole {
  DateEntry = 'data_entry',
}

export interface PublicApiAccess {
  /** Roles that give you scopes **/
  roles: ConnectionRole[];
  /** Custom scopes that are merged together with scopes provided by roles **/
  scopes?: ScopePermission[];

  /**
   * Represent for which company/account this access is for
   * Should always be top level initiative tag organization
   **/
  initiativeId: string;
}

export interface ApiKey extends PublicApiAccess {

  _id: string;

  /**
   * Hash version of the code that was used to generate the api key.
   * It was only visible when creating for the first time.
   *
   * based on GitHub Personal Access Token (Classic) ^ghp_[a-zA-Z0-9]{36}$
   * but support - and _ as that is what we use by nanoId by default [A-Za-z0-9_-]
   */
  longTokenHash: string;

  /**
   * It is very useful to see what was the original key was used
   * especially when you want to do the key rotation.
   * We can use name, but part of the token is even better and already supported
   * by the library. This short token is safe to use in the logs to identify it
   */
  shortToken: string;


  /**
   * Allow user to write name of the token if needed.
   * It is optional as we have shortToken property that should be enough to
   * identify the token we want to revoke or rotate.
   */
  name?: string

  /** Update max every 1 minute **/
  lastUsed: string;


  /** Allow to revoke the key but still keep the reference to it. **/
  revokedDate?: string;

  created: string;

  userId: string;
}


export type SafeApiKey = Pick<ApiKey,
  | '_id'
  | 'name'
  | 'initiativeId'
  | 'shortToken'
  | 'lastUsed'
  | 'created'
  | 'revokedDate'
  | 'roles'
  | 'scopes'
>;

export interface UserApiKeyExtended extends SafeApiKey {
  initiative?: Pick<InitiativePlain, '_id' | 'name'>
}

export interface CreateApiKeyResponse {
  token: string,
  apiKey: SafeApiKey
}

export type RevokeApiKeyResponse = Pick<ApiKey, '_id' | 'initiativeId' | 'name' | 'shortToken' | 'revokedDate'>;

export type CreateApiKeyData = Pick<ApiKey, 'name' | 'initiativeId' | 'roles' | 'scopes'>;
