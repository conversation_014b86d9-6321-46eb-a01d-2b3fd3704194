/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import type { UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import type { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { UniversalTrackerModalServiceUtrv } from '../reducers/universal-tracker-modal';
import { UserMin } from '@constants/users';

export type GeneratedUtr = Pick<UniversalTrackerPlain,
  | 'code'
  | 'valueLabel'
  | 'name'
  | 'valueType'
  | 'valueValidation'
  | 'instructions'
  | 'numberScale'
  | 'unit'
  | 'unitType'
  | 'type'
  | '_id'
  | 'alternatives'
>;

export type GeneratedUtrv = Pick<UniversalTrackerModalServiceUtrv,
  | 'value'
  | 'valueType'
  | 'valueData'
  | 'effectiveDate'
  | 'period'
  | 'type'
  | 'status'
  | 'universalTrackerId'
> & { utrCode: string };


type IntegrationConfig = {
  type: 'external';
  requirements: {
    /** These are UTR Like questions that are for now created on the fly **/
    questions: GeneratedUtr[];
  }
};

export interface IntegrationProvider {
  /**
   * Partner code used as slug in urls
   */
  code: string;
  name: string;
  shortName: string;
  logo: string;
  tags: string[];
  description: string;
  highlights: string[];
  link: string;
  color?: string;
  icon?: string;
}

export interface IntegrationProviderExtended extends IntegrationProvider {
  status: ProviderIntegrationStatus;
  login?: ExternalAppInfo['login'];
}

type BaseData = unknown;

export interface IntegrationConnectionCreate<D extends BaseData> {
  name?: string;
  initiativeId: string;
  integrationCode: string;
  createdBy: string;
  lastUpdated?: Date;
  status: ProviderIntegrationStatus;
  data: D,
}

export interface IntegrationConnectionPlain<D extends BaseData = BaseData> extends IntegrationConnectionCreate<D> {
  _id: string;
  created: Date;
}

export enum ProviderIntegrationStatus {
  // Setup is not completed
  SetupRequired = 'setup_required',
  // Setup is pending (3rd party is processing)
  Pending = 'pending',
  // Setup is completed and ready to use
  Active = 'active',
  // Error occurred during setup
  Error = 'error',
}

export type IntegrationSource = Pick<IntegrationProvider, 'code' | 'name' | 'logo' | 'icon'> & {
  questions: GeneratedUtr[];
};

interface ProviderSetupConfig {
  provider: IntegrationProvider,
  integration?: IntegrationConfig
}

export interface ExternalAppInfo {
  name: string;
  login?: {
    url: string;
    text?: string;
  }
}

export interface ProviderSetupInfo extends ProviderSetupConfig {
  status: ProviderIntegrationStatus;
  externalApp?: ExternalAppInfo;
}

export interface IntegrationConnectionInfo<D extends BaseData = BaseData> extends ProviderSetupConfig {
  connection: IntegrationConnectionPlain<D> & { creator: UserMin };
}

export interface SetupUtrv extends Pick<UniversalTrackerValuePlain, 'value' | 'valueData'>{
  utrCode: string,
}

export interface SetupData {
  initiativeId: string;
  code: string;
  // Simplified UTR answers
  generatedAnswers: SetupUtrv[];
}

export type CreateSetupFn = (setupData: Pick<SetupData, 'generatedAnswers'>) => void;

export type ProviderSetupInfoRequired = ProviderSetupInfo & { onCreate: CreateSetupFn, integration: IntegrationConfig };

// Min amount of data required to make it compatible with generated utrs/utrvs
export interface HistoricalGenerated {
  utr: GeneratedUtr;
  utrvs: GeneratedUtrv[];
}

export interface IntegrationUtrData extends HistoricalGenerated {
  integrationCode: string,
}

export type IntegrationData = {
  utrsData: IntegrationUtrData[]
}
