import { TableDataInfo } from '@components/survey/question/questionInterfaces';
import { Variation, VariationDataSource } from './universalTracker';
import { ValueData } from '@g17eco/types/surveyScope';
import { InputColumn } from '@components/survey/form/input/table/InputInterface';

export type CurrentInputData = {
  value?: number;
  table: TableDataInfo;
  valueData: ValueData;
  unit?: string;
  numberScale?: string;
  aggregatedTableData?: InputColumn[][];
};

// aggregation only support text / valueList for now, may need to pull unit / numberScale later if numeric value is supported
export type AggregationColumn = Pick<InputColumn, 'code' | 'value'>;

export interface UtrvVariation extends Variation {
  valueListCode?: string; // table column's code or numeric value list's code
  details: {
    baseline: number;
    min: number;
    max: number;
    unit?: string;
    numberScale?: string;
    effectiveDate: Date;
    aggregationColumns?: AggregationColumn[];
  };
}

export interface UtrvVariationWarning {
  valueListCode?: string;
  formattedBaseline: string;
  baselineReportingDate: string;
  formattedCurrentInput: string;
  variance: number;
  confirmationRequired: boolean;
  dataSource: VariationDataSource;
  aggregationColumns?: AggregationColumn[];
}
