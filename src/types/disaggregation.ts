import { DataPeriods } from './universalTracker';
import { ValueHistory } from './universalTrackerValue';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';

export enum ValueAggregation {
  ValueSumAggregator = 'valueSumAggregator',
  ValueCountAggregator = 'valueCountAggregator', // Just count utrvs
  ValueConcatenateAggregator = 'valueConcatenateAggregator',
  ValueAverageAggregator = 'valueAverageAggregator',
  ValueListCountAggregator = 'valueListCountAggregator',
  TextCountAggregator = 'textCountAggregator',
  NumericValueListSumAggregator = 'numericValueListSumAggregator',
  NumericValueListAverageAggregator = 'numericValueListAverageAggregator',
  TableColumnAggregator = 'tableAggregator',
  TableConcatenationAggregator = 'tableConcatenationAggregator',
  LatestAggregator = 'latestAggregator',
  EmptyAggregator = 'emptyAggregator',
  TableRowGroupAggregator = 'tableRowGroupAggregator',
}


export const valueAggregationMap: { [key in ValueAggregation]: string } = {
  [ValueAggregation.ValueSumAggregator]: 'Sum of all answers',
  [ValueAggregation.ValueAverageAggregator]: 'Average of all answers',
  [ValueAggregation.ValueConcatenateAggregator]: 'Concatenate all answers',
  [ValueAggregation.ValueListCountAggregator]: 'Count',
  [ValueAggregation.TextCountAggregator]: 'Count',
  [ValueAggregation.ValueCountAggregator]: 'Count',
  [ValueAggregation.NumericValueListSumAggregator]: 'Sum of all answers',
  [ValueAggregation.TableColumnAggregator]: 'Table concatenation',
  [ValueAggregation.TableConcatenationAggregator]: 'Table concatenation',
  [ValueAggregation.LatestAggregator]: 'Data from the latest answered metric',
  [ValueAggregation.EmptyAggregator]: 'Empty',
  [ValueAggregation.TableRowGroupAggregator]: 'Group rows by unique columns',
  [ValueAggregation.NumericValueListAverageAggregator]: 'Average of all answers',
};

type UtrvPick = Pick<UniversalTrackerValuePlain,  | 'initiativeId' | 'period' | 'status' | 'effectiveDate'>;
type HistoryPick = Pick<ValueHistory, 'value' | 'valueData' | 'action' | 'date' | 'unit' | 'numberScale'>;
export interface HistoryData extends HistoryPick, UtrvPick {
  utrvId: string;
  /** utrv.compositeData.surveyId **/
  surveyId?: string;
  // We fall back on to the utrv period if not set, therefore, always set
  period: DataPeriods;

  latestHistory?: HistoryPick;
}
