/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


export interface ReportDocument {
  _id: string;
  title: string;
  description?: string;

  type: string;

  initiativeId: string;
  createdBy: string

  /** ISO string */
  lastUpdated: string;

  /** ISO string */
  created: string;
}

export type CreateReportDocumentMin = Pick<ReportDocument,
  | 'type'
  | 'title'
  | 'description'
  | 'initiativeId'
> & { _id?: string };
