import { QueryDefinition, QueryArgFrom, ResultTypeFrom } from '@reduxjs/toolkit/query';
import { useEffect, useState } from 'react';

interface UseInfiniteScrollProps<T, <PERSON><PERSON> extends QueryArgFrom<D>, D extends QueryDefinition<any, any, any, any>> {
  initialData: ResultTypeFrom<D> | undefined;
  fetchData: (arg: QueryArgFrom<D>) => Promise<{ data?: ResultTypeFrom<D> }>;
  extractData: (response: ResultTypeFrom<D> | undefined) => { items: T[]; nextCursor?: string; hasNextPage: boolean };
  initialParams: Params;
}

export const useInfiniteScroll = <T, Para<PERSON> extends QueryArgFrom<D>, D extends QueryDefinition<any, any, any, any>>({
  initialData,
  fetchData,
  extractData,
  initialParams,
}: UseInfiniteScrollProps<T, Params, D>) => {
  const [items, setItems] = useState<T[]>([]);
  const [pagination, setPagination] = useState<{ cursor?: string; hasNextPage: boolean }>({ hasNextPage: false });

  useEffect(() => {
    if (initialData) {
      const { items, nextCursor, hasNextPage } = extractData(initialData);
      setItems(items);
      setPagination({
        cursor: nextCursor,
        hasNextPage,
      });
    }
  }, [extractData, initialData]);

  const fetchMore = () => {
    if (!pagination.hasNextPage) {
      return;
    }

    fetchData({ ...initialParams, cursor: pagination.cursor }).then(({ data }) => {
      const { items: newItems, nextCursor, hasNextPage } = extractData(data);
      setItems((prev) => [...prev, ...newItems]);
      setPagination({ cursor: nextCursor, hasNextPage });
    });
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const bottom = e.currentTarget.scrollHeight === e.currentTarget.scrollTop + e.currentTarget.clientHeight;
    if (bottom) {
      fetchMore();
    }
  };

  return { items, handleScroll };
};
