/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { RootInitiativeData } from '../types/initiative';
import { AppConfig } from '../types/app';
import { useRouteMatch } from 'react-router-dom';
import { isValidCompanyTrackerRoot } from '../routes/company-tracker/utils';

interface UseAppConfig {
  organization?: Pick<RootInitiativeData, 'appConfig'>;
}

export const useRouteAppConfig = (params: UseAppConfig): AppConfig | undefined => {
  const { organization } = params;

  const match = useRouteMatch<{ rootAppPath: string }>({ path: '/:rootAppPath/' });
  const rootAppPath = match?.params.rootAppPath;
  if (!rootAppPath) {
    // Weird case, so let's return the org's app config and let something else handle
    return organization?.appConfig;
  }

  if (isValidCompanyTrackerRoot(rootAppPath)) {
    // This is a CT route, so always return. If there is a mismatch in the appConfig, it will be handled elsewhere
    return organization?.appConfig;
  }

  return undefined;
};
