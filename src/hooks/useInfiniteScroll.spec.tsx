import { act, renderHook } from '@testing-library/react';
import { useInfiniteScroll } from './useInfiniteScroll';

describe('useInfiniteScroll', () => {
  it('sets initial data correctly', () => {
    const initialData = { items: ['item1', 'item2'], hasNextPage: true };
    const extractData = (data: any) => data;
    const { result } = renderHook(() =>
      useInfiniteScroll({
        initialData,
        fetchData: () => Promise.resolve({ data: initialData }) as any,
        extractData,
        initialParams: {},
      })
    );

    expect(result.current.items).toEqual(['item1', 'item2']);
  });

  it('fetches more data when scrolled to the bottom', async () => {
    const initialData = { items: ['item1', 'item2'], nextCursor: 'cursor1', hasNextPage: true };
    const fetchData = vi.fn(() =>
      Promise.resolve({ data: { items: ['item3', 'item4'], nextCursor: 'cursor2', hasNextPage: false } })
    ) as any;
    const extractData = (data: any) => data;
    const { result } = renderHook(() =>
      useInfiniteScroll({
        initialData,
        fetchData,
        extractData,
        initialParams: {},
      })
    );

    await act(async () => {
      result.current.handleScroll({
        currentTarget: { scrollHeight: 200, scrollTop: 100, clientHeight: 100 },
      } as React.UIEvent<HTMLDivElement>);
    });

    expect(fetchData).toHaveBeenCalledTimes(1);
    expect(result.current.items).toEqual(['item1', 'item2', 'item3', 'item4']);
  });

  it('doesn\'t fetch more data when there are no more pages', async () => {
    const initialData = { items: ['item1', 'item2'], nextCursor: 'cursor1', hasNextPage: false };
    const fetchData = vi.fn();
    const extractData = (data: any) => data;
    const { result } = renderHook(() =>
      useInfiniteScroll({
        initialData,
        fetchData,
        extractData,
        initialParams: {},
      })
    );

    await act(async () => {
      result.current.handleScroll({
        currentTarget: { scrollHeight: 200, scrollTop: 100, clientHeight: 100 },
      } as React.UIEvent<HTMLDivElement>);
    });

    expect(fetchData).not.toHaveBeenCalled();
  });
});
