/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { renderHook } from '@testing-library/react';
import { usePartialAssurance } from './usePartialAssurance';
import { numberOneWithInput } from '@fixtures/utr/utr-base-fixtures';
import { utrvNumericValueListOne } from '@fixtures/utr/utr-numericValueList-fixtures';
import { utrvTextValueListOne } from '@fixtures/utr/utr-textValueList-fixtures';
import {
  tableOneColumnCodes,
  utrSingleRowTableOne,
  utrTableOne,
  utrTableOneUtrv,
} from '@fixtures/utr/utr-table-fixtures';
import { utrvValueListOne } from '@fixtures/utr/utr-ValueList-fixtures';
import { createAssuranceUtrv, partialFieldFixtures } from '@fixtures/assuranceUtrv-fixture';
import UniversalTracker from '@models/UniversalTracker';
import { AssuranceStatus } from '@g17eco/types/assurance';
import { griValueListTestOne } from '@fixtures/value-list-factory';
import { act } from 'react';

// ValueListCode
const [generated, distributed, retained] = griValueListTestOne.options;

// Utr fixtures
const numericUtr = new UniversalTracker(numberOneWithInput.universalTracker);
const valueListUtr = new UniversalTracker(utrvValueListOne.universalTracker);
const numericValueListUtr = new UniversalTracker(utrvNumericValueListOne.universalTracker);
const textValueListUtr = new UniversalTracker(utrvTextValueListOne.universalTracker);
const singleRowTableUtr = new UniversalTracker(utrSingleRowTableOne);
const multiRowTableUtr = new UniversalTracker(utrTableOne);

const assuranceUtrv = createAssuranceUtrv();

describe('usePartialAssurance', () => {
  describe('Initialize with props: utr, utrv, and assuranceUtrv', () => {
    it('utr is either number/text/percentage type, addons = []', () => {
      const { result } = renderHook(() => usePartialAssurance(numericUtr, numberOneWithInput, assuranceUtrv));
      expect(result.current.addons).toEqual([]);
    });

    it('utr is valueList type, addons = []', () => {
      const { result } = renderHook(() => usePartialAssurance(valueListUtr, utrvValueListOne, assuranceUtrv));
      expect(result.current.addons).toEqual([]);
    });

    it('utrv is not verified, addons = []', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(
          numericValueListUtr,
          { ...utrvNumericValueListOne, status: 'created' },
          { ...assuranceUtrv, partialFields: partialFieldFixtures.numericValueList }
        )
      );
      expect(result.current.addons).toEqual([]);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.numericValueList);
    });

    it('utrv is not verified, addons = []', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(
          numericValueListUtr,
          { ...utrvNumericValueListOne, status: 'created' },
          { ...assuranceUtrv, partialFields: partialFieldFixtures.textValueList }
        )
      );
      expect(result.current.addons).toEqual([]);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.textValueList);
    });

    it('assuranceUtrv is completed, addons = []', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(
          singleRowTableUtr,
          { ...utrvTextValueListOne, status: 'created' },
          { ...assuranceUtrv, partialFields: partialFieldFixtures.textValueList, status: AssuranceStatus.Completed }
        )
      );
      expect(result.current.addons).toEqual([]);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.textValueList);
    });

    it('valid textValueList question', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(textValueListUtr, utrvTextValueListOne, {
          ...assuranceUtrv,
          partialFields: partialFieldFixtures.textValueList,
          status: AssuranceStatus.Partial,
        })
      );
      expect(result.current.addons.length).toEqual(2);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.textValueList);
      expect(result.current.isSelectingAll).toBeTruthy();
    });

    it('valid singleRowTable question', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(singleRowTableUtr, utrTableOneUtrv, {
          ...assuranceUtrv,
          partialFields: partialFieldFixtures.singleRowTable,
          status: AssuranceStatus.Partial,
        })
      );
      expect(result.current.addons.length).toEqual(Object.values(tableOneColumnCodes).length);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.singleRowTable);
      expect(result.current.isSelectingAll).toBeTruthy();
    });

    it('valid multiRowTable question', () => {
      const { result } = renderHook(() =>
        usePartialAssurance(multiRowTableUtr, utrTableOneUtrv, {
          ...assuranceUtrv,
          partialFields: partialFieldFixtures.multiRowTable,
          status: AssuranceStatus.Partial,
        })
      );
      expect(result.current.addons.length).toEqual(3);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.multiRowTable);
      expect(result.current.isSelectingAll).toBeFalsy();
    });
  });

  describe('Trigger handler for state changes', () => {
    it('trigger change numericValueList question', async () => {
      const assuranceUtrvProp = {
        ...assuranceUtrv,
        partialFields: partialFieldFixtures.numericValueList,
        status: AssuranceStatus.Partial,
      };
      const { result, rerender } = renderHook(() =>
        usePartialAssurance(numericValueListUtr, utrvNumericValueListOne, assuranceUtrvProp)
      );
      expect(result.current.addons.length).toEqual(3);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.numericValueList);
      expect(result.current.isSelectingAll).toBeFalsy();

      // select a existed field
      act(() => {
        result.current.handlePartiallyAssuredValueList(distributed.code);
      });
      expect(result.current.partialFields).toEqual(
        partialFieldFixtures.numericValueList.filter((f) => f.code !== distributed.code)
      );
      expect(result.current.isSelectingAll).toBeFalsy();

      // select another existed field
      rerender();
      act(() => {
        result.current.handlePartiallyAssuredValueList(generated.code);
      });
      expect(result.current.partialFields).toEqual([]);
      expect(result.current.isSelectingAll).toBeFalsy();

      // select a new field
      rerender();
      act(() => {
        result.current.handlePartiallyAssuredValueList(retained.code);
      });
      expect(result.current.partialFields).toEqual([{ code: retained.code }]);
      expect(result.current.isSelectingAll).toBeFalsy();

      // select all fields
      rerender();
      act(() => {
        result.current.handlePartiallyAssuredValueList(generated.code);
        result.current.handlePartiallyAssuredValueList(distributed.code);
      });
      expect(result.current.partialFields).toEqual([{ code: retained.code }, ...partialFieldFixtures.numericValueList]);
      expect(result.current.isSelectingAll).toBeTruthy();
    });

    it('trigger change singleRowTable question', async () => {
      const assuranceUtrvProp = {
        ...assuranceUtrv,
        partialFields: partialFieldFixtures.singleRowTable,
        status: AssuranceStatus.Partial,
      };
      const { result } = renderHook(() => usePartialAssurance(singleRowTableUtr, utrTableOneUtrv, assuranceUtrvProp));
      expect(result.current.addons.length).toEqual(Object.values(tableOneColumnCodes).length);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.singleRowTable);
      expect(result.current.isSelectingAll).toBeTruthy();

      // select a existed field
      act(() => {
        result.current.handlePartiallyAssuredSingleRowTable(tableOneColumnCodes.col1);
      });
      expect(result.current.partialFields).toEqual(
        partialFieldFixtures.singleRowTable.filter((f) => f.code !== tableOneColumnCodes.col1)
      );
      expect(result.current.isSelectingAll).toBeFalsy();
    });

    it('trigger change multiRowTable question', () => {
      const assuranceUtrvProp = {
        ...assuranceUtrv,
        partialFields: partialFieldFixtures.multiRowTable,
        status: AssuranceStatus.Partial,
      };
      const { result, rerender } = renderHook(() =>
        usePartialAssurance(multiRowTableUtr, utrTableOneUtrv, assuranceUtrvProp)
      );
      expect(result.current.addons.length).toEqual(3);
      expect(result.current.partialFields).toEqual(partialFieldFixtures.multiRowTable);
      expect(result.current.isSelectingAll).toBeFalsy();

      // select a existed row
      act(() => {
        result.current.handlePartiallyAssuredMultiTable(0);
      });
      expect(result.current.partialFields).toEqual(partialFieldFixtures.multiRowTable.filter((f) => f.rowIndex !== 0));
      expect(result.current.isSelectingAll).toBeFalsy();

      // select all rows
      rerender();
      act(() => {
        result.current.handlePartiallyAssuredMultiTable(0);
        result.current.handlePartiallyAssuredMultiTable(1);
      });
      expect(result.current.partialFields).toEqual([
        ...Object.values(tableOneColumnCodes).map((code) => ({ code, rowIndex: 2 })),
        ...Object.values(tableOneColumnCodes).map((code) => ({ code, rowIndex: 0 })),
        ...Object.values(tableOneColumnCodes).map((code) => ({ code, rowIndex: 1 })),
      ]);
      expect(result.current.isSelectingAll).toBeTruthy();
    });
  });
});
