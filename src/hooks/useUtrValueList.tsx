/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import UniversalTracker from '@models/UniversalTracker';
import { UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import { ValueListPlain } from '@g17eco/types/valueList';
import { useGetValueListByIdQuery } from '@api/value-list';
import { skipToken } from '@reduxjs/toolkit/query';
import { loggerMessage } from '../logger';

export const useUtrValueList = (utr: UniversalTracker | UniversalTrackerPlain): ValueListPlain['options'] => {

  const utrPlain = utr instanceof UniversalTracker ? utr.getRaw() : utr;
  const valueListProps = utrPlain.valueValidation?.valueList;

  const { data, isSuccess, error, isError } = useGetValueListByIdQuery(valueListProps?.listId ?? skipToken, {
    // Skip we already have the list
    skip: Array.isArray(valueListProps?.list),
  });

  if (!valueListProps) {
    return [];
  }

  const { custom, type } = valueListProps;

  if (type === 'list' && Array.isArray(valueListProps.list)) {
    // Already have list available, not need to fetch
    return valueListProps.list;
  }


  if (type === 'custom') {
    return Array.isArray(custom) ? custom : [];
  }

  if (isError) {
    loggerMessage(error.message ?? 'Failed to fetch value list', {
      listId: valueListProps.listId,
      utrId: utrPlain._id,
    })
    return [];
  }

  return isSuccess ? data.options : [];
}
