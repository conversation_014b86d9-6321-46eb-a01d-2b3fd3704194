/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import * as actionTypes from '../constants/action-types';
import type { DefaultThunk } from '../reducers';

export const openUpgradeModal = (permissionRequired: string[] = []): DefaultThunk => (dispatch) => {
  dispatch({ type: actionTypes.SHOW_UPGRADE_MODAL, permissionRequired: permissionRequired });
};

export const closeUpgradeModal = (): DefaultThunk => (dispatch) => {
  dispatch({ type: actionTypes.HIDE_UPGRADE_MODAL });
};

