import { formDataHeaders } from '@services/G17Client';
import { g17ecoApi, transformResponse } from './g17ecoApi';
import { ApiResponse } from '@g17eco/types/api';
import { UserRoles } from '@constants/user';
import { TAG as MANAGE_USER_TAG } from './manage-users';
import { OnboardingEmailTransaction, OnboardingListExtended } from '@g17eco/types/onboarding';

export interface UserRowData {
  email: string;
  initiativeCode: string;
  initiativeName: string;
  permissions: UserRoles[];
  initiativeId?: string;
  message?: string;
  validPermissions: UserRoles[];
}

export interface ValidatedResult {
  validatedData: UserRowData[];
}

interface Params {
  data: { file: File };
  initiativeId: string;
}

const BULK_ONBOARDING_TAGS = ['bulk-onboarding-import'];
const ONBOARDING_EMAIL_TAG = 'onboarding-emails';

const getUrl = (initiativeId: string) => `/initiatives/${initiativeId}/users`;
const getOnboardingUrl = (initiativeId: string) => `/initiatives/${initiativeId}/onboardings`;

export const bulkOnboardingImportApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: [...BULK_ONBOARDING_TAGS, ONBOARDING_EMAIL_TAG, MANAGE_USER_TAG],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      validateImportFile: builder.mutation<ValidatedResult, Params>({
        transformResponse,
        query: ({ initiativeId, data }) => ({
          url: `${getUrl(initiativeId)}/import/validate`,
          headers: formDataHeaders,
          method: 'post',
          data: data,
        }),
        invalidatesTags: BULK_ONBOARDING_TAGS,
      }),
      bulkOnboardEmails: builder.mutation<ApiResponse, Params>({
        query: ({ initiativeId, data }) => ({
          url: `${getUrl(initiativeId)}/import`,
          headers: formDataHeaders,
          method: 'post',
          data: data,
        }),
        invalidatesTags: (_result, _error, { initiativeId }) => [
          ...BULK_ONBOARDING_TAGS,
          { type: MANAGE_USER_TAG, id: initiativeId },
        ],
      }),
      bulkOnboardLists: builder.query<OnboardingListExtended[], string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `${getUrl(initiativeId)}/import/list`,
          method: 'get',
        }),
        providesTags: BULK_ONBOARDING_TAGS,
      }),
      getOnboardingEmails: builder.query<OnboardingEmailTransaction[], { onboardingId: string; initiativeId: string }>({
        transformResponse,
        query: ({ initiativeId, onboardingId }) => ({
          url: `${getOnboardingUrl(initiativeId)}/${onboardingId}/emails`,
          method: 'get',
        }),
        providesTags: (_result, _error, { initiativeId }) => {
          return [{ type: ONBOARDING_EMAIL_TAG, id: initiativeId }];
        },
      }),
      resendOnboardingEmail: builder.mutation<ApiResponse, { onboardingId: string; initiativeId: string }>({
        transformResponse,
        query: ({ initiativeId, onboardingId }) => ({
          url: `${getOnboardingUrl(initiativeId)}/${onboardingId}/emails`,
          method: 'post',
        }),
        invalidatesTags: (_result, _error, { initiativeId }) => [{ type: ONBOARDING_EMAIL_TAG, id: initiativeId }],
      }),
    }),
  });

export const {
  useValidateImportFileMutation,
  useBulkOnboardEmailsMutation,
  useBulkOnboardListsQuery,
  useGetOnboardingEmailsQuery,
  useResendOnboardingEmailMutation,
} = bulkOnboardingImportApi;
