import { PreviousUtrv } from '@features/assistant';
import { g17ecoApi, transformResponse } from './g17ecoApi';
import { AdditionalContext, AIResponse } from '@g17eco/types/ai';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { AIAutoAnswerSurveyJobPlain, CreatedJob } from '@g17eco/types/ai-auto-answer-job';

interface GetAIUtrvAssistantParams {
  utrvId: string;
  initiativeId: string;
  additionalContext?: AdditionalContext;
}

export interface AIUtrvSuggestion {
  predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  questionExplanation: string;
  previousUtrvs: PreviousUtrv[];
  targetValue?: number;
  bestPractice: string[];
  keyInfo: string[];
  suggestedEvidence: {
    primaryDocumentation: string[];
    supportingDocumentation: string[];
  };
  whereToFind: {
    externalSource: string[];
    internalSource: string[];
  };
}

interface GetAIUtrvFurtherNotesDraftParams {
  initiativeId: string;
  data: {
    utrvId: string;
    draftData: Pick<UniversalTrackerValuePlain, 'value' | 'unit' | 'numberScale' | 'valueData'>;
  };
}

interface GetAIAutoAnswerSurveyParams {
  surveyId: string;
  initiativeId: string;
}

const UTRV_ASSISTANT_TAG = 'ai-utrv-assistant';
const AUTO_ANSWER_SURVEY_TAG = 'ai-auto-answer-survey';
const TAGS = [UTRV_ASSISTANT_TAG, AUTO_ANSWER_SURVEY_TAG];

export const aiApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: TAGS,
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getAIUtrvAssistant: builder.query<AIUtrvSuggestion, GetAIUtrvAssistantParams>({
        transformResponse,
        query: ({ initiativeId, utrvId, additionalContext }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/utrv-assistant/${utrvId}`,
            method: 'post',
            data: additionalContext,
          };
        },
        providesTags: (_result, _error, agrs) => [{ type: UTRV_ASSISTANT_TAG, id: agrs.utrvId }],
      }),
      getAIUtrvFurtherNotesDraft: builder.mutation<AIResponse, GetAIUtrvFurtherNotesDraftParams>({
        transformResponse,
        query: ({ initiativeId, data }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/generate-draft/further-notes`,
            method: 'post',
            data,
          };
        },
      }),
      getAIAutoAnswerSurvey: builder.query<{ job: AIAutoAnswerSurveyJobPlain }, GetAIAutoAnswerSurveyParams>({
        transformResponse,
        query: ({ initiativeId, surveyId }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/survey/${surveyId}/auto-answer`,
            method: 'get',
          };
        },
        providesTags: (_result, _error, arg) => [{ type: AUTO_ANSWER_SURVEY_TAG, id: arg.surveyId }],
      }),
      aiAutoAnswerSurvey: builder.mutation<CreatedJob, GetAIAutoAnswerSurveyParams>({
        transformResponse,
        query: ({ initiativeId, surveyId }) => ({
          url: `/initiatives/${initiativeId}/ai/survey/${surveyId}/auto-answer`,
          method: 'post',
        }),
        invalidatesTags: (_result, _error, arg) => [{ type: AUTO_ANSWER_SURVEY_TAG, id: arg.surveyId }],
      }),
    }),
  });

export const {
  useGetAIUtrvAssistantQuery,
  useGetAIUtrvFurtherNotesDraftMutation,
  useGetAIAutoAnswerSurveyQuery,
  useAiAutoAnswerSurveyMutation,
} = aiApi;
