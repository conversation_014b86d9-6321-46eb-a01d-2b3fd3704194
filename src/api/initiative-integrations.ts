/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { g17ecoApi, transformResponse } from '@api/g17ecoApi';
import { IntegrationConnectionInfo, IntegrationProvider, IntegrationProviderExtended, ProviderSetupInfo, SetupData } from '@g17eco/types/integration';
import { ValueData } from '@g17eco/types/surveyScope';


interface PartnerGet {
  initiativeId: string,
  code: string
}

interface InitiativeIntegrationsResponse {
  allProviders: IntegrationProvider[];
  usedProviders: IntegrationProviderExtended[];
}

export const integrationsApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: ['integrations', 'integration', 'integration-setup-info'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      listInitiativeIntegrations: builder.query<InitiativeIntegrationsResponse, string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `/initiatives/${initiativeId}/integrations`,
          method: 'get',
        }),
        providesTags: ['integrations']
      }),
      getInitiativeIntegration: builder.query<ProviderSetupInfo, PartnerGet>({
        transformResponse,
        query: ({ initiativeId, code }) => ({
          url: `/initiatives/${initiativeId}/integrations/${code}`,
          method: 'get',
        }),
        providesTags: (_result, _error, arg) => [{ type: 'integration-setup-info', id: arg.code }],
      }),
      getUsedInitiativeIntegration: builder.query<
        IntegrationConnectionInfo<{ answers: { valueData: ValueData }[] }>,
        PartnerGet
      >({
        transformResponse,
        query: ({ initiativeId, code }) => ({
          url: `/initiatives/${initiativeId}/integrations/${code}/used`,
          method: 'get',
        }),
        providesTags: (_result, _error, arg) => [{ type: 'integration', id: arg.code }],
      }),
      createInitiativeIntegration: builder.mutation<ProviderSetupInfo, SetupData>({
        transformResponse,
        query: ({ initiativeId, code, ...data }) => ({
          url: `/initiatives/${initiativeId}/integrations/${code}`,
          method: 'post',
          data: data,
        }),
        invalidatesTags: (_result, _error, agr) => ['integrations', { type: 'integration-setup-info', id: agr.code }],
      }),
    }),
  });

export const {
  useGetInitiativeIntegrationQuery,
  useListInitiativeIntegrationsQuery,
  useGetUsedInitiativeIntegrationQuery,
  useCreateInitiativeIntegrationMutation,
} = integrationsApi;
