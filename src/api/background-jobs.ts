import { g17ecoApi, transformResponse } from './g17ecoApi';

export const backgroundJobApi = g17ecoApi.injectEndpoints({
  endpoints: (build) => ({
    retryJob: build.mutation<{ jobId: string }, string>({
      query: (id) => ({
        url: `background-jobs/${id}/rerun`,
        method: 'post',
      }),
      transformResponse,
    }),
  }),
});

export const { useRetryJobMutation } = backgroundJobApi;
