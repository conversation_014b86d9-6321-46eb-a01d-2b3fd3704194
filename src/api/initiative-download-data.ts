/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { g17ecoApi, transformResponse } from '@api/g17ecoApi';
import { ExportInitiativeDataFullJobPlain, JobStatus } from '@g17eco/types/background-jobs';

type CreateJobResult = {
  jobId: string;
  status: JobStatus;
};

const EXPORT_DATA_TAG = 'export-data-full';

const exportInitiativeApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: [EXPORT_DATA_TAG],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getExportInitiativeDataFull: builder.query<{ job: ExportInitiativeDataFullJobPlain }, string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `/initiatives/${initiativeId}/export/full`,
          method: 'get',
        }),
        providesTags: (_result, _error, arg) => [{ type: EXPORT_DATA_TAG, id: arg }],
      }),
      exportInitiativeDataFull: builder.mutation<CreateJobResult, string>({
        transformResponse,
        query: (initiativeId) => ({
          url: `/initiatives/${initiativeId}/export/full`,
          method: 'post',
        }),
        invalidatesTags: (_result, _error, arg) => [{ type: EXPORT_DATA_TAG, id: arg }],
      }),
      getBundleSignedUrl: builder.mutation<{ url: string }, { initiativeId: string; jobId: string }>({
        transformResponse,
        query: ({ initiativeId, jobId }) => ({
          url: `/initiatives/${initiativeId}/export/signed-url`,
          method: 'post',
          data: {
            jobId,
          },
        }),
      }),
    }),
  });

export const { useGetExportInitiativeDataFullQuery, useExportInitiativeDataFullMutation, useGetBundleSignedUrlMutation } = exportInitiativeApi;
