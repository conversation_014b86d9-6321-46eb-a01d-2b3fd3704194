import { ApiResponse } from '../types/api';
import { StockExchange } from '@g17eco/types/stock-exchange-settings';
import { g17ecoApi } from './g17ecoApi';

export const stockExchangeSettingsApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: ['stock-exchange-settings'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getListOfStockExchanges: builder.query<StockExchange[], void>({
        transformResponse: (response: ApiResponse<StockExchange[]>) => response.data,
        query: () => ({
          url: '/stock-exchange-settings/list',
          method: 'get',
        }),
        providesTags: ['stock-exchange-settings'],
      }),
    }),
  });

export const { useGetListOfStockExchangesQuery } = stockExchangeSettingsApi;
