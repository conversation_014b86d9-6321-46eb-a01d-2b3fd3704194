import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { SurveyActionData } from '@models/surveyData';
import { CurrentUserData } from '@reducers/current-user';

export type PermissionsUser = Pick<CurrentUserData, '_id' | 'permissions' | 'initiativeTree'>;
export type PermissionsSurvey = Pick<SurveyActionData, '_id' | 'roles' | 'stakeholders' | 'initiativeId'>;
export type PermissionsUtrv = Pick<
  UniversalTrackerValuePlain,
  '_id' | 'stakeholders' | 'compositeData' | 'initiativeId'
>;
