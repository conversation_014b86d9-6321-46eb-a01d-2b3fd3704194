/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { CalculationValue, GridDashboardChartItem } from '../../types/insight-custom-dashboard';
import { ResultStage, ValueStage, ValueStages } from '@services/aggregation/ValueStage';
import { FormulaVariables, tryCalculation } from '@utils/formula';
import { UniversalTrackerPlain, UtrValueType } from '../../types/universalTracker';
import type { UniversalTrackerModalServiceUtrv } from '../../reducers/universal-tracker-modal';
import { ValueData } from '@components/survey/question/questionInterfaces';
import { processExpression } from '@services/aggregation/jsonata';
import { handleRouteError, loggerMessage } from '../../logger';

type StageUtrv = Pick<UniversalTrackerModalServiceUtrv,
  | 'value'
  | 'valueData'
  | 'disaggregation'
  | 'valueType'
>;

export interface UtrStageData {
  utr: Pick<UniversalTrackerPlain, 'code' | 'valueType'>;
  utrvs: StageUtrv[];
}

export interface SetupData extends Pick<GridDashboardChartItem, 'variables'> {
  utrsData: UtrStageData[];
  stages: CalculationValue['stages'];
  fallback?: string | number;
  /**
   * When passed will keep updating with stage results
   * Useful when these results need to be used in tooltip or other places after
   * resolving the value
   **/
  acc?: StageResult;
}

type ResultValue = number | undefined | string | string[];
type StageResult = {
  result: ResultValue;
  [k: string]: ResultValue;
};

type DataInputRecord = Record<string, { value: ResultValue }>;

interface DataInput {
  rootValue?: number;
  data: DataInputRecord[];
}


interface ProcessStageParams {
  inputs: DataInput[];
  fallback: string | number;
  stage: ValueStage | ResultStage;
  results: Readonly<StageResult>;
}

async function executeJSONata({ stage, inputs, results, fallback }: ProcessStageParams) {
  try {
    const result = await processExpression(stage.formula, inputs, results) as ResultValue;
    if (isNaN(result as number)) {
      return fallback;
    }

    return result
  } catch (e) {
    handleRouteError(e, { stage })
    return fallback;
  }
}

const processStage = async (params: ProcessStageParams): Promise<ResultValue> => {

  const { stage, inputs, fallback } = params
  if (stage.type === 'mathjs') {
    const [firstInput] = inputs;
    if (!firstInput) {
      return fallback;
    }

    return tryCalculation({
      formula: stage.formula,
      variables: firstInput.data.reduce((acc, row) => {
        // @TODO This only works with single value inputs, not handling multi row tables
        Object.entries(row).forEach(([key, data]) => {
          // Table valueList is also not working
          acc[key] = Array.isArray(data.value) ? data.value.join(',') : data.value;
        })
        return acc;
      }, { rootValue: firstInput.rootValue } as FormulaVariables),
      fallback,
    })
  }

  if (stage.type === 'jsonata') {
    return executeJSONata(params);
  }

  loggerMessage(`Failed to find the right stage type ${stage.type} processor`, {
    stage
  });
  return fallback;
};

//  Record<string, { value: ResultValue }>
// [{ col1: { value: '235 } }, { col2: { value: undefined } }],
const convertToInputData = (valueType: string | UtrValueType, valueData: ValueData | undefined): DataInputRecord[] => {
  switch (valueType) {
    case UtrValueType.Table: {
      return valueData?.table?.map(row => {
        const acc: DataInputRecord = {}
        row.forEach((data) => {
          acc[data.code] = { value: data.value };
        })
        return acc;
      }) ?? [];
    }
    case UtrValueType.NumericValueList:
    case UtrValueType.TextValueList: {
      if (typeof valueData?.data === 'object' && !Array.isArray(valueData.data)) {
        return [
        ]
      }
      return [];
    }
    case UtrValueType.ValueListMulti: {
      const multiList = valueData?.data;
      return Array.isArray(multiList) ? multiList.map((value) => ({ [value]: { value } })) : [];
    }
    case UtrValueType.ValueList: {
      if (typeof valueData?.data === 'string') {
        return [{ [valueData.data]: { value: valueData?.data } }];
      }
      return [];
    }
    case UtrValueType.Percentage:
    case UtrValueType.Sample:
    case UtrValueType.Number:
    default:
      return [];
  }
};

function prepareInputs(input: ValueStages[0]['input'], inputData: UtrStageData): DataInput[] {

  if (input.context === 'all' || input.context === 'utrv') {
    const data = input.context === 'all' ? inputData.utrvs : inputData.utrvs.slice(0, 1);
    return data.map(utrv => {
      return {
        rootValue: utrv.value,
        data: convertToInputData(utrv.valueType ?? inputData.utr.valueType, utrv.valueData)
      } satisfies DataInput;
    });
  }

  if (input.context === 'disaggregations') {
    return inputData.utrvs.reduce((acc, { disaggregation = [] }) => {
      disaggregation.forEach(v => {
        acc.push({
          rootValue: v.value,
          data: convertToInputData(inputData.utr.valueType, v.valueData),
        });
      });
      return acc;
    }, [] as DataInput[]);
  }
  return [] as DataInput[];
}

export const processCalculation = async ({ stages, variables, utrsData, fallback = '', acc: initialAcc }: SetupData) => {

  if (!stages?.length) {
    return fallback;
  }

  const acc = initialAcc ?? {} as StageResult;

  for (const stage of stages) {
    const input = stage.input;

    if (input.context === 'output') {
      const result = stage.type === 'jsonata' ?
        // Example: The left side of the "/" operator must evaluate to a number
        // Silence expression errors and return fallback
        await processExpression(stage.formula, acc).catch(() => fallback) :
        tryCalculation({
          formula: stage.formula,
          variables: acc as FormulaVariables,
          fallback,
        });

      if ('decimals' in stage && stage.decimals && typeof result === 'number') {
        return Number(result.toFixed(stage.decimals));
      }
      // Allow to storage the result for further re-use (look tooltip consumption)
      acc[stage.output] = result;
      return result;
    }

    const utrCode = variables[input.variable]?.code;
    if (!utrCode) {
      return fallback;
    }

    const inputData = utrsData.find(data => data.utr.code === utrCode);
    if (!inputData) {
      return fallback;
    }

    acc[stage.output] = await processStage({
      stage,
      inputs: prepareInputs(input, inputData),
      results: acc,
      fallback,
    });
  }
}


