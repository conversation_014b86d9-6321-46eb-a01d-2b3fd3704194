/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


type StageProcessingType = 'mathjs' | 'jsonata';

export interface ValueStage {
  input: {
    context: 'all' | 'utrv' | 'disaggregations';
    variable: string;
  },
  type: StageProcessingType;
  formula: string;
  output: string;
}

export interface ResultStage {
  input: { context: 'output' },
  type: StageProcessingType,
  formula: string;
  output: string;
  decimals?: number;
}

/**
 * @unstable
 * Experimental new way of processing complex calculation stages required
 * to aggregate a value used in custom dashboard widgets.
 *
 * Until this becomes stable and more defined, we should avoid saving these
 * in database to avoid migrations.
 *
 */
export type ValueStages = [...ValueStage[], ResultStage];
