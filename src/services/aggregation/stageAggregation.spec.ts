/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { processCalculation, SetupData } from './stageAggregation';
import { numberTwoWithCurrency } from '@fixtures/utr/utr-base-fixtures';
import { createUtr, createUtrv } from '@fixtures/utr/utrv-factory';
import { ResultStage, ValueStage } from '@services/aggregation/ValueStage';
import { UtrValueType } from '../../types/universalTracker';
import { UniversalTrackerValuePlain } from '../../types/surveyScope';
import { transformVariables } from '../../routes/custom-dashboard/utils';
import { gri401_1_b, gri4051b, utrGri_401_1_b, utrGri_405_1_b } from '@fixtures/utr/utr-table-fixtures';

interface MathjsStage {
  formula: string;
  output?: string;
  variable?: string;
}

interface TestCaseSetup extends SetupData {
  name: string;
  expectedResult: undefined | number | string;
}

interface VariableSetup {
  utr: {
    code: string,
    valueListCode?: string;
    valueType?: UtrValueType | string;
  };
  utrvs: Pick<UniversalTrackerValuePlain, 'value' | 'valueType' | 'valueData'>[];
}

describe('stage aggregation process', () => {


  const createResultStage = (formula: string, decimals?: number): ResultStage => ({
    input: { context: 'output', },
    type: 'mathjs',
    formula,
    output: 'result',
    decimals
  });

  const createMathjsStage = ({ formula, output = 'result', variable = 'a' }: MathjsStage): ValueStage => ({
    input: { context: 'utrv', variable, },
    formula,
    type: 'mathjs',
    output,
  })

  describe('processCalculation', () => {

    it('should handle undefined stages', async () => {
      const result = await processCalculation({
        stages: undefined,
        variables: {},
        utrsData: [],
      });
      expect(result).toEqual('');
    });

    it('should handle undefined stages with pass in fallback', async () => {
      const fallback = 0;
      const result = await processCalculation({
        stages: undefined,
        variables: {},
        utrsData: [],
        fallback: fallback,
      });
      expect(result).toEqual(fallback);
    });

    it('should handle empty stages', async () => {

      const result = await processCalculation({
        stages: [] as any,
        variables: {},
        utrsData: [],
      });
      expect(result).toEqual('');
    });

    it('should process final stage stages', async () => {
      const result = await processCalculation({
        stages: [{
          input: { context: 'output' },
          type: 'mathjs',
          formula: '{scope1} + {scope2}',
          output: 'result',
        }],
        variables: {},
        utrsData: [],
      });
      expect(result).toEqual('');
    });


    const testCases: TestCaseSetup[] = [
      {
        name: 'Simple root value extraction',
        utrsData: [
          {
            utr: numberTwoWithCurrency.universalTracker,
            utrvs: [numberTwoWithCurrency],
          }
        ],
        variables: {
          a: { code: numberTwoWithCurrency.universalTracker.code },
        },
        stages: [
          {
            input: { context: 'utrv', variable: 'a', },
            formula: '{rootValue}',
            type: 'mathjs',
            // Will add to variables
            output: 'scope1',
          },
          createResultStage('{scope1}'),
        ],
        expectedResult: numberTwoWithCurrency.value,
      },
    ]

    testCases.forEach(({ name, stages, variables, utrsData, expectedResult }) => {
      it(name, async () => {
        const result = await processCalculation({ stages, variables, utrsData });
        expect(result).toEqual(expectedResult);
      });
    });
  });

  describe('JSONata processing', () => {


    const createVariablesData = (setup: VariableSetup[]): Pick<TestCaseSetup, 'variables' | 'utrsData'> => {
      return {
        variables: transformVariables(setup.map(({ utr }) => utr)),
        utrsData: setup.map(({ utr, utrvs }) => {
          return {
            utr: createUtr(utr.code, utr),
            utrvs: utrvs,
          };
        }),
      }
    }

    const variablesData = createVariablesData([
      {
        utr: { code: 'a' },
        utrvs: [{ value: 1 }, { value: 9 }],
      },
      {
        utr: { code: 'b' },
        utrvs: [{ value: 1 }, { value: 3 }],
      }
    ]);

    it('[GU-4920] handle error in the formula', async () => {
      const result = await processCalculation({
        stages: [
          {
            input: { context: 'utrv', variable: 'a' },
            // Error:  The left side of the "/" operator must evaluate to a number
            formula: '$sum(data.($number(employees_per_category.value) / 100 * $number(male.value)))',
            type: 'jsonata',
            output: 'maleTotal',
          },
          {
            input: { context: 'output' },
            type: 'jsonata',
            formula: 'maleTotal * 100',
            output: 'malePc',
            decimals: 2,
          },
        ],
        ...createVariablesData([
          {
            utr: utrGri_401_1_b,
            utrvs: [createUtrv(utrGri_401_1_b._id, { valueData: { table: [], } })]
          },
        ])
      });
      // Error:  The left side of the "/" operator must evaluate to a number will fallback to ''
      expect(result).toEqual('');
    });

    const testCases: TestCaseSetup[] = [
      {
        name: 'Simple Select first aggregation',
        ...variablesData,
        stages: [
          createMathjsStage({ formula: '{rootValue}', output: 'scope1' }),
          createMathjsStage({ formula: '{rootValue}', output: 'scope2' }),
          createResultStage('{scope1} + {scope2}'),
        ],
        expectedResult: 2,
      },
      {
        name: 'Simple Select first aggregation',
        ...variablesData,
        stages: [
          createMathjsStage({ formula: '{rootValue}', output: 'scope1' }),
          createMathjsStage({ formula: '{rootValue}', output: 'scope2' }),
          createResultStage('{scope1} + {scope2}'),
        ],
        expectedResult: 2,
      },
      {
        name: 'Aggregation of combined utrvs',
        ...variablesData,
        stages: [
          createMathjsStage({ formula: '{rootValue}', output: 'scope1' }),
          createMathjsStage({ formula: '{rootValue}', output: 'scope2' }),
          {
            input: {
              context: 'all',
              variable: 'b',
            },
            formula: '$sum(rootValue)',
            type: 'jsonata',
            output: 'combinedTotal',
          },
          //  1 + (1 / 4) = 1.25
          createResultStage('{scope1} + ({scope2} / {combinedTotal})'),
        ],
        expectedResult: 1.25,
      },
      {
        name: 'Calculation for 405-1/b male',
        ...createVariablesData([{ utr: utrGri_405_1_b, utrvs: [gri4051b] }]),
        stages: [
          {
            input: { context: 'utrv', variable: 'a' },
            formula: '$sum(data.($number(employees_per_category.value) / 100 * $number(male.value)))',
            type: 'jsonata',
            output: 'male_total',
          },
          {
            input: { context: 'utrv', variable: 'a' },
            formula: '$sum(data.($number(employees_per_category.value)))',
            type: 'jsonata',
            output: 'combinedTotal',
          },
          {
            input: { context: 'output' },
            type: 'jsonata',
            //  (15.84 + 3600)/(5033) * 100  = 71.84
            formula: 'male_total / combinedTotal * 100',
            output: 'result',
            decimals: 2,
          } as ResultStage,
        ],
        expectedResult: 71.84,
      },

      {
        name: 'Calculation for 401-1/b male',
        ...createVariablesData([{ utr: utrGri_401_1_b, utrvs: [gri401_1_b] }]),
        stages: [
          {
            input: { context: 'utrv', variable: 'a' },
            formula: '$sum(data[gender.value="male1"].($number(total_turnover.value)))',
            type: 'jsonata',
            output: 'maleTotal',
          },
          {
            input: { context: 'output' },
            type: 'jsonata',
            formula: 'maleTotal',
            output: 'result',
            decimals: 2,
          } as ResultStage,
        ],
        //  (700+200+100) + (300+200+300) = 1800
        expectedResult: 1800,
      },
      {
        name: 'Calculation for 401-1/b female',
        ...createVariablesData([{ utr: utrGri_401_1_b, utrvs: [gri401_1_b] }]),
        stages: [
          {
            input: { context: 'utrv', variable: 'a' },
            formula: '$sum(data[gender.value="females1"].($number(total_turnover.value)))',
            type: 'jsonata',
            output: 'femaleTotal',
          },
          {
            input: { context: 'output' },
            type: 'jsonata',
            formula: 'femaleTotal',
            output: 'result',
            decimals: 2,
          } as ResultStage,
        ],
        expectedResult: 600,
      },
    ];

    testCases.forEach(({ name, stages, variables, utrsData, expectedResult }) => {
      it(name, async () => {
        const result = await processCalculation({ stages, variables, utrsData });
        expect(result).toEqual(expectedResult);
      });
    })
  });
});
