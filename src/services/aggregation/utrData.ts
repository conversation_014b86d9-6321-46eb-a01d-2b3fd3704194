/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { GridDashboardChartItem } from '../../types/insight-custom-dashboard';
import { processCalculation, UtrStageData } from '@services/aggregation/stageAggregation';
import { UtrvType } from '@constants/status';
import { DATE, formatDateUTC } from '@utils/date';
import { BaseHistoricalUtr, ChartUtrData } from '@routes/custom-dashboard/items/types';

interface GenerateGroupedDataParams extends Required<Pick<GridDashboardChartItem, 'calculation' | 'variables'>> {
  date: string;
  utrsData: UtrStageData[];
  fallback?: number | string;
}

export const generateGroupedStageData = async (setupData: GenerateGroupedDataParams) => {

  const { fallback, date, calculation, variables, utrsData } = setupData;

  const acc = { result: fallback };
  const resultRow: (string | number)[] = [date];

  // Iterate 1 by one to ensure we can re-use accumulator for annotations
  for (const value of calculation.values) {
    const result = await processCalculation({
      variables,
      utrsData,
      stages: value.stages,
      fallback,
      acc
    })
    resultRow.push(result);
  }
  return resultRow;
};

export const getActualUtrsDataGrouped = (utrsData: BaseHistoricalUtr[], format = DATE.DATE_PICKER) => {

  const results = utrsData.reduce((acc, { utr, utrvs }) => {

    utrvs.forEach(utrv => {
      if (utrv.type !== UtrvType.Actual) {
        return; // Skip
      }
      const date = formatDateUTC(utrv.effectiveDate, format);
      const utrId = utr._id;

      if (!acc[date]) {
        acc[date] = {
          [utrId]: { utr, utrvs: [utrv], effectiveDate: utrv.effectiveDate }
        }
        return;
      }

      if (!acc[date][utrId]) {
        acc[date][utrId] = { utr, utrvs: [utrv], effectiveDate: utrv.effectiveDate };
        return;
      }
      acc[date][utrId].utrvs.push(utrv);

      // Store latest effectiveDate
      if (acc[date][utrId].effectiveDate < utrv.effectiveDate) {
        acc[date][utrId].effectiveDate = utrv.effectiveDate
      }

    })

    return acc;
  }, {} as {
    [date: string]: {
      [utrId: string]: BaseHistoricalUtr & { effectiveDate: string };
    }
  });

  return Object.entries(results).map(([date, utr]) => {
    return {
      date,
      utrsData: Object.values(utr),
    }
  })
};

const sortDateDesc = (a: { date: string }, b: { date: string }) => {
  if (a.date > b.date) {
    return -1; // Push (a) to the to front of array
  }
  return a.date < b.date ? 1 : 0;
}

export const getLatestActualHistoricalUtrs = (utrsData: ChartUtrData[]) => {

  // Use sortable date picker format
  const grouped = getActualUtrsDataGrouped(utrsData, DATE.ISO).sort(sortDateDesc);

  const [first] = grouped;

  return {
    effectiveDate: first?.date,
    latestUtrsData: first?.utrsData ?? [],
    // Assumptions that all data is the same period by now,
    // hence first one we can get is good enough
    period: first?.utrsData?.[0]?.utrvs[0]?.period,
  }
}
