/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

const googleDomains = [
  '.google.com', '.google.ad', '.google.ae', '.google.com.af', '.google.com.ag', '.google.al', '.google.am', '.google.co.ao', '.google.com.ar', '.google.as', '.google.at',
  '.google.com.au', '.google.az', '.google.ba', '.google.com.bd', '.google.be', '.google.bf', '.google.bg', '.google.com.bh', '.google.bi', '.google.bj', '.google.com.bn',
  '.google.com.bo', '.google.com.br', '.google.bs', '.google.bt', '.google.co.bw', '.google.by', '.google.com.bz', '.google.ca', '.google.cd', '.google.cf', '.google.cg',
  '.google.ch', '.google.ci', '.google.co.ck', '.google.cl', '.google.cm', '.google.cn', '.google.com.co', '.google.co.cr', '.google.com.cu', '.google.cv', '.google.com.cy',
  '.google.cz', '.google.de', '.google.dj', '.google.dk', '.google.dm', '.google.com.do', '.google.dz', '.google.com.ec', '.google.ee', '.google.com.eg', '.google.es',
  '.google.com.et', '.google.fi', '.google.com.fj', '.google.fm', '.google.fr', '.google.ga', '.google.ge', '.google.gg', '.google.com.gh', '.google.com.gi', '.google.gl',
  '.google.gm', '.google.gr', '.google.com.gt', '.google.gy', '.google.com.hk', '.google.hn', '.google.hr', '.google.ht', '.google.hu', '.google.co.id', '.google.ie',
  '.google.co.il', '.google.im', '.google.co.in', '.google.iq', '.google.is', '.google.it', '.google.je', '.google.com.jm', '.google.jo', '.google.co.jp', '.google.co.ke',
  '.google.com.kh', '.google.ki', '.google.kg', '.google.co.kr', '.google.com.kw', '.google.kz', '.google.la', '.google.com.lb', '.google.li', '.google.lk', '.google.co.ls',
  '.google.lt', '.google.lu', '.google.lv', '.google.com.ly', '.google.co.ma', '.google.md', '.google.me', '.google.mg', '.google.mk', '.google.ml', '.google.com.mm',
  '.google.mn', '.google.com.mt', '.google.mu', '.google.mv', '.google.mw', '.google.com.mx', '.google.com.my', '.google.co.mz', '.google.com.na', '.google.com.ng',
  '.google.com.ni', '.google.ne', '.google.nl', '.google.no', '.google.com.np', '.google.nr', '.google.nu', '.google.co.nz', '.google.com.om', '.google.com.pa',
  '.google.com.pe', '.google.com.pg', '.google.com.ph', '.google.com.pk', '.google.pl', '.google.pn', '.google.com.pr', '.google.ps', '.google.pt', '.google.com.py',
  '.google.com.qa', '.google.ro', '.google.ru', '.google.rw', '.google.com.sa', '.google.com.sb', '.google.sc', '.google.se', '.google.com.sg', '.google.sh', '.google.si',
  '.google.sk', '.google.com.sl', '.google.sn', '.google.so', '.google.sm', '.google.sr', '.google.st', '.google.com.sv', '.google.td', '.google.tg', '.google.co.th',
  '.google.com.tj', '.google.tl', '.google.tm', '.google.tn', '.google.to', '.google.com.tr', '.google.tt', '.google.com.tw', '.google.co.tz', '.google.com.ua',
  '.google.co.ug', '.google.co.uk', '.google.com.uy', '.google.co.uz', '.google.com.vc', '.google.co.ve', '.google.co.vi', '.google.com.vn', '.google.vu', '.google.ws',
  '.google.rs', '.google.co.za', '.google.co.zm', '.google.co.zw', '.google.cat'
];

const cspConfig = {
  'default-src': [
    '\'self\''
  ],
  'connect-src': [
    '*.g17.eco',
    'wss://*.g17.eco',
    '*.okta.com',
    'sentry.io',
    '*.opencagedata.com',
    '*.magicbell.com',
    'wss://realtime.ably.io',
    '*.ably-realtime.com',
    'wss://*.ably-realtime.com',
    '*.ably.io',
    '*.googleapis.com',
    '*.purechat.com',
    '*.graphcms.com',
    '*.google-analytics.com',
    '*.analytics.google.com',
    'stats.g.doubleclick.net',
    'analytics.google.com',
    'api.segment.io',
    '*.hotjar.com',
    '*.hotjar.io',
    'wss://*.hotjar.com',
    '*.segment.com',
    '*.pendo.io',
    '*.amazonaws.com',
    'ipapi.co',

    // ZenDesk
    '*.smooch.io',
    'wss://*.smooch.io',
    '*.zdassets.com',
    '*.zendesk.com',
    '*.weglot.com',
    'cdn-api-weglot.com',

    // SVG icons less than 4Kb
    'data:',
  ],
  'script-src': [
    '\'self\'',
    '\'unsafe-inline\'',
    'www.gstatic.com',
    'www.googletagmanager.com',
    'app.purechat.com',
    'prod.purechatcdn.com',
    // ZenDesk
    '*.smooch.io',
    '*.zdassets.com',
    'www.googleadservices.com',
    '*.google-analytics.com',
    'cdn.segment.com',
    '*.hotjar.com',
    '*.pendo.io',
    '*.storage.googleapis.com',
    '*.weglot.com',
  ],
  'img-src': [
    '\'self\'',
    '*.amazonaws.com',
    '*.googleapis.com',
    'api.purechat.com',
    '*.zdassets.com',
    '*.zendesk.com',
    '*.magicbell.com',
    '*.magicbell.io',
    '*.pendo.io',
    '*.wp.com',
    'secure.gravatar.com',
    '*.google-analytics.com',
    ...googleDomains.map(domain => `www${domain}`),
    'www.googletagmanager.com',
    'googleads.g.doubleclick.net',
    '*.graphcms.com',
    'media.graphassets.com',
    'data:',
    'blob:',
    '*.blob.core.windows.net' // Azure infrastructure
  ],
  'style-src': [
    '\'self\'',
    '\'unsafe-inline\'',
    'www.gstatic.com',
    '*.pendo.io',
    '*.weglot.com',
  ],
  'frame-src': [
    '*.ab.qa.greenprojecttech.com',
    '*.app.qa.greenprojecttech.com',
    'player.vimeo.com',
    '*.g17.eco',
    '*.okta.com',
    '*.pendo.io',
    '*.hotjar.com',
    'td.doubleclick.net'
  ],
  'media-src': [
    '*.amazonaws.com',
    '*.googleapis.com',
    '*.graphcms.com',
    'media.graphassets.com',
    'blob:'
  ],
  'font-src': [
    '\'self\'',
    'data:',
    '*.hotjar.com'
  ],
}

export function generateCSP(port?: number, api?: string, websocketUrl?: string): string {
  if (port) {
    cspConfig['connect-src'].push('http://localhost:' + port);
    cspConfig['connect-src'].push('ws://localhost:' + port);
  }
  if (websocketUrl) {
    cspConfig['connect-src'].push(websocketUrl);
  }

  if (api) {
    cspConfig['connect-src'].push(api);
  }

  return Object.entries(cspConfig).reduce((acc, [type, typeValues]) => {
    return [...acc, [type, ...typeValues].join(' ')];
  }, [] as string[]).join(';');
}
