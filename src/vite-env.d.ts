/// <reference types="vite/client" />
/// <reference types="redux-thunk/extend-redux" />

declare module 'd3-v6-tip';
declare var pendo: undefined | { initialize: Function; identify: Function; visitorId?: string; accountId?: string };

type zMessenger = (cmd: 'messenger:set', action: 'zIndex', zIndex: number) => void;
declare var zE: undefined | zMessenger;

declare module '*.module.scss' {
  const styles: { [className: string]: string };
  export default styles;
}

declare module '@analytics/segment' {
  import type { AnalyticsPlugin } from 'analytics';

  export interface SegmentConfig {
    writeKey: string;
    disableAnonymousTraffic?: boolean;
  }
  function segmentPlugin(config: SegmentConfig): AnalyticsPlugin;

  export default segmentPlugin;
}

declare module '@analytics/google-tag-manager' {
  import type { AnalyticsPlugin } from 'analytics';

  export interface TagManagerConfig {
    containerId: string;
  }

  function googleTagManagerPlugin(config: TagManagerConfig): AnalyticsPlugin;
  export default googleTagManagerPlugin;
}

declare module '@analytics/google-analytics' {
  import type { AnalyticsPlugin } from 'analytics';

  const defaultGtagConf = {
    // https://support.google.com/analytics/answer/7201382?hl=en#zippy=%2Cglobal-site-tag-websites
    debug_mode: false,
    /**
     * Disable automatic sending of page views, instead let analytics.page() do this
     * https://developers.google.com/analytics/devguides/collection/gtagjs
     */
    send_page_view: false,
    // https://developers.google.com/analytics/devguides/collection/gtagjs/ip-anonymization
    anonymize_ip: false,
    /**
     * Disable All Advertising
     * https://developers.google.com/analytics/devguides/collection/ga4/display-features#disable_all_advertising_features
     */
    allow_google_signals: true,
    /**
     * Disable Advertising Personalization
     * https://developers.google.com/analytics/devguides/collection/ga4/display-features#disable_advertising_personalization
     */
    allow_ad_personalization_signals: true,
    /**
     * https://developers.google.com/analytics/devguides/collection/gtagjs/cookies-user-id#configure_cookie_field_settings
     */
    // cookie_domain: 'auto',
    // cookie_expires
    // cookie_prefix
    // cookie_update
    // cookie_flags
    /**
     * Cookie Flags
     * https://developers.google.com/analytics/devguides/collection/ga4/cookies-user-id#cookie_flags
     */
    cookie_flags: '',
  };

  export interface GoogleAnalyticsConfig {
    /** default 'gtag' **/
    gtagName?: string;
    /** default 'ga4DataLayer' **/
    dataLayerName?: string;
    measurementIds: string[];
    gtagConfig?: typeof defaultGtagConf;
  }
  function googleAnalytics(config: GoogleAnalyticsConfig): AnalyticsPlugin;

  export default googleAnalytics;
}
