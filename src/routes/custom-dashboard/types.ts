import { TimeRangeSelectorProps } from '@g17eco/molecules/time-range-selector';
import { PeriodDropdownProps } from '@components/survey-period-dropdown';
import { UtrVariable } from '../summary/insights/utils/constants';
import { DashboardSurveyType } from '@g17eco/types/insight-custom-dashboard';

export interface ExtendedUtrVariable extends UtrVariable {
  name?: string;
}

export type DataFilters = Pick<PeriodDropdownProps, 'period' | 'setPeriod'> &
  Pick<TimeRangeSelectorProps, 'dateRange' | 'timeRange' | 'onChangeDateRange'> & { surveyType?: DashboardSurveyType };
