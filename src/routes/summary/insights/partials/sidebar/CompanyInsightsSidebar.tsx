/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { useCreateInsightDashboardMutation } from '@api/insight-dashboards';
import { InsightsSidebar } from './InsightsSidebar';
import { Loader } from '@g17eco/atoms/loader';
import {
  GridDashboardItem,
  InsightDashboard,
  InsightDashboardActions,
  InsightDashboardItemType,
  InsightDashboardType,
  UtrvFilter,
} from '@g17eco/types/insight-custom-dashboard';
import { dashbboardMetricStatusOptions, DEFAULT_FILTERS } from '@routes/custom-dashboard/dashboard-settings/utils';
import { DashboardSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/DashboardSettingsSidebar';
import { generateGridDashboardItem } from '@routes/custom-dashboard/utils';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { InsightDashboardOption } from '../../utils/sidebar';

interface CompanyInsightsSidebarProps {
  /** Inline buttons that need to be merged with dashboard buttons **/
  alignButtonRow: boolean;
  initiativeId: string;
  currentPage: string;
  availablePeriods: DataPeriods[];
  options: InsightDashboardOption[];
  openSettingsSidebar?: boolean;
  toggleSettingsSidebar?: () => void;
  handleClickOption: (item: InsightDashboardOption) => void;
  handleNavigateCustom?: (dashboardId: string) => void;
}

export const CompanyInsightsSidebar = (props: CompanyInsightsSidebarProps) => {
  const {
    initiativeId,
    alignButtonRow,
    currentPage,
    availablePeriods,
    options,
    openSettingsSidebar = false,
    toggleSettingsSidebar = () => {},
    handleClickOption,
    handleNavigateCustom = () => {},
  } = props;

  const [createInsightDashboard, { isLoading }] = useCreateInsightDashboardMutation();

  const handleSave = async (changes: Partial<InsightDashboard>) => {
    if (!initiativeId) {
      return;
    }
    const items: GridDashboardItem[] = [];
    if (changes.filters?.sdgContribution?.enabled) {
      const item = generateGridDashboardItem({ type: InsightDashboardItemType.SDGContributionChart }, items);
      items.push(item);
    }

    const dashboard = await createInsightDashboard({
      initiativeId,
      ...changes,
      items,
      title: changes.title ?? 'Custom Dashboard',
    }).unwrap();
    handleNavigateCustom(dashboard._id);
  };

  const defaultSurveyPeriod = !availablePeriods.includes(DEFAULT_FILTERS.period)
    ? availablePeriods[0]
    : DEFAULT_FILTERS.period;

  return (
    <>
      {isLoading ? <Loader /> : null}
      <InsightsSidebar
        options={options}
        currentPage={currentPage}
        handleClickOption={handleClickOption}
        className={alignButtonRow ? 'align-button-row' : ''}
      />
      <DashboardSettingsSidebar
        isOpenSidebar={openSettingsSidebar}
        toggleSidebar={toggleSettingsSidebar}
        dashboard={{
          title: 'Custom Dashboard',
          initiativeId,
          filters: { ...DEFAULT_FILTERS, period: defaultSurveyPeriod, utrv: UtrvFilter.AllAnswered },
          type: InsightDashboardType.Custom,
          items: [],
        }}
        handleSave={handleSave}
        // should hide actions when create a new dashboard
        hideOptions={[InsightDashboardActions]}
        availablePeriods={availablePeriods}
        metricStatusOptions={dashbboardMetricStatusOptions}
      />
    </>
  );
};
