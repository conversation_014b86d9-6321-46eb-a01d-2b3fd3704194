/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { Fragment } from 'react';
import { Button, Collapse } from 'reactstrap';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ellipsis } from '@utils/index';
import { CustomDashboardInfoIcon, TemplateMenuPopover } from '@features/custom-dashboard';
import { ADD_DASHBOARD_OPTION, getTemplateOptions, InsightDashboardOption, TOOLTIP_MESSAGE } from '../../utils/sidebar';

/**
 * If updates happen to sidebar, should check the updates in these places:
 * StaticDashboardRoute, CustomDashboardContainer, PortfolioCompanyInsights
 * StaticPortfolioInsights, PortfolioCustomDashboardRoute
 */

const TEMPLATE_MENU_TARGET = `${ADD_DASHBOARD_OPTION}-xxl`; // Use different target to avoid wrong menu position.

interface SidebarProps {
  options: InsightDashboardOption[];
  currentPage: string;
  className?: string;
  handleClickOption: (item: InsightDashboardOption) => void;
}

export const InsightsSidebar = (props: SidebarProps) => {
  const { options, currentPage, handleClickOption } = props;
  const templateOptions = getTemplateOptions(options);
  const getOption = (item: InsightDashboardOption) => {
    if (item.value === ADD_DASHBOARD_OPTION && templateOptions.length) {
      return (
        <SimpleTooltip text={item.tooltip}>
          <Button
            id={TEMPLATE_MENU_TARGET}
            color={item.color ?? 'transparent'}
            disabled={item.disabled}
            className={item.className ?? ''}
          >
            {item.label}
          </Button>
        </SimpleTooltip>
      );
    }

    return item.isCustom && typeof item.label === 'string' ? (
      <SimpleTooltip text={item.tooltip ?? item.label}>
        <Button
          color={item.color ?? 'transparent'}
          disabled={item.disabled}
          onClick={() => handleClickOption(item)}
          className={item.className ?? ''}
        >
          {ellipsis(item.label, 11)}
        </Button>
      </SimpleTooltip>
    ) : (
      <SimpleTooltip text={item.tooltip}>
        <Button
          color={item.color ?? 'transparent'}
          disabled={item.disabled}
          onClick={() => handleClickOption(item)}
          className={item.className ?? ''}
        >
          {item.label}
        </Button>
      </SimpleTooltip>
    );
  };
  return (
    <div className='d-none d-xxl-inline-block insights-sidebar-container settings-sidebar-container'>
      <div className='sticky-sidebar'>
        {options.map((item) => (
          <Fragment key={`sidebar_${item.value}`}>
            {item.value !== currentPage ? (
              <div>
                {getOption(item)}
                <CustomDashboardInfoIcon text={item.isSharedByParent ? TOOLTIP_MESSAGE.IS_SHARED_BY_PARENT : ''} />
              </div>
            ) : null}
            <Collapse isOpen={item.value === currentPage} className='sidebar-title strong'>
              <span className='my-3'>
                {item.isCustom && typeof item.label === 'string' ? (
                  <SimpleTooltip text={item.label}>{ellipsis(item.label, 11)}</SimpleTooltip>
                ) : (
                  item.label
                )}
              </span>
            </Collapse>
          </Fragment>
        ))}

        <TemplateMenuPopover
          target={TEMPLATE_MENU_TARGET}
          isUncontrolled
          onClickOption={handleClickOption}
          templates={templateOptions}
        />
      </div>
    </div>
  );
};
