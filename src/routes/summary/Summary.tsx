/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useGetSummaryInsightDashboardQuery } from '@api/insight-dashboards';
import { GridLayoutDashboard } from '../custom-dashboard/GridLayoutDashboard';
import { DashboardSurveyType, GridDashboardItem, OverviewDashboard } from '@g17eco/types/insight-custom-dashboard';
import { AddToOverviewBtn } from './AddToOverviewBtn';
import { useSelector } from 'react-redux';
import { selectMostRecentSurvey } from '../../slice/initiativeSurveyListSlice';
import { useGetSurveyByIdQuery } from '@api/surveys';
import { DashboardSection } from '@components/dashboard';
import { SdgChart } from '@components/impact-performance/SdgChart';
import { CompanyProfile } from '@components/company-profile';
import { InitiativeData } from '@g17eco/types/initiative';
import { SurveyType } from '@g17eco/types/survey';
import { DataPeriods } from '@g17eco/types/universalTracker';

interface Props {
  initiative: InitiativeData | undefined;
  dashboard: OverviewDashboard;
  initiativeId: string;
  canManage: boolean;
  filters?: {
    period?: DataPeriods;
    surveyType?: DashboardSurveyType;
  }
}

export const Summary = ({ initiative, dashboard, initiativeId, canManage, filters }: Props) => {
  const { data: overview, isFetching: isFetchingOverview } = useGetSummaryInsightDashboardQuery(
    { dashboardId: 'overview', initiativeId, queryParams: { surveyType: SurveyType.Default } },
    { skip: !initiativeId }
  );

  const mostRecentSurvey = useSelector(selectMostRecentSurvey);
  const { data: latestSurvey, isFetching: isFetchingLatestSurvey } = useGetSurveyByIdQuery(
    mostRecentSurvey?._id ?? '',
    {
      skip: !mostRecentSurvey?._id,
    }
  );

  return (
    <>
      {dashboard.filters.initiativeInfo?.enabled ? <CompanyProfile initiative={initiative} readOnly={false} /> : null}
      {dashboard.filters.sdgContribution?.enabled && dashboard.scorecard ? (
        <DashboardSection>
          <SdgChart initiativeId={initiativeId} scorecard={dashboard.scorecard} />
        </DashboardSection>
      ) : null}
      <GridLayoutDashboard
        isEditing={false}
        gridItems={dashboard.items}
        utrsData={dashboard.utrsData}
        readOnly
        initiativeId={initiativeId}
        actionBtn={({ item }: { item: GridDashboardItem }) => {
          if (!canManage) {
            return null;
          }
          return (
            <AddToOverviewBtn
              initiativeId={initiativeId}
              overviewItems={overview?.items ?? []}
              item={item}
              disabled={isFetchingOverview || isFetchingLatestSurvey}
            />
          );
        }}
        survey={latestSurvey}
        dataFilters={filters}
      />
    </>
  );
};
