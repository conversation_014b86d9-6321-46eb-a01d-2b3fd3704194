/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { Footer } from '@g17eco/molecules/footer';
import GlobeSlider from './globe-slider';
import GlobalIndex from './global-index';
import Countdown from '../../components/countdown';
import './styles.scss';

class WorldTracker extends Component {

  render() {
    return (
      <div className='world-tracker p-4 d-flex flex-column flex-fill position-relative'>
        <Countdown />
        <div className='globe-wrapper flex-grow-1 d-flex flex-column'>
          <div className='flex-grow-1'>
            <GlobalIndex />
            <div className='globe-utrs'>
              <GlobeSlider />
            </div>
          </div>
          <div className='text-ThemeTextPlaceholder' style={{ marginBottom: '30px' }}>The source of the data graphically visualised in World Tracker is accredited to the <a href='https://sdgindex.org/' target='_blank' rel='noopener noreferrer'>SDG Index</a>.
          To the best of our knowledge, it is the most up to date data set relating to SDG deficits per nation we are currently able to access.
          We understand that there may be inaccuracies or discrepancies within the data and therefore we cannot and do not give any assurances as to its veracity.
          That said, we are in the process of working with numerous partners across the globe to accelerate the ability to create tools for the input and output of SDG data.
          This will enable organisations and nations to refresh the data, automate the aggregation process and effectively communicate progress on the SDGs for the public good.
          If you are a Government or other interested organisation and would like to contribute to this initiative, we would welcome your input and invite you to <a href='https://www.worldwidegeneration.co/contact-us' target='_blank' rel='noopener noreferrer'>contact us</a>.
          </div>
        </div>
        <Footer />
      </div>
    )
  }
}

export default WorldTracker;
