/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

%box-shadow {
  border-radius: $borderRadius;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
  transition: border-radius 1s ease,
    box-shadow 1s ease,
    -webkit-box-shadow 1s ease,
    -moz-box-shadow 1s ease;
}

%box-shadow-hover {
  &:hover {
    box-shadow: 0px 13px 15px 0px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: 0px 13px 15px 0px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 13px 15px 0px rgba(0, 0, 0, 0.3);
    transition: box-shadow 1s ease,
      -webkit-box-shadow 1s ease,
      -moz-box-shadow 1s ease;
  }
}
