/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ClickHandlers } from './types';

export function createOnClickFromHandler(
  clickHandler: undefined | keyof ClickHandlers,
  clickHandlers: ClickHandlers
): React.MouseEventHandler<HTMLElement> {
  const handler = clickHandler ? clickHandlers[clickHandler] : undefined;
  return (e) => {
    e.preventDefault();
    e.stopPropagation();
    return handler?.();
  };
}
