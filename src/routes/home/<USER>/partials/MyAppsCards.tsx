/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useGetAvailableAppsQuery } from '@api/users';
import { HomepageProductCard } from '../types';
import { ProductAppType } from '@g17eco/types/app';
import { Loader } from '@g17eco/atoms/loader';
import './MyAppsCards.scss';

interface MyAppCard extends HomepageProductCard {
  isFree: boolean;
}

const Card = ({ card }: { card: MyAppCard }) => {
  return (
    <div className='my-apps__card' onClick={card.onClick}>
      <div className='my-apps__card-image'>
        <img src={card.logo} alt={card.title} />
        {card.isFree ? <div className='my-apps__card-free text-xs text-ThemeSuccessDark'>FREE</div> : null}
      </div>
    </div>
  );
};

interface MyAppsCardsProps {
  cards: HomepageProductCard[];
}
export const MyAppsCards = (props: MyAppsCardsProps) => {
  const { cards } = props;

  const { data: availableApps = [], isLoading } = useGetAvailableAppsQuery();

  if (isLoading) {
    return (
      <div className='d-flex justify-content-center my-2'>
        <Loader relative />
      </div>
    );
  }

  const staticFreeApps = [ProductAppType.WorldTracker, ProductAppType.SDGAP];
  const allApps = [...availableApps, ...staticFreeApps];

  const myAppCards = cards.reduce((acc, card) => {
    if (card.id && allApps.includes(card.id)) {
      acc.push({
        ...card,
        isFree: staticFreeApps.includes(card.id),
      });
    }
    return acc;
  }, [] as MyAppCard[]);

  return (
    <section className='d-flex flex-wrap'>
      <h2 className='mt-6'>MY APPS</h2>
      <div className='my-apps__list w-100 flex-wrap justify-content-center mt-3'>
        {myAppCards.map((card) => (
          <Card key={card.id} card={card} />
        ))}
      </div>
      <div className='my-apps__divider background-ThemeBorderDefault mt-6' />
    </section>
  );
};
