/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BulletListItem } from '../types';
import './BulletList.scss';

interface BulletListProps {
  items: BulletListItem[];
}

export const BulletList = (props: BulletListProps) => {
  const { items } = props;

  return (
    <div className='w-100 homepage-bullet-list'>
      {items.map((item) => (
        <div key={`b-${item.text}`} className='homepage-bullet-list-item d-flex'>
          <div>
            <i className='fal fa-duotone fa-circle-check mr-2 text-ThemeSuccessLight' />
          </div>
          <div>{item.text}</div>
        </div>
      ))}
    </div>
  );
}
