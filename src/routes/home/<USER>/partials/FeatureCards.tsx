/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { HomepageFeatureCard, HomepageFeatureCardGroup } from '../types';
import './FeatureCards.scss';
import { Hero } from './Hero';

interface FeatureCardProps {
  from?: HomepageFeatureCardGroup['from'];
  card: HomepageFeatureCard;
  onClick: () => void;
  imgHeight?: number;
}

interface FeatureCardsProps {
  cardGroups: HomepageFeatureCardGroup[];
  defaultOnClick: () => void;
}

const FeatureCard = (props: FeatureCardProps) => {
  const { card, from, onClick, imgHeight } = props;

  return (
    <div className='feature-card-group'>
      <div className='feature-card-from'>
        {from ? (
          <div className='feature-card-from-logo'>
            FROM... <img {...(imgHeight ? { height: imgHeight } : {})} src={from.logo} alt='logo' />
          </div>
        ) : null}
      </div>
      <div className='feature-card'>
        <div className='feature-card-fader' />
        <div className='feature-card-image'>
          <img src={card.image} alt={card.title} />
        </div>
        <div className='feature-card-title mt-2'>{card.title}</div>
        <div className='feature-card-description mt-2'>{card.description}</div>
        <div className='feature-card-buttons mt-2 pb-4'>
          <Button outline={true} onClick={onClick}>
            Contact us
          </Button>
        </div>
      </div>
    </div>
  );
};

export const FeatureCards = (props: FeatureCardsProps) => {
  const { cardGroups, defaultOnClick } = props;

  const cardCount = cardGroups.reduce((acc, group) => acc + group.cards.length, 0);
  const remainder = cardCount % 3;
  const heroClass = remainder === 0 ? '' : remainder === 1 ? 'two-card' : 'one-card';

  return (
    <div className='feature-cards w-100 d-flex flex-wrap justify-content-center'>
      {cardGroups.map((cardGroup) =>
        cardGroup.cards.map((card, i) => (
          <FeatureCard
            key={card.title}
            from={i === 0 ? cardGroup.from : undefined}
            card={card}
            onClick={defaultOnClick}
            imgHeight={card.imageHeight}
          />
        ))
      )}

      <Hero
        title='Interested In Becoming A Partner?'
        text={`Contact us if you are interested in offering your sustainable service, consultancy
          or finance solution through the G17Eco Marketplace.`}
        className={heroClass}
      >
        <div className='mt-5'>
          <Button color='primary' outline={true} size='xl' onClick={defaultOnClick}>
            Contact us
          </Button>
        </div>
      </Hero>
    </div>
  );
};
