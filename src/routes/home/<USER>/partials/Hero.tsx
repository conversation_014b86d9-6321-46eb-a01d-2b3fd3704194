/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ReactNode } from 'react';
import './Hero.scss';

interface HeroProps {
  color?: 'blue' | 'white';
  padding?: 1 | 2 | 3 | 4 | 5 | 6 | 7;
  className?: string;
  title?: string;
  text?: string;
  children: ReactNode;
}

export const Hero = (props: HeroProps) => {
  const { title, text, children, className = '', padding = 6, color = 'blue' } = props;

  return (
    <div className={`homepage-hero homepage-hero-${color} p-${padding} ${className} d-flex flex-column justify-content-center`}>
      {title ? <h2 className='mt-0'>{title}</h2> : null}
      {text ? <div className='homepage-hero-text'>{text}</div> : null}
      <div className='homepage-hero-children'>{children}</div>
    </div>
  );
}
