/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */
import { ReactNode } from 'react';
import './AnimatedBackground.scss';

interface AnimatedBackgroundProps {
  className?: string;
  children: ReactNode;
}

export const AnimatedBackground = (props: AnimatedBackgroundProps) => {
  const { className, children } = props;
  const count = 10;

  return (
    <div className={`animated-bg ${className}`}>
      <div className='hexagons-container'>
        <div className='hexagons'>
          {new Array(count).fill(null).map((_, i) => (
            <div key={i} className='hexagon'>
              &#x2B22;
            </div>
          ))}
        </div>
      </div>
      <div className='animated-bg-children'>
      {children}
      </div>
    </div>
  );
}
