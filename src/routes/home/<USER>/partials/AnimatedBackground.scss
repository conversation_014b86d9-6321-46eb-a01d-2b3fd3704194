/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

$hexagon-count: 10;
$sway-type: "no-movement", "sway-left-to-right", "sway-right-to-left";
$float-type: "no-movement", "float-up-and-down", "float-down-and-up";

@function random_range($min, $max) {
  $rand: random();
  $random_range: $min + floor($rand * (($max - $min) + 1));
  @return $random_range;
}

@function sample($list) {
  @return nth($list, random(length($list)));
}

.animated-bg {
  .hexagons-container {
    position: fixed;
    z-index: 0;
    .hexagons {
      position: absolute;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      pointer-events: none;
      z-index: -1;

      .hexagon {
        opacity: 0.3;
        color: var(--theme-DangerLight);
        position: absolute;
        top: var(--hexagon-top-offset);
        left: var(--hexagon-left-offset);
        display: block;
        font-size: var(--hexagon-font-size);
        line-height: var(--hexagon-font-size);

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to right, rgba(255, 251, 252, .5), rgb(255, 251, 252));
          pointer-events: none;
        }

        animation:
          var(--hexagon-float-type) var(--hexagon-animation-duration) var(--hexagon-start-delay) ease-in-out alternate infinite,
          var(--hexagon-sway-type) var(--hexagon-animation-duration) var(--hexagon-start-delay) ease-in-out alternate infinite,
          rainbow calc(10 * var(--hexagon-animation-duration)) var(--hexagon-start-delay) linear infinite;
          ;


        @for $i from 0 through $hexagon-count {
          &:nth-child(#{$i}) {
            --hexagon-left-offset: #{random_range(0vw, 100vw)};
            --hexagon-top-offset: #{random_range(0vh, 100vh)};
            --hexagon-font-size: #{random_range(50, 130)}px;
            --hexagon-animation-duration: #{random_range(4s, 6s)};
            --hexagon-start-delay: #{random_range(0s, 4s)};
            --hexagon-sway-type: #{sample($sway-type)};
            --hexagon-float-type: #{sample($float-type)};
          }
        }
      }
    }
  }
  .animated-bg-children {
    z-index: 1;
  }
}

@keyframes no-movement {
  from {
  }
  to {
  }
}

@keyframes float-down-and-up {
  from {
    transform: translateY(0%);
  }
  to {
    transform: translateY(-20%);
  }
}

@keyframes float-up-and-down {
  from {
    transform: translateY(0%);
  }
  to {
    transform: translateY(20%);
  }
}

@keyframes sway-left-to-right {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(20%);
  }
}

@keyframes sway-right-to-left {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-20%);
  }
}
@keyframes rainbow {
  0% { color: var(--theme-DangerLight); }
  12.5% { color: #ff00a8; }
  25% { color: #c400ff; }
  37.5% { color: #00d3ff; }
  50% { color: #00ffaf; }
  62.5% { color: #1aff00; }
  75% { color: #dbff00; }
  87.5% { color: #ffc000; }
  100% { color: var(--theme-DangerLight); }
}
