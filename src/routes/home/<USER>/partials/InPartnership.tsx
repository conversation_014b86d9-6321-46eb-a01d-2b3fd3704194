/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import './InPartnership.scss';

interface InPartnershipProps {
  partner?: {
    logo: string;
  }
}

export const InPartnership = (props: InPartnershipProps) => {
  const { partner } = props;

  if (!partner) {
    return null;
  }

  return (
    <div className='in-partnership d-flex mt-6 flex-column text-center w-100'>
      <div className='in-partnership-title'>
        In Partnership With
      </div>
      <div className='in-partnership-logo'>
        <img src={partner.logo} alt={'Partner'} height={30} />
      </div>
    </div>
  );
}
