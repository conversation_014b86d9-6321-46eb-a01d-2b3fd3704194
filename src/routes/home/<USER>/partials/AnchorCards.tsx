/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { HomepageAnchorCard } from '../types';
import './AnchorCards.scss';

interface AnchorCardProps {
  card: HomepageAnchorCard;
}

const AnchorCard = (props: AnchorCardProps) => {
  const handleClick = () =>
    document.getElementById(props.card.anchor)?.scrollIntoView({ behavior: 'smooth', block: 'start' });

  return (
    <div className='anchor-card'  onClick={handleClick}>
      <div className='anchor-card-image'>
        <img src={props.card.image} alt={props.card.title} />
      </div>
      <div className='anchor-card-title'>{props.card.title}</div>
    </div>
  );
}

interface AnchorCardsProps {
  cards: HomepageAnchorCard[];
}
export const AnchorCards = (props: AnchorCardsProps) => {
  const { cards } = props;

  return (
      <div className='anchor-cards d-flex flex-wrap justify-content-center'>
        {cards.map((card) => (
          <AnchorCard key={card.title} card={card} />
        ))}
      </div>
    );
}
