/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import '../common.scss';

$productCardHeight: 200px;
$productCardSmallHeight: 140px;

.product-cards {
  margin: 0 auto;
  row-gap: 2rem;

  .col {
    padding: 0 0.75rem;
  }

  .product-card {
    padding: 0.5rem;
    position: relative;
    width: 100%;
    background-color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    height: $productCardHeight;
    &.product-card-small {
      height: $productCardSmallHeight;
      .product-card-image img {
        max-width: calc(100% - 20px);
      }
    }

    &.product-card-disabled {
      background: var(--theme-NeutralsExtralight);
      border: 1px solid var(--theme-BorderDefault);
      .product-card-image, .product-card-text {
        top: -5px;
      }
    }

    .product-card-image, .product-card-text {
      position: relative;
      top: 20px;
      overflow: hidden;
      text-align: center;
      color: var(--theme-TextDark);
      text-transform: uppercase;
      font-family: "oswald";
      z-index: 1;
      height: 76px;
      img {
        height: 76px;
        max-width: calc(100% - 40px);
      }
    }
    .product-card-buttons {
      position: relative;
      text-align: center;
      top: 20px;
      height: 60px;
      button {
        margin-top: 0.25rem;
        text-transform: uppercase;
      }
    }
    .product-card-fader {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 0;
      background: var(--theme-NeutralsLight);
      transition: height 0.5s ease-out;
      opacity: 0;
    }
    &:not(.product-card-disabled) {
      @extend %box-shadow;
      @extend %box-shadow-hover;

      &:hover {
        .product-card-fader {
          height: 100%;
          opacity: 1;
        }
      }
    }

    .pending-tag {
      position: absolute;
      top: 12px;
      right: -24px;
      padding: 2px 24px;
      transform: rotate(50deg);
    }
  }
}
