/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import '../common.scss';

$featureCardWidth: 300px;

.feature-cards {
  display: flex;
  margin: 0 auto;
  row-gap: 3rem;
  column-gap: 4rem;

  @media (max-width: 710px) {
    padding: 0;
    column-gap: calc((100% - $featureCardWidth) / 1);
  }

  @media (min-width: 711px) and (max-width: 1000px) {
    column-gap: calc((100% - (2 * $featureCardWidth)) / 3);
  }

  @media (min-width: 1001px) {
    column-gap: calc((100% - (3 * $featureCardWidth)) / 3);
  }

  .feature-card-group {
    .feature-card-from {
      height: 65px;
      color: var(--theme-TextLight);
      padding: 1rem 0;
      text-transform: uppercase;
      font-family: "oswald";
      font-size: 1.4rem;
      font-weight: 500;
      letter-spacing: 0.3px;
    }

    .feature-card {
      position: relative;
      width: $featureCardWidth;
      @media (max-width: 710px) {
        width: 100%;
        .feature-card-image {
          width: 100%;
          img {
            width: 100%;
          }
        }
      }

      height: 480px;
      background-color: white;
      overflow: hidden;

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      @extend %box-shadow;
      @extend %box-shadow-hover;
      border-radius: 20px;

      .feature-card-image {
        background-color: white;
        height: 225px;
        overflow: hidden;
        text-align: center;
        z-index: 1;
      }
      .feature-card-title {
        width: 100%;
        padding: 0 1rem;
        color: var(--theme-TextDark);
        text-align: left;
        font-weight: bold;
        font-size: 1rem;
        z-index: 1;
      }
      .feature-card-description {
        width: 100%;
        padding: 0 1rem;
        color: var(--theme-TextLight);
        flex-grow: 1;
        z-index: 1;
      }
      .feature-card-fader {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0;
        background: var(--theme-NeutralsExtralight);
        transition: all 0.5s ease-out;
        opacity: 0;
      }
      &:hover {
        .feature-card-fader {
          height: calc(100% - 225px);
          opacity: 1;
        }
      }
    }
  }

  .homepage-hero {
    &.one-card {
      width: $featureCardWidth;
    }
    &.two-card {
      width: calc(2 * $featureCardWidth);
    }
  }
}
