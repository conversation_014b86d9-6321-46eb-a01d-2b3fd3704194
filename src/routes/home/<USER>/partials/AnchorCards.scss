/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import '../common.scss';

.anchor-cards {
  display: flex;
  column-gap: 1rem;
  row-gap: 1rem;
  margin: 0 auto;

  .anchor-card {
    width: 100%;

    @media (min-width: 455px) {
      width: 150px;
    }

    height: 134px;
    background: white;
    @extend %box-shadow;
    @extend %box-shadow-hover;

    .anchor-card-image {
      text-align: center;
      background: var(--theme-BgLight);
      height: 90px;
      transition: all 0.5s ease;
      overflow: hidden;
      img {
        width: 100%;
        min-height: 100%;
      }
    }
    .anchor-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 44px;
      text-align: center;
      text-transform: uppercase;
      font-family: "oswald";
      font-size: 0.8rem;
      transition: all 0.5s ease;
    }
    &:hover {
      cursor: pointer;
      .anchor-card-image {
        height: 0px;
      }
      .anchor-card-title {
        background: var(--theme-AccentExtralight);
        height: 134px;
      }
    }
  }
}
