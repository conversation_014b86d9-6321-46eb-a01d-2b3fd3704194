/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button, Col, Row } from 'reactstrap';
import { ClickHandlers, HomepageProductCard } from '../types';
import { createOnClickFromHandler } from '../utils';
import './ProductCards.scss';
import classNames from 'classnames';

interface ProductCardProps {
  card: HomepageProductCard;
  mode: 'simple' | 'detailed';
  className?: string;
  classes?: {
    img?: string;
  }
}

interface ProductCardsProps extends Pick<ProductCardProps, 'classes'> {
  cards: HomepageProductCard[];
  showContactUs?: boolean;
  columns?: 3 | 4;
  clickHandlers?: ClickHandlers;
  mode?: 'simple' | 'detailed';
}

const ProductCard = (props: ProductCardProps) => {
  const { card, mode, className = '' } = props;

  if (card.disabled) {
    return (
      <div className={`product-card ${className} product-card-disabled`}>
        <div className='product-card-image'>
          <img src={card.logo} alt={'Logo'} />
        </div>
      </div>
    );
  }

  return (
    <div className={`product-card ${className} ${card.onClick ? 'cursor-pointer' : ''}`} onClick={card.onClick} data-testid={card.title}>
      <div className='product-card-fader' style={card.color ? { background: card.color } : undefined} />
      <div className={classNames('product-card-image', props.classes?.img)}>
        <img src={card.logo} alt={'Logo'} />
      </div>
      {card.status && card.status === 'pending' ? (
        <div className='pending-tag text-xs text-uppercase background-ThemeAccentExtralight text-ThemeAccentDark'>
          {card.status}
        </div>
      ) : null}
      <div className='product-card-buttons'>
        {mode !== 'simple' ? card.buttons?.map((button, i) => {
          if (button === undefined) return null;
          return (
            <Button
              key={`button-${i}`}
              color='transparent'
              size='xs'
              className='mx-1'
              outline={true}
              onClick={(e) => {
                e.stopPropagation();
                button.onClick(e);
              }}
            >
              {button.text}
            </Button>
          );
        }) : null}
      </div>
    </div>
  );
}

const ContactUsCard = ({ clickHandlers }: { clickHandlers: ClickHandlers }) => {
  const handleClick = createOnClickFromHandler('ShowRequestADemo', clickHandlers);

  return (
    <div className='product-card background-ThemeBgCanvas cursor-pointer' onClick={handleClick}>
      <div className='product-card-fader' style={{ backgroundColor: 'var(--theme-InfoLight)' }} />
      <div className='product-card-text align-content px-3'>Book a demo<br />(15 mins)</div>
      <div className='product-card-buttons'>
        <Button size='xs' color='primary' onClick={handleClick}>
          Book a Demo
        </Button>
      </div>
    </div>
  );
};

export const ProductCards = (props: ProductCardsProps) => {
  const { cards, showContactUs, clickHandlers, columns = 4, mode = 'detailed' } = props;

  const columnClassNames = columns === 3 ? 'col-12 col-sm-6 col-md-4' : 'col-12 col-sm-6 col-md-4 col-lg-3';
  const cardClassName = columns === 3 ? 'product-card-small' : '';
  return (
    <>
      <Row className='product-cards'>
        {cards.map((card, i) => (
          <Col key={`card-${i}`} className={columnClassNames}>
            <ProductCard card={card} mode={mode} className={cardClassName} classes={props.classes} />
          </Col>
        ))}
        {showContactUs && clickHandlers ? (
          <Col className={columnClassNames}>
            <ContactUsCard clickHandlers={clickHandlers} />
          </Col>
        ) : null}
      </Row>
    </>
  );
}
