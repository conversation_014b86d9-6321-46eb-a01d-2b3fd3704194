/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import './HeadingList.scss';

interface HeadingListItem {
  icon: string;
  text: string;
}

interface HeadingListProps {
  nowrap?: boolean;
  items: HeadingListItem[];
}

export const HeadingList = (props: HeadingListProps) => {
  const { items, nowrap = false } = props;

  return (
    <div className='w-100 homepage-heading-list'>
      {items.map(item => (
        <div key={`b-${item.text}`} className={`homepage-heading-list-item ${nowrap ? 'text-nowrap' : ''}`}>
          <i className={`fal ${item.icon} fa-fw mr-3 text-ThemeDangerLight`} />
          {item.text}
        </div>
      ))}
    </div>
  );
}
