/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { HomepageFeatureBar } from '../types';
import { BulletList } from './BulletList';
import './FeatureBar.scss';

interface FeatureBarProps {
  bar: HomepageFeatureBar;
  defaultOnClick: () => void;
}

interface FeatureBarCardProps {
  card: HomepageFeatureBar['cardLeft'];
  onClick: () => void;
}

interface FeatureBarCategoryProps {
  category: HomepageFeatureBar['categoryOne'];
}

const FeatureBarCard = (props: FeatureBarCardProps) => {
  const { card, onClick } = props;

  return (
    <div className='feature-bar-card'>
      <div className='feature-bar-card-image'>
        <img src={card.image} alt={card.title} />
      </div>
      <div className='feature-bar-card-title mt-4'>{card.title}</div>
      <div className='feature-bar-card-description mt-4'>
        <BulletList items={card.list} />
      </div>
      <div className='feature-bar-card-buttons text-center'>
        <Button outline={true} onClick={onClick}>Contact us</Button>
      </div>
    </div>
  );
}

const FeatureBarCategory = (props: FeatureBarCategoryProps) => {
  const { category } = props;

  return (
    <div className='feature-bar-category'>
      <div className='feature-bar-category-icon'>
        <i className={`fal text-danger ${category.icon}`} />
      </div>
      <div className='feature-bar-category-title mt-2'>{category.title}</div>
    </div>
  );
}

export const FeatureBar = (props: FeatureBarProps) => {
  const { bar, defaultOnClick } = props;
  const { cardLeft, cardRight } = bar;

  return (
    <div className='feature-bar d-flex flex-column'>
      <div className='feature-bar-cards d-flex flex-wrap justify-content-around'>
        <FeatureBarCard key={cardLeft.title} card={cardLeft} onClick={defaultOnClick} />
        <FeatureBarCard key={cardRight.title} card={cardRight} onClick={defaultOnClick} />
      </div>

      <div className='feature-bar-categories d-flex justify-content-around'>
        <FeatureBarCategory category={bar.categoryOne} />
        <FeatureBarCategory category={bar.categoryTwo} />
        <FeatureBarCategory category={bar.categoryThree} />
      </div>
    </div>
  );
}
