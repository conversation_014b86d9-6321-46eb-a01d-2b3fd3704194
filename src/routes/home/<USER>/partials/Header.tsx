/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DropdownItem, DropdownToggle, DropdownMenu, UncontrolledDropdown } from 'reactstrap';
import { homepageViews } from '../config';
import { HomepageViewConfig } from '../types';
import './Header.scss';

interface HeaderProps {
  viewConfig: HomepageViewConfig;
  onChange: (viewConfig: HomepageViewConfig) => void;
}

export const Header = (props: HeaderProps) => {
  const { viewConfig, onChange } = props;

  return (
    <div className='d-flex w-100 homepage-header flex-column flex-md-row'>
      <div className='flex-fill order-1 order-md-0'>
        <div className='homepage-title'>The G17Eco Marketplace</div>
        <div className='homepage-subtitle'>
          <span className='text-danger'>Sustainability</span> Data, Finance & Solutions Exchange
        </div>
      </div>
      <div className='order-0 order-md-1 text-right'>
        <UncontrolledDropdown>
          <DropdownToggle color='transparent' caret>
            <img
              src={viewConfig.flag}
              width={25}
              alt={viewConfig.title}
              className='mr-2'
              style={{ marginBottom: '2px' }}
            />
            {viewConfig.title}
          </DropdownToggle>
          <DropdownMenu>
            {homepageViews.map((homepageView) => (
              <DropdownItem key={`hp-${homepageView.view}`} onClick={() => onChange(homepageView)}>
                <img src={homepageView.flag} width={25} alt={homepageView.title} className='mb-1 mr-2' />
                {homepageView.title}
              </DropdownItem>
            ))}
          </DropdownMenu>
        </UncontrolledDropdown>
      </div>
    </div>
  );
}
