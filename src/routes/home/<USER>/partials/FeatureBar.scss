/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import '../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import '../common.scss';

$featureCardWidth: 300px;
$featureBarCardWidth: 350px;

.feature-bar {
  .feature-bar-cards {
    margin: 0 auto;
    row-gap: 3rem;
    width: 100%;

    @media (max-width: 850px) {
      padding: 0 calc((100% - (2 * $featureCardWidth)) / 2);
      column-gap: calc((100% - (2 * $featureCardWidth)) / 1);
    }

    @media (min-width: 851px) {
      column-gap: calc((100% - (3 * $featureCardWidth)) / 2);
    }

    .feature-bar-card {
      top: 0px;
      padding: 2rem 2rem 2rem 2rem;
      position: relative;
      width: $featureBarCardWidth;
      height: 470px;
      background-color: white;

      display: flex;
      flex-direction: column;

      @extend %box-shadow;
      @extend %box-shadow-hover;
      border-radius: 20px 20px 0 0 !important;
      transition: all 0.5s ease-out;

      &:hover {
        cursor: pointer;
        top: -30px;
      }

      .feature-bar-card-image {
        height: 30px;
        text-align: center;
        z-index: 1;
      }
      .feature-bar-card-title {
        width: 100%;
        color: var(--theme-TextDark);
        text-align: left;
        font-weight: bold;
        font-size: 1rem;
        z-index: 1;
      }
      .feature-bar-card-buttons {
        margin-top: auto;
      }
    }
  }

  .feature-bar-categories {
    top: -35px;
    margin-bottom: -30px;
    padding: 2.5rem 2rem;
    position: relative;
    width: 100%;
    background-color: white;

    border-radius: 20px 20px $borderRadius $borderRadius;
    box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
    -webkit-box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
    -moz-box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
    transition: all 0.5s ease;

    .feature-bar-category-icon {
      text-align: center;
      .fal {
        font-size: 1.3rem;
        font-weight: 200;
      }
    }
    .feature-bar-category-title {
      color: var(--theme-TextLight);
      text-transform: uppercase;
      font-family: "oswald";
      text-align: center;
    }
  }
}
