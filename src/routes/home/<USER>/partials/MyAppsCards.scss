@import '../common.scss';

.my-apps__list {
  display: flex;
  column-gap: 1rem;
  row-gap: 1rem;

  .my-apps__card {
    width: 100%;

    @media (min-width: 455px) {
      width: 150px;
    }

    height: 134px;
    @extend %box-shadow;
    @extend %box-shadow-hover;

    .my-apps__card-image {
      position: relative;
      background: var(--theme-BgLight);
      height: 100%;
      overflow: hidden;
      cursor: pointer;
      text-align: center;

      img {
        width: 70%;
        min-height: 100%;
      }

      .my-apps__card-free {
        position: absolute;
        width: 80px;
        transform: rotate(45deg);
        top: 13px;
        right: -20px;
        background-color: var(--theme-SuccessExtralight);
      }
    }
  }
}

.my-apps__divider {
  height: 1px;
  width: 70%;
  margin: 0 auto;
}