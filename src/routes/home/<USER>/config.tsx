/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import config from '../../../config';
import { HomepageView, HomepageViewConfig } from './types';
import FlagUAE from '../../../images/flags/uae.svg';
import FlagKSA from '../../../images/flags/ksa.svg';
import FlagSG from '../../../images/flags/sg.svg';
import FlagEU from '../../../images/flags/eu.svg';

export const homepageViews: HomepageViewConfig[] = [
  {
    title: 'Europe',
    flag: FlagEU,
    view: HomepageView.Europe,
    url: config.platform.international,
    locationMatch: {
      country: [
        'GB', // UK
      ],
    },
  },
  {
    title: 'Saudi Arabia',
    flag: FlagKSA,
    view: HomepageView.KSA,
    url: config.platform.ksa,
    locationMatch: {
      country: [
        'SA', // Saudi Arabia
      ],
    },
  },
  {
    title: 'Singapore',
    flag: FlagSG,
    view: HomepageView.Singapore,
    url: config.platform.singapore,
    locationMatch: {
      country: [
        'SG', // Singapore
        'MY', // Malaysia
        'ID', // Indonesia
        'TH', // Thailand
        'PH', // Philippines
        'VN', // Vietnam
        'MM', // Myanmar
        'KH', // Cambodia
        'LA', // Laos
        'BN', // Brunei
        'TL', // Timor-Leste
      ],
    },
  },
  {
    title: 'UAE',
    flag: FlagUAE,
    view: HomepageView.UAE,
    url: config.platform.uae,
    locationMatch: {
      country: [
        'IR', // Iran
        'JO', // Jordan
        'AE', // United Arab Emirates
        'IL', // Israel
        'EG', // Egypt
        'IQ', // Iraq
        'YE', // Yemen
        'KW', // Kuwait
        'LB', // Lebanon
        'BH', // Bahrain
        'SY', // Syria
        'QA', // Qatar
        'OM', // Oman
        'TR', // Turkey
        'PS', // Palestine
        'CY', // Cyprus
      ],
    },
  },
];
