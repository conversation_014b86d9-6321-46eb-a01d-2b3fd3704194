/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { HomepageView } from '../types';
import { homepageConfig as ksa } from '../configs/ksa';
import { homepageConfig as uae } from '../configs/uae';
import { homepageConfig as global } from '../configs/global';
import { homepageConfig as singapore } from '../configs/singapore';

interface ConfigHookProps {
  view: HomepageView;
}

export const useConfig = (props: ConfigHookProps) => {
  const { view } = props;

  switch (view) {
    case HomepageView.KSA:
      return ksa;
    case HomepageView.UAE:
      return uae;
    case HomepageView.Singapore:
      return singapore;
    case HomepageView.Europe:
    default:
      return global;
  }
};
