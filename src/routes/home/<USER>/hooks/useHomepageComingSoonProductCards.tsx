/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { HomepageView } from '../types';
import { useConfig } from './useConfig';

interface HomepageProductCardHookProps {
  view: HomepageView;
}

export const useHomepageComingSoonProductCards = (props: HomepageProductCardHookProps) => {
  const { view } = props;
  const config = useConfig({ view });

  return config.comingSoonProductCards
    .map((card) => ({
      ...card,
      disabled: true,
    }));
}
