/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useAppSelector } from '../../../../reducers';
import { isStaff } from '../../../../selectors/user';
import { ClickHandlers, HomepageProductCardProps, HomepageView } from '../types';
import { useConfig } from './useConfig';

interface HomepageProductCardHookProps {
  view: HomepageView;
  clickHandlers: ClickHandlers;
}

export const useHomepageProductCards = (props: HomepageProductCardHookProps) => {
  const { view, clickHandlers } = props;
  const config = useConfig({ view });

  const isLoggedIn = useAppSelector((state) => state.currentUser.loaded);
  const isUserStaff = useAppSelector(isStaff);

  const cardProps: HomepageProductCardProps = {
    clickHandlers,
    isLoggedIn,
    isUserStaff,
    view
  }

  return config.productCards(cardProps);
}
