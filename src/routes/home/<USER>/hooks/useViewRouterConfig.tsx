/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { HomepageViewConfig } from '../types';
import { homepageViews } from '../config';
import { useHistory, useLocation } from 'react-router-dom';
import config from '../../../../config';

enum ContinentCode {
  Africa = 'AF',
  Anatartica = 'AN',
  Asia = 'AS',
  Europe = 'EU',
  NorthAmerica = 'NA',
  Oceania = 'OC',
  SouthAmerica = 'SA',
}

interface LocationInfo {
  ip: string; // 'xxx.xxx.xxx.xxx'
  city: string; // 'Singapore'
  country: string; // 'SG'
  in_eu: boolean;
  continent_code: ContinentCode;
}

export const getUserLocation = async () => {
  if (!config.isUserLocationEnabled) {
    return null;
  }

  try {
    const response = await axios.get('https://ipapi.co/json/');
    return response.data as LocationInfo;
  } catch (error) {
    console.error('Error getting user country:', error);
    return null;
  }
};

/*
  This hook is used to determine the view configuration based on:
  1) Check URL domain. If there is only one view for that domain, use that.
  2) If there is more than one, then use the view parameter and use that
  3) If there is no view parameter, then use the user's IP/location to determine the view, and add parameter to URL
  4) Fallback to Global if no other view is set
*/

const appUrlViews = homepageViews.filter((view) => view.url === window.location.origin);
// If you are missing the correct .env variables, this will redirect to production URLs
const DefaultView = appUrlViews[0] ?? homepageViews[0];

export const useViewRouterConfig = (props?: { readOnly?: boolean }) => {
  const readOnly = props?.readOnly ?? false;

  const [viewConfig, setViewConfig] = useState<HomepageViewConfig>(DefaultView);
  const history = useHistory();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const urlView = queryParams.get('v');

  const handleChangeViewConfig = useCallback((view: HomepageViewConfig) => {
    if (readOnly) {
      return; // Don\t change view if it's read only
    }

    const isMultiViewDomain = homepageViews.filter((homepageView) => homepageView.url === window.location.origin);

    if (window.location.origin !== view.url) {
      // Wrong domain, so full redirect required
      window.location.href = `${view.url}/marketplace${isMultiViewDomain ? `?v=${view.view}` : ''}`;
      return;
    }

    if (isMultiViewDomain) {
      // Right domain, but view is ambiguous view, so let's set it in the URL
      return history.replace(`/marketplace?v=${view.view}`);
    }
    history.replace('/marketplace'); // Just to clean up any weird views
  }, [history, readOnly]);

  useEffect(() => {
    // 1
    const appDomainViews = homepageViews.filter((view) => view.url === window.location.origin);
    if (appDomainViews.length === 1) {
      setViewConfig(appDomainViews[0]);
      return;
    }

    // 2
    const urlViewConfig = appDomainViews.find((view) => view.view === urlView); // Only search within views that match domain
    if (urlViewConfig) {
      setViewConfig(urlViewConfig);
      return;
    }

    // 3
    getUserLocation().then((location) => {
      if (location) {
        const locationViewConfig = homepageViews.find((view) => {
          if (view.locationMatch?.country) {
            return view.locationMatch.country?.includes(location.country);
          }
          return false;
        });
        if (locationViewConfig)  {
          handleChangeViewConfig(locationViewConfig);
        }
      }
    }).catch(() => {
      // 4
      handleChangeViewConfig(DefaultView);
    });
  }, [urlView, handleChangeViewConfig]);

  return {
    viewConfig,
    handleChangeViewConfig
  };
}
