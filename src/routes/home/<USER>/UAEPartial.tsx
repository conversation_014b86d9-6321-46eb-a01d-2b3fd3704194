/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { Hero } from './partials/Hero';
import { HeadingList } from './partials/HeadingList';
import { FeatureCards } from './partials/FeatureCards';
import { FeatureBar } from './partials/FeatureBar';
import { useViewRouterConfig } from './hooks/useViewRouterConfig';
import { useConfig } from './hooks/useConfig';
import { ClickHandlers } from './types';
import config from '../../../config';
import './UAEPartial.scss';
import { ConsultancyCard } from './partials/ConsultancyCard';

const TOLILogo = `${config.media.appsBaseUrl}/toli/toli-logo.svg`;
const AnthesisLogo = `${config.media.imagesBaseUrl}/home/<USER>
const JordiskLogo = `${config.media.imagesBaseUrl}/home/<USER>
const SustainablesgLogo = `${config.media.imagesBaseUrl}/home/<USER>
const EcosisLogo = `${config.media.imagesBaseUrl}/home/<USER>
const Ae4riaLogo = `${config.media.imagesBaseUrl}/home/<USER>

interface UAEPartialProps {
  clickHandlers: ClickHandlers;
}

export function UAEPartial(props: UAEPartialProps) {
  const { clickHandlers } = props;
  const { viewConfig } = useViewRouterConfig();

  const { featureCards, featureBarCards } = useConfig({ view: viewConfig.view });

  const handleRequestADemo = () => clickHandlers['ShowRequestADemo']();

  return (
    <>
      <Hero
        className='w-100 mt-6'
        title='Book a demo'
        text={`Simply enter your email address and we’ll send you out a booking link so you can book a demo
          with our friendly team at a time that works for you.`}
      >
        <div className='mt-5'>
          <Button color='primary' size='xl' onClick={handleRequestADemo}>
            Book a Demo
          </Button>
        </div>
      </Hero>

      <div className='d-flex justify-content-center align-items-center mt-6 gap-4'>
        <img src={TOLILogo} alt='TOLI Logo' width={'14%'} />
        <div className='uae__headlines text-center'>
          <h2 className='mb-0'>Explore our sustainability solutions</h2>
          <h3 className='h3 uae__subtitle'>From the tree of life inside institute</h3>
        </div>
      </div>

      <h2 id='education-and-training' className='mt-6'>
        Education, Training and Accreditation
      </h2>

      <Hero color='white' className='w-100 mt-3' padding={4}>
        <div className='w-100 py-4 d-flex align-items-center flex-column flex-md-row text-start'>
          <div className='flex-fill m-auto'>
            <HeadingList
              items={[
                { text: 'Sustainability education courses', icon: 'fa-graduation-cap' },
                { text: 'Webinars', icon: 'fa-chalkboard-user' },
                { text: 'Access to subject matter experts', icon: 'fa-award' },
              ]}
            />
            <Button
              className='mt-5'
              color='primary'
              outline={true}
              size='xl'
              onClick={() => window.open('https://www.g17.eco/tree-of-life-institute')}
            >
              Check out our Sustainability courses now
            </Button>
          </div>
          <div className='d-flex toli-video-container'>
            <video
              preload='metadata'
              controls={true}
              poster={`${config.media.appsBaseUrl}/toli/toli-marketplace-poster.png`}>
              <source src={`${config.media.appsBaseUrl}/toli/toli-marketplace.mp4`} type={'video/mp4'} />
            </video>
          </div>
        </div>
      </Hero>

      <h2 id='consultancy' className='mt-6'>
        Consultancy
      </h2>

      <Hero color='white' className='w-100 mt-3' padding={4}>
        <div className='w-100 d-flex align-items-center pb-4 flex-column flex-md-row'>
          <div className='flex-fill'>
            <HeadingList
              nowrap={true}
              items={[
                { text: 'Materiality Assessments', icon: 'fa-clipboard-list-check' },
                { text: 'GHG & ESG Data Mapping', icon: 'fa-share-nodes' },
                { text: 'SBTI', icon: 'fa-chart-line' },
                { text: 'Target Setting', icon: 'fa-bullseye-pointer' },
                { text: 'Sustainability Plans', icon: 'fa-file-signature' },
              ]}
            />
          </div>
          <div className='mt-3 pt-3 d-block d-md-none' />
          <ConsultancyCard
            className={{
              wrapper: 'mx-5 px-3',
            }}
            logo={{ src: AnthesisLogo, alt: 'Anthesis' }}
            text={'Global consultancy specialising in strategy & regulatory compliance'}
            bulletListItems={[
              { text: 'Education & Engagement' },
              { text: 'Innovation & Capital Solutions' },
              { text: 'Net Zero & Decarbonisation' },
              { text: 'ESG Reporting' },
            ]}
          />
          <div className='mt-3 pt-3 d-block d-md-none' />
          <ConsultancyCard
            logo={{ src: JordiskLogo, alt: 'Jordisk Consulting' }}
            text={'Assisting business in complying with EU policies such as CSRD'}
            bulletListItems={[
              { text: 'Policy Horizon Scan' },
              { text: 'Readiness' },
              { text: 'Compliance' },
              { text: 'Operational Integration' },
            ]}
          />
        </div>
      </Hero>

      <Hero color='white' className='w-100 mt-3' padding={4}>
        <div className='w-100 d-flex align-items-center pb-4 flex-column flex-md-row justify-content-between'>
          <ConsultancyCard
            className={{
              img: 'py-2',
            }}
            logo={{ src: SustainablesgLogo, alt: 'Sustainablesg' }}
            text={'Singapore-based specialising in sustainability strategy'}
            bulletListItems={[
              { text: 'Materiality Assessments' },
              { text: 'Risk Analysis & Capability' },
              { text: 'Integrated Reporting' },
              { text: 'Circular Economy Support' },
            ]}
          />
          <div className='mt-3 pt-3 d-block d-md-none' />
          <ConsultancyCard
            logo={{ src: EcosisLogo, alt: 'Ecosis' }}
            text={'Africa and APAC-focused for large-scale projects'}
            bulletListItems={[
              { text: 'Operational Development' },
              { text: 'Built Environment' },
              { text: 'Sustainable Financing' },
              { text: 'Green Building Councils' },
            ]}
          />
          <div className='mt-3 pt-3 d-block d-md-none' />
          <ConsultancyCard
            logo={{ src: Ae4riaLogo, alt: 'Ae4ria' }}
            text={'Collaborative network for research and innovation'}
            bulletListItems={[
              { text: 'Complex Data Modelling' },
              { text: 'ROI Calculations Initiatives' },
              { text: 'Technology Integration' },
              { text: 'Interdisciplinary Collaboration' },
            ]}
          />
        </div>
      </Hero>

      {featureCards && featureCards.length > 0 ? (
        <>
          <h2 id='sustainable-solutions' className='mt-6'>
            Sustainability Solutions
          </h2>
          <FeatureCards cardGroups={featureCards} defaultOnClick={handleRequestADemo} />
        </>
      ) : null}

      {featureBarCards ? (
        <>
          <h2 id='sustainable-finance' className='mt-6'>
            Sustainable Finance
          </h2>

          <div className='mt-6'>
            <FeatureBar bar={featureBarCards} defaultOnClick={handleRequestADemo} />
          </div>
        </>
      ) : null}

      <Hero
        className='w-100 mt-6'
        title='Finance'
        text={`Looking for capital to fund a project with sustainability credentials or an
          SME looking for finance with some rebates built in linked to ESG reporting? We can
          make sure you're a fit for one of our partner finance solution providers.`}
      >
        <div className='mt-5'>
          <Button color='primary' outline={true} size='xl' onClick={handleRequestADemo}>
            Contact us
          </Button>
        </div>
      </Hero>
    </>
  );
}
