import { random } from 'mathjs';
import { ChildrenTree, Tree } from '../../types/tree';
import { SelectionTreeNode, StepPathNode, TreeNode } from './types';
import {
  ARCHIVED_BG,
  ARCHIVED_COLOUR,
  BUTTON_WIDTH,
  DISTANCE_TO_BUTTON,
  LEVELCOLOURS,
  MINIMUM_DISTANCE,
} from './constants';
import { CompanyTagType, InitiativePlain, InitiativeTags } from '../../types/initiative';
import { findAllChildrenById, findCurrentBranchInTree } from '../../utils/initiative';
import * as d3 from 'd3';
import Papa from 'papaparse';
import FileSaver from 'file-saver';

export const getLevelColor = (depth: number) => {
  const style = 'border-color: ';
  if (LEVELCOLOURS[depth]) {
    return style + LEVELCOLOURS[depth];
  }
  return style + LEVELCOLOURS[random(0, LEVELCOLOURS.length)];
};

export const expandedNodePositionShift = (parent: Partial<Pick<TreeNode, 'children' | 'data' | 'parent'>>) => {
  if (!parent.parent) {
    // must be root node
    return DISTANCE_TO_BUTTON;
  }
  // if expanded, make sure the expanded button and connecting lines not overlap other buttons from same level
  return parent.children?.length ? DISTANCE_TO_BUTTON + BUTTON_WIDTH : DISTANCE_TO_BUTTON;
};

export const getExpandButtonDistance = (d: TreeNode) => {
  const firstChildNode = d.children ? d.children[0] : d._children ? d._children[0] : d;
  const source = { x: firstChildNode.x, y: firstChildNode.y };
  const target = { x: d.x, y: d.y };
  const extra = expandedNodePositionShift(d);
  return 0.5 * (source.y - target.y) + MINIMUM_DISTANCE + extra - BUTTON_WIDTH / 2;
};

export const drawConnectingLineToExpandButton = (nodeEnter: SelectionTreeNode) => {
  nodeEnter
    .append('svg:foreignObject')
    .attr('height', 2)
    .attr('width', (d) => {
      return getExpandButtonDistance(d);
    })
    .style('display', (d) => {
      return d.children || d._children ? 'inline-block' : 'none';
    })
    .attr('class', 'expand-line');
};

export const drawStepPath = (child: StepPathNode, parent: StepPathNode) => {
  const deltaY = parent.y - child.y;
  const extra = expandedNodePositionShift(parent);
  return `M${child.y}, ${child.x} H${child.y + 50 + extra + deltaY / 2} V${parent.x} H${parent.y}`;
};

export const getCTLightPreviewOrgMap = (company: ChildrenTree): ChildrenTree => {
  return {
    code: '',
    disabled: true,
    name: company.name,
    id: company.id,
    children: [
      {
        id: '',
        code: '',
        disabled: false,
        name: 'Business Unit 1',
        children: [
          {
            id: '',
            code: '',
            disabled: false,
            name: 'Americas',
            children: [],
          },
          {
            id: '',
            code: '',
            disabled: false,
            name: 'ASEAN',
            children: [
              {
                id: '',
                code: '',
                disabled: false,
                name: 'Subsidiary 1',
                children: [
                  {
                    id: '',
                    code: '',
                    disabled: false,
                    name: 'Site/Branch/Asset 1',
                    children: [],
                  },
                  {
                    id: '',
                    code: '',
                    disabled: false,
                    name: 'Site/Branch/Asset 2',
                    children: [],
                  },
                  {
                    id: '',
                    code: '',
                    disabled: false,
                    name: 'Site/Branch/Asset 3',
                    children: [],
                  },
                ],
              },
              {
                id: '',
                code: '',
                disabled: false,
                name: 'Subsidiary 2',
                children: [],
              },
              {
                id: '',
                code: '',
                disabled: false,
                name: 'Subsidiary 3',
                children: [],
              },
            ],
          },
          {
            id: '',
            code: '',
            disabled: false,
            name: 'APAC',
            children: [],
          },
        ],
      },
      {
        id: '',
        code: '',
        disabled: false,
        name: 'Business Unit 2',
        children: [],
      },
      {
        id: '',
        code: '',
        disabled: false,
        name: 'Business Unit 3',
        children: [],
      },
    ],
  };
};

export const getSubsidiaryOptions = ({
  fullTreeList,
  excludedBranchRootId,
}: {
  fullTreeList: InitiativePlain[];
  excludedBranchRootId?: string;
}) => {
  if (!excludedBranchRootId) {
    return fullTreeList.map((initiative) => ({
      label: initiative.name,
      value: initiative._id,
    }));
  }

  const subsidiaryIds = findAllChildrenById(fullTreeList, excludedBranchRootId).map((initiative) => initiative._id);
  const currentInitiativeAndChildrenIds = new Set([...subsidiaryIds, excludedBranchRootId]);

  return fullTreeList
    .filter((initiative) => !currentInitiativeAndChildrenIds.has(initiative._id))
    .map((initiative) => ({
      label: initiative.name,
      value: initiative._id,
    }));
};

export const isOrganisation = (tags: CompanyTagType[]) => {
  return tags.includes(InitiativeTags.Organization);
};

export const getBranchInitiativeNameText = ({
  initiativeTree,
  initiativeId,
  showInitiativeId = false,
}: {
  initiativeTree: Tree;
  initiativeId: string | undefined;
  showInitiativeId?: boolean;
}) => {
  if (!initiativeId) {
    return '';
  }
  const currentBranch = showInitiativeId
    ? findCurrentBranchInTree(initiativeTree, initiativeId).branch
    : findCurrentBranchInTree(initiativeTree, initiativeId).branch.filter(
        (initiative) => initiative.id !== initiativeId,
      );

  return currentBranch
    .map((initiative) => initiative.name)
    .reverse()
    .join(' / ');
};

export const getArchivedNodeStyles = () => {
  return `border-color: ${ARCHIVED_COLOUR};background-color: ${ARCHIVED_BG};`;
};

export const resetTreeMap = () => {
  d3.select('.mindmap').html('');
};

export const downloadOrgMapCsv = ({
  initiativeTree,
  initiativeTreeList,
}: {
  initiativeTree: Tree;
  initiativeTreeList: InitiativePlain[];
}) => {
  const initiativeMap = new Map(initiativeTreeList.map((initiative) => [initiative._id, initiative]));
  const rowValues = initiativeTreeList.map((initiative) => {
    const parent = initiative.parentId ? initiativeMap.get(initiative.parentId) : undefined;
    const parentName = parent?.name ?? '';
    return {
      'Subsidiary name': initiative.name,
      'Parent name': parentName,
      'Navigational path': getBranchInitiativeNameText({
        initiativeTree: initiativeTree,
        initiativeId: initiative._id,
        showInitiativeId: true,
      }),
    };
  });

  const data = {
    fields: ['Subsidiary name', 'Parent name', 'Navigational path'],
    data: rowValues,
  };

  // Use Papa Parse with quotes configuration to force text interpretation
  const csvData = Papa.unparse(data, {
    quotes: true, // Force quotes around all fields
    quoteChar: '"',
    escapeChar: '"',
  });

  // Add BOM to ensure proper encoding and Excel compatibility
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csvData], { type: 'text/csv;charset=utf-8;' });
  FileSaver.saveAs(blob, 'Organisation Map.csv');
};
