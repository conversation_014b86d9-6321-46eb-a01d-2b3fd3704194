/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { Loader } from '@g17eco/atoms/loader';
import { getUnsubscribeTokenData } from '../../actions/unsubscribe';
import queryString from 'query-string';
import './styles.scss';
import { renderContainer } from '@g17eco/molecules/direct-route-container';

const renderHeader = (data = '') => {
  return (
    <div className='headerTop' id='top'>{data}</div>
  );
};


function UnsubscribeRoute(props) {
  const [tokenData, setTokenData] = useState({loaded: false, errored: false});

  useEffect(() => {
    document.title = 'G17Eco - Unsubscribe';
    const token = queryString.parse(props.location.search)['token'];
    if (!token) {
      setTokenData({errored: true, message: 'Token is not valid'});
    }

    getUnsubscribeTokenData(token)
      .then(data => setTokenData({loaded: true, data }))
      .catch(e => setTokenData({errored: true, message: e.message}))
  }, [props.location.search]);

  if (tokenData.errored) {
    return renderContainer(
      renderHeader(),
      <h1 className='mt-5 text-center'>{tokenData.message}</h1>
    );
  }

  if (!tokenData.loaded) {
    return <Loader/>;
  }

  return (
    renderContainer(
      renderHeader('Sorry to see you go'),
      <h1 className='mt-5 text-center'>Unsubscribed</h1>
    )
  );
}

const Unsubscribe = withRouter(UnsubscribeRoute);
export default Unsubscribe;
