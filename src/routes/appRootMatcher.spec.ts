/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { navigateToRootApp } from './appRootMatcher';
import { SettingStorage } from '@services/SettingStorage';
import { AppIds, PERMISSION_GROUPS } from '@utils/permission-groups';
import { Mock } from 'vitest';
import { RootInitiativeData } from '@g17eco/types/initiative';
import { createRootInitiative } from '@fixtures/initiative-factory';
import { faker } from '@faker-js/faker';

vitest.mock('../services/SettingStorage');

describe('navigateToRootApp', () => {
  beforeEach(() => {
    vitest.resetAllMocks();
  });

  const firstId = faker.database.mongodbObjectId();

  const rootInitiativeData = createRootInitiative({ permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE });
  const rootWithFirst = createRootInitiative({
    permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE,
    firstInitiativeId: firstId,
  });

  it('should return the URL for the first initiative if no stored ID is found', () => {
    (SettingStorage.getItem as Mock).mockReturnValue(null);
    const result = navigateToRootApp([rootInitiativeData,], AppIds.CompanyTracker);
    expect(result).toContain(rootInitiativeData._id);
  });

  it('should return the URL for the stored ID if it is found in the initiatives', () => {
    (SettingStorage.getItem as Mock).mockReturnValue(rootWithFirst._id);
    const initiatives = [rootInitiativeData, rootWithFirst];

    const result = navigateToRootApp(initiatives, AppIds.CompanyTracker);
    expect(result).toContain(rootWithFirst.firstInitiativeId);
  });

  it('should throw error, if no initiativeId is available', () => {
    (SettingStorage.getItem as Mock).mockReturnValue('1');
    const initiatives: RootInitiativeData[] = [];

    expect(() => navigateToRootApp(initiatives, AppIds.CompanyTracker)).to.throw(
      Error,
      'Expected "initiativeId" to be defined'
    );
  });
});
