/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { Button } from 'reactstrap';
import { formatDate } from '../../../utils/date';
import { onboardUserOnSurvey } from '../../../actions/user';
import { renderOutcome } from '../../../components/survey/form/OutcomeMessage';
import { SURVEY } from '@constants/terminology';

class SurveyOnboarding extends React.Component {

  onboardUserOnSurvey = (action) => onboardUserOnSurvey(this.props.token.token, action);

  constructor(props) {
    super(props)
    this.state = {
      saving: false,
      error: false
    }
    document.title = `G17Eco - ${SURVEY.CAPITALIZED_SINGULAR} Onboarding`;
    this.accept = this.accept.bind(this);
    this.reject = this.reject.bind(this);
  }

  accept(e) {
    e.preventDefault();
    this.save('accept', 'Joining...');
  }

  reject(e) {
    e.preventDefault();
    this.save('reject', 'Rejecting...');
  }

  save(action, savingMessage) {
    this.setState({
      ...this.state,
      saving: true,
    });
    this.props.setUserMessage(savingMessage);

    this.onboardUserOnSurvey(action).then(result => {
      this.setState({
        ...this.state,
        saving: false,
        action,
        isSaved: result.success,
      });
      this.props.setUserMessage();
    });

    //do something
    return false;
  }

  renderOutcomeMessage() {
    const { token } = this.props;

    switch (this.state.action) {
      case 'accept':
        return renderOutcome({
          title: 'Welcome!',
          message: `You have successfully joined the <strong>${token.initiativeName} reporting</strong>`,
          link: '/',
          linkText: 'Go to the homepage',
          reload: true,
        });
      case 'reject':
        return renderOutcome({ title: 'Thank you for your reply' });
      default:
    }

    return renderOutcome({ title: '' });
  }

  render() {
    const { saving, error, isSaved } = this.state;
    const { token } = this.props;

    if (isSaved) {
      return this.renderOutcomeMessage();
    }

    let welcomeText = '';

    if (token.surveyConfig) {
      const surveyConfig = token.surveyConfig;
      const dateStr = formatDate(surveyConfig.effectiveDate, 'MMMM YYYY');
      const surveyName = `${dateStr} ${surveyConfig.utrvType}`;
      welcomeText = <Fragment>You have been invited to assist with the <strong>{surveyName}</strong> reporting for <strong>{token.initiativeName}</strong></Fragment>;
    } else if (token.utrvConfig || token.surveyStakeholders) {
      welcomeText = <Fragment>You have been invited to contribute or verify data for <strong>{token.initiativeName}</strong></Fragment>;
    } else {
      welcomeText = <Fragment>You have been invited to access the sustainability profile for <strong>{token.initiativeName}</strong></Fragment>;
    }

    return <div className='surveyOnboarding row'>
      <div className='offset-3 col-6'>
        <h1>Invitation Confirmation</h1>
        <p>{welcomeText}</p>

        {error &&
          <div className='text-danger mt-xs'>An unexpected error occurred. Please try again.</div>
        }

        <div className='mt-3 d-flex justify-content-end'>
          <Button
            color='link'
            type='button'
            onClick={this.reject}
            name={'action'}
            value={'reject'}
            disabled={saving}
            className='mr-2 text-nowrap'>
            No thanks
          </Button>
          <Button
            type='button'
            color='primary'
            onClick={this.accept}
            name={'action'}
            value={'accept'}
            disabled={saving}>
            Accept
          </Button>

        </div>
      </div>
    </div>
  }
}

export default withRouter(SurveyOnboarding);
