/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { Link, withRouter } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { generateUrl } from '../../util';
import { SURVEY } from '@constants/terminology';

class Error extends React.Component {

  componentDidUpdate() {
    document.title = 'G17Eco - Invalid Token';
  }

  render() {
    const { token, errorMessage } = this.props;

    const getTitle = (status) => {
      switch (status) {
        case 'rejected':
        case 'deleted':
          return 'This token has expired or is no longer valid';
        default:
          return errorMessage || 'This token is not valid';
      }
    }

    const title = getTitle(token.status);

    return <div className='surveyOnboarding row'>
      <div className='offset-3 col-6'>
        <h1>{title}</h1>
        <p>If you think this is an error, please contact support.</p>

        <Link to={generateUrl(ROUTES.COMPANY_TRACKER)} className='mt-4 btn btn-secondary w-100'>
          Go to {SURVEY.PLURAL}
          </Link>
      </div>
    </div>
  }
}

export default withRouter(Error);
