/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { withRouter } from 'react-router-dom';
import { Form, FormGroup, Input, Button, Label } from 'reactstrap';
import Logo from '../../images/g17Eco.svg';
import { Footer } from '@g17eco/molecules/footer';
import { registerUser } from '../../actions/user';
import { isValidEmail } from '../../utils';
import './styles.scss';

class Registration extends React.Component {
  state = {
    form: {
      title: '',
      email: '',
      firstName: '',
      surname: '',
    },
    submitted: false,
    saving: false,
    error: ''
  };

  componentDidMount() {
    document.title = 'G17Eco - User Registration';
  }

  handleBackButton = (e) => {
    e.preventDefault();
    this.props.history.goBack();
  }

  handleChange = (e) => {
    const target = e.target;
    this.setState({
      form: {
        ...this.state.form,
        [target.name]: target.value
      }
    })
  };

  save = (e, form) => {
    e.preventDefault();
    this.setState({ saving: true, error: false });

    registerUser(form)
      .then(res => this.setState({ saving: false, submitted: true, error: '' }))
      .catch(e => this.setState({ saving: false, error: e.message }));
  }

  render() {
    return <div className='registrationWrapper'>
      <div className='registrationContainer'>
        <header>
          <img className='logo' src={Logo} alt='G17Eco' />
        </header>
        <div className='registrationBodyContainer'>
          <div className='row'>
            <div className='col offset-md-2 col-md-8'>
              {this.renderForm()}
            </div>
          </div>
          <Footer />
        </div>
      </div>
    </div>
  }

  renderForm = () => {
    const { form, saving, error, submitted } = this.state;

    if (submitted) {
      return <>
        <h1>Thank You!</h1>
        <p>You should now receive an activation e-mail to complete your registration.</p>
      </>
    }

    const { title, firstName, surname, email } = form;

    const fields = ['title', 'email', 'firstName', 'surname'];
    const validEmail = isValidEmail(email);
    const missingData = fields.some(field => !form[field]);
    const formDisabled = !validEmail || missingData || saving;

    const emailLink = <a href='mailto:<EMAIL>' rel='noopener noreferrer' target='_blank'><EMAIL></a>;

    return <>
      <h1>Sign Up to G17Eco</h1>
      <p>Once you complete your details you will receive a confirmation e-mail to activate your account. If you have any problems please contact us on {emailLink}.</p>

      <div className='row'>
        <div className='col offset-md-2 col-md-8'>
          <Form onSubmit={(e) => this.save(e, form)}>

            <FormGroup>
              <Label for='title' className='strong'>Title*</Label>
              <Input valid={title.length > 0} type='text' required={true} defaultValue={title} name='title' onChange={this.handleChange} />
            </FormGroup>

            <FormGroup>
              <Label for='firstName' className='strong'>First Name*</Label>
              <Input valid={firstName.length > 0} type='text' required={true} name='firstName' defaultValue={firstName} onChange={this.handleChange} />
            </FormGroup>

            <FormGroup>
              <Label for='surname' className='strong'>Last Name*</Label>
              <Input valid={surname.length > 0} type='text' required={true} name='surname' defaultValue={surname} onChange={this.handleChange} />
            </FormGroup>

            <FormGroup>
              <Label for='email' className='strong'>E-mail*</Label>
              <Input invalid={email.length > 0 && !validEmail} valid={email.length > 0 && validEmail} type='email' required={true} name='email' value={email} onChange={this.handleChange} />
            </FormGroup>

            <Label className='strong'>How my personal details are used</Label>
            <div className='mb-3'>
              To find out how we handle your data, please see our <a href='/legal-privacy-policy' target='_blank'>Privacy Policy</a>.
            </div>

            {error && <div className='alert alert-danger mt-xs'>{error}</div>}

            <div className='row mt-1 g-0'>

              <div className='col text-right pr-1'>
                <Button
                  color='link-secondary'
                  onClick={this.handleBackButton}>
                  Back
                </Button>
                <Button
                  disabled={formDisabled}
                  type='submit'
                  color='primary'>
                  {saving ? 'Saving...' : 'Create Account'}
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </>
  }
}

export default withRouter(Registration);
