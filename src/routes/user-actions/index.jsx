/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import Logo from '../../images/logo.svg';
import { connect } from 'react-redux';
import { Loader } from '@g17eco/atoms/loader';
import UserActionsComponent from '../../components/user-actions';
import { loadUserActions } from '../../actions/userTracker';
import UTrModal from '../../components/utr-modal';
import './styles.scss';

class UserActions extends React.Component {

  componentDidMount() {
    this.props.loadUserActions();
  }

  render() {

    const { userActionsState } = this.props;

    if (userActionsState.errored) {
      return this.renderError();
    }

    if (!userActionsState.loaded) {
      return (<Loader />);
    }

    const { user, actions } = userActionsState.data;
    const currentUser = user;

    const actionCount = actions.frameworkUtrs.length + actions.utrs.length;

    return this.renderContainer(
      <div className='headerTop mb-2'>
        Welcome <strong>{currentUser.name}</strong>! You currently have <a
          href='#actions'><u>{actionCount} action{actionCount > 1 ? 's' : ''}</u></a> to
      complete.
      </div>,
      <UserActionsComponent />
    );
  }

  renderError() {
    return this.renderContainer(
      <div className='headerTop'>&nbsp;</div>,
      <div className='sectionContainer'>
        <h1>Unable to load pending tasks</h1>
      </div>
    );
  }

  renderContainer(headerTop, actions) {
    return (
      <div className='userActionsWrapper'>
        <div className='userActionsContainer'>
          <header>
            <div className='headerBottom'>
              <img className='logo' src={Logo} alt='G17Eco' />
            </div>
            {headerTop}
          </header>
          {actions}
          <UTrModal />
        </div>
      </div>
    )
  }
}

const mapStateToProps = (state) => ({
  userActionsState: state.userTrackers,
});

const mapDispatchToProps = ({
    loadUserActions,
});

export default connect(mapStateToProps, mapDispatchToProps)(UserActions);
