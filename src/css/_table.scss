.collapsible-table {
  .table {
    .thead {
      background-color: var(--theme-BgToolbar) !important;
      z-index: initial;

      .tr {
        background-color: var(--theme-BgToolbar) !important;
        font-weight: bold;
        z-index: initial;

        .th {
          height: 42px;
          background-color: var(--theme-BgToolbar) !important;
          z-index: initial;

          &:first-of-type {
            padding-left: 12px;
          }
        }
        .header-button {
          z-index: 1;
        }
      }
    }

    .tbody {
      .tr {
        margin-top: 1px;

        &:hover {
          .td {
            background-color: var(--theme-NeutralsExtralight);
          }
        }

        button:hover {
          i {
            color: var(--theme-WarningMedium) !important;
          }
        }
      }
    }

    .tr {
      border-radius: $borderRadius;

      &.row-select-selected {
        font-weight: normal;
      }

      .td,
      .th {
        border-bottom: none;
        padding: 6px 4px;

        &:last-of-type {
          border-radius: 0 $borderRadius $borderRadius 0;
          text-align: center;

          > div {
            width: 100%;
          }
        }

        &:first-of-type {
          border-radius: $borderRadius 0 0 $borderRadius;
        }

        .form-check-input {
          position: static;
          margin-left: 0;
        }
      }
    }
  }

  .date-filter {
    &--dropdown {
      width: 150px;
    }

    &--dropdown-toggle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
  }
}
