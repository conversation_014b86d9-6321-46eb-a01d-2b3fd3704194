/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

 .dropdown {
  button:not(.dropdown-item) {
    border-radius: $borderRadius;
    min-width: 1.8rem;
  }
  .dropdown-menu {
    font-size: $font-size-base;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.3);
    max-height: 500px;
    overflow-x: scroll;
    .dropdown-item {
      min-height: 2rem;
      color: var(--theme-TextDark);
      i, svg {
        color: var(--theme-IconSecondary);
      }

      &.disabled {
        color: var(--theme-TextPlaceholder);
        i, svg {
          color: var(--theme-TextPlaceholder);
        }
      }
      &:hover,
      &.active {
        color: white;
        i, svg {
          color: white !important;
        }
      }
      &:hover {
        background-color: var(--theme-AccentMedium);
      }
      &:active, &.active {
        background-color: var(--theme-AccentDark);
      }
    }
  }
}

.dropdown_deprecated {
  &.show {
    button:not(.dropdown-item) {
      text-decoration: none;
    }
  }
  button:not(.dropdown-item) {
    border-radius: $borderRadius;
    min-width: 1.8rem;
    height: 1.8rem;
    padding: 0rem 0.5rem;
  }
  .dropdown-menu {
    font-size: 0.8rem;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.3);
    max-height: 500px;
    overflow-x: scroll;
    .dropdown-header {
      padding: 2px 12px;
    }
    .dropdown-item {
      padding: 2px 12px;
      min-height: 2rem;
      &.disabled {
        color: var(--theme-BgDisabled);
        i {
          color: var(--theme-BgDisabled);
        }
        span {
          background-color: var(--theme-BgDisabled);
          color: var(--theme-TextWhite);
        }
      }
      i,
      span {
        text-align: center;
        width: 20px;
      }
      i {
        font-size: 0.9rem;
      }
      span {
        font-size: 0.5rem;
        padding: 3px 0px;
      }
      &:hover,
      &.active {
        background-color: var(--theme-AccentMedium);
        color: white;
        i {
          color: white !important;
        }
      }
    }
  }
}
