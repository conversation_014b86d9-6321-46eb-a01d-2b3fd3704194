/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */



import { getRootAppPath, rootAppPath } from '@routes/company-tracker/utils';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { ReportEditorCreateRoute } from './ReportEditorCreateRoute';
import { ReportEditorListRoute } from './ReportEditorListRoute';
import { ReportEditorViewRoute } from './ReportEditorViewRoute';
import { RoutesInterface } from '@g17eco/types/routes';

const supportedTypes = ['csrd'] as const;
const validTypes = supportedTypes.map((type) => type).join('|');
const typePathElement = `:type(${validTypes})`;
const basePathPrefix = `/${rootAppPath}/editor/:initiativeId`;

const routeDefaults = {
  auth: true,
  getRootAppPath: getRootAppPath,
  requiresInitiativeId: true,
  appPermissionId: 'app_company_tracker',
}

export const REPORT_EDITOR_ROUTE_MAP = {
  REPORT_EDITOR_CREATE: {
    ...routeDefaults,
    id: 'report_editor_create',
    label: 'Report Editor Create',
    path: `${basePathPrefix}/${typePathElement}/create`,
    component: ReportEditorCreateRoute,
    requiredUserRoles: InitiativePermissions.canContributeRoles,
    tooltip: 'Report Editor Create',
  },
  REPORT_EDITOR_LIST: {
    ...routeDefaults,
    id: 'report_editor_list',
    label: 'Report Editor List',
    path: `${basePathPrefix}/${typePathElement}`,
    component: ReportEditorListRoute,
    requiredUserRoles: InitiativePermissions.canContributeRoles,
    tooltip: 'Report Editor List',
  },
  REPORT_EDITOR_VIEW: {
    ...routeDefaults,
    id: 'report_editor',
    label: 'Report Editor',
    path: `${basePathPrefix}/:reportId`,
    component: ReportEditorViewRoute,
    requiredUserRoles: InitiativePermissions.canContributeRoles,
    tooltip: 'Report Editor',
  },
} satisfies RoutesInterface;
