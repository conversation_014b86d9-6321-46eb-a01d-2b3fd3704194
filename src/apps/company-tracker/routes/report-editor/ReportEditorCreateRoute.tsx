/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useParams } from 'react-router';
import Dashboard from '@components/dashboard';
import { ReportDocumentCreate } from '@apps/company-tracker/routes/report-editor/components/ReportDocumentCreate';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';


export const ReportEditorCreateRoute = () => {

  const { initiativeId, type } = useParams<{ initiativeId: string, type: string }>();
  const history = useHistory();

  const onCancel = () => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_LIST, { initiativeId, type }));
  };

  const onView = (reportId: string) => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_VIEW, { initiativeId, type, reportId }));
  };

  return (
    <Dashboard>
      <ReportDocumentCreate
        onCancel={onCancel}
        onView={onView}
        initiativeId={initiativeId}
        type={type}
      />
    </Dashboard>
  );
}
