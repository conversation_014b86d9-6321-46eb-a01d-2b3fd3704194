/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useState } from 'react';
import { generateErrorToast, generateToast } from '@components/toasts';
import { Button } from 'reactstrap';
import { DashboardRow, DashboardSection } from '@components/dashboard';
import { CreateReportDocumentMin, ReportDocument } from '@g17eco/types/reportDocument';
import { useCreateReportDocumentMutation, useUpdateReportDocumentMutation } from '@api/initiative-report-documents';
import { getGroup } from '@g17eco/core';
import { FieldProps, FormGenerator } from '@g17eco/molecules/form';
import { SubmitButton } from '@components/button/SubmitButton';

interface Props {
  initiativeId: string;
  type: string;
  onCancel: () => void;
  onView: (reportId: string) => void;
}

const fields = [
  {
    code: 'title',
    label: 'Title',
    type: 'text',
    required: true,
  },
  {
    code: 'description',
    label: 'Description',
    type: 'textarea',
  },
] satisfies FieldProps<CreateReportDocumentMin>[];

export const ReportDocumentCreate = (props: Props) => {

  const { initiativeId, type, onCancel, onView } = props;

  const [form, setForm] = useState<CreateReportDocumentMin | ReportDocument>({
    title: '',
    description: '',
    initiativeId,
    type
  });

  const [createCustomReport, { isLoading: isCreating }] = useCreateReportDocumentMutation();
  const [updateCustomReport, { isLoading: isUpdating }] = useUpdateReportDocumentMutation();

  const isDisabled = !form.title || !form.type || isCreating || isUpdating;
  const reportId = form._id;

  const handleSubmit = async () => {
    if (isDisabled) {
      return;
    }

    const action = reportId ? 'updated' : 'created';
    const method = reportId && 'created' in form
      ? updateCustomReport(form)
      : createCustomReport(form);

    return method
      .unwrap()
      .then((reportDocument) => {
        setForm(reportDocument);
        generateToast({
          title: `Custom report ${action}`,
          color: 'success',
          message: `Custom report template has been ${action}`,
        });
        onView(reportDocument._id);
      })
      .catch((error) => {
        generateErrorToast(error);
      });
  };


  const buttonText = reportId ? 'Save' : 'Create report';
  const group = getGroup('standards', type);
  return (
    <>
      <DashboardRow>
        <div className='w-100 d-flex justify-content-between align-items-center'>
          <h3>{group?.name ?? type} Report</h3>
        </div>
      </DashboardRow>
      <DashboardSection paddingInternal={0}>
        <FormGenerator
          fields={fields}
          form={form}
          updateForm={(update) => {
            const { name, value } = update.currentTarget
            setForm((currentForm) => ({ ...currentForm, [name]: value, }));
          }} />
        <div className='d-flex flex-row justify-content-between'>
          <div className='mt-3 d-flex justify-content-end gap-3'>
            <Button color='link-secondary' onClick={onCancel}>
              Cancel
            </Button>
            <SubmitButton color='primary' onClick={handleSubmit} disabled={isDisabled}>
              {buttonText}
            </SubmitButton>
          </div>
        </div>
      </DashboardSection>
    </>
  );
}
