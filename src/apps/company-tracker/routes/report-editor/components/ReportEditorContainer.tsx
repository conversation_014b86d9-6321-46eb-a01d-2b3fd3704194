/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { ReportEditor } from '@features/rich-text-editor/ReportEditor';
import { useProviderFactory } from '@features/rich-text-editor';
import { ReportDocument } from '@g17eco/types/reportDocument';
import { DashboardSection } from '@components/dashboard';
import { useCurrentUser } from '@hooks/useCurrentUser';
import { getFullName } from '@utils/user';
import { useMemo } from 'react';
import { UserMin } from '@constants/users';
import { useListExternalMappingQuery } from '@api/initiative-external-mapping';
import { QueryWrapper } from '@components/query/QueryWrapper';

interface Props {
  reportDocument: ReportDocument;
  disabled?: boolean;
}

export const ReportEditorContainer = (props: Props) => {
  const { reportDocument, disabled = false } = props;
  const { providerFactory } = useProviderFactory();
  const currentUser = useCurrentUser();

  const users = useMemo<UserMin[]>(() => {
    return [];
  }, []);

  const query = useListExternalMappingQuery({
    initiativeId: reportDocument.initiativeId,
    type: reportDocument.type,
  });

  return (
    <DashboardSection title={reportDocument.title}>
      <p>{reportDocument.description}</p>
      <QueryWrapper query={query} onSuccess={(externalMappings) => {
        return (
          <ReportEditor
            username={getFullName(currentUser)}
            users={users}
            disabled={disabled}
            externalMappings={externalMappings}
            documentId={reportDocument._id}
            providerFactory={providerFactory} />
        )
      }} />

    </DashboardSection>
  )
}
