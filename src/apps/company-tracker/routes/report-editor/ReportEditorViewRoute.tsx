/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useParams } from 'react-router';
import { ReportEditorContainer } from './components/ReportEditorContainer';
import { useGetReportDocumentQuery } from '@api/initiative-report-documents';
import { QueryWrapper } from '@components/query/QueryWrapper';
import Dashboard from '@components/dashboard';

export const ReportEditorViewRoute = () => {

  const { initiativeId, reportId } = useParams<{ initiativeId: string; reportId: string }>();
  const query = useGetReportDocumentQuery({
    initiativeId,
    reportId,
  });

  return (
    <Dashboard>
      <QueryWrapper
        query={query}
        onSuccess={(reportDocument) => {
          return <ReportEditorContainer reportDocument={reportDocument} />;
        }}
      />
    </Dashboard>
  );
}
