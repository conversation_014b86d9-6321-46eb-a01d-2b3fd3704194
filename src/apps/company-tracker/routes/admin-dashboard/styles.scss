/*!
 * Copyright (c) 2021. World Wide Generation Ltd
 */
@import 'react-date-range/dist/styles.css'; // main css file
@import 'react-date-range/dist/theme/default.css'; // theme css file
@import 'src/css/functions';

.admin-dashboard {
  .header-row {
    .header-subtitle-prefix {
      border-right: 1px solid var(--theme-IconSecondary);
    }
    .dashboard-row__title,
    .dashboard-row__title__container {
      min-width: 0;
    }
    .dashboard-row__title {
      .text-truncate {
        line-height: var(--bs-body-line-height);
      }
    }
  }

  .title-line:hover span {
    color: var(--theme-AccentMedium) !important;
  }

  .columnStyle {
    .dashboard-children {
      margin-top: 20%;
      text-align: center;
      color: var(--theme-HeadingLight);
      font-weight: bold;
    }
  }

  .report-switcher-container {
    text-align: left;
  }

  .sub-reporting-level-list {
    max-height: 260px;
    overflow-y: auto;
    padding: 0.5rem 2rem;
  }

  .complete-btn {
    white-space: nowrap;
    padding: 0 0.5rem;
  }

  &__title {
    i {
      cursor: pointer;
      margin-left: 7px;
      color: var(--theme-AccentMedium);
    }
  }
  .dashboard-section {
    .action-buttons {
      width: 100%;
      gap: 0.5rem;
      margin-bottom: 10px;
    }
  }
}
