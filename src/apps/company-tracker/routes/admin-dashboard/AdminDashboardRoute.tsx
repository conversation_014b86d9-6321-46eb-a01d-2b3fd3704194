/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { AdminOverview } from '../../components/admin-dashboard/AdminOverview';
import { AdminSurveyRoute } from './AdminSurveyRoute';
import { rootAppPath } from '@routes/company-tracker/utils';
import { AdminUserRoute } from './AdminUserRoute';
import { RouteInterface } from '@g17eco/types/routes';
import './styles.scss'
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { AdminQuestionRoute } from './AdminQuestionRoute';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';

export const adminRoute: RouteInterface = {
  id: 'admin_dashboard',
  label: 'Admin Dashboard',
  path: `/${rootAppPath}/admin/:initiativeId/admin-dashboard`,
  component: AdminDashboardRoute,
  tooltip: 'Admin Dashboard',
  requiredUserRoles: InitiativePermissions.canManageRoles,
  allowInitiativeChange: true,
  hideOnMissingPermission: true,
  canAccessFeaturePermission: FeaturePermissions.canAccessAdminDashboard
}

export function AdminDashboardRoute() {

  return (
    <div className={'admin-dashoboard-route'}>
      <Switch>
        <Route path={`${adminRoute.path}/questions`}>
          <AdminQuestionRoute />
        </Route>
        <Route path={`${adminRoute.path}/users/:userId`}>
          <AdminUserRoute />
        </Route>
        <Route path={`${adminRoute.path}/overview/:surveyId`}>
          <AdminSurveyRoute />
        </Route>
        <Route>
          <AdminOverview />
        </Route>
      </Switch>
    </div>
  )
}

