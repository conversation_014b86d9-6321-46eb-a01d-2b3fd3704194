/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import { UserManagement } from '../../../routes/user-management'
import { useAppSelector } from '../../../reducers';
import { FeaturePermissions } from '../../../services/permissions/FeaturePermissions';
import { useParams } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { UserLimitIncrease } from '../components/user-limit/UserLimitIncrease';
import { useMemo } from 'react';
import { AddonButton } from '../components/payment-details/AddonButton';
import { FeatureCode } from '@g17eco/core';
import { TrialModal } from '../components/payment-details/TrialModal';
import { UserLimitUpgradeModal } from '../components/user-limit/UserLimitUpgradeModal';
import { CTAdminBreadcrumbs } from '../components/breadcrumbs/CTAdminBreadcrumbs';

export const UserManagementRoute = () => {

  const { initiativeId, page } = useParams<{ page?: string, initiativeId: string }>();
  const userLimit = useAppSelector(FeaturePermissions.getLimitUsers);

  const globalData = useAppSelector(state => state.globalData);
  const rootOrg = globalData.loaded ? globalData.data?.organization : undefined;
  const appConfig = globalData.loaded ? globalData.data?.appConfig : undefined;

  const buttons = useMemo(() => {
    if (!rootOrg) {
      return [];
    }

    return [
      <AddonButton
        key={FeatureCode.Users}
        featureCode={FeatureCode.Users}
        rootOrg={rootOrg}
        appConfig={appConfig}
        TrialModalComponent={({ toggle }) => (
          <TrialModal
            title={'Additional user seats unavailable during trial'}
            toggle={toggle}
            initiativeId={rootOrg._id}>
            During the free trial, the option to buy additional user seats is not available.
            To buy more seats, first add credit card details or another payment method.
          </TrialModal>
        )}
        UpgradeModal={UserLimitUpgradeModal}
      >
        <i className='fal fa-user-plus mr-2' />Add seats
      </AddonButton>
    ]
  }, [appConfig, rootOrg])


  return (
    <UserManagement
      buttons={buttons}
      UserLimitComponent={UserLimitIncrease}
      baseRoute={ROUTES.MANAGE_USERS}
      initiativeId={initiativeId}
      page={page}
      userLimit={userLimit}
      canInviteMultipleUsers
      BreadcrumbsComponent={CTAdminBreadcrumbs}
    />
  );
}
