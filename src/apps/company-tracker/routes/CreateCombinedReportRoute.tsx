/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { CombinedReportForm } from '../components/combined/CombinedReportForm';
import { useHistory, useParams } from 'react-router';
import { SurveyInitiative, SurveyType } from '@g17eco/types/survey';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { handleUnloadSurvey, loadSurvey, reloadSurvey, reloadSurveyListSummary } from '@actions/survey';
import { reloadSurveyList } from '@g17eco/slices/initiativeSurveyListSlice';
import Dashboard, { DashboardRow, DashboardSection } from '@components/dashboard';
import { useSearchParams } from '@hooks/useSearchParams';
import { CombinedReportAdvancedForm } from '@apps/company-tracker/components/combined/CombinedReportAdvancedForm';
import { SURVEY } from '@constants/terminology';
import { Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useEffect } from 'react';
import { SurveyState } from '../../../reducers/survey';
import { Loader } from '@g17eco/atoms/loader';

export const CreateCombinedReportRoute = () => {
  const { initiativeId, surveyId } = useParams<{ initiativeId: string; surveyId: string }>();
  const dispatch = useAppDispatch();
  const history = useHistory();
  const surveyState = useAppSelector<SurveyState>((state) => state.survey);

  useEffect(() => {
    if (!surveyId) {
      dispatch(handleUnloadSurvey());
      return;
    }
    if (surveyId !== surveyState.surveyId) {
      dispatch(loadSurvey(surveyId));
    }
  }, [surveyId, surveyState.surveyId, dispatch]);

  const version = useSearchParams()?.get('version') ?? 'latest';

  const toggle = (_update?: { status: string }) => {
    history.push(`/company-tracker/reports/${initiativeId}?tab=${SurveyType.Aggregation}`);
  };

  const redirectToOverview = (surveyId: string) => {
    history.push(`/company-tracker/reports/${initiativeId}/${surveyId}/overview`);
  };

  const handleSubmit = (survey: Pick<SurveyInitiative, '_id'>) => {
    dispatch(reloadSurveyList());
    dispatch(reloadSurveyListSummary());
    if (surveyId) {
      dispatch(reloadSurvey(survey._id));
    }
    redirectToOverview(survey._id);
  };

  const breadcrumbs = [{ label: surveyId ? `Combined ${SURVEY.SINGULAR}` : `Create combined ${SURVEY.SINGULAR}` }];

  if (surveyId && !surveyState.loaded) {
    return <Loader />;
  }

  return (
    <Dashboard>
      <DashboardRow>
        <Breadcrumbs
          breadcrumbs={breadcrumbs}
          rootLabel={`All ${SURVEY.PLURAL}`}
          rootUrl={generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId })}
        />
      </DashboardRow>
      <DashboardRow>
        <h3>{surveyId ? `Combined ${SURVEY.SINGULAR}` : `Create combined ${SURVEY.SINGULAR}`}</h3>
      </DashboardRow>
      <DashboardSection padding={3}>
        {version === '1' ? (
          <CombinedReportForm initiativeId={initiativeId} onCreate={toggle} onCancel={toggle} />
        ) : (
          <CombinedReportAdvancedForm
            key={surveyState.surveyId}
            onCancel={toggle}
            initiativeId={initiativeId}
            survey={surveyState.loaded ? surveyState.data : undefined}
            onSubmit={handleSubmit}
          />
        )}
      </DashboardSection>
    </Dashboard>
  );
};
