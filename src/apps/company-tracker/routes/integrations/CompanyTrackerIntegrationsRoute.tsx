/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useParams } from 'react-router-dom';
import { CTAdminBreadcrumbs } from '../../components/breadcrumbs/CTAdminBreadcrumbs';
import { ProviderCard, IntegrationsDashboard } from '@features/integrations';
import config from '../../../../config';
import { useToggle } from '@hooks/useToggle';
import { useAppSelector } from '@reducers/index';
import { getRootOrg, isRootOrg } from '@selectors/initiative';
import NotAuthorised from '@routes/not-authorised';
import { ProductCodes, SubscriptionService } from '@services/SubscriptionService';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { companyTrackerCardConfig, materialityTrackerCardConfig } from '@constants/app-card';
import { createOnClickFromExternalUrl } from '@utils/click-handler';
import { MaterialityTrackerIntegrationModal } from './MaterialityTrackerIntegrationModal';

export const CompanyTrackerIntegrationsRoute = () => {
  const { initiativeId } = useParams<{ initiativeId: string }>();
  const [openModal, toggleModal] = useToggle(false);

  const isRootOrganization = useAppSelector(isRootOrg);
  const rootOrg = useAppSelector(getRootOrg);

  if (!rootOrg || !isRootOrganization || initiativeId !== rootOrg._id) {
    return <NotAuthorised />;
  }

  const activeCards: ProviderCard[] = [
    {
      logo: companyTrackerCardConfig.logo,
      color: companyTrackerCardConfig.color,
      buttons: [{ text: 'More info', onClick: createOnClickFromExternalUrl(config.brochureURL.companyTracker) }],
    },
  ];

  const availableCards: ProviderCard[] = [];

  const isMTIntegrated = SubscriptionService.hasActive(
    ProductCodes.MaterialityTracker,
    rootOrg.calculatedSubscriptions,
  );
  (isMTIntegrated ? activeCards : availableCards).push({
    logo: materialityTrackerCardConfig.logo,
    color: materialityTrackerCardConfig.color,
    buttons: [
      { text: 'More info', onClick: createOnClickFromExternalUrl(config.brochureURL.materialityTracker) },
      isMTIntegrated
        ? {
            text: 'Open',
            onClick: createOnClickFromExternalUrl(generateUrl(ROUTES.MATERIALITY_TRACKER, { initiativeId })),
          }
        : {
            text: 'Connect',
            onClick: toggleModal,
          },
    ],
  });

  return (
    <>
      <IntegrationsDashboard
        initiativeId={rootOrg._id}
        additionalCards={{ active: activeCards, available: availableCards }}
        BreadCrumbsComponent={CTAdminBreadcrumbs}
      />
      <MaterialityTrackerIntegrationModal initiative={rootOrg} isOpen={openModal} toggle={toggleModal} />
    </>
  );
};
