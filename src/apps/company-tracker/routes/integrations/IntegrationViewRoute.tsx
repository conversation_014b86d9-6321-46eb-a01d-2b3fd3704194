/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useParams } from 'react-router-dom';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { QueryWrapper } from '@components/query/QueryWrapper';
import { useCreateInitiativeIntegrationMutation, useGetInitiativeIntegrationQuery } from '@api/initiative-integrations';
import { ProviderView } from '@features/integrations/IntegrationView';
import { CreateSetupFn } from '@g17eco/types/integration';
import { CTAdminBreadcrumbs } from '../../components/breadcrumbs/CTAdminBreadcrumbs';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';

export const IntegrationViewRoute = () => {

  const { code, initiativeId } = useParams<{ code: string, initiativeId: string }>()

  const query = useGetInitiativeIntegrationQuery({ initiativeId, code, }, { skip: !code });

  const [createMutation] = useCreateInitiativeIntegrationMutation()

  const onCreate: CreateSetupFn = (data) => {
    createMutation({
      code,
      initiativeId,
      generatedAnswers: data.generatedAnswers,
    })
  }

  return (
    <Dashboard>
      <div className='pl-2 pb-4'>
        <CTAdminBreadcrumbs
          initiativeId={initiativeId}
          breadcrumbs={[
            { label: 'Connections', url: generateUrl(ROUTES.INTEGRATIONS, { initiativeId }) },
            { label: 'Set up' },
          ]}
        />
      </div>
      <DashboardSectionTitle title={'Connection set up'} />
      <DashboardSection>
        <QueryWrapper query={query} onSuccess={(data) => <ProviderView onCreate={onCreate} {...data} />} />
      </DashboardSection>
    </Dashboard>
  );
}
