import { generateErrorToast } from '@components/toasts';
import { InitiativePlain } from '@g17eco/types/initiative';
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'reactstrap';
import config from '../../../../config';
import { useIntegrateMaterialityTrackerMutation } from '@api/initiatives';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { SettingStorage } from '@services/SettingStorage';
import { materialityTrackerRouteMatcher } from '@routes/appRootMatcher';
import { useAppDispatch } from '@reducers/index';
import { reloadInitiativeTree } from '@actions/index';
import { FeatureIntro } from '@g17eco/molecules/feature-intro';

interface Props {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  isOpen: boolean;
  toggle: () => void;
}

export const MaterialityTrackerIntegrationModal = ({ initiative, isOpen, toggle }: Props) => {
  const [integrateMT, { isLoading }] = useIntegrateMaterialityTrackerMutation();
  const dispatch = useAppDispatch();

  const handleSubmit = async () => {
    try {
      await integrateMT({ initiativeId: initiative._id }).unwrap();

      // Reload root organization to have new calculatedSubscriptions
      dispatch(reloadInitiativeTree());
      // Avoid redirect to preselected MT
      SettingStorage.setItem(materialityTrackerRouteMatcher.storageKey, initiative._id);

      window.open(generateUrl(ROUTES.MATERIALITY_TRACKER, { initiativeId: initiative._id }), '_blank');
    } catch (e) {
      generateErrorToast(e);
    } finally {
      toggle();
    }
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static' returnFocusAfterClose={false}>
      <ModalHeader toggle={toggle}>Connect Materiality Tracker</ModalHeader>
      <ModalBody>
        {isLoading ? <BlockingLoader /> : null}
        <p>
          Unlock the full potential of your sustainability journey by connecting Materiality Tracker to{' '}
          {initiative.name}:
        </p>
        <FeatureIntro
          icon='fa-ballot-check'
          header='Data collection'
          content='A detailed questionnaire will be deployed to compile the essential data. Metrics can be delegated to simplify collaboration.'
        />
        <FeatureIntro
          icon='fa-magnifying-glass-chart'
          header='Materiality Tracker analyses your data'
          content='Previous reports, new articles, journals, and other textual data sources.'
        />
        <FeatureIntro
          icon='fa-file-chart-column'
          header='Produce comprehensive report based on data analysis'
          content='A comprehensive summary of materiality findings, including key ESG topics by pillar and boundary, with definitions and a suggested 3-year action plan.'
        />
        <p>
          <a href={config.brochureURL.materialityTracker} target='_blank'>
            Click here
          </a>{' '}
          to learn more about Materiality Tracker.
        </p>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='primary' onClick={handleSubmit} disabled={isLoading}>
          Connect
        </Button>
      </ModalFooter>
    </Modal>
  );
};
