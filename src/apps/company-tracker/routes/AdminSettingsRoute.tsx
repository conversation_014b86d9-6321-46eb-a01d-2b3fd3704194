/*
 * Copyright (c) 2024. Word Wide Generation Ltd
 */

import AdminSettings, { AdminSettingKey, CardSettings } from '@features/admin-settings';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useAppSelector } from '@reducers/index';
import { isSingleOrg } from '@selectors/initiative';
import { isUserManagerByInitiativeId } from '@selectors/user';
import NotAuthorised from '@routes/not-authorised';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';

export const AdminSettingsRoute = () => {
  const history = useHistory();
  const match = useRouteMatch<{ initiativeId?: string }>();
  const { initiativeId } = match.params;

  const isManager = useAppSelector((state) =>
    isUserManagerByInitiativeId(state, initiativeId)
  );

  const limitReportingLevels = useAppSelector(FeaturePermissions.getLimitReportingLevels);
  const isSingleOrganisation = useAppSelector(isSingleOrg);

  if (!initiativeId || !isManager) {
    return <NotAuthorised />;
  }

  const cardSettings: CardSettings[] = [
    {
      key: AdminSettingKey.CompanySettings,
      handler: () => history.push(generateUrl(ROUTES.ACCOUNT_SETTINGS, { initiativeId })),
      icon: 'fal fa-sliders fa-3x',
      text: 'Account settings',
    },
    {
      key: AdminSettingKey.ManageUsers,
      handler: () => history.push(generateUrl(ROUTES.MANAGE_USERS, { initiativeId })),
      icon: 'fal fa-users-gear fa-3x',
      text: 'Manage users',
    },
    {
      key: AdminSettingKey.ManageMetrics,
      handler: () => history.push(generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId })),
      icon: 'fal fa-file-circle-question fa-3x',
      text: 'Custom metrics',
    },
    {
      key: AdminSettingKey.AdminDashboard,
      handler: () => history.push(generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId })),
      icon: 'fal fa-chart-line-up fa-3x',
      text: 'Admin dashboard',
      disabledText:
        limitReportingLevels === 1 && isSingleOrganisation
          ? 'Add additional subsidiaries to your organisation map to enable this feature'
          : '',
    },
    {
      key: AdminSettingKey.DocumentLibrary,
      handler: () => history.push(generateUrl(ROUTES.DOCUMENT_LIBRARY, { initiativeId })),
      icon: 'fal fa-folders fa-3x',
      text: 'Document library',
    },
    {
      key: AdminSettingKey.DataSharing,
      handler: () => history.push(generateUrl(ROUTES.DATA_SHARE_INITIATIVE, { initiativeId })),
      icon: 'fal fa-share-from-square fa-3x',
      text: 'Data sharing',
    },
    {
      key: AdminSettingKey.SystemLogs,
      handler: () => history.push(generateUrl(ROUTES.SYSTEM_LOG, { initiativeId })),
      icon: 'fal fa-file-waveform fa-3x',
      text: 'System logs',
    },
  ];

  cardSettings.push({
    key: AdminSettingKey.AppIntegrations,
    handler: () => history.push(generateUrl(ROUTES.INTEGRATIONS, { initiativeId })),
    icon: 'fal fa-circle-nodes fa-3x',
    text: (
      <>
        App Integrations
        <FeatureStability className={'ms-2'} feature={ExtraFeature.PartnerIntegration} />
      </>
    ),
  });

  return <AdminSettings cardSettings={cardSettings} />;
};
