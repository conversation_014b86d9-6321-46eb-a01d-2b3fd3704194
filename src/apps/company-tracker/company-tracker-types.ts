/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { RootInitiativeData } from '../../types/initiative';
import { CanUpgradeDetails } from '../../types/subscriptions';

export interface UpgradeProps {
  rootOrg: RootInitiativeData;
  toggle: () => void;
  upgradeDetails: CanUpgradeDetails;
  title?: string;
}

export enum CompanySettingsPages {
  Details = 'details',
  ReportSettings = 'report-settings',
  AccountManagement = 'account-management',
}
