/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState, useEffect } from 'react';
import { Tab<PERSON>ontent, TabPane, Button } from 'reactstrap';
import { FlickityWrapper } from '@components/flickity-wrapper';
import { isUserManager } from '@selectors/user';
import { getCalendarDate } from '@utils/date';
import { getCurrentInitiativeDocuments } from '@selectors/initiative';
import { connect } from 'react-redux';
import EditButton from '@components/button/EditButton';
import { bytesToHumanReadable } from '@utils/index';
import { getIconClass } from '@utils/files';
import UploadMediaAndDocumentModal from '../../upload-media-documents/UploadMediaAndDocumentModal';
import EditMediaAndDocument from '../../EditMediaAndDocument';
import G17EcoLogo from '../../../../../images/g17eco-icon.svg';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { DashboardSection } from '@components/dashboard';
import { RootState } from '@reducers/index';
import { InitiativeFile } from '@g17eco/types/initiative';
import './media-slider.scss';
import { Menu } from '@components/menu/Menu';

interface DocumentType {
  key: string;
  title: string;
  icon: string;
  renderAsTable: boolean;
  render?: (media: InitiativeFile, initiativeId: string) => JSX.Element;
  emptyText?: string;
}

interface ContentProps {
  currentDocuments: InitiativeFile[];
  currentDocType: DocumentType;
  initiativeId: string;
  editHandler: (media: InitiativeFile) => void;
  readOnly: boolean;
  isManager: boolean;
}

interface MediaSliderProps {
  readOnly: boolean;
  isManager: boolean;
  documents: {
    [key: string]: InitiativeFile[];
  };
  initiativeId: string;
}

const documentTypeIcon = (media: InitiativeFile) => (
  <div className='type-container'>
    <i className={`fas ${getIconClass(media)}`} />
  </div>
);

const renderVideo = (media: InitiativeFile) => {
  return (
    <div key={'video-' + media._id} className='homepageMediaTile'>
      <div className='card-img-top mb-5'>
        <video controls title={media.title}>
          <source src={media.url} type={media.metadata.mimetype} />
        </video>
        <div className='card-body p-2'>
          <h6 className='card-title'>{media.title}</h6>
          <p className='card-text'>{media.description}</p>
        </div>
      </div>
    </div>
  );
};

const renderPhoto = (media: InitiativeFile) => {
  return (
    <div key={'photo-' + media._id} className='homepageMediaTile'>
      <div className='img-thumbnail'>
        {documentTypeIcon(media)}
        <div style={{ backgroundImage: `url(${media.url})` }}>
          <div className='mediaTileText'>
            <h5>{media.title}</h5>
            <p>{media.description}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const documentTypes: DocumentType[] = [
  {
    key: 'photos',
    title: 'Photo',
    icon: 'fa-image',
    renderAsTable: false,
    render: renderPhoto,
  },
  {
    key: 'videos',
    title: 'Video',
    icon: 'fa-video',
    renderAsTable: false,
    render: renderVideo,
  },
  {
    key: 'files',
    title: 'File',
    emptyText: 'documents',
    icon: 'fa-file',
    renderAsTable: true,
  },
  {
    key: 'assurance',
    title: 'Assurance',
    emptyText: 'assurance files',
    icon: 'fa-award',
    renderAsTable: true,
  },
];

function getFilesContent(
  files: InitiativeFile[],
  editHandler: (media: InitiativeFile) => void,
  initiativeId: string,
  readOnly: boolean,
  isManager: boolean
) {
  const canEdit = !!isManager && !readOnly;

  const columns: ColumnDef<InitiativeFile>[] = [
    {
      header: 'Title',
      accessorFn: (file: InitiativeFile) => (file.title ? file.title : file.metadata.name),
    },
    {
      header: 'Type',
      meta: {
        cellProps: {
          className: 'text-center',
        },
      },
      accessorFn: (file: InitiativeFile) => file.metadata.extension.toUpperCase(),
    },
    {
      header: 'Size',
      meta: {
        cellProps: {
          className: 'text-right',
        },
      },
      accessorFn: (file: InitiativeFile) => bytesToHumanReadable(file.size),
    },
    {
      header: 'Date',
      accessorFn: (file: InitiativeFile) => getCalendarDate(file.created),
    },
    {
      header: ' ',
      id: 'actions',
      meta: {
        cellProps: {
          className: 'text-right px-0',
        },
      },
      enableSorting: false,
      cell: ({ row }) => {
        const file = row.original;
        return (
          <>
            <Button color='link' className='p-0 mx-1' onClick={() => window.open(file.url)}>
              <i className='fas fa-download' />
            </Button>
            {!!canEdit && file.ownerId === initiativeId && (
              <Button color='link' className='p-0 mx-1' onClick={() => editHandler(file)}>
                <i className='fas fa-pen' />
              </Button>
            )}
          </>
        );
      },
    },
  ];

  return <Table data={files} className='mt-3' columns={columns} />;
}

const options = {
  cellAlign: 'left',
  pageDots: true,
  draggable: true,
  groupCells: true,
};

function getNoDocuments(name: string, key: string) {
  return (
    <div className='alert alert-primary mt-4' key={key}>
      There are no {name || 'documents'} currently available
    </div>
  );
}

const getContent = (props: ContentProps) => {
  const { currentDocuments, currentDocType, initiativeId, editHandler, readOnly, isManager } = props;
  if (currentDocuments.length === 0) {
    if (currentDocType.key === 'photos') {
      const placeholder = (
        <div className='media-slider-placeholder p-5 border border-ThemeNeutralsLight mr-2'>
          <span>Placeholder</span>
          <img src={G17EcoLogo} alt='Placeholder' height={130} />
        </div>
      );
      return (
        <FlickityWrapper disableImagesLoaded={true} reloadOnUpdate={true} options={options}>
          {placeholder}
          {placeholder}
          {placeholder}
        </FlickityWrapper>
      );
    }

    return getNoDocuments(currentDocType.emptyText || currentDocType.key, currentDocType.key);
  }

  if (currentDocType.renderAsTable) {
    return getFilesContent(currentDocuments.slice(0, 5), editHandler, initiativeId, readOnly, isManager);
  }

  const canEdit = !!isManager && !readOnly;
  return (
    <FlickityWrapper disableImagesLoaded={true} reloadOnUpdate={true} options={options}>
      {currentDocuments.slice(0, 5).map((doc) => {
        return (
          <div key={doc._id}>
            {currentDocType.render?.(doc, initiativeId)}
            {canEdit ? <EditButton outline={false} onClick={() => editHandler(doc)} className='removeButton' /> : null}
          </div>
        );
      })}
    </FlickityWrapper>
  );
};

export const MediaSlider = (props: MediaSliderProps) => {
  const { readOnly, isManager, documents, initiativeId } = props;

  const [activeTab, setActiveTab] = useState('images');
  useEffect(() => {
    const defaultActiveTab = documentTypes.find(
      (docType) => documents[docType.key] && documents[docType.key].length > 0
    );
    if (defaultActiveTab) {
      setActiveTab(defaultActiveTab.key);
    }
  }, [documents]);
  const navChange = (tabKey: string) => {
    setActiveTab(tabKey);
  };

  let currentDocType = documentTypes.find((docType) => docType.key === activeTab);
  if (!currentDocType) {
    currentDocType = documentTypes[0];
  }
  const currentDocuments = documents[currentDocType.key];
  const canEdit = !!isManager && !readOnly;
  const [displayUploadModal, toggleUploadModal] = useState(false);
  const [displayEditModal, toggleEditModal] = useState(false);
  const [mediaDetails, setMediaDetails] = useState<InitiativeFile | undefined>();

  const editHandler = (media: InitiativeFile) => {
    toggleEditModal(true);
    setMediaDetails(media);
  };
  const contentParams: ContentProps = {
    currentDocuments,
    currentDocType,
    initiativeId,
    editHandler,
    readOnly,
    isManager,
  };

  if (readOnly && documents.all.length === 0) {
    return <></>;
  }

  return (
    <DashboardSection
      title='Media and Documents'
      className='hidden-print flickity-wrapper g-0'
      padding={2}
      buttons={canEdit ? [<EditButton key='edit-media-document' onClick={(e) => toggleUploadModal(true)} />] : []}
    >
      <div className='media-container'>
        <div className='tabbed-header mb-2'>
          <Menu
            items={documentTypes
              .filter(({ key }) => documents[key])
              .map(({ key, title, icon }) => {
                const length = documents[key].length;
                const label = `${title}${length === 1 ? '' : 's'}`;
                return {
                  active: activeTab === key,
                  onClick: () => navChange(key),
                  label: (
                    <>
                      {icon ? <i className={`fas ${icon} mr-2`} /> : length}
                      {` ${label}`}
                    </>
                  ),
                };
              })}
          />
        </div>
        <TabContent activeTab={activeTab}>
          <TabPane key={currentDocType.key} tabId={activeTab}>
            {getContent(contentParams)}
          </TabPane>
        </TabContent>
      </div>
      {displayUploadModal ? (
        <UploadMediaAndDocumentModal isOpen={true} toggle={(e) => toggleUploadModal(!displayUploadModal)} />
      ) : (
        <></>
      )}
      {displayEditModal ? (
        <EditMediaAndDocument
          mediaDetails={mediaDetails}
          isOpen={true}
          toggle={() => toggleEditModal(!displayEditModal)}
        />
      ) : (
        <></>
      )}
    </DashboardSection>
  );
};

const mapStateToProps = (state: RootState) => ({
  isManager: isUserManager(state),
  documents: getCurrentInitiativeDocuments(state),
});

export default connect(mapStateToProps)(MediaSlider);
