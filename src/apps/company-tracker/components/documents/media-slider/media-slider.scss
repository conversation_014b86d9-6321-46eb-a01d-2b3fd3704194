/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import '../../../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';

$wrapperBorder: 1px solid #dee2e6;

.media-container {
  .nav-item button {
    width: 11rem;
  }
  i {
    border-radius: 100%;
    display: inline-block;
    line-height: 0 !important;
    transition: 0.5s;
  }
  .tab-content {
    .removeButton {
      position: absolute;
      top: 10px;
      right: 20px;
    }

    .flickity-viewport {
      min-height: 100px;
      .media-slider-placeholder {
        width: 200px;
        border-radius: $borderRadius;
        span {
          font-weight: bold;
          font-size: 30px;
          opacity: 0.2;
          text-align: center;
          top: 33%;
          left: 8%;
          width: 200px;
          height: 100px;
          position: absolute;
          -moz-transform: rotate(-45deg);
          -o-transform: rotate(-45deg);
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
        }
        img {
          opacity: 0.05;
          filter: grayscale(100%);
        }
      }
    }

    .homepageMediaTile {
      margin-right: 10px;
      width: 200px !important;
      height: 200px !important;

      .img-thumbnail {
        &.file {
          text-align: center;

          i {
            font-size: 12rem;
            margin: 1rem 2rem 1rem 0;
          }
          .fa-file-pdf {
            color: red;
          }
          .fa-file {
            color: var(--theme-BgDisabled);
          }
        }
        > div {
          width: 100%;
          padding-bottom: 100%;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;

          .mediaTileText {
            position: absolute;
            overflow: ellipsis;
            top: 5px;
            bottom: 5px;
            left: 5px;
            right: 5px;
            padding: 20px 20px;
            opacity: 0;
            background-color: rgba(255, 255, 255, 0.6);
            color: black;
            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }

  .type-container {
    position: absolute;
    > i {
      position: relative;
      top: 10px;
      left: 10px;
      color: #ffffff;
      text-shadow: 0 0 1px black;
      font-size: 1rem;
    }
  }
}
