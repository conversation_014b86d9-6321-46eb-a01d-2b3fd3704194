/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { ChangeEvent, useState } from 'react';
import { useSelector } from 'react-redux';
import { Modal, ModalBody, ModalHeader, Button, Form, FormGroup, Label, Input } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { FileDropZone } from '@components/files/FileDropZone';
import G17Client, { formDataConfig } from '@services/G17Client';
import { reloadInitiative } from '@actions/initiative';
import { SubmitButton } from '@components/button/SubmitButton';
import { RootState, useAppDispatch } from '@reducers/index';
import { withProfile } from '@utils/files';
import { BasicAlert } from '@g17eco/molecules/alert';

interface UploadMediaAndDocumentModalParams {
  toggle: (e: any) => void;
  isOpen: boolean | undefined;
}

interface FormState {
  title: string;
  description: string;
  files: any[];
}

const renderFile = function (file: any) {
  return (
    <div>
      {file.preview && <img src={file.preview} alt={file.name} className='imgUpload' />}
      <p className='fileUpload'>{file.name}</p>
    </div>
  );
};

const UploadMediaAndDocumentModal = (props: UploadMediaAndDocumentModalParams) => {
  const [form, setForm] = useState<FormState>({
    title: '',
    description: '',
    files: [],
  });
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [fileError, setFileError] = useState('');

  const initiativeId = useSelector((state: RootState) => state.initiative.data?._id);
  const dispatch = useAppDispatch();

  const updateForm = (e: ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value.trim() });
  };

  const addFiles = (files: File[]) => {
    setForm({
      ...form,
      files: files.map((file) => Object.assign(file, { preview: URL.createObjectURL(file) })),
    });
  };

  const handleSubmit = async (_: React.MouseEvent<any>) => {
    if (form.files.length === 0) {
      setFileError('There are no files to be uploaded');
      return;
    }
    setSaving(true);
    return G17Client.post(`initiatives/${initiativeId}/documents`, withProfile(form), formDataConfig)
      .then(() => {
        setSaving(false);
        dispatch(reloadInitiative());
        props.toggle(false);
      })
      .catch((e) => {
        setSaving(false);
        setMessage(e.message);
      });
  };

  return (
    <Modal isOpen={props.isOpen} toggle={props.toggle} backdrop='static' className='upload-media-documents-modal'>
      <ModalHeader toggle={props.toggle}>Upload Documents</ModalHeader>
      <ModalBody>
        {saving && <Loader />}
        {message && <div className='alert alert-danger'>{message}</div>}
        <Form>
          <FormGroup>
            <FileDropZone multiple={false} onDrop={addFiles} />
            <BasicAlert type={'danger'}>{fileError}</BasicAlert>
            <div className='mt-3'>
              {form.files.map((file) => (
                <span key={file.name}> {renderFile(file)} </span>
              ))}
            </div>
          </FormGroup>
          <FormGroup>
            <Label className='strong' for='title'>
              Title
            </Label>
            <Input id='title' type='text' name='title' onChange={updateForm} required />
          </FormGroup>
          <FormGroup>
            <Label className='strong' for='description'>
              Description
            </Label>
            <Input id='description' type='text' name='description' onChange={updateForm} />
          </FormGroup>
          <div className='mt-4 text-right'>
            <Button color='link-secondary' className='me-3' onClick={props.toggle}>
              Cancel
            </Button>
            <SubmitButton onClick={handleSubmit}>Submit</SubmitButton>
          </div>
        </Form>
      </ModalBody>
    </Modal>
  );
};

export default UploadMediaAndDocumentModal;
