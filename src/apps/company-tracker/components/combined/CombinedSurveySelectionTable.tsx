/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { Action, State } from '@table-library/react-table-library/common';
import { Body, Cell, Header, HeaderCell, HeaderRow, Row, Table } from '@table-library/react-table-library/table';
import { CellTree, TreeExpandClickTypes, useTree } from '@table-library/react-table-library/tree';
import { CellSelect, SelectTypes as TreeSelectTypes, useRowSelect } from '@table-library/react-table-library/select';
import { useTheme } from '@table-library/react-table-library/theme';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { SURVEY } from '@constants/terminology';
import { Filters, getSurveyFilters, SurveyFilters } from '@components/downloads/util/filters';
import { StringIdTableNode, TableData } from '@components/downloads/util/treeNavigation';
import { findNodesByIds, getExpandedIds, getYears, SurveyNode } from './combined-node-tree';
import { CustomReportSubsidiarySurvey } from '@g17eco/types/custom-report';


interface Props {
  handleSelectedNodes: (selectedNodes: SurveyNode[])=> void;
  surveys: CustomReportSubsidiarySurvey[];
  // These are currently selected, but might not be valid on submission
  selectedNodeIds: string[];
  // These are the valid nodes, that will be used to extract surveyIds
  validNodeIds: string[];
  filters: Filters;
  enabledFilters?: (keyof Filters)[];
  toggleFilter: (filter: keyof Filters, options: string[]) => void;
  hideCount?: boolean;
  tableData: TableData;
}
const defaultFilters = [
  SurveyFilters.Month,
  SurveyFilters.Year,
  SurveyFilters.Period,
  SurveyFilters.SurveyType,
];

export const CombinedSurveySelectionTable = (props: Props) => {
  const {
    selectedNodeIds,
    validNodeIds,
    handleSelectedNodes,
    filters,
    tableData,
    toggleFilter,
    enabledFilters = defaultFilters,
    surveys,
    hideCount,
  } = props;

  const expandedIds = useMemo(() => {
    return getExpandedIds(selectedNodeIds, tableData.nodes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableData.nodes]);

  const theme = useTheme({
    Table: `
      height: unset;
      max-height: 100%;
      --data-table-library_grid-template-columns: 50% repeat(1, minmax(0, 2fr)) repeat(1, minmax(0, 1fr));
    `,
  });

  const tree = useTree(tableData,
    { state: { ids: expandedIds } },
    {
      treeIcon: {
        margin: '4px',
        iconDefault: <i className='fa mx-2' />,
        iconRight: <i className='fa fa-caret-right text-ThemeAccentExtradark mx-2' />,
        iconDown: <i className='fa fa-caret-down text-ThemeAccentExtradark mx-2' />,
      },
      clickType: TreeExpandClickTypes.RowClick,
    }
  );

  const select = useRowSelect(tableData,
    {
      state: { ids: selectedNodeIds },
      onChange: function (_action: Action, state: State) {
        const selectedNodes = findNodesByIds(tableData, state.ids ?? []);
        return handleSelectedNodes(selectedNodes);
      },
    },
    {
      rowSelect: TreeSelectTypes.MultiSelect,
      buttonSelect: TreeSelectTypes.MultiSelect,
    }
  );

  const yearOptions = useMemo(() => {
    return getYears(surveys).map((year) => ({ label: year, value: year }));
  }, [surveys]);

  const surveyFilters = getSurveyFilters({ yearOptions });

  return (
    <>
      <div className='d-flex align-items-center gap-2'>
        {enabledFilters.map((filter) => {
          const { label, options } = surveyFilters[filter];
          return (
            <SelectFactory
              selectType={SelectTypes.MultipleSelect}
              className='flex-fill'
              key={filter}
              placeholder={label}
              options={options}
              values={filters[filter]}
              onChange={(op) => toggleFilter(filter, op)}
              isMenuPortalTargetBody
              isFlexibleSize
            />
          )
        })}
        {hideCount ? null : <div className='ms-3'>{`${validNodeIds.length} ${SURVEY.PLURAL} selected`}</div>}
      </div>
      <div
        className='mt-2 collapsible-table border border-ThemeBorderDefault border-1 rounded-1'
        style={{ height: '400px', }}>
        <Table
          data={tableData}
          tree={tree}
          select={select}
          theme={theme}
          layout={{ fixedHeader: true, custom: true }}>
          {(tableList: StringIdTableNode[]) => {
            if (!tableList.length) {
              return (
                <Header>
                  <HeaderRow>
                    <HeaderCell gridColumnStart={1} gridColumnEnd={4}>
                      <BasicAlert className='mt-3' type='warning'>
                        No {SURVEY.PLURAL} available for the selected subsidiaries
                      </BasicAlert>
                    </HeaderCell>
                  </HeaderRow>
                </Header>
              );
            }

            return (
              <>
                <Header>
                  <HeaderRow>
                    <HeaderCell resize={true}>{SURVEY.CAPITALIZED_SINGULAR}</HeaderCell>
                    <HeaderCell>Count</HeaderCell>
                    <HeaderCell />
                  </HeaderRow>
                </Header>

                <Body>
                  {tableList.map((item) => (
                    <Row key={item.id} item={item}>
                      <CellTree item={item}>
                        <SimpleTooltip id={item.id} text={item.name}>
                          {item.name}
                        </SimpleTooltip>
                      </CellTree>
                      <Cell>{item.surveyCount}</Cell>
                      {Array.isArray(item.nodes) ? <Cell /> : <CellSelect item={item} />}
                    </Row>
                  ))}
                </Body>
              </>
            );
          }}
        </Table>
      </div>
    </>
  );
};
