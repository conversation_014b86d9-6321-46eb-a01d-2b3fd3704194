/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Input } from 'reactstrap';
import { isUserManager } from '@selectors/user';
import { getPeriodOptions, getYear } from '@utils/date';
import { SubmitButton } from '@components/button/SubmitButton';
import { Loader } from '@g17eco/atoms/loader';
import { useAppSelector } from '@reducers/index';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { UpgradeRequired } from '@routes/upgrade-required';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SubsidiarySelectionTable } from '@components/downloads/partials/SubsidiarySelectionTable';
import { CombinedSurveySelectionTable } from './CombinedSurveySelectionTable';
import {
  getDefaultScope,
  getModulesFromScope,
  getScopeModules,
  getScopesDifferent,
  isScopeEmpty,
  mergeSurveysScopes
} from '@utils/survey-scope';
import {
  useGetMetricGroupsForCustomReportQuery,
  useGetSubsidiariesForCustomReportQuery,
  useGetSurveysByInitiativeIdsForCustomReportQuery
} from '@api/custom-reports';
import { compareStringArrays } from '@utils/string';
import { mergeScopes } from '@utils/dataShare';
import {
  buildCustomTree,
  filterBySurveyFilters,
  findNodesByIds,
  getNewSurveyFilters,
  SurveyNode
} from './combined-node-tree';
import { AggregatedSurveyFilters, AllowedAggregatedUtrvStatus, SurveyInitiative, SurveyType } from '@g17eco/types/survey';
import { skipToken } from '@reduxjs/toolkit/query';
import { Filters } from '@components/downloads/util/filters';
import { Scope, SurveyActionData } from '@models/surveyData';
import { useCreateAggregatedSurveyMutation, useUpdateAggregatedSurveyMutation } from '@api/aggregated-surveys';
import { aggregatedUtrvStatusOptions } from '@utils/survey';

interface Props {
  initiativeId: string;
  survey?: SurveyActionData;
  onCancel: () => void;
  onSubmit: (survey: Pick<SurveyInitiative, '_id'>) => void;
}

type ExtendedAggregatedSurveyFilters = Filters & AggregatedSurveyFilters;

interface Form {
  name: string;
  period: DataPeriods | undefined;
  initiativeIds: string[];
  scope: Scope;
  surveyFilters: ExtendedAggregatedSurveyFilters;
}

const initialSurveyFilter: ExtendedAggregatedSurveyFilters = {
  period: [],
  surveyType: [SurveyType.Default, SurveyType.Aggregation],
  year: [],
  month: [],
  utrv: undefined,
}

const getInitialForm = (survey?: SurveyActionData) => {
  if (!survey) {
    return {
      name: '',
      period: undefined,
      initiativeIds: [],
      surveyFilters: initialSurveyFilter,
      scope: getDefaultScope(),
    };
  }

  return {
    name: survey.name ?? '',
    period: survey.period ?? DataPeriods.Yearly,
    initiativeIds: Array.from(new Set((survey.aggregatedSurveys ?? []).map(({ initiativeId }) => initiativeId))),
    surveyFilters: { ...initialSurveyFilter, ...survey.filters },
    scope: survey.scope ?? getDefaultScope(),
  };
};

const getInitialNodeIds = (survey?: SurveyActionData) => {
  if (!survey || !survey.aggregatedSurveys) {
    return [];
  }
  return survey.aggregatedSurveys.map(
    ({ _id, effectiveDate, type, period }) => `${getYear(effectiveDate, true)}-${type}-${period}-${_id}`
  );
};

export const CombinedReportAdvancedForm = (props: Props) => {

  const { initiativeId, onCancel, onSubmit, survey } = props;

  const [message, setMessage] = useState('');
  const isManager = useAppSelector(isUserManager);
  const canAccessCombinedReport = useAppSelector(FeaturePermissions.canAccessCombinedReport);

  const [nodeIds, setNodeIds] = useState<string[]>(() => getInitialNodeIds(survey));
  const msgRef = useRef<HTMLDivElement>(null);

  const [form, setForm] = useState<Form>(() => getInitialForm(survey));

  const { data: metricGroups = [] } = useGetMetricGroupsForCustomReportQuery(initiativeId);
  const { data: subsidiaryList, isLoading: isSubsidiaryLoading } = useGetSubsidiariesForCustomReportQuery(initiativeId);
  const { data: surveys, isLoading: isSurveyLoading } = useGetSurveysByInitiativeIdsForCustomReportQuery(
    subsidiaryList?.length ? {
      initiativeId,
      initiativeIds: subsidiaryList.map(({ _id }) => _id),
    } : skipToken
  );

  const [createAggregatedSurvey, { isLoading: isCreating }] = useCreateAggregatedSurveyMutation();
  const [updateAggregatedSurvey, { isLoading: isUpdating }] = useUpdateAggregatedSurveyMutation();

  const { tableData, surveyList } = useMemo(() => {
    const filteredSurveys = filterBySurveyFilters(surveys ?? [], form.surveyFilters, form.initiativeIds);
    return {
      tableData: buildCustomTree(filteredSurveys, subsidiaryList ?? [], form.surveyFilters),
      surveyList: filteredSurveys,
    }
  }, [surveys, form.surveyFilters, form.initiativeIds, subsidiaryList]);

  const validNodes = useMemo(() => {
    return findNodesByIds(tableData, nodeIds);
  }, [nodeIds, tableData]);

  const getScope = useCallback((surveyFilters: Omit<Form['surveyFilters'], 'utrv'>) => {
    const filteredSurveys = filterBySurveyFilters(surveys ?? [], surveyFilters, form.initiativeIds);
    return mergeSurveysScopes(filteredSurveys, { parentGroupsOnly: true });
  }, [form.initiativeIds, surveys]);

  const handleSelectedNodes = useCallback((selectedNodes: SurveyNode[] = []) => {
    const newSurveyFilters = getNewSurveyFilters(selectedNodes);
    setNodeIds(selectedNodes.map((node) => node.id));

    setForm((f) => {
      const originalScope = getScope(f.surveyFilters);
      const unSelectedScope = getScopesDifferent(f.scope, originalScope);
      const newScope = getScope(newSurveyFilters);
      const finalScope = getScopesDifferent(unSelectedScope, newScope);
      // If everything is empty then try to select all as default option
      const scope = isScopeEmpty(finalScope) ? originalScope : finalScope;

      return ({
        ...f,
        scope: scope,
      });
    });

  }, [getScope]);

  const onMessage = (message: string) => {
    setMessage(message);
    if (msgRef.current) {
      msgRef.current.scrollIntoView({ block: 'center', behavior: 'smooth' });
    }
  }

  const validateForm = () => {
    const { name, period, scope, surveyFilters } = form;
    const surveyIds = validNodes.map((node) => node.surveyId);
    if (surveyIds.length === 0) {
      onMessage(`Please select ${SURVEY.PLURAL} to combine`);
      return;
    }

    if (!selectedPeriod) {
      onMessage('Please select reporting period');
      return;
    }

    if (name.length === 0) {
      onMessage('Please provide a name');
      return;
    }

    if (!surveyFilters.utrv) {
      onMessage('Please choose utrv status used for aggregation');
      return;
    }

    setMessage('');
    return { name, period, scope, surveyIds, filters: { utrv: surveyFilters.utrv } };
  };

  const handleSave = async () => {
    const validForm = validateForm();
    if (!validForm) {
      return;
    }

    if (survey) {
      updateAggregatedSurvey({ initiativeId, surveyId: survey._id, ...validForm })
        .unwrap()
        .then(() => {
          onSubmit(survey);
        })
        .catch((e) => {
          onMessage(e.message);
        });
      return;
    }

    createAggregatedSurvey({ initiativeId, ...validForm })
      .unwrap()
      .then((result) => {
        onSubmit(result);
      })
      .catch((e) => {
        onMessage(e.message);
      });
  }

  if (!canAccessCombinedReport) {
    return <UpgradeRequired />;
  }

  if (!isManager) {
    return <BasicAlert type={'warning'}>
      Please contact your manager to create a combined report
    </BasicAlert>
  }

  const periodOptions = getPeriodOptions();
  const selectedPeriod = periodOptions.find(p => p.value === form.period);

  const scopeModules = getScopeModules({ scope: getScope(form.surveyFilters), metricGroups })
  const moduleOptions: Option<string>[] = scopeModules.map((item) => ({
    value: item.code,
    label: item.name,
    searchString: `${item.name} ${item.code}`
  }));

  const moduleCodes = scopeModules.map((s) => s.code);
  const modulesFromScope = getModulesFromScope(form.scope);
  const moduleValues =  modulesFromScope.filter((m) => moduleCodes.includes(m));

  const handleChangeModule = (values: string[]) => {
    // This is called when the user click on selection so the action is add or remove
    // So only one of the added or removed will have elements.
    const { added, removed } = compareStringArrays({ oldArray: moduleValues, newArray: values });

    const isAdding = added.length > 0;
    const action = isAdding ? 'add' : 'remove';
    const modules = isAdding ? added : removed;

    const toChangeScopeModules = scopeModules
      .filter((s) => modules.includes(s.code))
      .map((s) => ({ code: s.code, scopeType: s.type }));

    setForm({
      ...form,
      scope: mergeScopes(form.scope, toChangeScopeModules, action),
    });
  };

  const toggleFilter = (key: keyof Filters, value: string[]) => {
    setForm((state) => ({
      ...state,
      surveyFilters: { ...state.surveyFilters, [key]: value }
    }));
  };

  const isSaving = isCreating || isUpdating;

  const submitDisabled = !isManager|| isSaving || isSubsidiaryLoading || validNodes.length === 0;

  return (
    <div className={'combine-report-advanced-form'} style={{ opacity: isSubsidiaryLoading ? 0.5 : 1 }}>
      <div ref={msgRef}>
        <BasicAlert type={'danger'}>{message}</BasicAlert>
      </div>
      <div>
        <p>
          Combined {SURVEY.PLURAL} create a single exportable {SURVEY.SINGULAR} made up from the aggregated data
          selected from the subsidiaries and date ranges below. Please note the combined {SURVEY.SINGULAR}
          will use all instances in the aggregation process, including subsidiary, date range, period and{' '}
          {SURVEY.SINGULAR} type.
        </p>
      </div>
      <div>
        {isSaving ? <Loader /> : null}
        <div className='mt-4 fw-bold mb-2'>{SURVEY.CAPITALIZED_SINGULAR} name*</div>
        <Input
          value={form.name}
          onChange={(e) => {
            setForm({ ...form, name: e.target.value });
            setMessage('');
          }}
          disabled={isSaving}
          type={'text'}
          name={'report-name'}
          placeholder={`Combined ${SURVEY.SINGULAR} name`}
          data-testid='combined-survey-name-input'
        />

        <div className='mt-4 fw-bold mb-2'>{SURVEY.CAPITALIZED_ADJECTIVE} period*</div>
        <SelectFactory
          selectType={SelectTypes.SingleSelect}
          placeholder={'Monthly, Quarterly, Annual'}
          isDisabled={isSaving}
          className='w-100'
          menuPortalTarget={document.body}
          styles={{ menuPortal: (base) => ({ ...base, zIndex: 9999 }) }}
          onChange={(option) => (option ? setForm({ ...form, period: option.value }) : undefined)}
          value={selectedPeriod}
          options={periodOptions}
        />

        <div className='mt-4 fw-bold mb-2'>{QUESTION.CAPITALIZED_SINGULAR} status</div>
        <SelectFactory<AllowedAggregatedUtrvStatus>
          className='w-100 mb-5'
          isDisabled={isSaving}
          selectType={SelectTypes.SingleSelect}
          options={aggregatedUtrvStatusOptions}
          onChange={(option) =>
            option ? setForm({ ...form, surveyFilters: { ...form.surveyFilters, utrv: option.value } }) : undefined
          }
          isSearchable={false}
          value={aggregatedUtrvStatusOptions.find((option) => option.value === form.surveyFilters.utrv)}
        />

        <div className={'mb-3'}>
          <span className='fw-bold'>Select subsidiaries to combine</span>
          <div>
            <p className={'mb-2'}>
              Combine only {SURVEY.CAPITALIZED_PLURAL} from the following selected subsidiaries. Data from any
              subsidiary not selected will not be included in the combined {SURVEY.SINGULAR}.
            </p>
          </div>
          <LoadingPlaceholder isLoading={isSubsidiaryLoading} height={400}>
            <SubsidiarySelectionTable
              className={'border border-ThemeBorderDefault border-1 rounded-1'}
              initiativeId={initiativeId}
              selectedIds={form.initiativeIds}
              handleSetSelectedIds={(ids) => {
                setForm({ ...form, initiativeIds: ids });
              }}
              hideCount={true}
            />
          </LoadingPlaceholder>
        </div>

        <div className={'mb-3'}>
          <span className='fw-bold'>Combined report dates to combine</span>
          <div>
            <p className={'mb-2'}>
              Use only the following date, period and {SURVEY.SINGULAR} types when creating the combined{' '}
              {SURVEY.SINGULAR}.
            </p>
          </div>
          {form.initiativeIds.length ? (
            <LoadingPlaceholder isLoading={isSurveyLoading} height={400}>
              <CombinedSurveySelectionTable
                key={initiativeId}
                tableData={tableData}
                surveys={surveyList}
                filters={form.surveyFilters}
                toggleFilter={toggleFilter}
                handleSelectedNodes={handleSelectedNodes}
                selectedNodeIds={nodeIds}
                validNodeIds={validNodes.map((node) => node.id)}
              />
            </LoadingPlaceholder>
          ) : (
            <BasicAlert type={'warning'}>Please select subsidiaries above</BasicAlert>
          )}
        </div>

        <div className={'mb-5'}>
          <p className='fw-bold'>Export selected modules only</p>
          <SelectFactory
            selectType={SelectTypes.MultipleSelect}
            className='w-100 mt-3'
            options={moduleOptions}
            onChange={handleChangeModule}
            values={moduleValues}
            placeholder={`Export only selected ${PACK.PLURAL}`}
            menuPlacement='top'
          />
        </div>
      </div>
      <div className='d-flex justify-content-end'>
        <Button color='link-secondary' className={'mr-3'} onClick={() => onCancel()}>
          Cancel
        </Button>
        <SubmitButton
          color='primary'
          disabled={submitDisabled}
          onClick={handleSave}
          data-testid='combined-survey-submit-btn'
        >
          {survey ? 'Update' : 'Create'}
        </SubmitButton>
      </div>
    </div>
  );
}
