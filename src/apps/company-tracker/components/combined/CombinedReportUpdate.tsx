/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { SurveySummary } from '@g17eco/types/survey';
import { Button } from 'reactstrap';
import G17Client from '@services/G17Client';
import LoadingAnimation from '@components/loading-animation';
import { SubmitButton } from '@components/button/SubmitButton';
import { formatDate } from '@utils/date';
import { BasicAlert } from '@g17eco/molecules/alert';

interface Props {
  survey: SurveySummary;
  onCancel: () => void;
  onUpdate: () => void;
}

export const CombinedReportUpdate = (props: Props) => {

  const { survey, onCancel, onUpdate } = props;

  const [message, setMessage] = useState('')
  const [saving, setSaving] = useState(false)

  const triggerUpdate = async () => {
    setSaving(true)
    setMessage('')
    return G17Client.patch(`/initiatives/${survey.initiativeId}/aggregated-survey/${survey._id}/aggregate`)
      .then(() => {
        setSaving(false);
        onUpdate()
      })
      .catch(e => {
        setMessage(e.message)
        setSaving(false)
      })
  };

  const date = formatDate(survey.effectiveDate, 'MMMM YYYY', true);
  const name = `${date} - ${survey.name || survey.initiative?.name || ''}`

  return (
    <div>
      <BasicAlert type={'danger'}>{message}</BasicAlert>
      <div className={'p-3 mb-3'}>
        {saving ? <>
            <LoadingAnimation style={{ height: '35px' }} />
            <p className={'mt-2'}>Updating your combined report data...</p>
          </> :
          <p className={'mb-4'}>
            <strong>Warning</strong>: By clicking update, the data in your combined
            report <strong>{name}</strong> will be updated. If you do
            not wish to update your combined report answers, or want to check your
            answers before updating please select cancel.
          </p>
        }
      </div>
      <div className={'text-right'}>
        <Button className='mr-2' color='link-secondary' disabled={saving} onClick={() => onCancel()}>Cancel</Button>
        <SubmitButton onClick={triggerUpdate}>
          Update
        </SubmitButton>
      </div>
    </div>
  )
}
