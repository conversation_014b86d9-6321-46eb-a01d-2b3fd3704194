/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { CustomReportSubsidiarySurvey, SURVEY_TYPE_LABELS, SurveyFilter } from '@g17eco/types/custom-report';
import { Filters } from '@components/downloads/util/filters';
import { StringIdTableNode, TableData } from '@components/downloads/util/treeNavigation';
import { SurveyType } from '@g17eco/types/survey';
import { DATE, formatDateUTC } from '@utils/date';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { getPeriodName } from '@utils/universalTracker';
import { naturalSort } from '@utils/index';
import { Data } from '@table-library/react-table-library/table';
import { InitiativePlain } from '@g17eco/types/initiative';


export interface SurveyNode extends StringIdTableNode {
  surveyId: string;
  name: string;
  period: DataPeriods;
  effectiveDate: string;
  surveyType: SurveyType;
}

export interface PeriodNode extends StringIdTableNode {
  name: string;
  period: DataPeriods;
  surveyType: SurveyType;
}

// Ensure we have consistent formatting
const formatYear = (effectiveDate: string) => formatDateUTC(effectiveDate, DATE.YEAR_ONLY);
const formatMonth = (effectiveDate: string) => formatDateUTC(effectiveDate, DATE.MONTH_NUMBER);


const generateSurveyNodeName = (survey: CustomReportSubsidiarySurvey, initiativeMap: Map<string, Pick<InitiativePlain, '_id' | 'name' | 'parentId'>>) => {
  const monthYear = formatDateUTC(survey.effectiveDate, DATE.MONTH_YEAR);

  const initiative = initiativeMap.get(survey.initiativeId)
  const names = [initiative?.name ?? 'unknown'];

  let count = 0
  let parentId = initiative?.parentId;
  while (parentId && count < 5) {
    const parent = initiativeMap.get(parentId);
    if (!parent) {
      break;
    }
    names.push(parent.name);
    parentId = parent.parentId;
    count++;
  }

  return `${monthYear} - ${names.join(' / ')}`;
}

const initMaps = (initiatives: Pick<InitiativePlain, '_id' | 'name' | 'parentId'>[], list: CustomReportSubsidiarySurvey[]) => {
  // Structure Year -> SurveyType -> Period -> Survey
  const yearMap = new Map<string, Map<SurveyType, Map<DataPeriods, CustomReportSubsidiarySurvey[]>>>();

  const initiativeMap = new Map(initiatives.map((initiative) => [initiative._id, initiative]));

  list.forEach((survey) => {
    const { period, type = SurveyType.Default } = survey;

    const year = formatYear(survey.effectiveDate);
    if (!yearMap.has(year)) {
      yearMap.set(year, new Map());
    }
    const surveyTypeMap = yearMap.get(year);
    if (!surveyTypeMap) {
      return;
    }

    if (!surveyTypeMap.has(type)) {
      surveyTypeMap.set(type, new Map());
    }

    const periodMap = surveyTypeMap.get(type);
    if (!periodMap) return;

    if (!periodMap.has(period)) {
      periodMap.set(period, []);
    }

    const periodList = periodMap.get(period);
    if (!periodList) return;

    periodList.push(survey);
  });
  return { yearMap, initiativeMap };
}

export const buildCustomTree = (
  list: CustomReportSubsidiarySurvey[],
  initiatives: Pick<InitiativePlain, '_id' | 'name' | 'parentId'>[],
  filters: Filters
): TableData => {

  const { yearMap, initiativeMap } = initMaps(initiatives, list);

  const data: TableData = {
    nodes: [],
  };

  yearMap.forEach((surveyTypeGroup, year) => {
    const yearNodes: StringIdTableNode[] = [];

    if (filters.year.length && !filters.year.includes(year)) {
      return;
    }

    surveyTypeGroup.forEach((monthYearGroups, surveyType) => {
      if (filters.surveyType.length && !filters.surveyType.includes(surveyType)) {
        return;
      }
      const periodNodes: PeriodNode[] = [];
      monthYearGroups.forEach((monthYearList, period) => {

        if (filters.period.length && !filters.period.includes(period)) {
          return;
        }
        const groupId = `${year}-${surveyType}-${period}`;
        const periodName = getPeriodName(period, false);
        periodNodes.push({
          id: groupId,
          name: periodName,
          period: period,
          surveyType,
          surveyCount: monthYearList.length,
          nodes: monthYearList.reduce((acc, survey) => {
            if (filters.month.length && !filters.month.includes(formatMonth(survey.effectiveDate))) {
              return acc;
            }

            acc.push({
              id: `${groupId}-${survey._id}`,
              name: generateSurveyNodeName(survey, initiativeMap),
              surveyId: survey._id,
              effectiveDate: survey.effectiveDate,
              period: survey.period,
              periodName,
              surveyType,
            } as SurveyNode);

            return acc;
          }, [] as SurveyNode[]),
        });
      });

      if (periodNodes.length === 0) {
        return;
      }

      yearNodes.push({
        id: `${year}-${surveyType}`,
        name: SURVEY_TYPE_LABELS[surveyType],
        nodes: periodNodes,
        surveyCount: periodNodes.reduce((acc, cur) => acc + cur.surveyCount, 0),
      });
    });

    if (yearNodes.length === 0) {
      return;
    }

    data.nodes.push({
      id: year,
      name: year,
      nodes: yearNodes,
      surveyCount: yearNodes.reduce((acc, cur) => acc + cur.surveyCount, 0),
    });
  });

  // Sort DESC by year
  data.nodes.sort((a, b) => naturalSort(b.id, a.id));

  return data;
};

const isSurveyNode = (node: StringIdTableNode): node is SurveyNode => 'surveyId' in node;

interface StringIdNode extends StringIdTableNode {
  surveyId?: string;
}

export const findNodesByIds = (tableData: Data<StringIdNode>, ids?: string[]): SurveyNode[] => {
  if (!ids || ids.length === 0) {
    return [];
  }
  const nodesMatch: SurveyNode[] = [];
  tableData.nodes.forEach((yearGroup) =>
    yearGroup.nodes?.forEach((typeGroup) => {
      typeGroup.nodes?.forEach((periodGroup) => {
        periodGroup.nodes?.forEach((survey) => {
          if (ids.includes(survey.id) && isSurveyNode(survey)) {
            nodesMatch.push(survey);
          }
        })
      });
    })
  );
  return nodesMatch;
};

export const getExpandedIds = (selectedNodeIds: string[], nodes: TableData['nodes']) => {
  if (selectedNodeIds.length) {
    return selectedNodeIds.reduce((ids, curNodeId) => {
      const parts = curNodeId.split('-');
      // Must be 4 parts, `${year}-${surveyType}-${period}-${surveyId}` as we select leaf nodes
      if (parts.length === 4) {
        ids.push(parts[0], `${parts[0]}-${parts[1]}`, `${parts[0]}-${parts[1]}-${parts[2]}`);
      }
      return ids;
    }, [] as string[]);
  }

  const firstNode = nodes[0]?.id;
  return firstNode ? [firstNode] : [];
}
export const getNewSurveyFilters = (selectedNodes: SurveyNode[]) => {
  return selectedNodes.reduce<Filters>((acc, node) => {
    if (!acc.period.includes(node.period)) {
      acc.period.push(node.period);
    }
    if (!acc.surveyType.includes(node.surveyType)) {
      acc.surveyType.push(node.surveyType);
    }
    const year = formatYear(node.effectiveDate);
    if (!acc.year.includes(year)) {
      acc.year.push(year);
    }
    const month = formatMonth(node.effectiveDate);
    if (!acc.month.includes(month)) {
      acc.month.push(month);
    }
    return acc;
  }, {
    period: [],
    surveyType: [],
    year: [],
    month: [],
  });
}

export const getYears = (surveys: CustomReportSubsidiarySurvey[]) => {
  return Array.from(
    new Set(surveys.map((survey) => formatYear(survey.effectiveDate))).values()
  ).sort();
}

type SurveysForFiltering = SurveyFilter & { initiativeId: string };

export const filterBySurveyFilters = <T extends SurveysForFiltering>(
  toBeFilteredSurveys: T[],
  surveyFilters: Filters,
  initiativeIds: string[],
) => {
  return toBeFilteredSurveys.filter((survey) => {

    if (!initiativeIds.includes(survey.initiativeId)) {
      return false;
    }

    if (surveyFilters.period.length && !surveyFilters.period.includes(survey.period)) {
      return false;
    }

    if (surveyFilters.surveyType.length && !surveyFilters.surveyType.includes(survey.type)) {
      return false;
    }

    if (surveyFilters.year.length && !surveyFilters.year.includes(formatYear(survey.effectiveDate))) {
      return false;
    }

    if (surveyFilters.month.length && !surveyFilters.month.includes(formatMonth(survey.effectiveDate))) {
      return false;
    }

    return true;
  });
};
