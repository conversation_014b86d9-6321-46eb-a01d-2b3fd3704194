/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { screen, fireEvent } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { CombinedReportAdvancedForm } from './CombinedReportAdvancedForm';
import { expect, vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import config from '../../../../config';
import { configureStore } from '@reduxjs/toolkit';
import { reducer } from '@reducers/index';
import { getCurrentUserState, userOne } from '@fixtures/user-factory';
import { createReduxLoadedState, reduxMiddleware } from '@fixtures/redux-store';
import { renderWithProviders } from '@fixtures/utils';
import { generateUrl } from '@routes/util';
import { getGlobalData } from '@fixtures/global-data-fixture';
import { createRootInitiative, initiativeOne } from '@fixtures/initiative-factory';
import { FeatureTag, getFeatureDetails } from '@g17eco/core';
import { createCustomReportSubsidiarySurvey } from '@fixtures/survey-factory';
import { userEvent } from '@testing-library/user-event';
import { DATE, formatDateUTC } from '@utils/date';


describe('CombinedReportAdvancedForm', () => {

  const initiative = createRootInitiative(initiativeOne);
  const surveyOne = createCustomReportSubsidiarySurvey({ initiativeId: initiative._id });
  const surveyTwo = createCustomReportSubsidiarySurvey({ initiativeId: initiative._id });
  const surveyList = [surveyOne, surveyTwo]


  const getUrl = (url: string = '') => `${config.apiUrl}${url}`
  // Mock server setup
  const server = setupServer(
    http.get(getUrl('initiatives/:initiativeId/custom-reports/metric-groups'), async () => {
      return HttpResponse.json({ success: true, data: [] })
    }),
    http.get(getUrl('initiatives/:initiativeId/custom-reports/subsidiary-report/subsidiary-list'), async () => {
      return HttpResponse.json({ success: true, data: [initiative] })
    }),
    http.post(getUrl('initiatives/:initiativeId/custom-reports/subsidiary-report/survey-list'), async () => {
      return HttpResponse.json({ success: true, data: surveyList })
    }),
  );

  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  const mockOnCancel = vi.fn();
  const mockOnCreate = vi.fn();


  const globalData = getGlobalData({
    organization: initiative,
    config: {
      features: [getFeatureDetails(FeatureTag.CombinedReport)],
      survey: { scope: [] },
    },
  });

  const createNewReduxStore = () => {
    return configureStore({
      reducer,
      preloadedState: {
        initiative: {
          ...createReduxLoadedState(initiativeOne),
          id: initiativeOne._id,
        } as any,
        currentUser: getCurrentUserState(userOne),
        globalData,
      },
      middleware: reduxMiddleware,
    });
  }

  const initiativeId = globalData.organization._id;

  const store = createNewReduxStore();
  const baseRoute = { path: 'company-tracker/reports/:initiativeId' };
  const baseUrl = generateUrl(baseRoute, { initiativeId });

  const baseRoutePath = `${baseRoute.path}/create-combined`
  const initialEntry = `${baseUrl}/create-combined`;

  const renderOptions = {
    store,
    route: {
      initialEntries: [initialEntry],
      path: baseRoutePath,

    }
  }

  it('should render the form and handle input changes', async () => {

    const user = userEvent.setup();

    renderWithProviders(
      <CombinedReportAdvancedForm initiativeId={initiativeId} onSubmit={mockOnCreate} onCancel={mockOnCancel} />,
      renderOptions
    );

    // Check if the form elements are rendered
    expect(screen.getByText('Please select subsidiaries above')).toBeInTheDocument();
    const button = await screen.findByRole('button', { name: 'Select all' });
    await user.click(button);

    expect(screen.queryByText('Please select subsidiaries above')).not.toBeInTheDocument();
    expect(screen.getByTestId('combined-survey-submit-btn')).toBeDisabled();

    // Select the first survey
    const year = formatDateUTC(surveyOne.effectiveDate, DATE.YEAR_ONLY);
    const yearRow = await screen.findByRole('gridcell', { name: year });
    expect(yearRow).toBeInTheDocument();
    await user.click(yearRow);
    // It doesn't work due to some kind of internal state management
    // and mix of reference state vs controlled state...

    // await user.click(await screen.findByRole('gridcell', { name: 'Reports' }, { timeout: 2000 }));
    // await user.click(await screen.findByRole('gridcell', { name: 'Annual' }));
    // const name = formatDateUTC(surveyOne.effectiveDate, DATE.MONTH_YEAR);
    // const allByRole = screen.getAllByRole('gridcell', { name: new RegExp(`/${name}/`) });
    // expect(allByRole).toHaveLength(surveyList.length);
    // await user.click(allByRole[0]);
    // expect(screen.getByTestId('combined-survey-submit-btn')).not.toBeDisabled();
  });

  it('should display an error message if no surveys are selected', async () => {
    renderWithProviders(
      <CombinedReportAdvancedForm initiativeId={initiativeId} onSubmit={mockOnCreate} onCancel={mockOnCancel} />,
      renderOptions
    );
    expect(screen.getByTestId('combined-survey-submit-btn')).toBeDisabled();
    expect(await screen.findByText('Please select subsidiaries above')).toBeInTheDocument();
  });

  it('should call onCancel when the cancel button is clicked', async () => {
    renderWithProviders(
      <CombinedReportAdvancedForm initiativeId={initiativeId} onSubmit={mockOnCreate} onCancel={mockOnCancel} />,
      renderOptions
    );

    fireEvent.click(await screen.findByRole('button', { name: 'Cancel' }));
    expect(mockOnCancel).toHaveBeenCalled();
  });
});
