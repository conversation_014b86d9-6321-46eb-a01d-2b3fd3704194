/*!
 * Copyright (c) 2022. World Wide Generation Ltd
 */

@import "../../../../css/variables";
// Import default styles
@import "../../../../components/tree/TreeCheckbox";

.combine-report-form {
  .loadingBar-container {
    padding: 5rem;
    margin-bottom: 5rem;
    .mosaic-loader {
      height: 35px;
    }
  }

  h3 {
    margin-top: 1.3rem;
  }

  p {
    hyphens: auto;
  }

  .tree-dropdown-grid {
    .react-dropdown-tree-select {
      .dropdown {
        .dropdown-trigger {
          .tag-list .search {
            display: none;
          }
        }

        .dropdown-content {
          max-height: 500px;
          font-size: 0.9rem;

          .fa {
            font-size: 1.1rem;
          }

          .action-righ-1 {
            position: absolute;
            right: 100px;
          }

          .action-righ-2 {
            position: absolute;
            right: 50px;
          }

          li {
            background-color: transparent !important;
            &.tree {
              color: var(--theme-AccentExtradark);
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}
