/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react'
import { Modal, ModalHeader, ModalBody } from 'reactstrap';
import { CombinedReportForm } from './CombinedReportForm';

interface CombinedReportModalProps {
  isOpen: boolean;
  initiativeId: string;
  toggle: (props: { status?: 'created' }) => void;
}

export const CombinedReportModal = (props: CombinedReportModalProps) => {
  const { isOpen, toggle } = props;

  return <Modal size='lg' isOpen={isOpen} toggle={() => toggle({})} backdrop='static'>
    <ModalHeader toggle={() => toggle({})}><i className='fa fa-object-exclude mr-3' />Create combined report</ModalHeader>
    <ModalBody>
      <CombinedReportForm
        initiativeId={props.initiativeId}
        onCancel={() => toggle({})}
        onCreate={() => toggle({ status: 'created'})}/>
    </ModalBody>
  </Modal>
}
