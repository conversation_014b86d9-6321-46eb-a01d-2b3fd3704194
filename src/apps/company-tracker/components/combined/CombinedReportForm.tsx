/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import './CombinedReportForm.scss';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { SurveySummary, SurveyType } from '@g17eco/types/survey';
import { Button, ButtonGroup, Input } from 'reactstrap';
import { isUserManager } from '@selectors/user';
import G17Client from '@services/G17Client';
import DropdownTreeSelect, {
  DropdownTreeSelectProps,
  TreeNode,
  TreeNodeProps,
} from 'react-dropdown-tree-select';
import { formatDateUTC, getPeriodOptions, getYear } from '@utils/date';
import { sortSurveysDesc } from '@utils/sort';
import { SubmitButton } from '@components/button/SubmitButton';
import LoadingAnimation from '@components/loading-animation';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { addSiteAlert } from '@g17eco/slices/siteAlertsSlice';
import { naturalSort } from '@utils/index';
import { Loader } from '@g17eco/atoms/loader';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { UpgradeRequired } from '@routes/upgrade-required';
import { SURVEY } from '@constants/terminology';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { BasicAlert } from '@g17eco/molecules/alert';
import { UtrvFilter } from '@g17eco/types/insight-custom-dashboard';

interface Props {
  initiativeId: string;
  onCreate: () => void;
  onCancel: () => void;
}

interface Item extends TreeNode {
  surveyIds: string[]
}

const TreesSelect = memo((props: DropdownTreeSelectProps) => {
  return <DropdownTreeSelect {...props} />;
});

export const CombinedReportForm = (props: Props) => {

  const { initiativeId, onCreate, onCancel } = props;

  const dispatch = useAppDispatch();

  const [message, setMessage] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [period, setPeriod] = useState(DataPeriods.Yearly);
  const [nodes, setNodes] = useState<TreeNode[]>([]);
  const [surveyList, setSurveyList] = useState<SurveySummary[]>([]);
  const [childView, setChildView] = useState(false);
  const surveyListState = useAppSelector((state) => state.surveyListSummary);
  const isManager = useAppSelector(isUserManager);
  const canAccessCombinedReport = useAppSelector(FeaturePermissions.canAccessCombinedReport);

  const onChange = useCallback((_currentNode: TreeNode, selectedNodes: TreeNode[]) => {
    setMessage('')
    setNodes(selectedNodes);
  }, []);

  const onSubmit = async () => {
    const surveyIds = nodes.reduce((a, c) => [...a, ...c.surveyIds], [] as string[]);
    if (surveyIds.length === 0) {
      setMessage(`Please select ${SURVEY.PLURAL} to combine`);
      return
    }

    if (name.length === 0) {
      setMessage('Please provide a name');
      return
    }

    setCreating(true)
    try {
      await G17Client.createCombinedReport(initiativeId, {
        surveyIds,
        name,
        period,
        filters: {
          // We don't add the utrv filter in the old version
          // Setting utrv filter to verified to keep it as backward compatible
          utrv: UtrvFilter.Verified
        }
      })
      onCreate();
    } catch (e) {
      setMessage(e.message)
    }
    setCreating(false)
  }

  React.useEffect(() => {
    setNodes([]);
    if (childView) {
      setLoading(true);
      G17Client.getSurveyListForChildren(initiativeId)
        .then((data) => {
          setSurveyList(data);
          setLoading(false);
        })
        .catch((e) => {
          dispatch(addSiteAlert({
            content: e,
            timeout: 5000
          }));
          setLoading(false);
        });
      return;
    }
    if (surveyListState.loaded) {
      setSurveyList(surveyListState.data);
      if (surveyListState.data.length === 0) {
        setChildView(true);
      }
    }
  }, [dispatch, initiativeId, surveyListState, childView]);

  const data = useMemo(() => {

    const surveyGroups = [...surveyList]
      .sort((a, b) => {
        if (childView) {
          return naturalSort(a.initiative.name, b.initiative.name);
        }
        return sortSurveysDesc(a, b);
      })
      .reduce((a, c) => {
        // Skip aggregations for auto-aggregation reports
        if (c.type === SurveyType.AutoAggregation) {
          return a;
        }

        // Skip aggregations for same-level reports
        if (!childView && c.type === SurveyType.Aggregation) {
          return a;
        }

        const group = childView ? c.initiative.name : getYear(c.effectiveDate, true);
        if (!a[group]) {
          a[group] = [];
        }
        a[group].push(c);
        return a;
      }, {} as Record<string | number, SurveySummary[]>)

    return Object.entries(surveyGroups)
      .sort(([a], [b]) => childView ? naturalSort(a, b) : naturalSort(b, a))
      .map(([groupName, surveys], index) => {
        const children: Item[] = [];

        surveys
          .sort((a, b) => naturalSort(b.effectiveDate, a.effectiveDate))
          .forEach((survey) => {
            const date = formatDateUTC(survey.effectiveDate, 'MMMM YYYY');
            const id = survey._id;

            const items: Item = {
              value: id,
              label: `${survey.type === 'aggregation' ? `(Combined ${date})` : date} - ${survey.name || survey.initiative.name}`,
              surveyIds: [id],
              checked: false,
              actions: [
                {
                  text: survey.status.created.toString(),
                  className: 'action-righ-1'
                },
                {
                  text: survey.status.verified.toString(),
                  className: 'action-righ-2'
                },
              ],
            };
            children.push(items);
          });

        const allIds = children.reduce((a, c) => [...a, ...c.surveyIds], [] as string[]);
        const actions = index !== 0 ? undefined : [
          { className: 'fa fa-user-clock action-righ-1' },
          { className: 'fa fa-user-check text-success action-righ-2' }
        ];

        return {
          label: `${groupName} ${SURVEY.CAPITALIZED_PLURAL}`,
          value: '',
          surveyIds: allIds,
          checked: false,
          expanded: true,
          children: children,
          actions: actions,
          disabled: children.length === 0,
        } as TreeNodeProps
      })

  }, [surveyList, childView]);

  if (!canAccessCombinedReport) {
    return <UpgradeRequired />;
  }

  if (!isManager) {
    return <BasicAlert type={'warning'}>
      Please contact your manager to create a combined report
    </BasicAlert>
  }

  const periodOptions = getPeriodOptions();
  const selectedPeriod = periodOptions.find(p => p.value === period);

  return (
    <div className={'combine-report-form'} style={{ opacity: loading ? 0.5 : 1 }}>
      <BasicAlert type={'danger'}>{message}</BasicAlert>
      <div>
        <p>
          This will combine selected {SURVEY.PLURAL}, adding together or averaging data from the sub-{SURVEY.PLURAL}{' '}
          selected to give you an overview. If you later update the data in the underlying {SURVEY.PLURAL} you will need
          to update this combined {SURVEY.SINGULAR} from the Reports page.
        </p>
      </div>
      {loading ? <Loader /> : null}
      {creating ? <div className={'loadingBar-container'}><LoadingAnimation /></div> :
        <div className='combined-report-form'>

          <h5 className='mt-4'>Name your {SURVEY.SINGULAR}:</h5>
          <Input value={name}
            onChange={(e) => {
              setName(e.target.value);
              setMessage('')
            }}
            type={'text'} name={'report-name'}
            placeholder={`Combined ${SURVEY.SINGULAR} name`}
            data-testid='combined-survey-name-input'
          />

          <h5 className='mt-4'>{SURVEY.CAPITALIZED_SINGULAR} type</h5>
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            placeholder={'Monthly, Quarterly, Annual'}
            className='w-100'
            menuPortalTarget={document.body}
            styles={{ menuPortal: base => ({ ...base, zIndex: 9999 }) }}
            onChange={(option: { value: DataPeriods } | null) => option ? setPeriod(option.value) : undefined}
            value={selectedPeriod}
            options={periodOptions}
          />

          <h5 className='mt-4'>Which {SURVEY.PLURAL} would you like to combine?</h5>
          <ButtonGroup>
            <Button size='sm' color='primary' disabled={surveyListState.data.length === 0} onClick={() => setChildView(false)} active={!childView} outline={childView}>Current level {SURVEY.PLURAL}</Button>
            <Button size='sm' color='primary' onClick={() => setChildView(true)} active={childView} outline={!childView}>Subsidiary {SURVEY.PLURAL}</Button>
          </ButtonGroup>

          <div className={'tree-dropdown-grid mb-4'}>
            <TreesSelect
              showPartiallySelected={true}
              keepTreeOnSearch={true}
              keepChildrenOnSearch={true}
              showDropdown={'always'}
              data={data}
              onChange={onChange} />
          </div>
        </div>
      }

      <div className='d-flex justify-content-end'>
        <Button color='link-secondary' className={'mr-3'} disabled={loading} onClick={() => onCancel()}>Cancel</Button>
        <SubmitButton color='primary' disabled={!isManager} onClick={() => onSubmit()} data-testid='combined-survey-submit-btn'>
          Create combined report
        </SubmitButton>
      </div>
    </div >
  )
}
