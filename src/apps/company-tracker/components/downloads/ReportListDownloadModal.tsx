import {
  convertToUtrvStatuses,
  DisplayOption,
  DownloadSettingsType,
  DownloadUtrvStatusCombined,
  getDefaultDownloadSettings,
  getDisplaySelectOptions,
  getMetricStatusOptions,
  getSelectedDisplayOption,
  getSurveyOptions,
} from '@components/downloads/util/downloadReportHandler';
import { QUESTION, SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { DownloadType, HandleDownload } from '@g17eco/types/download';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { useAppSelector } from '@reducers/index';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useMemo, useState } from 'react';
import { Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { DownloadSettingsChange, handleSettingsChange } from '@components/downloads/util/custom';
import { DownloadButton } from '@components/button/DownloadButton';
import { SurveySummary } from '@g17eco/types/survey';
import { handleRouteError } from '../../../../logger';
import G17Client from '@services/G17Client';
import { getCurrentUser } from '@selectors/user';
import { BlockingLoader } from '@g17eco/atoms/loader';

interface Props {
  toggle: () => void;
  isDisabled?: boolean;
  surveys: SurveySummary[];
  initiativeId: string;
}

const overrideOptionLabelMap: { [key in DisplayOption]: string } = {
  [DisplayOption.UserInput]: 'Use user input',
  [DisplayOption.MetricOverrides]: 'Convert answers to company preferences',
  [DisplayOption.SystemDefault]: 'Convert answers to system preferences',
};

const downloadDelegatedReportHandler = async ({
  type,
  downloadSettings,
  userIds,
  surveyIds,
  initiativeId,
}: {
  type: DownloadType;
  downloadSettings: DownloadSettingsType;
  userIds: string[];
  surveyIds: string[];
  initiativeId: string;
}) => {
  const { status, assuranceStatus, ...rest } = downloadSettings;
  const appliedStatus = assuranceStatus ?? status;
  const statuses = convertToUtrvStatuses(appliedStatus);

  switch (type) {
    case DownloadType.Xlsx:
    case DownloadType.Csv:
      return G17Client.downloadDelegatedSurveysData({
        initiativeId,
        surveyIds,
        userIds,
        type,
        downloadScope: {
          ...statuses,
          ...rest,
        },
      });
    default:
      handleRouteError(new Error('Unknown download type'), { type });
      return;
  }
};

export const ReportListDownloadModal = (props: Props) => {
  const { toggle, isDisabled = false, surveys, initiativeId } = props;

  const surveyOptions = useMemo(() => getSurveyOptions(surveys), [surveys]);
  const currentUser = useAppSelector(getCurrentUser);
  const canAccessAssurance = useAppSelector(FeaturePermissions.canAccessAssurance);
  const { defaultDownloadOptions } = useAppSettings();
  const [downloadSettings, setDownloadSettings] = useState<DownloadSettingsType>({
    ...getDefaultDownloadSettings(),
    selectedScopes: [],
  });
  const [surveyIds, setSurveyIds] = useState<string[]>(surveyOptions.length ? [surveyOptions[0].value] : []);
  const [isGeneratingReport, setGeneratingReport] = useState(false);
  const [message, setMessage] = useState('');

  if (!currentUser) {
    return <BlockingLoader />;
  }

  const handleDownloadReport: HandleDownload = async (type) => {
    setMessage('');
    setGeneratingReport(true);
    try {
      await downloadDelegatedReportHandler({
        type,
        downloadSettings,
        userIds: [currentUser._id],
        surveyIds,
        initiativeId,
      });
      toggle();
    } catch (e) {
      setMessage(e.message);
    } finally {
      setGeneratingReport(false);
    }
  };

  const handleChange = (change: DownloadSettingsChange) =>
    handleSettingsChange(change, setDownloadSettings, downloadSettings);

  const metricStatusOptions = getMetricStatusOptions({
    hasAssuredOption: canAccessAssurance,
    isDisableAssuredOption: false,
    defaultOptions: defaultDownloadOptions?.metricStatuses,
  });

  const displayOptions = getDisplaySelectOptions({
    hasInitiativeUtrs: true,
    defaultOptions: defaultDownloadOptions?.metricOverrides,
    overrideOptionLabelMap,
  });

  const downloadOptions = [
    {
      type: 'Excel',
      handler: () => handleDownloadReport(DownloadType.Xlsx),
      icon: 'fal fa-file-excel',
    },
    {
      type: 'CSV',
      handler: () => handleDownloadReport(DownloadType.Csv),
      icon: 'fal fa-file-csv',
    },
  ];

  const isDownloadDisabled = isDisabled || surveyIds.length === 0;

  return (
    <Modal isOpen toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Download data</ModalHeader>
      <ModalBody>
        <BasicAlert key={'error'} type={'danger'}>
          {message}
        </BasicAlert>
        {isGeneratingReport ? (
          <BasicAlert type='info'>{`Generating ${SURVEY.SINGULAR}...`}</BasicAlert>
        ) : (
          <>
            <p className='mb-4'>This will download data that you have been delegated to you. If you select a report</p>
            <h6 className='fw-bold text-ThemeTextDark mb-3'>Download Settings</h6>
            <h6 className='text-ThemeTextDark'>{SURVEY.CAPITALIZED_PLURAL}</h6>
            <SelectFactory
              selectType={SelectTypes.MultipleSelect}
              placeholder={`Select ${SURVEY.PLURAL} to download`}
              options={surveyOptions}
              onChange={setSurveyIds}
              values={surveyIds}
              className='w-100'
            />
            {downloadSettings.status || downloadSettings.assuranceStatus ? (
              <>
                <h6 className='text-ThemeTextDark'>{QUESTION.CAPITALIZED_SINGULAR} status</h6>
                <SelectFactory
                  value={metricStatusOptions.find(
                    (v) => v.value === downloadSettings.assuranceStatus || v.value === downloadSettings.status,
                  )}
                  className='w-100'
                  selectType={SelectTypes.SingleSelect}
                  options={metricStatusOptions}
                  onChange={(option) =>
                    option?.value &&
                    handleChange({
                      key: DownloadUtrvStatusCombined.Assured === option.value ? 'assuranceStatus' : 'status',
                      value: option.value,
                    })
                  }
                  isDisabled={isDisabled}
                  isSearchable={false}
                />
              </>
            ) : null}
            {displayOptions.length ? (
              <>
                <h6 className='text-ThemeTextDark mt-3'>Answer type</h6>
                <SelectFactory
                  value={displayOptions.find((op) => op.value === getSelectedDisplayOption(downloadSettings))}
                  className='w-100'
                  selectType={SelectTypes.SingleSelect}
                  options={displayOptions}
                  onChange={(option) =>
                    option?.value !== undefined &&
                    handleChange({
                      key: 'display',
                      value: option.value,
                    })
                  }
                  isDisabled={isDisabled}
                  isSearchable={false}
                />
              </>
            ) : null}
            <h6 className='fw-bold text-ThemeTextDark mt-4 mb-3'>Download as...</h6>
            <div className='d-flex justify-content-between align-items-center'>
              <div className='d-flex align-items-center gap-2'>
                {downloadOptions.map((option, index) => {
                  return (
                    <DownloadButton
                      key={index}
                      color='secondary'
                      onClick={option.handler}
                      loadingText={<span className={'ml-2'}>Downloading</span>}
                      disabled={isDownloadDisabled}
                    >
                      {option.icon ? <i className={`${option.icon} mr-2 fs-6`} /> : null}
                      {option.type}
                    </DownloadButton>
                  );
                })}
              </div>
              <Button color='link-secondary' onClick={toggle}>
                Close
              </Button>
            </div>
          </>
        )}
      </ModalBody>
    </Modal>
  );
};
