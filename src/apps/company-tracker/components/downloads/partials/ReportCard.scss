@import '../../../../../css/variables';
@import '../../../../../css/functions';

@mixin card-highlight {
  box-shadow: 2px 4px 2px var(--theme-BgExtradark) !important;
  .card-body {
    background-color: var(--theme-AccentExtralight);
  }
}

.card {
  transition: 0.25s;
  .card-body {
    transition: 0.25s;
  }

  &:hover {
    @include card-highlight();
  }
}

.card.active {
  @include card-highlight();
}
