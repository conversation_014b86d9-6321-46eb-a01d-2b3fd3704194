/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { Loader } from '@g17eco/atoms/loader';
import G17Client from '@services/G17Client';
import { ErrorComponent } from '@features/error';
import { escapeRegexCharacters } from '@utils/string-format';
import { isValidDate } from '@components/stats/utils';
import { Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { useRouteMatch } from 'react-router-dom';
import { DEFAULT_API_PAST_MONTHS, formatDate, formatDatePickerString, parseUserStringToDate } from '@utils/date';
import { AuditLogEntry, LEVEL } from '@g17eco/types/audit-log';
import { SystemLogHeader } from './SystemLogHeader';
import { AdminBreadcrumbs } from '@routes/admin-dashboard/AdminBreadcrumbs';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { naturalSort } from '@utils/index';

const columns: ColumnDef<TableRow>[] = [
  {
    header: 'Date',
    accessorFn: (row: TableRow) => formatDate(row.eventDate),
    sortingFn: (a, b) => naturalSort(a.original.eventDate.toISOString(), b.original.eventDate.toISOString())
  },
  {
    header: 'Severity',
    accessorFn: (row: TableRow) => LEVEL[row.severity],
  },
  {
    header: 'Message',
    accessorKey: 'message',
  },
  {
    header: 'User',
    accessorKey: 'actor.displayName',
  },
  {
    header: 'Event',
    accessorFn: (row: TableRow) => row.auditEvent?.name ?? row.event,
  },
  {
    header: 'Result',
    accessorFn: (row: TableRow) => `${row.outcome.result[0].toUpperCase()}${row.outcome.result.toLowerCase().slice(1)}`,
  },
  {
    header: 'Details',
    accessorKey: 'details',
    cell: ({ row }) => (
      <Button className='p-0 text-wrap text-left' color='link' size='sm' onClick={row.original.onClick}>
        View details
      </Button>
    ),
  },
];

const getObjectProperty = (inputObject: object, path: string) => {
  if (inputObject == null) {
    // null or undefined
    return inputObject;
  }
  const parts = path.split('.');
  return parts.reduce((object, key) => object?.[key as keyof typeof inputObject], inputObject);
};

interface TableRow extends AuditLogEntry {
  searchString: string;
  onClick: () => void;
}

const SystemLog = () => {
  const routeMatch = useRouteMatch<{ initiativeId: string }>();
  const organizationId = routeMatch.params.initiativeId;

  const [errored, setErrored] = useState<boolean>(false);
  const [rows, setRows] = useState<AuditLogEntry[] | undefined>();
  const [searchText, setSearchText] = useState<string>('');
  const [fromDate, setFromDate] = useState<string | undefined>(undefined);
  const [toDate, setToDate] = useState<string | undefined>(undefined);
  const [params, setParams] = useState<{ fromDate?: Date; toDate?: Date }>({});
  const [openDetails, setOpenDetails] = useState<AuditLogEntry | undefined>(undefined);

  const detailsId = openDetails?._id;
  const toggleDetails = useCallback(
    (id?: string) => {
      if (detailsId === id) {
        setOpenDetails(undefined);
      } else {
        setOpenDetails(rows?.find((r) => r._id === id));
      }
    },
    [detailsId, rows]
  );

  const onFromDateChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const dateStr = e.target.value;
    setFromDate(dateStr);
    handleUpdate(dateStr, toDate);
  };

  const onToDateChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const dateStr = e.target.value;
    setToDate(dateStr);
    handleUpdate(fromDate, dateStr);
  };

  const onSearchTextChange = (e: React.FormEvent<HTMLInputElement>): void => {
    setSearchText(String((e.target as HTMLInputElement).value));
  };

  const handleUpdate = useCallback(
    (fromDate?: string, toDate?: string) => {
      const newParams: { fromDate?: Date; toDate?: Date } = {};
      if (!isValidDate(fromDate)) {
        return;
      }
      newParams.fromDate = parseUserStringToDate(fromDate).toDate();
      if (!isValidDate(toDate)) {
        return;
      }
      newParams.toDate = parseUserStringToDate(toDate).endOf('day').toDate();

      if (JSON.stringify(newParams) === JSON.stringify(params)) {
        return;
      }

      setParams(newParams);
      setRows(undefined);
      G17Client.getSystemLog(organizationId, newParams)
        .then((d) => {
          setErrored(false);
          setRows(d);
        })
        .catch(() => setErrored(true));
    },
    [organizationId, params]
  );

  useEffect(() => {
    if (organizationId) {
      const formattedCurrentDate = formatDatePickerString();
      const formattedPastDate = formatDatePickerString(DEFAULT_API_PAST_MONTHS, 'sub');
      const params = {
        fromDate: parseUserStringToDate(formattedPastDate).toDate(),
        toDate: parseUserStringToDate(formattedCurrentDate).endOf('day').toDate(),
      };
      setFromDate(formattedPastDate);
      setToDate(formattedCurrentDate);
      setParams(params);
      setRows(undefined);
      G17Client.getSystemLog(organizationId, params)
        .then((d) => {
          setErrored(false);
          setRows(d);
        })
        .catch(() => setErrored(true));
    }
  }, [organizationId]);

  const getSearchString = (row: AuditLogEntry, rowIndex: number) => {
    return columns
      .reduce((accumulator, column) => {
        if ('accessorFn' in column) {
          accumulator.push(column.accessorFn(row as TableRow, rowIndex) as string);
        }
        if ('accessorKey' in column) {
          const val = getObjectProperty(row, column.accessorKey);
          if (val) {
            accumulator.push(JSON.stringify(val));
          }
        }
        return accumulator;
      }, [] as string[])
      .toString();
  };

  const filteredRows = useMemo(() => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: TableRow) => {
      if (searchText) {
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        const searchString = `${row.searchString}`.toLowerCase();
        return (searchString.match(searchTextRegex) || []).length;
      }
      return 1;
    };

    return rows
      .map((r, i) => ({
        ...r,
        searchString: getSearchString(r, i),
        onClick: () => toggleDetails(r._id),
      }))
      .filter(filterScore);
  }, [rows, searchText, toggleDetails]);

  if (errored) {
    return <ErrorComponent />;
  }

  const isLoading = rows === undefined;

  return (
    <>
      <Dashboard className='w-100'>
        <DashboardRow>
          <AdminBreadcrumbs breadcrumbs={[{ label: 'System logs' }]} initiativeId={organizationId} />
        </DashboardRow>
        <DashboardSectionTitle title='System logs' />
        <DashboardSection>
          <SystemLogHeader
            onSearchTextChange={onSearchTextChange}
            searchText={searchText}
            fromDate={fromDate}
            toDate={toDate}
            onFromDateChange={onFromDateChange}
            onToDateChange={onToDateChange}
          />
          {isLoading ? (
            <Loader />
          ) : (
            <>
              {filteredRows.length === 0 ? (
                <>No results</>
              ) : (
                <>
                  <Table showRowCount={true} columns={columns} data={filteredRows} />
                </>
              )}
            </>
          )}
        </DashboardSection>
      </Dashboard>
      <Modal isOpen={!!openDetails} toggle={() => toggleDetails()} backdrop='static' size='lg'>
        <ModalHeader toggle={() => toggleDetails()}>Log entry</ModalHeader>
        <ModalBody>
          <pre>{JSON.stringify(openDetails, null, 2)}</pre>
        </ModalBody>
      </Modal>
    </>
  );
};

export default SystemLog;
