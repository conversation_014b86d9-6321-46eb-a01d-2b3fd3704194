@import '../../../../css/variables';

.system-log-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .search-box {
    display: flex;
    align-items: center;
    flex-basis: 30%;
    label {
      margin: 0 5px 0 0;
    }
    div.mt-2 {
      margin-top: 0 !important;
      flex-grow: 1;
    }
  }
  .date-filter {
    display: flex;
    align-items: center;
    flex-basis: 40%;
    .from-date,
    .to-date {
      position: relative;
      display: grid;
      align-items: center;
      flex-basis: 50%;
      grid-template-areas:
        'label input input input'
        'feedback feedback feedback feedback';
      label {
        text-align: center;
        margin: 0 5px 0 0;
        grid-area: label;
      }
      input.form-control {
        grid-area: input;
      }
      .invalid-tooltip {
        top: 10%;
        grid-area: feedback;
      }
    }
    .to-date {
      label {
        margin: 0 15px;
      }
    }
  }
}
