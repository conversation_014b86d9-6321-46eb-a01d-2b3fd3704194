import { DashboardRow } from '@components/dashboard';
import { SurveySummary, SurveyType } from '@g17eco/types/survey';
import { useAppSelector } from '@reducers/index';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { getSurveyTypeOptions } from '@utils/survey';
import { UpdateCombinedSurveyButton } from './UpdateCombinedSurveyButton';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { QUESTION, SURVEY } from '@constants/terminology';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useHistory } from 'react-router-dom';
import { Menu, MenuProps } from '@components/menu/Menu';
import { Button, FormGroup, Input } from 'reactstrap';
import { ReportListDownloadModal } from '@apps/company-tracker/components/downloads/ReportListDownloadModal';
import { useToggle } from '@hooks/useToggle';

interface Props {
  showDelegatedOnly: boolean;
  surveyType: SurveyType | undefined;
  initiativeId: string | undefined;
  canCreateSurvey: boolean;
  canManageInitiative: boolean;
  setDelegatedMode: (newDelegatedMode: boolean) => void;
  setSurveyType: (filter: SurveyType) => void;
  defaultSurveys: SurveySummary[];
  isDisabled?: boolean;
}

export const ReportListTopSection = (props: Props) => {
  const {
    showDelegatedOnly,
    surveyType,
    initiativeId,
    canCreateSurvey,
    canManageInitiative,
    setDelegatedMode,
    setSurveyType,
    defaultSurveys,
    isDisabled = false,
  } = props;
  const surveyListState = useAppSelector((state) => state.surveyListSummary);
  const canAccessCombinedSurvey = useAppSelector((state) => FeaturePermissions.canAccessCombinedSurvey(state));
  const history = useHistory();
  const [isOpen, toggle] = useToggle();

  const hasAssuredMetricInCombinedReport = surveyListState.data.some(
    (survey) => survey.type === SurveyType.Aggregation && survey.status.assured && survey.status.assured > 0,
  );

  const canUpdateCombinedReports = canAccessCombinedSurvey && !hasAssuredMetricInCombinedReport;

  const isCreateDisabled =
    surveyType === SurveyType.Aggregation ? !canCreateSurvey || !canAccessCombinedSurvey : !canCreateSurvey;

  const getCreateTooltip = () => {
    if (!canCreateSurvey) {
      return `Please contact your manager to create a ${surveyType === SurveyType.Aggregation ? 'combined' : 'new company'} ${SURVEY.SINGULAR}`;
    }

    if (surveyType === SurveyType.Aggregation && !canAccessCombinedSurvey) {
      return 'This feature is not available on your current plan';
    }

    return '';
  };

  const handleCreate = () => {
    if (surveyType === SurveyType.Aggregation) {
      return history.push(`${generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId })}/create-combined`);
    }
    return history.push(`${generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId })}/create`);
  };

  const items: MenuProps['items'] = getSurveyTypeOptions().map((option) => {
    const isDisabled = option.value === SurveyType.Aggregation && !canAccessCombinedSurvey;
    const isActive = surveyType ? option.value === surveyType : option.value === SurveyType.Default;
    return {
      ...option,
      isDisabled,
      tooltip: isDisabled ? 'This feature is not available on your current plan' : '',
      onClick: () => setSurveyType(option.value),
      active: isActive,
    };
  });

  return (
    <>
      {canManageInitiative ? <Menu className='p-3' items={items} /> : null}
      <DashboardRow className='mb-3' key='survey-list-topbar' mb={2} classes={{ children: 'align-items-center' }}>
        {surveyType === SurveyType.Default ? (
          <FormGroup switch className='d-flex align-items-center'>
            <Input
              type='switch'
              className='mr-2'
              checked={showDelegatedOnly}
              disabled={isDisabled}
              onChange={() => setDelegatedMode(!showDelegatedOnly)}
            />
            <label className='text-md'>{`Only ${SURVEY.PLURAL} with delegated ${QUESTION.PLURAL}`}</label>
          </FormGroup>
        ) : null}
        <div className='d-flex align-items-center ml-auto gap-2'>
          {initiativeId && canCreateSurvey && surveyType === SurveyType.Aggregation ? (
            <UpdateCombinedSurveyButton
              initiativeId={initiativeId}
              canUpdate={canUpdateCombinedReports}
              isDisabled={isDisabled}
            />
          ) : null}
          {surveyType === SurveyType.Default && initiativeId ? (
            <>
              <Button color='secondary' disabled={isDisabled} onClick={toggle}>
                <i className='fal fa-file-arrow-down mr-2' />
                Download data
              </Button>
              {isOpen && (
                <ReportListDownloadModal toggle={toggle} surveys={defaultSurveys} initiativeId={initiativeId} />
              )}
            </>
          ) : null}
          <SimpleTooltip text={getCreateTooltip()}>
            <div data-testid='survey-create-switcher'>
              <Button color='primary' disabled={isCreateDisabled || isDisabled} onClick={handleCreate}>
                <i className='fal fa-plus mr-2' />
                {surveyType === SurveyType.Aggregation
                  ? `Create combined ${SURVEY.SINGULAR}`
                  : `Create ${SURVEY.SINGULAR}`}
              </Button>
            </div>
          </SimpleTooltip>
        </div>
      </DashboardRow>
    </>
  );
};
