/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import { connect, ConnectedProps } from 'react-redux';
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownItemProps,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  ModalHeader,
} from 'reactstrap';
import { DATE, formatDate, getDiff, getYear } from '@utils/date';
import NumberFormat from '@utils/number-format';
import { canCreateSurvey, isStaff } from '@selectors/user';
import { ScorecardStatusbar } from '@components/scorecard-statusbar';
import {
  completeSurvey,
  loadSurvey,
  loadSurveyListSummaryByInitiativeId,
  loadDelegatedSurveyListSummary,
  reloadSurvey,
  reloadSurveyListSummary,
  uncompleteSurvey,
} from '@actions/survey';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { Scorecard } from '@g17eco/types/scorecard';
import { SurveyAddAssurance, SurveySummary, SurveyType } from '@g17eco/types/survey';
import Dashboard, { DashboardSection } from '@components/dashboard';
import SurveyDuplicateModal from '@components/survey-duplicate-modal';
import { reloadSurveyList } from '@g17eco/slices/initiativeSurveyListSlice';
import { reloadInitiative } from '@actions/initiative';
import { RootState } from '@reducers/index';
import { sortSurveysDesc } from '@utils/sort';
import { Loader } from '@g17eco/atoms/loader';
import { getPeriodName } from '@utils/universalTracker';
import { CombinedReportModal } from '../combined/CombinedReportModal';
import { CombinedReportUpdate } from '../combined/CombinedReportUpdate';
import ReportDeleteModal from '@components/survey/delete-modal/ReportDeleteModal';
import './styles.scss';
import { addSiteAlert } from '@g17eco/slices/siteAlertsSlice';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { SurveyPermissions } from '@services/permissions/SurveyPermissions';
import { CompleteSurveyModal } from '@components/complete-survey-modal';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { QUESTION, SURVEY } from '@constants/terminology';
import { AutoAggregatedSurveyCreate } from '@components/auto-aggregated-survey/AutoAggregatedSurveyCreate';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { Column, Divider, Row, TrackingList } from '@g17eco/molecules/tracking-list';
import { SurveyIcon } from '@g17eco/molecules/survey-icon';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ReportListTopSection } from './ReportListTopSection';
import { CurrentUserData } from '@reducers/current-user';

interface SurveyListState {
  isLoading: boolean;
  showDelegatedOnly: boolean;
  aggregateUpdateSurvey?: SurveySummary;
  isCombinedReportOpen: boolean;
  optionsMenu: {
    display?: string;
  };
  duplicateReportSurvey: {
    survey?: SurveySummary;
    display: boolean;
  };
  deleteReportSurvey: {
    survey?: SurveySummary;
    display: boolean;
  };
  surveyFilter: SurveyType | undefined;
  completeSurvey: {
    survey?: SurveySummary;
    display: boolean;
  };
}

interface SurveyListProps extends PropsFromRedux {
  organizationId?: string;
  handleAddAssurance: (survey: SurveyAddAssurance) => void;
}

const canUserManageInitiative = (
  currentUser: { data: CurrentUserData; loaded: true } | { data: Partial<CurrentUserData>; loaded: false },
  initiativeId: string | undefined,
) => {
  if (!currentUser.loaded) {
    return false;
  }
  return InitiativePermissions.canManageInitiative(currentUser.data, initiativeId ?? '');
};

const getDefaultSurveys = (surveys: SurveySummary[], currentFilter: SurveyType | undefined): SurveySummary[] => {
  return currentFilter === SurveyType.Default ? surveys.filter((survey) => survey.type === SurveyType.Default) : [];
};

/** @todo: migrate to function component to reduce duplication */
class SurveyList extends Component<SurveyListProps & RouteComponentProps<{ initiativeId?: string }>, SurveyListState> {
  state: SurveyListState = {
    isLoading: false,
    showDelegatedOnly: false,
    isCombinedReportOpen: false,
    optionsMenu: {
      display: undefined,
    },
    duplicateReportSurvey: {
      survey: undefined,
      display: false,
    },
    deleteReportSurvey: {
      survey: undefined,
      display: false,
    },
    surveyFilter: SurveyType.Default,
    completeSurvey: {
      survey: undefined,
      display: false,
    },
  };

  componentDidMount = () => {
    const { match } = this.props;
    const { showDelegatedOnly } = this.state;
    if (!match.params.initiativeId) {
      this.setState({ showDelegatedOnly: true });
      return;
    }
    if (showDelegatedOnly) {
      this.props.loadDelegatedSurveyListSummary(match.params.initiativeId);
      return;
    }
    this.props.loadSurveyListSummaryByInitiativeId(match.params.initiativeId);
  };

  componentDidUpdate = () => {
    const { surveyListState, currentUser, match } = this.props;
    const { showDelegatedOnly, surveyFilter } = this.state;
    const canManageInitiative = canUserManageInitiative(currentUser, match.params.initiativeId);
    if (surveyListState.errored && !showDelegatedOnly) {
      this.setDelegatedMode(true);
    }
    if (surveyFilter === undefined) {
      const counts = surveyListState.data.reduce(
        (acc, survey) => {
          if (survey.type === SurveyType.Aggregation) {
            acc.aggregated += 1;
          } else if (survey.type === SurveyType.AutoAggregation) {
            acc.autoAggregated += 1;
          } else {
            acc.survey += 1;
          }
          return acc;
        },
        { survey: 0, aggregated: 0, autoAggregated: 0 },
      );

      if (counts.survey === 0 && counts.aggregated > 0 && canManageInitiative) {
        this.setSurveyFilter(SurveyType.Aggregation);
      }
    }
  };

  setDelegatedMode = (newDelegatedMode: boolean) => {
    const { showDelegatedOnly } = this.state;
    const { match } = this.props;
    if (newDelegatedMode === showDelegatedOnly || !match.params.initiativeId) {
      return;
    }

    this.setState({ showDelegatedOnly: newDelegatedMode });
    if (newDelegatedMode) {
      this.props.loadDelegatedSurveyListSummary(match.params.initiativeId);
      return;
    }
    this.props.loadSurveyListSummaryByInitiativeId(match.params.initiativeId);
  };

  setSurveyFilter = (filter: SurveyType) => {
    this.setState({ surveyFilter: filter });
  };

  redirectToSurveyOverview = (surveyId: string, initiativeId: string) => {
    const url = `${generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' })}${
      this.props.history.location.search
    }`;
    return this.props.history.push(url);
  };

  redirectToSettings = (surveyId: string, initiativeId: string, page: string, search: string = '') => {
    const url = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page });
    return this.props.history.push({ pathname: url, search });
  };

  handleToggleOptionsMenu = (surveyId: string) => {
    const { optionsMenu } = this.state;
    if (optionsMenu.display === surveyId) {
      return this.setState({ optionsMenu: { display: undefined } });
    }

    this.setState({ optionsMenu: { display: surveyId } });
  };

  handleCreateCombinedReport = () => {
    const initiativeId = this.props.match.params.initiativeId;
    const url = generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId });
    this.props.history.push(`${url}/create-combined`);
  };

  toggleComplete = async (survey: SurveySummary) => {
    this.setState({ isLoading: true }, async () => {
      try {
        if (survey.completedDate) {
          await uncompleteSurvey(survey._id);
          this.trackSurveyComplete(survey, false);
        } else {
          await completeSurvey(survey._id);
          this.trackSurveyComplete(survey, true);
        }
        await this.handleReload(survey._id);
        this.setState({ isLoading: false });
      } catch (e) {
        this.setState({ isLoading: false });
      }
    });
  };

  trackSurveyComplete = async (survey: SurveySummary, isCompleted: boolean) => {
    const analytics = getAnalytics();
    await analytics.track(AnalyticsEvents.SurveyCompleted, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
      isCompleted,
    });
  };

  showReportDeleteModal = (survey: SurveySummary) => {
    this.setState({
      deleteReportSurvey: {
        display: true,
        survey: survey,
      },
    });
  };

  cancelReportDeleteModal = () => {
    this.setState({
      deleteReportSurvey: {
        display: false,
        survey: undefined,
      },
    });
  };

  showSurveyDuplicateModal = (survey: SurveySummary) => {
    this.setState({
      duplicateReportSurvey: {
        survey: survey,
        display: true,
      },
    });
  };

  cancelReportSurveyDuplicate = () => {
    this.setState({
      duplicateReportSurvey: {
        survey: undefined,
        display: false,
      },
    });
  };

  canManage = (survey: SurveySummary) => {
    const { currentUser } = this.props;
    if (!currentUser.loaded) {
      return false;
    }
    return SurveyPermissions.canManage(survey, currentUser.data);
  };

  renderNoSurveys = () => {
    const { canCreateSurvey, match, currentUser } = this.props;
    const { surveyFilter } = this.state;
    const initiativeId = match.params.initiativeId;
    const canManageInitiative = canUserManageInitiative(currentUser, initiativeId);

    return (
      <div className='flex-column'>
        <Dashboard>
          <ReportListTopSection
            showDelegatedOnly={this.state.showDelegatedOnly}
            surveyType={this.state.surveyFilter}
            initiativeId={initiativeId}
            canCreateSurvey={this.props.canCreateSurvey}
            canManageInitiative={canManageInitiative}
            setDelegatedMode={this.setDelegatedMode}
            setSurveyType={this.setSurveyFilter}
            defaultSurveys={[]}
          />
          {this.renderAutoAggregatedSurveySection()}
          <DashboardSection className='surveyList' icon='fa-tasks' title={SURVEY.CAPITALIZED_PLURAL}>
            <div className='text-center' data-testid='no-report-message'>
              <p>
                There are no {surveyFilter === SurveyType.Aggregation ? 'combined ' : ''}
                {SURVEY.PLURAL} set up.
              </p>
              {canCreateSurvey ? (
                <Button
                  className={'my-2'}
                  onClick={surveyFilter === SurveyType.Aggregation ? this.handleCreateCombinedReport : this.goToCreate}
                >
                  Create a new {surveyFilter === SurveyType.Aggregation ? 'combined ' : ''}
                  {SURVEY.SINGULAR}
                </Button>
              ) : null}
            </div>
          </DashboardSection>
          {this.renderCombingSurveyModal()}
        </Dashboard>
      </div>
    );
  };

  renderCombingSurveyModal = () => {
    const initiativeId = this.props.match.params.initiativeId;
    if (!initiativeId) {
      return null;
    }

    return (
      <CombinedReportModal
        key={initiativeId}
        initiativeId={initiativeId}
        isOpen={this.state.isCombinedReportOpen}
        toggle={({ status }) => {
          this.setState({ isCombinedReportOpen: !this.state.isCombinedReportOpen });
          if (status === 'created') {
            this.setSurveyFilter(SurveyType.Aggregation);
            this.props.reloadSurveyListSummary();
            this.props.reloadSurveyList();
          }
        }}
      />
    );
  };

  renderLoadingSurveys = () => {
    const { showDelegatedOnly, surveyFilter } = this.state;
    const { currentUser, match, canCreateSurvey, surveyListState } = this.props;
    const initiativeId = match.params.initiativeId;
    const canManageInitiative = canUserManageInitiative(currentUser, initiativeId);
    const defaultSurveys = getDefaultSurveys(surveyListState.data, this.state.surveyFilter);

    return (
      <Dashboard className='surveyList'>
        <ReportListTopSection
          showDelegatedOnly={showDelegatedOnly}
          surveyType={surveyFilter}
          initiativeId={initiativeId}
          canCreateSurvey={canCreateSurvey}
          canManageInitiative={canManageInitiative}
          setDelegatedMode={this.setDelegatedMode}
          setSurveyType={this.setSurveyFilter}
          defaultSurveys={defaultSurveys}
        />
        <DashboardSection title={`${getYear()} ${SURVEY.CAPITALIZED_PLURAL}`}>
          {this.renderHeaderRow(String(getYear()))}
          <LoadingPlaceholder wrapper={Row} className='background-ThemeTextWhite my-2' count={1} height={64}>
            <TrackingList />
          </LoadingPlaceholder>
        </DashboardSection>
      </Dashboard>
    );
  };

  renderReportSurveyDeleteModal() {
    const { deleteReportSurvey } = this.state;

    return (
      <ReportDeleteModal
        display={deleteReportSurvey.display}
        handleClose={this.cancelReportDeleteModal}
        survey={deleteReportSurvey.survey}
      />
    );
  }

  renderReportSurveyDuplicateModal() {
    const { duplicateReportSurvey } = this.state;
    return (
      <SurveyDuplicateModal
        display={duplicateReportSurvey.display}
        handleClose={this.cancelReportSurveyDuplicate}
        survey={duplicateReportSurvey.survey}
      />
    );
  }

  handleReload = async (surveyId: string) => {
    this.setState({ isLoading: true });
    await this.props.reloadSurveyListSummary();
    await this.props.reloadSurvey(surveyId);
    await this.props.reloadSurveyList();
    await this.props.reloadInitiative();
    this.setState({ isLoading: false });
  };

  toggleOpenSurveyCompleteModal = () =>
    this.setState({
      completeSurvey: {
        ...this.state.completeSurvey,
        display: !this.state.completeSurvey.display,
      },
    });

  openSurveyCompleteModal = (survey: SurveySummary) =>
    this.setState({
      completeSurvey: {
        survey,
        display: !this.state.completeSurvey.display,
      },
    });

  renderCompleteSurveyModal() {
    const { completeSurvey } = this.state;
    return (
      <CompleteSurveyModal
        isOpen={completeSurvey.display}
        toggle={this.toggleOpenSurveyCompleteModal}
        completeSurveyCallback={() => {
          if (!completeSurvey.survey) {
            return;
          }
          return this.toggleComplete(completeSurvey.survey);
        }}
      />
    );
  }

  render() {
    const { surveyListState, match, currentUser } = this.props;
    const { isLoading, surveyFilter } = this.state;
    const initiativeId = match.params.initiativeId;
    const canManageInitiative = canUserManageInitiative(currentUser, initiativeId);
    surveyListState.data.sort(sortSurveysDesc);

    if (!initiativeId) {
      return null;
    }

    if (!surveyListState.loaded) {
      return this.renderLoadingSurveys();
    }

    if (surveyListState.data.length === 0) {
      return this.renderNoSurveys();
    }

    const companyList: { [key: string]: any } = {};
    const surveyListGroups: { [key: string]: SurveySummary[] } = {};
    const currentSurveyFilter = surveyFilter ?? SurveyType.Default;
    surveyListState.data.forEach((survey: SurveySummary) => {
      if (survey.type !== currentSurveyFilter) {
        return;
      }
      companyList[survey.initiative.name] = survey.initiative;
      const group = getYear(survey.effectiveDate, true);
      if (!surveyListGroups[group]) {
        surveyListGroups[group] = [];
      }
      surveyListGroups[group].push(survey);
    });

    const dashboards = Object.keys(surveyListGroups).sort().reverse();

    if (dashboards.length === 0) {
      return this.renderNoSurveys();
    }

    const defaultSurveys = getDefaultSurveys(surveyListState.data, this.state.surveyFilter);

    return (
      <>
        {isLoading ? <Loader /> : null}
        <div style={{ opacity: isLoading ? 0.5 : 1 }}>
          <Dashboard>
            {[
              <ReportListTopSection
                showDelegatedOnly={this.state.showDelegatedOnly}
                surveyType={this.state.surveyFilter}
                initiativeId={initiativeId}
                canCreateSurvey={this.props.canCreateSurvey}
                canManageInitiative={canManageInitiative}
                setDelegatedMode={this.setDelegatedMode}
                setSurveyType={this.setSurveyFilter}
                defaultSurveys={defaultSurveys}
              />,
              this.renderAutoAggregatedSurveySection(),
              ...dashboards.map((group) => {
                const surveys = surveyListGroups[group].sort(sortSurveysDesc);
                return (
                  <DashboardSection
                    padding={0}
                    key={group}
                    className='surveyList'
                    title={`${group} ${SURVEY.CAPITALIZED_PLURAL}`}
                  >
                    {this.renderHeaderRow(group)}
                    <TrackingList key={`surveylist-group-${group}`} data-testid='survey-list'>
                      {surveys.map((survey) => this.renderSurveyListRow(survey))}
                    </TrackingList>
                  </DashboardSection>
                );
              }),
            ]}
          </Dashboard>
          {this.renderReportSurveyDeleteModal()}
          {this.renderReportSurveyDuplicateModal()}
          {this.renderCombingSurveyModal()}
          {this.renderAggregateDataModal()}
          {this.renderCompleteSurveyModal()}
        </div>
      </>
    );
  }

  renderAutoAggregatedSurveySection() {
    const { isUserStaff, match, currentUser, limitReportingLevels } = this.props;
    const canManageInitiative = canUserManageInitiative(currentUser, match.params.initiativeId);

    // staff check is subject to be removed later
    if (!isUserStaff) {
      return null;
    }

    if (limitReportingLevels < 2 || !canManageInitiative || this.state.surveyFilter === SurveyType.Default) {
      return null;
    }

    return <AutoAggregatedSurveyCreate key='auto-aggregated-survey-create' initiativeId={match.params.initiativeId} />;
  }

  private goToCreate = () => {
    const initiativeId = this.props.match.params.initiativeId;
    this.props.history.push(`${generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId })}/create`);
  };

  renderScorecardStatusbar = (scorecard: Scorecard, surveyToolTip: React.JSX.Element) => {
    const goals: { [key: number]: number } = {};
    if (scorecard?.goals) {
      scorecard.goals.forEach((goal) => {
        goals[Number(goal.sdgCode)] = goal.actual || 0;
      });
    }
    return <ScorecardStatusbar goals={goals} tooltipPrefix={surveyToolTip} />;
  };

  getTooltip = (surveyTitle: string, surveySubtitle: string, surveyScore: string | number) => {
    return (
      <div>
        <div className='strong'>{surveyTitle}</div>
        <div className='strong'>{surveySubtitle}</div>
        <div>
          SDG Index: <NumberFormat value={surveyScore} suffix={'%'} />
        </div>
      </div>
    );
  };

  renderSurveyListRow(survey: SurveySummary) {
    const rowClick = () => this.redirectToSurveyOverview(survey._id, survey.initiativeId);

    const date = formatDate(survey.effectiveDate, 'MMMM YYYY', true);
    const month = formatDate(survey.effectiveDate, 'MMM', true);

    // Fallback on empty string
    const surveyTitle = survey.name || survey.initiative.name;
    const surveySubtitle = `${date} ${SURVEY.CAPITALIZED_SINGULAR}`;
    const surveyToolTip = this.getTooltip(surveyTitle, surveySubtitle, survey?.scorecard?.actual ?? 0);

    const valueOrEmpty = (value: number) => {
      return value === 0 ? '' : value;
    };

    const canManageSurvey = this.canManage(survey);
    const isAggregation = this.state.surveyFilter === SurveyType.Aggregation;
    return (
      <Row className='hover-highlight' key={survey._id}>
        <Column className='statusCol'>
          <SurveyIcon survey={survey} />
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} className='periodCol'>
          {getPeriodName(survey.period, false)}
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} className='monthCol'>
          {month}
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} stretch truncate className='titleCol'>
          {surveyTitle}
        </Column>
        <Column onClick={rowClick} className={isAggregation ? 'lastUpdatedCol' : 'scorecardBar'}>
          {isAggregation
            ? this.renderAggregationDate(survey)
            : this.renderScorecardStatusbar(survey.scorecard, surveyToolTip)}
        </Column>
        <Divider />
        <Column onClick={rowClick} tooltip={surveyToolTip} className='actionCount'>
          <NumberFormat
            value={valueOrEmpty(survey.status.created + survey.status.rejected)}
            decimalPlaces={0}
            emptyValue='-'
          />
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} className='actionCount'>
          <NumberFormat value={valueOrEmpty(survey.status.updated)} decimalPlaces={0} emptyValue='-' />
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} className='actionCount'>
          <NumberFormat value={valueOrEmpty(survey.status.rejected)} decimalPlaces={0} emptyValue='-' />
        </Column>
        <Column onClick={rowClick} tooltip={surveyToolTip} className='actionCount mr-2'>
          <NumberFormat value={valueOrEmpty(survey.status.verified)} decimalPlaces={0} emptyValue='-' />
        </Column>
        <Divider />
        <Column className='pl-2 pr-0 actionIcon'>
          {canManageSurvey ? (
            this.renderOptionsMenu(survey)
          ) : (
            <Button size='sm' outline disabled>
              <i className='fas fa-bars' />
            </Button>
          )}
        </Column>
      </Row>
    );
  }

  renderOptionsMenu = (survey: SurveySummary, tooltip?: string) => {
    const { optionsMenu } = this.state;
    const { canCreateSurvey } = this.props;

    const isOpen = optionsMenu.display === survey._id;
    const isCombinedReport = Boolean(survey.type === SurveyType.Aggregation);

    const ConditionalDropdownItem = (props: DropdownItemProps & { hidden?: boolean }) => {
      if (props.hidden === true) {
        return null;
      }
      return <DropdownItem {...props}>{props.children}</DropdownItem>;
    };

    return (
      <Dropdown isOpen={isOpen} toggle={() => this.handleToggleOptionsMenu(survey._id)}>
        <SimpleTooltip text={tooltip ?? 'Access this Report\'s Configuration, Units, Scope or Delegation'}>
          <DropdownToggle color='transparent' outline className='px-2'>
            <i className='fas fa-bars' />
          </DropdownToggle>
        </SimpleTooltip>
        <DropdownMenu>
          <ConditionalDropdownItem
            hidden={!isCombinedReport}
            onClick={() => this.redirectToSettings(survey._id, survey.initiativeId, 'update-combined')}
          >
            <i className='fal fa-pencil mr-2' />
            Update {SURVEY.SINGULAR}
          </ConditionalDropdownItem>
          <ConditionalDropdownItem
            hidden={isCombinedReport}
            onClick={() => this.redirectToSettings(survey._id, survey.initiativeId, 'configuration')}
          >
            <i className='fal fa-cog mr-2' />
            {SURVEY.CAPITALIZED_SINGULAR} settings
          </ConditionalDropdownItem>

          <ConditionalDropdownItem
            hidden={isCombinedReport}
            onClick={() => (survey.completedDate ? this.toggleComplete(survey) : this.openSurveyCompleteModal(survey))}
          >
            <i className={`fas fa-check-circle ${survey.completedDate ? 'text-ThemeSuccessMedium' : ''} mr-2`} />
            {survey.completedDate ? 'Mark as not Complete' : 'Mark as Complete'}
          </ConditionalDropdownItem>

          <ConditionalDropdownItem hidden={!isCombinedReport} onClick={() => this.updateAggregatedData(survey)}>
            <i className={'fa fa-arrows-rotate mr-2 '} />
            Refresh data
          </ConditionalDropdownItem>

          <ConditionalDropdownItem hidden={!canCreateSurvey} onClick={() => this.showSurveyDuplicateModal(survey)}>
            <i className='fas fa-copy mr-2' />
            Duplicate
          </ConditionalDropdownItem>

          <ConditionalDropdownItem divider />
          <ConditionalDropdownItem onClick={() => this.exportQuestionDownload(survey)}>
            <i className='fas fa-file-excel  mr-2' />
            Download data
          </ConditionalDropdownItem>

          {this.renderAssuranceDropdownItem(survey)}

          <ConditionalDropdownItem divider />
          <ConditionalDropdownItem onClick={() => this.showReportDeleteModal(survey)}>
            <i className='fas fa-trash mr-2' />
            Delete
          </ConditionalDropdownItem>
        </DropdownMenu>
      </Dropdown>
    );
  };

  renderAssuranceDropdownItem(survey: SurveySummary) {
    const { canAccessAssurance } = this.props;
    const isCombinedReport = Boolean(survey.type === SurveyType.Aggregation);

    if (isCombinedReport) {
      return null;
    }

    if (!canAccessAssurance) {
      return (
        <SimpleTooltip text='You do not have access to assurance. Please contact us if you would like access.'>
          <DropdownItem disabled={true}>
            <i className='fas fa-award assurance-btn add-assurance mr-2' />
            Assurance
          </DropdownItem>
        </SimpleTooltip>
      );
    }

    return (
      <SimpleTooltip text='View/Manage Assurances'>
        <DropdownItem onClick={() => this.props.handleAddAssurance(survey)}>
          <i className='fas fa-award assurance-btn add-assurance mr-2' />
          Assurance
        </DropdownItem>
      </SimpleTooltip>
    );
  }

  exportQuestionDownload(survey: SurveySummary) {
    const surveyId = survey._id;
    this.props.history.push({
      pathname: generateUrl(ROUTES.DOWNLOADS, { initiativeId: survey.initiativeId, surveyId }),
      search: this.props.history.location.search,
    });
  }

  renderHeaderRow(id: string) {
    const isAggregation = this.state.surveyFilter === SurveyType.Aggregation;
    return (
      <div className='surveyListHeaderRow d-flex align-items-center py-1'>
        <div className='statusCol' />
        <div className='fw-semibold periodCol'>Period</div>
        <div className='fw-semibold monthCol'>Month</div>
        <div className='flex-grow-1 pl-2 fw-semibold titleCol'>Title</div>
        <div className={`fw-semibold ${isAggregation ? 'lastUpdatedCol' : 'scorecardBar'}`}>
          {isAggregation ? 'Last updated' : 'SDG progress'}
        </div>
        <div className='actionCount'>
          <SimpleTooltip
            id={`surveyList-header-${id}-outstanding`}
            text={`Unanswered ${QUESTION.CAPITALIZED_PLURAL}`}
            component={<i className='fal fa-user-clock text-secondary' />}
          />
        </div>
        <div className='actionCount'>
          <SimpleTooltip
            id={`surveyList-header-${id}-updated`}
            text={`Completed ${QUESTION.CAPITALIZED_PLURAL}`}
            component={<i className='fal fa-user-edit text-primary' />}
          />
        </div>
        <div className='actionCount'>
          <SimpleTooltip
            id={`surveyList-header-${id}-rejected`}
            text={`Rejected ${QUESTION.CAPITALIZED_PLURAL}`}
            component={<i className='fal fa-user-times text-danger' />}
          />
        </div>
        <div className='actionCount'>
          <SimpleTooltip
            id={`surveyList-header-${id}-verified`}
            text={`Verified ${QUESTION.CAPITALIZED_PLURAL}`}
            component={<i className='fal fa-user-check text-success' />}
          />
        </div>
        <div className='actionIcon'>&nbsp;</div>
        <div>&nbsp;</div>
        <div>&nbsp;</div>
      </div>
    );
  }

  private renderAggregationDate(survey: SurveySummary) {
    const date = survey.aggregatedDate;

    return (
      <SimpleTooltip text={date ? formatDate(date, DATE.DEFAULT_SPACES_WITH_TIME) : undefined}>
        <span className={this.getUpdatedColor(date)}>{date ? formatDate(date, DATE.HUMANIZE) : '-'}</span>
      </SimpleTooltip>
    );
  }

  private getUpdatedColor(date?: string) {
    if (!date) {
      return '';
    }

    const now = new Date();
    if (getDiff(date, now, 'week') < 1) {
      return 'text-ThemeTextDark'; // Shark black
    }

    if (getDiff(date, now, 'month') < 1) {
      return 'text-ThemeTextMedium';
    }
    return '';
  }

  private updateAggregatedData(survey?: SurveySummary) {
    this.setState({ aggregateUpdateSurvey: survey });
  }

  private renderAggregateDataModal() {
    const surveyToUpdate = this.state.aggregateUpdateSurvey;

    if (!surveyToUpdate) {
      return null;
    }

    return (
      <Modal isOpen={true} toggle={() => this.updateAggregatedData()} backdrop='static'>
        <ModalHeader toggle={() => this.updateAggregatedData()}>
          <i className='fa fa-object-exclude mr-3' />
          Update combined report
        </ModalHeader>
        <ModalBody>
          <BasicAlert type='warning' hide={surveyToUpdate.aggregatedVersionMismatch === false}>
            The report you are updating was aggregated using an outdated aggregation version. If you continue, some data
            may change.
          </BasicAlert>
          <CombinedReportUpdate
            onCancel={() => this.updateAggregatedData()}
            onUpdate={async () => {
              this.props.addSiteAlert({
                content: `Success! Combined ${SURVEY.SINGULAR} data was updated`,
                timeout: 5000,
              });
              this.setState({ isLoading: true, aggregateUpdateSurvey: undefined });
              await this.props.reloadSurveyListSummary();
              this.setState({ isLoading: false });
            }}
            survey={surveyToUpdate}
          />
        </ModalBody>
      </Modal>
    );
  }
}

const mapStateToProps = (state: RootState) => ({
  surveyListState: state.surveyListSummary,
  currentUser: state.currentUser,
  canCreateSurvey: canCreateSurvey(state),
  initiativeState: state.initiative,
  canAccessCombinedSurvey: FeaturePermissions.canAccessCombinedSurvey(state),
  canAccessAssurance: FeaturePermissions.canAccessAssurance(state),
  isUserStaff: isStaff(state),
  limitReportingLevels: FeaturePermissions.getLimitReportingLevels(state),
});

const mapDispatchToProps = (dispatch: any) => {
  return {
    addSiteAlert: (alert: { content: string; timeout: number }) => dispatch(addSiteAlert(alert)),
    loadSurveyListSummaryByInitiativeId: async (initiativeId?: string) =>
      dispatch(loadSurveyListSummaryByInitiativeId(initiativeId, true)),
    loadDelegatedSurveyListSummary: async (delegatedInitiativeId?: string) =>
      dispatch(loadDelegatedSurveyListSummary(delegatedInitiativeId, true)),
    reloadSurvey: async (surveyId: string) => dispatch(reloadSurvey(surveyId)),
    reloadSurveyList: async () => dispatch(reloadSurveyList()),
    reloadInitiative: async () => dispatch(reloadInitiative()),
    reloadSurveyListSummary: async () => dispatch(reloadSurveyListSummary()),
    loadSurvey: async (surveyId: string, blockingLoad: boolean, forceReload: boolean) =>
      dispatch(loadSurvey(surveyId, blockingLoad, forceReload)),
  };
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default withRouter(connector(SurveyList));
