/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "src/css/functions";

.surveyList {
  .clickable:hover {
    color: initial;
  }

  .open-assurance {
    color: var(--theme-AccentMedium);
  }

  .open-assurance-completed {
    color: var(--theme-SuccessMedium);
  }

  .surveyListHeaderRow {
    align-items: center;
    background-color: var(--theme-BgToolbar);
    i {
      font-size: 1rem;
    }
    h2 i {
      font-size: 1.5rem;
    }
  }

  .trackingListContainer {
    border: 1px solid transparent;
    .trackingListRow {
      padding: 0 !important;
    }

    .titleCol {
      text-align: left;
      padding-right: 0.5rem;
      color: var(--theme-AccentMedium);
    }
  }

  .trackingListContainer,
  .surveyListHeaderRow {
    text-align: center;

    .scorecardBar {
      min-width: 300px;
      .mosaic-loader {
        height: 17px;
      }
    }

    .lastUpdatedCol {
      min-width: 160px;
    }

    .statusCol {
      min-width: 34px;
      i {
        font-size: 1rem;
      }
    }

    .titleCol {
      text-align: left;
    }

    .periodCol {
      min-width: 4.8rem;
    }

    .monthCol {
      min-width: 3rem;
    }
  }

  .actionIcon {
    min-width: 45px !important;
  }

  .actionCount {
    min-width: 45px !important;
    padding: 6px !important;
    text-align: right;
  }

  .loadingTitle:after {
    overflow: hidden;
    display: inline-block;
    vertical-align: bottom;
    -webkit-animation: ellipsis steps(4, end) 900ms infinite;
    animation: ellipsis steps(4, end) 2000ms infinite;
    content: "\2026"; /* ascii code for the ellipsis character */
    width: 0px;
  }

  @keyframes ellipsis {
    to {
      width: 1.25em;
    }
  }

  @-webkit-keyframes ellipsis {
    to {
      width: 1.25em;
    }
  }

  .dashboard-section {
    .h2,
    .h3 {
      .action-buttons {
        .report-button {
          color: var(--theme-AccentMedium);
          padding: 0 1rem;
          text-decoration: none;
          border: 2px solid var(--theme-NeutralsLight);
          border-radius: $borderRadius;

          &:hover {
            background-color: var(--theme-AccentMedium);
            color: var(--theme-TextWhite);
            border-color: var(--theme-AccentMedium);
          }

          &.disabled {
            background-color: var(--theme-HeadingLight) !important;
            opacity: 0.2;
            color: var(--theme-TextWhite) !important;
          }
        }
      }
    }
  }
}

@keyframes ripple {
  0% {
    background-color: var(--theme-NeutralsLight);
  }

  30% {
    background-color: var(--cell-color);
    opacity: 0.1;
  }

  60% {
    background-color: var(--theme-NeutralsLight);
  }

  100% {
    background-color: var(--theme-NeutralsLight);
  }
}
