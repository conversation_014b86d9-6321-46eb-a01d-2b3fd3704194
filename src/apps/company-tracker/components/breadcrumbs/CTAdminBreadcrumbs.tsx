import { ROUTES } from '@constants/routes';
import { AdminBreadcrumbsProps, Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';

export const CTAdminBreadcrumbs = ({ breadcrumbs, initiativeId }: AdminBreadcrumbsProps) => {
  const rootUrl = generateUrl(ROUTES.ADMIN_SETTINGS, { initiativeId });
  return <Breadcrumbs breadcrumbs={breadcrumbs} rootLabel={'Admin Settings'} rootUrl={rootUrl} />;
};
