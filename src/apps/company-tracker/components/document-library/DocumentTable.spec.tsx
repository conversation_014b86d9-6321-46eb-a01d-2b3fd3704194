import { screen, within } from '@testing-library/react';
import { vi, Mock } from 'vitest';
import { DocumentTable } from './DocumentTable';
import {
  useBulkUpdateDocumentMutation,
  useDeleteDocumentMutation,
  useGetDocumentsQuery,
  useUpdateDocumentMutation,
} from '@api/documents';
import { DocumentSubType } from '@g17eco/types/document';
import { configureStore } from '@reduxjs/toolkit';
import { reduxMiddleware } from '@fixtures/redux-store';
import { reducer } from '@reducers/index';
import { renderWithProviders } from '@fixtures/utils';
import { DATE, formatDateUTC } from '@utils/date';

vi.mock('@api/documents', () => ({
  useGetDocumentsQuery: vi.fn(),
  useDeleteDocumentMutation: vi.fn(),
  useUpdateDocumentMutation: vi.fn(),
  useBulkUpdateDocumentMutation: vi.fn(),
}));

describe('DocumentTable', () => {
  const initiativeId = 'test-initiative-id';
  const renderOptions = {
    route: {
      initialEntries: [`/company-tracker/admin/${initiativeId}/document-library`],
      path: '/company-tracker/admin/:initiativeId/document-library',
    },
    store: configureStore({
      reducer,
      middleware: reduxMiddleware,
    }),
  };

  beforeEach(() => {
    (useGetDocumentsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    (useDeleteDocumentMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);

    (useUpdateDocumentMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);

    (useBulkUpdateDocumentMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders no documents message when data is empty', () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    renderWithProviders(<DocumentTable initiativeId={initiativeId} />, renderOptions);

    expect(screen.getByText('No documents uploaded yet!')).toBeInTheDocument();
  });

  it('renders loader when data is fetching', () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({
      data: [],
      isFetching: true,
      isError: false,
    });

    renderWithProviders(<DocumentTable initiativeId={initiativeId} />, renderOptions);

    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  it('renders error message when data is in error state', () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
      error: {
        message: 'Error loading documents',
      },
    });

    renderWithProviders(<DocumentTable initiativeId={initiativeId} />, renderOptions);

    expect(screen.getByText('Error loading documents')).toBeInTheDocument();
  });

  const documentsData = [
    {
      _id: '1',
      title: 'Document 1',
      ownerSubType: DocumentSubType.Image,
      created: new Date(),
      metadata: {
        name: '',
        mimetype: '',
        extension: 'png',
      },
    },
    {
      _id: '2',
      title: 'Document 2',
      ownerSubType: DocumentSubType.Spreadsheet,
      created: new Date(),
      metadata: {
        name: '',
        mimetype: '',
        extension: 'xlsx',
      },
    },
  ];

  it('renders table with columns and documents data', async () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({
      data: { documents: documentsData },
      isLoading: false,
      isError: false,
    });

    renderWithProviders(<DocumentTable initiativeId={initiativeId} />, renderOptions);

    const table = await screen.findByTestId('manage-documents-table');
    const rows = screen.queryAllByTestId('document-type-dropdown');

    expect(rows.length).toEqual(2);
    expect(within(table).getByText('Type')).toBeInTheDocument();
    expect(within(table).getByText('File name')).toBeInTheDocument();
    expect(within(table).getByText('Format')).toBeInTheDocument();
    expect(within(table).getByText('Upload date')).toBeInTheDocument();
    rows.forEach(async (row, index) => {
      expect(await within(row).findByText(documentsData[index].ownerSubType)).toBeInTheDocument();
      expect(await within(row).findByText(documentsData[index].title)).toBeInTheDocument();
      expect(await within(row).findByText(documentsData[index].metadata.extension)).toBeInTheDocument();
      expect(await within(row).findByText(formatDateUTC(documentsData[index].created, DATE.YEAR_MONTH_DATE))).toBeInTheDocument();
    });
  });
});
