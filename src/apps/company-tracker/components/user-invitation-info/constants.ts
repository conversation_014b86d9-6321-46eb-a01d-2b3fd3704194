export enum OnboardingEmailMessage {
  AutoReminderNotElapsed = 'You can re-send an invite 8 days after the initial invite to allow the following auto-reminders: one at 24 hours, one at 7 days.',
  EmailDeliveryFailure = 'We couldn\'t deliver your reminder. The recipient\'s email address may be incorrect or no longer active.',
  CooldownPeriodActive = 'This invite has been re-sent. You may resend again after 1 hour has passed (to avoid flagging spam).',
  ResendInvitation = 'Resend invite',
}
