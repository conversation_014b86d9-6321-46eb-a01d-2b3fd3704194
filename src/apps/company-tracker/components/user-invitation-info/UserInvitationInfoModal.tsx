import { useGetOnboardingEmailsQuery, useResendOnboardingEmailMutation } from '@api/bulk-onboarding-import';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import {
  EmailDeliveryType,
  eventLabelMap,
  EmailDeliverySubType,
  ObNotificationCode,
  OnboardingEmailTransaction,
} from '@g17eco/types/onboarding';
import { skipToken } from '@reduxjs/toolkit/query';
import { DATE, formatDateUTC } from '@utils/date';
import { capitaliseFirstLetter } from '@utils/index';
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import { OnboardingEmailMessage } from './constants';
import { QueryWrapper } from '@components/query/QueryWrapper';

interface Props {
  initiativeId: string;
  onboardingId: string | undefined;
  toggle: () => void;
}

const getResendInformation = (transactions?: OnboardingEmailTransaction[]) => {
  if (!transactions || transactions.length === 0) {
    return {
      tooltip: OnboardingEmailMessage.AutoReminderNotElapsed,
      disabled: true,
    };
  }

  const hasSentAllReminders = [ObNotificationCode.FirstReminder, ObNotificationCode.FinalReminder].every((event) =>
    transactions.some((item) => item.event === event),
  );

  if (!hasSentAllReminders) {
    return {
      tooltip: OnboardingEmailMessage.AutoReminderNotElapsed,
      disabled: true,
    };
  }

  const containHardBounce = transactions.some(
    (item) => item.type === EmailDeliveryType.Bounce && item.subType === EmailDeliverySubType.Permanent,
  );

  if (containHardBounce) {
    return {
      tooltip: OnboardingEmailMessage.EmailDeliveryFailure,
      disabled: true,
    };
  }

  const latestManualReminder = [...transactions]
    .reverse()
    .find((item) => item.event === ObNotificationCode.ManualReminder && item.createdAt);

  const hasSentReminderRecently =
    !!latestManualReminder?.createdAt &&
    new Date().getTime() - new Date(latestManualReminder.createdAt).getTime() < 60 * 60 * 1000;

  if (hasSentReminderRecently) {
    return {
      tooltip: OnboardingEmailMessage.CooldownPeriodActive,
      disabled: true,
    };
  }

  return {
    tooltip: OnboardingEmailMessage.ResendInvitation,
    disabled: false,
  };
};

export const UserInvitationInfoModal = ({ initiativeId, onboardingId, toggle }: Props) => {
  const query = useGetOnboardingEmailsQuery(onboardingId ? { onboardingId, initiativeId } : skipToken);
  const [resendOnboardingEmail, { isLoading }] = useResendOnboardingEmailMutation();

  const { tooltip, disabled: resendDisabled } = getResendInformation(query.data);

  const columns: ColumnDef<OnboardingEmailTransaction>[] = [
    {
      header: 'Event',
      cell: (c) => eventLabelMap[c.row.original.event],
    },
    {
      header: 'Email status',
      accessorKey: 'type',
      cell: (c) => capitaliseFirstLetter(c.row.original.type ?? ''),
    },
    {
      header: 'Created at',
      cell: (c) => {
        const { createdAt } = c.row.original;
        return createdAt ? formatDateUTC(createdAt, DATE.DEFAULT_SPACES_WITH_TIME) : '';
      },
    },
  ];

  const handleResend = () => {
    if (!onboardingId) {
      return;
    }
    resendOnboardingEmail({ onboardingId, initiativeId });
  };

  return (
    <Modal isOpen={!!onboardingId} toggle={toggle} backdrop='static' returnFocusAfterClose={false} size='lg'>
      <ModalHeader toggle={toggle}>Invite info</ModalHeader>
      <ModalBody>
        <QueryWrapper query={query} onSuccess={(data) => <Table columns={columns} data={data ?? []} pageSize={10} />} />
      </ModalBody>
      <ModalFooter>
        <div className='w-100 d-flex justify-content-end mx-2'>
          <Button onClick={toggle} disabled={isLoading} className='me-2'>
            Cancel
          </Button>
          <SimpleTooltip text={tooltip}>
            <Button color='primary' onClick={handleResend} disabled={resendDisabled || isLoading}>
              Send again
            </Button>
          </SimpleTooltip>
        </div>
      </ModalFooter>
    </Modal>
  );
};
