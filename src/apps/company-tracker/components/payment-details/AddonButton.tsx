/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import React, { ReactNode } from 'react';
import { Button, ButtonProps } from 'reactstrap';
import { FeatureCode } from '@g17eco/core';
import { RootInitiativeData } from '../../../../types/initiative';
import { AppConfig } from '../../../../types/app';
import { useToggle } from '../../../../hooks/useToggle';
import { useGetUpgradeDetailsQuery } from '../../../../api/initiatives';
import { QueryWrapper } from '../../../../components/query/QueryWrapper';
import { SubscriptionService } from '../../../../services/SubscriptionService';
import { UpgradeProps } from '../../company-tracker-types';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

interface Props extends Pick<ButtonProps, 'color' | 'size' | 'outline'>{
  featureCode: FeatureCode;
  rootOrg: RootInitiativeData;
  appConfig: AppConfig | undefined;
  children: ReactNode;
  TrialModalComponent: React.ElementType<{ toggle: () => void }>;
  UpgradeModal: React.ElementType<UpgradeProps>;
}

export const AddonButton = (props: Props) => {

  const {
    rootOrg,
    appConfig,
    featureCode,
    UpgradeModal,
    TrialModalComponent,
    color = 'link-secondary',
    size,
    outline,
  } = props;

  const shouldAddTrialMethod = SubscriptionService.shouldAddTrialPaymentMethod({ appConfig, organization: rootOrg });
  const [isOpen, toggle] = useToggle(false);

  const canUpgrade = SubscriptionService.hasUpgradeableSub(appConfig, rootOrg);
  const query = useGetUpgradeDetailsQuery(
    { initiativeId: rootOrg._id, featureCode },
    // issue[GU-5237]: appConfig changes when this fetch for upgrade details
    // that cause infinite refetch when refetchOnMountOrArgChange: true
    // instead refetch when this is mount and after every 10s
    { refetchOnMountOrArgChange: true, skip: shouldAddTrialMethod || !canUpgrade },
  );

  if (!canUpgrade) {
    return null;
  }

  const btn = (
    <Button color={color} size={size} outline={outline} onClick={toggle} className={'mr-2'}>
      {props.children}
    </Button>
  );

  if (shouldAddTrialMethod) {
    return (
      <>
        {btn}
        {isOpen ? <TrialModalComponent toggle={toggle} /> : null}
      </>
    )
  }

  return (
    <>
      <QueryWrapper
        onLoading={() => <LoadingPlaceholder height={24} isLoading={true} className={'d-inline-block mr-2'} width={100} />}
        query={query}
        onError={() => null}
        onSuccess={(data) => {
          if (!data.canUpgrade) {
            return null;
          }

          return (
            <>
              {btn}
              {isOpen ? <UpgradeModal
                upgradeDetails={data}
                toggle={toggle}
                rootOrg={rootOrg} /> : null}
            </>
          );
        }} />
    </>
  )
}
