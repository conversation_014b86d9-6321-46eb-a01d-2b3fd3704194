/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { RootInitiativeData } from '../../../../types/initiative';
import { useLocation } from 'react-router-dom';
import { addSiteAlert, SiteAlertColors } from '../../../../slice/siteAlertsSlice';
import { useAppDispatch } from '../../../../reducers';
import { useAddPaymentDetailsMutation } from '../../../../api/initiatives';
import { useToggle } from '../../../../hooks/useToggle';
import RequestDemoModal from '../../../../components/request-demo-modal';
import { AppConfig } from '../../../../types/app';
import { BasicAlert } from '@g17eco/molecules/alert';

const INFO = 'To purchase additional seats, first add credit card details or another payment method.'
interface Props {
  rootOrg: RootInitiativeData;
  appConfig: AppConfig | undefined;
  canUpgrade: boolean;
}

export const AddPaymentDetailsAlert = ({ rootOrg, appConfig, canUpgrade }: Props) => {

  const location = useLocation();
  const dispatch = useAppDispatch();
  const [addDetails] = useAddPaymentDetailsMutation()
  const [isOpen, toggle] = useToggle(false);

  if (!canUpgrade) {
    return (
      <>
        <BasicAlert type={'warning'}>
          <i className='fas fa-info-circle mr-2' />{INFO} <span style={{ cursor: 'pointer' }} onClick={toggle}>
            <u>Contact us to find out more and subscribe.</u>
          </span>
        </BasicAlert>
        <RequestDemoModal productCode={appConfig?.productCode} isOpen={isOpen} title={'Contact us'} toggle={toggle} />
      </>
    )
  }

  return (
    <BasicAlert type={'warning'}>
      <i className='fas fa-info-circle mr-2' />{INFO} <span
      style={{ cursor: 'pointer' }}
      onClick={(e) => {
        e.preventDefault();
        addDetails({ initiativeId: rootOrg._id, returnUrl: location.pathname })
          .unwrap()
          .then(s => {
            window.location.href = s.url;
          })
          .catch((e: Error) => {
            dispatch(addSiteAlert({
              content: e.message,
              color: SiteAlertColors.Danger,
            }))
          })
      }}>
      <u>Add payment details.</u>
    </span>
    </BasicAlert>
  )
}
