/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { PreviewUpgradeResult } from '@g17eco/types/subscriptions';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { DATE, formatDateUTC } from '@utils/date';

type PreviewDetails = { isSuccess: false } | { isSuccess: true, data: PreviewUpgradeResult }

export const getPreviewDetails = (previewResponse: PreviewDetails) => {

  if (!previewResponse.isSuccess) {
    return {
      total: '-',
      dueDate: <>today</>,
    }
  }

  const { total, status } = previewResponse.data;

  return {
    total: total,
    dueDate: status === 'trialing' ?
      <>at the end of the trial</> :
      <>today</>
  };
};


interface InvoiceDetailsParams {
  previewResponse: PreviewDetails;
}

interface GroupedLine {
  lines: PreviewUpgradeResult['lines'],
  periodStart: string;
  periodEnd: string;
}

const groupByDate = (lines: PreviewUpgradeResult['lines']) => {

  return lines.reduce((acc, line) => {
    const start = formatDateUTC(line.periodStart, DATE.DATE_PICKER);
    const end = formatDateUTC(line.periodEnd, DATE.DATE_PICKER);
    const key = `${start} - ${end}`;

    if (!acc[key]) {
      acc[key] = { lines: [line], periodStart: line.periodStart, periodEnd: line.periodEnd };
      return acc;
    }

    acc[key].lines.push(line);
    return acc;
  }, {} as Record<string, GroupedLine>);
}

export const InvoiceDetails = ({ previewResponse }: InvoiceDetailsParams) => {

  if (!previewResponse.isSuccess) {
    return null;
  }

  const { lines = [], total, currencySymbol } = previewResponse.data;

  const groupedLines = groupByDate(lines);

  return (
    <CollapsePanel collapsed={true}>
      <CollapseButton><span>View More</span></CollapseButton>
      <CollapseContent className='mt-2'>
        <div>
          {
            Object.entries(groupedLines).map(([key, group]) => {
              return (
                <div key={key}>
                  <div className={'d-flex justify-content-between'}>
                    <h6>{formatDateUTC(group.periodStart)} - {formatDateUTC(group.periodEnd)}</h6>
                  </div>
                  {group.lines.map((line) => {
                    return (
                      <div key={line.id} className={'d-flex justify-content-between'}>
                        <p className={'flex-grow-1'}>{line.description}</p>
                        <p>{line.currencySymbol}{line.amount}</p>
                      </div>
                    )
                  })}
                </div>
              )
            })
          }
          <div className={'text-right text-lg'}>
            {currencySymbol}{total}
          </div>
          <div className={'text-xs text-right'}>
            tax included in price
          </div>
        </div>
      </CollapseContent>
    </CollapsePanel>
  )
}
