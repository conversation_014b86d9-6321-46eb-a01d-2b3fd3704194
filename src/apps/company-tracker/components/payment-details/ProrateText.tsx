/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PriceInterval } from '@g17eco/types/subscriptions';

interface Props {
  className?: string
  interval: PriceInterval | undefined
}


const getText = (interval: Omit<PriceInterval, 'year'> | undefined) => {
  switch (interval) {
    case 'day':
      return 'prorated this day';
    case 'week':
      return 'prorated this week';
    case 'month':
      return 'prorated this month';
    default:
      return 'prorated';
  }
}

export const ProrateText = ({ interval, className = 'text-xs mr-2' }: Props) => {
  if (interval === 'year') {
    return (
      <div className={className}>
        The amount is prorated for the remainder of this year’s billing cycle. If applicable, the full amount will be
        charged at the beginning of your next billing cycle.
      </div>
    );
  }

  // Default case for other types, even though should be month for now always
  const text = getText(interval);

  return (
    <div className={className}>
      The amount is {text}, you will be charged the full amount at the beginning of your next billing cycle.
    </div>
  );
};
