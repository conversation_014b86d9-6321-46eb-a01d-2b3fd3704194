import { render, screen } from '@testing-library/react';
import { ProrateText } from './ProrateText';

describe('ProrateText', () => {
  it('should render prorated text for year interval', () => {
    render(<ProrateText interval='year' />);
    const proratedText = screen.getByText(/The amount is prorated for the remainder of this year’s billing cycle/i);
    expect(proratedText).toBeInTheDocument();
  });

  it('should render default prorated text for other intervals', () => {
    render(<ProrateText interval='month' />);
    const proratedText = screen.getByText(/prorated this month/i);
    expect(proratedText).toBeInTheDocument();
  });
});
