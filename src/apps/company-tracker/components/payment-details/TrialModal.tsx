/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { ReactNode, useState } from 'react';
import { <PERSON><PERSON>, Modal, Modal<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap';
import { addSiteError } from '../../../../slice/siteAlertsSlice';
import { useAppDispatch } from '../../../../reducers';
import { useLocation } from 'react-router-dom';
import G17Client from '../../../../services/G17Client';
import ButtonWithLoader from '../../../../components/button/ButtonWithLoader';

interface TrialModalProps {
  initiativeId: string;
  toggle: () => void;
  children: ReactNode;
  title?: string;
}

const defaultTitle = 'Limit increase unavailable during trial.';

export const TrialModal = ({ children, title = defaultTitle, initiativeId, toggle }: TrialModalProps) => {

  const location = useLocation();
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const manageSubscription = async () => {
    setIsLoading(true);
    return G17Client.addPaymentDetails({ initiativeId, returnUrl: location.pathname })
      .then(s => {
        // Will redirect, no need to stop loading;
        window.location.href = s.url;
      })
      .catch((e: Error) => {
        dispatch(addSiteError(e));
      })
  }

  return (
    <Modal isOpen={true} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>{title}</ModalHeader>
        <ModalBody>
          {children}
        </ModalBody>
        <ModalFooter className='d-flex flex-shrink-1'>
          <Button className={'mr-3'} color='link-secondary' onClick={toggle}>
            Cancel
          </Button>
          <ButtonWithLoader loading={isLoading} color={'primary'} onClick={manageSubscription}>
            Continue
          </ButtonWithLoader>
        </ModalFooter>
    </Modal>
  )

}
