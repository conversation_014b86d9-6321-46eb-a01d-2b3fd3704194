/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { SurveyStats } from '@api/initiative-stats';
import { getFilteredUtrIds, getTaggedUtrIds } from '@components/survey/utils/getDisableUtrs';
import { TagStatus } from '@constants/status';
import { Tag } from '@g17eco/types/metricGroup';
import { UniversalTrackerBlueprintMin, UtrValueType } from '@g17eco/types/universalTracker';
import { OverridesOption } from './questions/QuestionFilter';
import {
  hasDecimalOverrides,
  hasUnitOrNumScaleOverrides,
  hasOverriddenUtrvConfig,
} from '@features/question-configuration';

// Convert to percentage
export function toPercentageProgress(status: SurveyStats['status']): SurveyStats['status'] {
  const total = status.created + status.updated + status.rejected + status.verified;
  if (total === 0) {
    return status;
  }

  return {
    created: Math.round((status.created / total) * 100),
    updated: Math.round((status.updated / total) * 100),
    rejected: Math.round((status.rejected / total) * 100),
    verified: Math.round((status.verified / total) * 100),
  };
}

export const PAGE_SIZE = 10;

export const getFilteredUtrIdsByTag = ({
  utrIds,
  tags,
  values,
}: {
  utrIds: string[];
  tags: Tag[];
  values: string[];
}) => {
  if (values.includes(TagStatus.NoTag) && values.length > 1) {
    return [];
  }

  if (!values.includes(TagStatus.NoTag)) {
    return getFilteredUtrIds(values, tags);
  }

  if (values.includes(TagStatus.NoTag) && values.length === 1) {
    const taggedUtrIds = getTaggedUtrIds(tags);
    return utrIds.filter((id) => !taggedUtrIds.includes(id));
  }

  return [];
};

export const isMatchSelectedTags = ({
  utrId,
  selectedTags,
  filteredUtrIdsByTag,
}: {
  utrId: string;
  selectedTags: string[];
  filteredUtrIdsByTag: string[];
}) => {
  if (selectedTags.length === 0) {
    return true;
  }
  return filteredUtrIdsByTag.includes(utrId);
};

const hasOverridesOption = (overrides: OverridesOption[], overridesOption: OverridesOption) => {
  return overrides.includes(overridesOption);
};

export const isMatchSelectedOverrides = ({
  utr,
  overrides,
  rootInitiativeUtrMap,
}: {
  utr: UniversalTrackerBlueprintMin;
  overrides: OverridesOption[];
  rootInitiativeUtrMap: Map<string, InitiativeUniversalTracker>;
}) => {
  if (overrides.length === 0) {
    return true;
  }
  // Conflict between no-set and other options
  if (overrides.includes(OverridesOption.NoSet) && overrides.length > 1) {
    return false;
  }

  const initiativeUtr = rootInitiativeUtrMap.get(utr._id);
  const matchParams = { initiativeUtr, valueType: utr.valueType as UtrValueType };
  const hasOverriddenUnit = hasUnitOrNumScaleOverrides({ ...matchParams, field: 'unitInput' });
  const hasOverriddenNumberScale = hasUnitOrNumScaleOverrides({ ...matchParams, field: 'numberScaleInput' });
  const hasOverriddenDecimal = hasDecimalOverrides(matchParams);
  const hasOverriddenEvidence = hasOverriddenUtrvConfig({
    ...matchParams,
    field: 'evidenceRequired',
  });
  const hasOverriddenVerification = hasOverriddenUtrvConfig({
    ...matchParams,
    field: 'verificationRequired',
  });
  const hasOverriddenExplanation = hasOverriddenUtrvConfig({
    ...matchParams,
    field: 'noteRequired',
  });
  const hasOverriddenPrivacy = hasOverriddenUtrvConfig({
    ...matchParams,
    field: 'isPrivate',
  });
  // Only no-set option selected
  if (overrides.includes(OverridesOption.NoSet) && overrides.length === 1) {
    const noSetMatch =
      !hasOverriddenUnit &&
      !hasOverriddenNumberScale &&
      !hasOverriddenDecimal &&
      !hasOverriddenEvidence &&
      !hasOverriddenVerification &&
      !hasOverriddenExplanation &&
      !hasOverriddenPrivacy;
    return !initiativeUtr || noSetMatch;
  }

  if (!initiativeUtr) {
    return false;
  }
  const matchUnitOverrides = hasOverridesOption(overrides, OverridesOption.Unit) ? hasOverriddenUnit : true;
  const matchNumberScaleOverrides = hasOverridesOption(overrides, OverridesOption.NumberScale)
    ? hasOverriddenNumberScale
    : true;
  const matchDecimalOverrides = hasOverridesOption(overrides, OverridesOption.Decimal) ? hasOverriddenDecimal : true;
  const matchEvidenceOverrides = hasOverridesOption(overrides, OverridesOption.Evidence)
    ? hasOverriddenEvidence
    : true;
  const matchVerificationOverrides = hasOverridesOption(overrides, OverridesOption.Verification)
    ? hasOverriddenVerification
    : true;
  const matchFurtherExplanationOverrides = hasOverridesOption(overrides, OverridesOption.FurtherExplanation)
    ? hasOverriddenExplanation
    : true;
  const matchPrivacyOverrides = hasOverridesOption(overrides, OverridesOption.MetricPrivacy)
    ? hasOverriddenPrivacy
    : true;

  return (
    matchUnitOverrides &&
    matchNumberScaleOverrides &&
    matchDecimalOverrides &&
    matchEvidenceOverrides &&
    matchVerificationOverrides &&
    matchFurtherExplanationOverrides &&
    matchPrivacyOverrides
  );
};
