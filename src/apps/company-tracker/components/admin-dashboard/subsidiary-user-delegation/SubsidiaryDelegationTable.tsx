/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React, { useCallback, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { But<PERSON> } from 'reactstrap';
import { DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { SubsidiarySelectionTable } from '@components/downloads/partials/SubsidiarySelectionTable';
import {
  SurveySelectionTable,
  TableNodeWithSurveyIds,
} from '@components/downloads/partials/SurveySelectionTable';
import { Loader } from '@g17eco/atoms/loader';
import { ROUTES } from '@constants/routes';
import { generateUrl, generateUrlWithQuery } from '@routes/util';
import { DelegationQuestionView } from './DelegationQuestionView';
import { useDelegateToUserMutation, UserDelegationData } from '@api/admin-dashboard';
import { useAppDispatch } from '@reducers/index';
import { addSiteAlert, addSiteError } from '../../../../../slice/siteAlertsSlice';
import { initiativeApi } from '@api/initiative-stats';
import { SurveyType } from '@g17eco/types/survey';
import { getSelectedSurveyNodeIds } from '@components/downloads/util/treeNavigation';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';

enum ViewMode {
  Subsidiary = 'subsidiary',
  Survey = 'survey',
  Question = 'question',
}

export enum SurveyFilters {
  SurveyType = 'surveyType',
  Period = 'period',
  Month = 'month',
  Year = 'year',
}

interface TableFooterProps {
  initiativeId: string;
  isDisabled: boolean;
  handleSubmit: () => void;
  handleCancel: () => void;
  submitText: string;
}

const getSubtitle = (view: ViewMode) => {
  switch (view) {
    case ViewMode.Subsidiary:
      return `Select from the below list of subsidiaries, the options that have a ${SURVEY.SINGULAR}, scope or ${QUESTION.SINGULAR} you would like to delegate to this user.`;
    case ViewMode.Survey:
      return `Select from the below list of ${SURVEY.PLURAL}, the options that have a scope ${PACK.SINGULAR} or ${QUESTION.SINGULAR} you would like to delegate to this user.`;
    case ViewMode.Question:
      return `Select from the below list of ${QUESTION.PLURAL}, the ones you would like to delegate to this user.`;
    default:
      return undefined;
  }
};

const TableFooter = ({ isDisabled, handleSubmit, submitText, handleCancel }: TableFooterProps) => {
  return (
    <div className='mt-4 text-right'>
      <Button color='link-secondary' onClick={handleCancel} className='mr-4'>
        <i className='fa-light fa-angle-left mr-2' />
        Previous page
      </Button>
      <Button disabled={isDisabled} size='lg' color='primary' onClick={handleSubmit}>
        {submitText}
      </Button>
    </div>
  );
};

interface Props {
  initiativeId: string;
  userId: string;
  fullName: string;
}

export const SubsidiaryDelegationTable = ({ initiativeId, userId, fullName }: Props) => {
  const dispatch = useAppDispatch();
  const [handler] = useDelegateToUserMutation();

  const [view, setView] = useState<ViewMode>(ViewMode.Subsidiary);
  const [delegation, setDelegation] = useState<UserDelegationData>({
    initiativeIds: [],
    datePeriods: [],
    stakeholderUtrIds: [],
    verifierUtrIds: [],
  });
  const history = useHistory();

  const onSubmit = async () => {
    return handler({ ...delegation, userId, initiativeId })
      .unwrap()
      .then(() => {
        dispatch(addSiteAlert({ timeout: 5000, content: 'Delegation completed' }));
        dispatch(initiativeApi.util.invalidateTags(['user-stats', 'stats-survey-users']));
        history.push(generateUrlWithQuery(ROUTES.ADMIN_DASHBOARD, { initiativeId }, { userId }));
      })
      .catch((e) => {
        dispatch(addSiteError(e));
      });
  };
  const handleSelectSubsidiaries = (ids: string[]) => {
    setDelegation((prev) => ({ ...prev, initiativeIds: ids }));
  };
  const handleSelectedNodes = useCallback((selectedNodes?: TableNodeWithSurveyIds[]) => {
    if (!selectedNodes) {
      return;
    }
    const datePeriods = selectedNodes.map((node) => ({ period: node.period, effectiveDate: node.effectiveDate }));
    setDelegation((prev) => ({ ...prev, datePeriods }));
  }, []);

  const renderByView = () => {
    const hasSelectedSubsidiaries = delegation.initiativeIds.length > 0;
    const hasSelectedPeriods = delegation.datePeriods.length > 0;
    const hasUtrsSelected = delegation.verifierUtrIds.length > 0 || delegation.stakeholderUtrIds.length > 0;
    const allowedToSubmit = hasSelectedSubsidiaries && hasSelectedPeriods && hasUtrsSelected;

    switch (view) {
      case ViewMode.Subsidiary:
        return (
          <>
            <SubsidiarySelectionTable
              initiativeId={initiativeId}
              handleSetSelectedIds={handleSelectSubsidiaries}
              selectedIds={delegation.initiativeIds}
            />
            <TableFooter
              isDisabled={!hasSelectedSubsidiaries}
              initiativeId={initiativeId}
              handleSubmit={() => setView(ViewMode.Survey)}
              submitText={'Select subsidiaries'}
              handleCancel={() => history.push(generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId }))}
            />
          </>
        );
      case ViewMode.Survey:
        if (!hasSelectedSubsidiaries) {
          return <Loader relative={true} />;
        }

        return (
          <>
            <SurveySelectionTable
              initiativeId={initiativeId}
              initiativeIds={delegation.initiativeIds ?? []}
              handleSelectedNodes={handleSelectedNodes}
              enabledFilters={[SurveyFilters.Period, SurveyFilters.Month, SurveyFilters.Year]}
              isMulti
              defaultFilters={{ surveyType: [SurveyType.Default] }}
              selectedNodeIds={getSelectedSurveyNodeIds(delegation.datePeriods)}
            />
            <TableFooter
              isDisabled={!hasSelectedPeriods}
              initiativeId={initiativeId}
              handleSubmit={() => setView(ViewMode.Question)}
              submitText={`Select ${SURVEY.PLURAL}`}
              handleCancel={() => setView(ViewMode.Subsidiary)}
            />
          </>
        );
      case ViewMode.Question:
        if (!hasSelectedSubsidiaries || !hasSelectedPeriods) {
          return <Loader relative={true} />;
        }

        return (
          <>
            <DelegationQuestionView
              initiativeId={initiativeId}
              delegation={delegation}
              setAssignedUtrs={setDelegation}
              userId={userId}
            />
            <TableFooter
              isDisabled={!allowedToSubmit}
              initiativeId={initiativeId}
              handleSubmit={onSubmit}
              submitText={`Delegate ${QUESTION.PLURAL} to user`}
              handleCancel={() => setView(ViewMode.Survey)}
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <DashboardSectionTitle title={<>Delegate to {fullName}</>} subtitle={getSubtitle(view)} />
      <DashboardSection>{renderByView()}</DashboardSection>
    </>
  );
};
