/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { useAppSelector } from '@reducers/index';
import { Loader } from '@g17eco/atoms/loader';
import { SurveyGroupContainer, SetAssignedUtrsFn } from '@components/survey-group/SurveyGroupContainer';
import { useUtrSurveyGroupsOptionalValue } from '@utils/survey';
import { useGetInitiativeBlueprintQuestionsQuery, useGetSurveyGroupsQuery, UserDelegationData } from '@api/admin-dashboard';
import { generateFlexSearchMap } from '@selectors/blueprint';
import { BasicAlert } from '@g17eco/molecules/alert';

interface Props {
  initiativeId: string;
  userId: string;
  delegation: UserDelegationData
  setAssignedUtrs: SetAssignedUtrsFn;
}

export const DelegationQuestionView = (props: Props) => {
  const { initiativeId, delegation, userId } = props;
  const {
    data,
    isError,
    error,
    isFetching,
  } = useGetSurveyGroupsQuery({
    userId,
    initiativeId,
    datePeriods: delegation.datePeriods,
    initiativeIds: delegation.initiativeIds,
  });

  const {
    data: blueprintQuestions,
    isFetching: isFetchingBluesprintQuestions,
  } = useGetInitiativeBlueprintQuestionsQuery({initiativeId})

  const overviewMode = useAppSelector((state) => state.surveySettings.overviewMode);
  const surveyGroups = useUtrSurveyGroupsOptionalValue(overviewMode, data);

  const searchIndex = generateFlexSearchMap(blueprintQuestions);

  if (isError) {
    return <BasicAlert type={'danger'}>{error.message}</BasicAlert>;
  }

  if (isFetching || isFetchingBluesprintQuestions) {
    return <Loader relative={true} />;
  }

  return (
    <SurveyGroupContainer
      stakeholderUtrIds={delegation.stakeholderUtrIds}
      verifierUtrIds={delegation.verifierUtrIds}
      setAssignedUtrsFn={props.setAssignedUtrs}
      initiativeId={initiativeId}
      surveyGroups={surveyGroups}
      searchIndex={searchIndex}
    />
  );
}
