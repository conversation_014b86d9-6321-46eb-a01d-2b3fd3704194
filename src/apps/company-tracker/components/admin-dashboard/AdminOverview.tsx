/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { useEffect, useMemo, useState } from 'react';
import { Loader } from '@g17eco/atoms/loader';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { TIME_RANGE_YEARLY, getUTCDateRange } from '@utils/date';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { useParams } from 'react-router';
import { AdminBreadcrumbs } from '@routes/admin-dashboard/AdminBreadcrumbs';
import { Button, Progress } from 'reactstrap';
import { Redirect, useHistory, useRouteMatch } from 'react-router-dom';
import { initiativeApi, useGetStatsQuery, useGetUserStatsQuery } from '@api/initiative-stats';
import { loadInitiativeById } from '@actions/initiative';
import { naturalSort } from '@utils/index';
import { ProgressRow } from './ProgressRow';
import { toPercentageProgress } from './util';
import { isUserManagerByInitiativeId } from '@selectors/user';
import G17Client from '@services/G17Client';
import { TimeRangeSelector, getDateRange } from '@g17eco/molecules/time-range-selector';
import { DateRangeType } from '@g17eco/types/common';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { isSingleOrg } from '@selectors/initiative';
import { QuestionManagement } from './questions/QuestionManagement';
import { QUESTION } from '@constants/terminology';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { SurveyStatsTable } from '@features/admin-dashboard/tables/SurveyStatsTable';
import { UserStatsTable } from '@features/admin-dashboard/tables/UserStatsTable';

interface RouteParams {
  initiativeId: string,
}

export const AdminOverview = () => {
  const { url } = useRouteMatch();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const { initiativeId } = useParams<RouteParams>();

  const limitReportingLevels = useAppSelector(FeaturePermissions.getLimitReportingLevels);
  const isSingleOrganisation = useAppSelector(isSingleOrg);
  const initiativeState = useAppSelector((state) => state.initiative);
  const [dateRange, setDateRange] = useState<DateRangeType>(getDateRange(TIME_RANGE_YEARLY));
  const query = useMemo(() => new URLSearchParams(history.location.search), [history.location.search])
  const userId = query.get('userId');
  const isUserAdmin = useAppSelector((state) => isUserManagerByInitiativeId(state, initiativeId));

  const utcDateRange = getUTCDateRange(dateRange);
  const { isLoading, data: surveyListStats = [] } = useGetStatsQuery({ initiativeId, ...utcDateRange, });

  const { isLoading: isUserStatsLoading } = useGetUserStatsQuery({ initiativeId, ...utcDateRange, })

  const handleReload = async () => {
    dispatch(initiativeApi.util.invalidateTags(['stats']))
  }

  useEffect(() => {
    dispatch(loadInitiativeById(initiativeId));
  }, [dispatch, initiativeId]);

  if (!initiativeState.loaded || isLoading || isUserStatsLoading) {
    return <Loader />;
  }

  if (limitReportingLevels === 1 && isSingleOrganisation) {
    const redirectUrl = generateUrl(ROUTES.ADMIN_SETTINGS, {
      initiativeId: initiativeId,
    });
    return <Redirect to={redirectUrl} />;
  }

  const initiative = initiativeState.data;
  const initiativeStatuses = surveyListStats.find(i => i._id === initiativeId)?.status;
  const progress = initiativeStatuses ? toPercentageProgress(initiativeStatuses) : undefined;

  const directChildInitiatives = surveyListStats
    .filter(i => i.parentId === initiativeId)
    .sort((a, b) => naturalSort(a.name, b.name));

  const handleDownload = () => {
    G17Client.downloadManagerInitiativeStats(initiative._id, utcDateRange);
  }

  return (
    <div className='admin-dashboard'>
      <Dashboard>
        <DashboardRow>
          <AdminBreadcrumbs breadcrumbs={[{ label: 'Admin Dashboard' }]} initiativeId={initiativeId} />
        </DashboardRow>

        <DashboardSectionTitle
          title={
            <>
              Admin dashboard: Overview
              <Button color='link' className='ml-2'>
                <i className='fas fa-file-excel' onClick={() => handleDownload()}></i>
              </Button>{' '}
            </>
          }
          buttons={[
            <TimeRangeSelector
              key='time-range-selector'
              dateRange={dateRange}
              onChangeDateRange={(range) => setDateRange(range)}
              styleProps={{ isTransparent: true, isFlexibleSize: true }}
            />,
          ]}
        />

        {progress ? <ProgressRow title={initiative.name} progress={progress} style={{ height: '2rem' }} /> : null}

        <DashboardSection padding={1}>
          <div className={'sub-reporting-level-list'}>
            {directChildInitiatives.map(({ _id, name, status }) => {
              const counts = toPercentageProgress(status);

              return (
                <div key={_id} className={'mb-4'}>
                  <h6>{name}</h6>
                  <Progress className={'w-100'} style={{ height: '1rem' }} multi>
                    <Progress className={'background-ThemeAccentExtradark'} bar value={counts.updated}>
                      {counts.updated}%
                    </Progress>
                    <Progress className={'background-ThemeDangerMedium'} bar value={counts.rejected}>
                      {counts.rejected}%
                    </Progress>
                    <Progress className={'background-ThemeSuccessMedium'} bar value={counts.verified}>
                      {counts.verified}%
                    </Progress>
                    <Progress className={'background-chateauGrey'} bar value={counts.created}>
                      {counts.created}%
                    </Progress>
                  </Progress>
                </div>
              );
            })}
          </div>
        </DashboardSection>

        <SurveyStatsTable
          initiativeId={initiativeId}
          isUserAdmin={isUserAdmin}
          dateRange={dateRange}
          handleReload={handleReload}
          url={url}
        />
        <UserStatsTable initiativeId={initiativeId} dateRange={dateRange} url={url} userId={userId} />

        <DashboardSectionTitle
          title={QUESTION.CAPITALIZED_PLURAL}
          buttons={[
            <Button key='view-all-questions' className='py-1' onClick={() => history.push(`${url}/questions`)}>
              <span className='lh-base'>View all {QUESTION.PLURAL}</span>
            </Button>,
          ]}
        />
        <DashboardSection>
          <QuestionManagement pageSize={10} initiativeId={initiativeId} />
        </DashboardSection>
      </Dashboard>
    </div>
  );
};
