/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { CSSProperties } from 'react';
import { DashboardRow } from '@components/dashboard';
import { Progress } from 'reactstrap';
import { StatusStats } from '@api/initiative-stats';

interface ProgressRowProps {
  title: string,
  progress: StatusStats,
  style?: CSSProperties;
}

export const ProgressRow = ({ title, progress, style }: ProgressRowProps) => {
  return (
    <DashboardRow className={'mb-5'} headingStyle={6} title={title} buttons={[
      <span className={'mr-3'} key={'created'}>
        <i className={'fa-solid fa-square mr-1 text-ThemeTextLight'} />Unanswered
      </span>,
      <span className={'mr-3'} key={'updated'}>
        <i className={'fa-solid fa-square mr-1 text-ThemeAccentMedium'} />Answered
      </span>,
      <span className={'mr-3'} key={'rejected'}>
        <i className={'fa-solid fa-square mr-1 text-ThemeDangerMedium'} />Rejected
      </span>,
      <span className={'mr-3'} key={'verified'}>
          <i className={'fa-solid fa-square mr-1 text-ThemeSuccessMedium'} />Verified
      </span>,
    ]}>
      <Progress className={'mx-2 w-100 mt-2'} style={style} multi>
        <Progress className={'background-ThemeAccentMedium'} bar value={progress.updated}>{progress.updated}%</Progress>
        <Progress className={'background-ThemeSuccessMedium'} bar value={progress.verified}>{progress.verified}%</Progress>
        <Progress className={'background-ThemeDangerMedium'} bar value={progress.rejected}>{progress.rejected}%</Progress>
        <Progress className={'background-ThemeTextLight'} bar value={progress.created}>{progress.created}%</Progress>
      </Progress>
    </DashboardRow>
  )
};
