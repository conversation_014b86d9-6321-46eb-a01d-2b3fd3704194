import Select from 'react-select';
import {
  OnChangeReportingProps,
  OPTIONS,
} from '@components/survey-configuration/partials/SurveyReportingSettings';

interface ReportingDateProps {
  onChange: (props: OnChangeReportingProps) => void;
  selectedYear?: number;
  selectedMonth?: number;
}

/** @deprecated not used? **/
export const ReportingDate = (props: ReportingDateProps) => {
  return (
    <div className='row mt-2'>
      <div className='col-12 col-md-3'>
        <Select
          placeholder={'Select Month'}
          className='w-100'
          onChange={(option) => props.onChange({ option, code: 'month' })}
          isSearchable={true}
          options={OPTIONS.MONTH}
          value={props.selectedMonth !== undefined ? OPTIONS.MONTH.find((m) => m.value === props.selectedMonth) : null}
        />
      </div>
      <div className='col-12 col-md-3'>
        <Select
          placeholder={'Select Year'}
          className='w-100'
          onChange={(option) => props.onChange({ option, code: 'year' })}
          isSearchable={true}
          options={OPTIONS.LAST_YEAR}
          value={
            props.selectedYear !== undefined ? OPTIONS.LAST_YEAR.find((y) => y.value === props.selectedYear) : null
          }
        />
      </div>
    </div>
  );
};
