/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useRouteMatch } from 'react-router-dom';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { ViewScopeType, ViewMap, ViewValues } from '@components/survey-overview-sidebar/viewOptions';

interface Props {
  view: ViewValues;
  onChangeView: (view: ViewValues) => void;
}

const getViewOptions = () => {
  return [ViewValues.StandardsAndFrameworks, ViewValues.Sdg, ViewValues.Materiality, ViewValues.Custom].map(
    (k: ViewValues) => ViewMap[k],
  );
};

export const ViewDropdown = ({ view, onChangeView }: Props) => {

  const viewOptions: ViewScopeType = [];

  const route = useRouteMatch<{ page: string; initiativeId: string }>();
  if (route?.params?.page === 'delegation') {
    viewOptions.push({
      value: ViewValues.Users,
      label: ViewMap[ViewValues.Users].label,
    });
  }

  getViewOptions().forEach((v) => viewOptions.push(v));

  viewOptions.push({
    value: ViewValues.AssignedMetrics,
    label: 'Assigned Metrics',
  });

  const value = viewOptions.find((o) => o.value === view);

  return (
    <SelectFactory<ViewValues>
      selectType={SelectTypes.SingleSelect}
      placeholder='Select view'
      options={viewOptions}
      value={value}
      onChange={(selection) => (selection ? onChangeView(selection.value) : undefined)}
      isSearchable={false}
      backgroundColor={'var(--theme-BgExtralight)'}
    />
  );
};
