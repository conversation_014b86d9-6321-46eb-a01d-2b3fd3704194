/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */


import { Modal, ModalBody, ModalHeader } from 'reactstrap';
import React from 'react';
import { CustomMetricUpgradeFlow } from './CustomMetricUpgradeFlow';
import { UpgradeProps } from '../../company-tracker-types';

/**
 *  Simple wrapper around upgrade flow in modal
 */
export const CustomMetricUpgradeModal = (props: UpgradeProps) => {

  const {
    upgradeDetails,
    rootOrg,
    toggle,
    title = 'Buy custom metrics x10',
  } = props


  return (
    <Modal isOpen={true} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>
        {title}
      </ModalHeader>
      <ModalBody>
        <CustomMetricUpgradeFlow
          title={title}
          upgradeDetails={upgradeDetails}
          rootOrg={rootOrg}
          onCancel={toggle}
          onSuccess={toggle} />
      </ModalBody>
    </Modal>
  );
};
