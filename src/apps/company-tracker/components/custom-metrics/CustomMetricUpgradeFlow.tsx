/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import { useEffect, useState } from 'react';
import { Button } from 'reactstrap';
import { SubmitButton } from '@components/button/SubmitButton';
import { RootInitiativeData } from '@g17eco/types/initiative';
import { BasicContainer } from '@g17eco/atoms/basic-container';
import { CounterWidget } from '@g17eco/atoms/counter-widget';
import IconButton from '../../../../components/button/IconButton';
import { usePreviewPlanMutation, useUpgradePlanMutation } from '@api/initiatives';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { useAppDispatch } from '../../../../reducers';
import { QueryError } from '@components/query/QueryError';
import { CanUpgradeDetails } from '@g17eco/types/subscriptions';
import { FeatureCode } from '@g17eco/core';
import { reloadInitiativeTree } from '@actions/index';
import { useDebouncedCallback } from 'use-debounce';
import { getPreviewDetails, InvoiceDetails } from '../payment-details/InvoicePreview';
import { ProrateText } from '../payment-details/ProrateText';
import { PACK } from '@constants/terminology';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';


export interface UpgradeFlowProps {
  rootOrg: RootInitiativeData;
  onSuccess: () => void;
  onCancel: () => void;
  minSeats?: number;
  maxSeats?: number;
  upgradeDetails: CanUpgradeDetails;
  defaultUnits?: number;
  title: string;
}

export function CustomMetricUpgradeFlow(props: Readonly<UpgradeFlowProps>) {

  const featureCode = FeatureCode.CustomMetrics;
  const {
    rootOrg,
    onSuccess,
    onCancel,
    upgradeDetails,
    minSeats = 1,
    maxSeats = 10,
    defaultUnits = 1,
    title,
  } = props;

  const [metricBundle, setMetricBundle] = useState(defaultUnits);

  const [onUpgrade, { isError, error }] = useUpgradePlanMutation();
  const [onPreview, previewResponse] = usePreviewPlanMutation();
  const { addSiteAlert } = useSiteAlert();
  const dispatch = useAppDispatch();

  const interval = upgradeDetails.productDetails.interval;

  const previewInvoice = useDebouncedCallback((units: number) => {
    onPreview({
      interval,
      additionalUnits: units,
      initiativeId: rootOrg._id,
      featureCode: featureCode
    });
  }, 1000);

  useEffect(() => {
    onPreview({ interval, additionalUnits: defaultUnits, initiativeId: rootOrg._id, featureCode: featureCode });
  }, [defaultUnits, featureCode, interval, onPreview, rootOrg._id])


  const handleClick = async () => {
    await onUpgrade({
      interval,
      additionalUnits: metricBundle,
      initiativeId: rootOrg._id,
      featureCode: FeatureCode.CustomMetrics
    }).unwrap().then(() => {
      dispatch(reloadInitiativeTree())
      addSiteAlert({
        content: `Success. Additional ${metricBundle} custom metrics were added`
      })
      onSuccess();
    }).catch(() => {
    })
  };



  const onChangeUnit = (changeUnit: number) => {
    const units = metricBundle + changeUnit;
    setMetricBundle(units)
    previewInvoice(units)
  }

  const { price, currencySymbol } = upgradeDetails.productDetails;

  const isValid = metricBundle > 0;
  const extraCost = metricBundle * price;

  const { currentUsage, limit } = upgradeDetails.usageDetails
  const { total, dueDate } = getPreviewDetails(previewResponse);

  const additionalUnits = metricBundle * 10;

  return (
    <div className={'upgrade-flow-container'}>
      {isError && error ? <QueryError error={error} type={'danger'} /> : null}
      <h3>{title}</h3>

      <div className={'mb-3 d-flex align-items-center justify-content-between'}>
        <div className={'mr-1'}>
          You are using {currentUsage}/{limit} custom metrics in your subscription. <br />
          You can buy {PACK.PLURAL} of 10 here.
        </div>
        <div className={'d-flex align-items-center'}>
          <IconButton
            color={'primary'}
            icon={'fa-minus'}
            disabled={metricBundle <= minSeats}
            onClick={() => onChangeUnit(-1)} />

          <CounterWidget count={metricBundle} />

          <IconButton
            color={'primary'}
            icon={'fa-plus'}
            outline={false}
            disabled={metricBundle >= maxSeats}
            onClick={() => onChangeUnit(1)} />
        </div>
      </div>


      <BasicContainer size={'lg'} mb={3} appearance={'info'}>
        <div className={'d-flex justify-content-between'}>
          <h5>Summary</h5>
          {interval ? <h5>Cost per {interval}</h5> : null}
        </div>
        <div className={'d-flex justify-content-between'}>
          <p className={'flex-grow-1'}>You are adding {additionalUnits} custom metrics today</p>
          <p className={'text-lg'}>+{currencySymbol}{extraCost}</p>
        </div>

        <LoadingPlaceholder isLoading={previewResponse.isLoading || previewResponse.isUninitialized} height={40}>
          <div className={'d-flex justify-content-between'}>
            <span className={'flex-grow-1'}>Total due {dueDate}</span>
            <span className={'text-lg'}>{currencySymbol}{total}</span>
          </div>
          <div className={'text-xs text-right'}>
            tax included in price
          </div>
          <InvoiceDetails previewResponse={previewResponse} />
        </LoadingPlaceholder>
      </BasicContainer>


      <div className={'d-flex align-items-center justify-content-between'}>
        <ProrateText interval={interval} />
        <div className='d-flex flex-shrink-1'>
          <Button className={'mr-3'} color='link-secondary' onClick={onCancel}>
            Cancel
          </Button>
          <SubmitButton color={'primary'} disabled={!isValid} onClick={handleClick}>
            Continue
          </SubmitButton>
        </div>
      </div>
    </div>
  );
}
