import { ColumnC<PERSON>, OrderingColumn, OrderingType } from '@g17eco/types/custom-report';
import { useState } from 'react';
import { <PERSON>ton, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import { Form, OnChangeForm, Option } from './types';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { TableDraggableColumn, TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import { getColumnOptions, getColumnsMap, getDefaultDirection } from './utils';
import IconButton from '@components/button/IconButton';
import { replaceItem } from '@utils/array';
import { OrderingDirectionSelect } from './OrderingDirectionSelect';
import { OrderingDirection } from '@g17eco/types/common';

type OptionValue = Option<false>['value'];

const draggableColumns: TableDraggableColumn[] = [
  {
    header: 'Heading',
  },
  {
    header: '',
    className: 'text-right',
  },
];

interface Props {
  open: boolean;
  toggle: () => void;
  form: Pick<Form, 'ordering' | 'columns' | 'templateType'>;
  onChangeForm: OnChangeForm;
}
export const CustomOrderingModal = ({ open, toggle, form, onChangeForm }: Props) => {
  const { columns, ordering, templateType } = form;

  const [orderingColumns, setOrderingColumns] = useState<OrderingColumn[]>(() => {
    return ordering.type === OrderingType.Custom ? ordering.columns : [];
  });
  const [isChanged, setIsChanged] = useState(false);
  const changeOrderingColumns = (changeCallback: (newOrderingColumns: OrderingColumn[]) => OrderingColumn[]) => {
    setOrderingColumns(changeCallback);
    setIsChanged(true);
  };

  const [selectedColumn, setSelectedColumn] = useState<ColumnCode | undefined>(undefined);

  const handleSubmit = () => {
    onChangeForm({ ordering: { type: OrderingType.Custom, columns: orderingColumns } });
    toggle();
  };

  const options = getColumnOptions({
    columns: columns.filter(({ code }) => !orderingColumns.some((column) => column.code === code)),
    templateType,
  });
  const value = options.find((option) => option.value === selectedColumn);

  const handleClickOption = (code: OptionValue) => {
    setSelectedColumn(code);
  };

  const handleAddHeading = () => {
    if (!selectedColumn) {
      return;
    }

    const direction = getDefaultDirection(selectedColumn);

    changeOrderingColumns((orderingColumns) => [...orderingColumns, { code: selectedColumn, direction }]);
    setSelectedColumn(undefined);
  };

  const handleArrange = (newOrderingColumns: string[]) => {
    changeOrderingColumns((orderingColumns) =>
      newOrderingColumns.map(
        (newColumn) => orderingColumns.find((column) => column.code === newColumn) as OrderingColumn
      )
    );
  };

  const handleChangeDirection = (code: ColumnCode, direction: OrderingDirection) => {
    changeOrderingColumns((orderingColumns) => {
      const index = orderingColumns.findIndex((column) => column.code === code);
      if (index === -1) {
        return orderingColumns;
      }

      const updatedColumn: OrderingColumn = {
        ...orderingColumns[index],
        direction,
      };

      return replaceItem(orderingColumns, updatedColumn, index);
    });
  };

  const handleRemoveColumn = (code: ColumnCode) => {
    changeOrderingColumns((orderingColumns) => orderingColumns.filter((column) => column.code !== code));
  };

  const draggableRows = orderingColumns.map(({ code, direction }) => ({
    id: code,
    cols: [
      <div key={'header'}>{getColumnsMap(templateType)[code].header}</div>,
      <div key={'action'} className='d-flex align-items-center justify-content-end'>
        <OrderingDirectionSelect
          column={{ code, direction }}
          onChange={(direction) => handleChangeDirection(code, direction)}
          isTransparent
        />
        <IconButton
          key={'delete-btn'}
          icon='fal fa-trash text-danger'
          color='link'
          onClick={() => handleRemoveColumn(code)}
          className='ml-2'
        />
      </div>,
    ],
  }));

  return (
    <Modal isOpen={open} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Order report headings</ModalHeader>
      <ModalBody>
        <TableDraggableRows columns={draggableColumns} data={draggableRows} handleUpdate={handleArrange} />
        <div className='d-flex justify-content-between align-items-stretch'>
          <SelectFactory<OptionValue>
            key={`SelectOrderingColumn-${options.length}`} // To clear the option when click add heading.
            selectType={SelectTypes.SingleSelect}
            isClearable={false}
            options={options}
            onChange={(op) => (op ? handleClickOption(op.value) : undefined)}
            value={value}
            placeholder='Select a heading'
            className='w-100 mr-2'
          />
          <Button className='flex-shrink-0' onClick={handleAddHeading} disabled={!selectedColumn}>
            <i className='fal fa-plus mr-2' />
            Add heading
          </Button>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='primary' onClick={handleSubmit} disabled={Boolean(!orderingColumns.length) || !isChanged}>
          Submit
        </Button>
      </ModalFooter>
    </Modal>
  );
};
