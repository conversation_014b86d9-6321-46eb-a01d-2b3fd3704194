import { render, screen } from '@testing-library/react';
import { vi, Mock } from 'vitest';
import { WorkgroupsTable } from './WorkgroupsTable';
import { useGetWorkgroupsQuery, useDeleteWorkgroupMutation, useDuplicateWorkgroupMutation } from '@api/workgroups';
import { Workgroup } from '@g17eco/types/workgroup';

vi.mock('@api/workgroups', () => ({
  useGetWorkgroupsQuery: vi.fn(),
  useDeleteWorkgroupMutation: vi.fn(),
  useDuplicateWorkgroupMutation: vi.fn(),
}));

describe('WorkgroupsTable', () => {
  const initiativeId = 'test-initiative-id';
  const readOnly = false;

  beforeEach(() => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    (useDeleteWorkgroupMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);

    (useDuplicateWorkgroupMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders no workgroups message when data is empty', () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={readOnly} />);

    expect(screen.getByText('No available workgroups')).toBeInTheDocument();
  });

  it('renders loader when data is loading', () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: true,
      isError: false,
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={readOnly} />);

    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  it('renders error message when data is in error state', () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
      error: {
        message: 'Error loading workgroups',
      },
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={readOnly} />);

    expect(screen.getByText('Error loading workgroups')).toBeInTheDocument();
  });

  const workgroupsData: Workgroup[] = [
    {
      _id: '1',
      name: 'Workgroup 1',
      users: [{ _id: '1', permissions: [] }],
      creatorId: '',
      created: '',
      updated: '',
      icon: '',
      color: '',
      initiativeId,
    },
    {
      _id: '2',
      name: 'Workgroup 2',
      users: [],
      creatorId: '',
      created: '',
      updated: '',
      icon: '',
      color: '',
      initiativeId,
    },
  ];

  it('renders table with columns and workgroups data', async () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: workgroupsData,
      isLoading: false,
      isError: false,
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={readOnly} />);

    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Workgroup size')).toBeInTheDocument();
    expect(screen.getByText('Created')).toBeInTheDocument();
    expect(screen.getByText('Workgroup 1')).toBeInTheDocument();
    expect(screen.getByText('1 users')).toBeInTheDocument();
    expect(screen.getByText('Workgroup 2')).toBeInTheDocument();
    expect(screen.getByText('0 users')).toBeInTheDocument();
  });

  it('renders action buttons when not in read-only mode', async () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: workgroupsData,
      isLoading: false,
      isError: false,
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={false} />);

    expect(screen.getAllByRole('menuitem', { hidden: true, name: 'Edit' }).length).toBe(2);
    expect(screen.getAllByRole('menuitem', { hidden: true, name: 'Duplicate' }).length).toBe(2);
    expect(screen.getAllByRole('menuitem', { hidden: true, name: 'Delete' }).length).toBe(2);
  });

  it('does not render action buttons when in read-only mode', async () => {
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: workgroupsData,
      isLoading: false,
      isError: false,
    });

    render(<WorkgroupsTable initiativeId={initiativeId} readOnly={true} />);

    expect(screen.queryByRole('menuitem', { name: 'Edit' })).not.toBeInTheDocument();
    expect(screen.queryByRole('menuitem', { name: 'Duplicate' })).not.toBeInTheDocument();
    expect(screen.queryByRole('menuitem', { name: 'Delete' })).not.toBeInTheDocument();
  });
});
