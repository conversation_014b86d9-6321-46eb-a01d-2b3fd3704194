/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */


import { Modal, ModalBody, ModalHeader } from 'reactstrap';
import React from 'react';
import { UpgradeFlow } from './UpgradeFlow';
import { UpgradeProps } from '../../company-tracker-types';

/**
 *  Simple wrapper around upgrade flow in modal
 */
export const UserLimitUpgradeModal = (props: UpgradeProps) => {

  const { upgradeDetails, rootOrg, toggle } = props

  return (
    <Modal isOpen={true} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>
        Add seats
      </ModalHeader>
      <ModalBody>
        <UpgradeFlow
          upgradeDetails={upgradeDetails}
          rootOrg={rootOrg}
          onCancel={toggle}
          onSuccess={toggle} />
      </ModalBody>
    </Modal>
  );
};
