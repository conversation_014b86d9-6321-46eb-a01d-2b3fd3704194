/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { useAppSelector } from '../../../../reducers';
import { UserLimitUpgradeModal } from './UserLimitUpgradeModal';
import { useToggle } from '../../../../hooks/useToggle';
import { UserLimitUsage } from './UserLimitTypes';
import { useGetUpgradeDetailsQuery } from '../../../../api/initiatives';
import { skipToken } from '@reduxjs/toolkit/query';
import { FeatureCode } from '@g17eco/core';
import { SubscriptionService } from '../../../../services/SubscriptionService';
import { AddPaymentDetailsAlert } from '../payment-details/AddPaymentDetailsAlert';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';


const DefaultMessage = ({ userLimit }: Pick<UserLimitUsage, 'userLimit'>) => (
  <BasicAlert type={'warning'}>
    You have reached your user limit of {userLimit}. Please contact customer support to increase the limit.
  </BasicAlert>
);

export const UserLimitIncrease = ({ userLimit }: UserLimitUsage) => {

  const globalData = useAppSelector(state => state.globalData);
  const [isOpen, toggle] = useToggle(false);
  const rootOrg = globalData.loaded ? globalData.data?.organization : undefined;

  const isMissingPaymentDetails = globalData.loaded ? SubscriptionService.shouldAddTrialPaymentMethod(globalData.data) : false;
  const canUpgrade = globalData.loaded && SubscriptionService.hasUpgradeableSub(globalData.data.appConfig, globalData.data.organization);
  const { data, isLoading, isSuccess } = useGetUpgradeDetailsQuery(
    rootOrg && !isMissingPaymentDetails && canUpgrade ? {
      initiativeId: rootOrg._id,
      featureCode: FeatureCode.Users
    } : skipToken,
    { refetchOnMountOrArgChange: true },
  );

  if (!globalData.loaded || !rootOrg) {
    return <DefaultMessage userLimit={userLimit} />;
  }

  if (isMissingPaymentDetails) {
    return <AddPaymentDetailsAlert appConfig={globalData.data.appConfig} rootOrg={rootOrg} canUpgrade={canUpgrade} />
  }

  if (!canUpgrade) {
    return <DefaultMessage userLimit={userLimit} />;
  }

  if (isLoading) {
    return <LoadingPlaceholder isLoading={isLoading} height={60} />;
  }

  if (!isSuccess || !data.canUpgrade) {
    return <DefaultMessage userLimit={userLimit} />;
  }

  return (
    <div>
      <BasicAlert className={'d-flex justify-content-between align-items-center'} type='warning'>
        <span>You have reached your user limit of {userLimit}.</span>
        <Button color={'warning'} onClick={toggle} size={'sm'} className={'d-flex align-items-center'}>
          <i className='fal fa-user-plus fa-2xs mr-1' /> Add seats
        </Button>
      </BasicAlert>
      {isOpen ? (
        <UserLimitUpgradeModal
          upgradeDetails={data}
          toggle={toggle}
          rootOrg={rootOrg} />
      ) : null}
    </div>
  );
};
