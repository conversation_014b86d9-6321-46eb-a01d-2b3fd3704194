/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { saveUniversalTrackerValue } from '@actions/universalTrackerValue';
import { ExistingEvidenceFile } from '@components/survey/question/questionInterfaces';
import { UtrvType } from '@constants/status';
import { UserMin } from '@constants/users';
import { UniversalTrackerValueView } from '@features/universal-tracker-value';
import UniversalTracker from '@models/UniversalTracker';
import { ColumnDef } from '@tanstack/react-table';
import { DATE, formatDateUTC } from '@utils/date';
import { getPeriodName } from '@utils/universalTracker';
import { Table } from '@g17eco/molecules/table';
import { CurrentUserData } from '@reducers/current-user';
import { UniversalTrackerModalServiceUtrv } from '@reducers/universal-tracker-modal';
import { capitalise } from '@utils/index';
import { UtrvActions } from './UtrvActions';
import { isAggregatedSurvey } from '@utils/survey';
import { Tab } from '@components/utr-modal/ContentTabs';
import { useAppDispatch } from '@reducers/index';

interface DataTableProps {
  selectedColumnCode?: string;
  utr: UniversalTracker;
  utrvs: UniversalTrackerModalServiceUtrv[];
  users?: UserMin[];
  documents?: ExistingEvidenceFile[];
  hideProvenance?: boolean;
  reloadUtrvs?: () => Promise<void>;
  currentUser?: CurrentUserData;
  handleSelectUtrv?: (selectedUtrv: UniversalTrackerModalServiceUtrv, tabId: Tab['navId']) => void;
}

interface TableColumn {
  displayDate: string;
  shortDisplayDate: string;
  sortableDate: string;
  value: JSX.Element;
  utrType: JSX.Element;
  utrvPeriod: string;
  actions: JSX.Element;
}

const tableColumns: ColumnDef<TableColumn>[] = [
  {
    accessorKey: 'utrType',
    header: '',
    cell: ({ row }) => row.original.utrType,
  },
  {
    accessorKey: 'utrvPeriod',
    header: 'Period',
  },
  {
    accessorKey: 'sortableDate',
    header: 'Date',
    cell: ({ row }) => (
      <>
        <span className='d-md-none'>{row.original.shortDisplayDate}</span>
        <span className='d-none d-md-inline-block'>{row.original.displayDate}</span>
      </>
    ),
  },
  {
    accessorKey: 'value',
    header: 'Value',
    cell: ({ row }) => row.original.value,
  },
  {
    accessorKey: 'actions',
    id: 'actions',
    header: '',
    enableSorting: false,
    cell: ({ row }) => row.original.actions,
  },
];

const getFormattedUTRValue = ({
  utr,
  utrv,
  selectedColumnCode,
}: Pick<DataTableProps, 'utr' | 'selectedColumnCode'> & { utrv: UniversalTrackerModalServiceUtrv }) => {
  return (
    <div className='text-truncate'>
      <UniversalTrackerValueView
        utr={utr}
        utrv={utrv}
        valueType={utr.getValueType()}
        showTableDownload={false}
        extractColumnCode={selectedColumnCode}
        classes={{ popover: undefined }}
      />
      {utrv && Boolean(utrv.aggregationCount) ? '*' : ''}
    </div>
  );
};

const getDataTypeIcon = (utrValue: UniversalTrackerModalServiceUtrv) => {
  const color =
    utrValue.type === UtrvType.Target
      ? 'text-ChartTarget'
      : utrValue.type === UtrvType.Baseline
        ? 'text-ChartBaseline'
        : 'text-ChartActual';

  const icon = isAggregatedSurvey(utrValue.surveyType) ? 'fa-object-exclude' : 'fa-circle';

  return (
    <>
      <i className={`fa ${icon} me-2 ${color} text-lg`} />
      <span className='text-ThemeTextLight d-none d-md-inline-block'>{capitalise(utrValue.type)}</span>
    </>
  );
};

export const DataTable = (props: DataTableProps) => {
  const {
    utr,
    utrvs,
    hideProvenance,
    users,
    documents,
    currentUser,
    selectedColumnCode,
    reloadUtrvs,
    handleSelectUtrv,
  } = props;

  const dispatch = useAppDispatch();

  const handleReject = async (utrValue: UniversalTrackerModalServiceUtrv) => {
    await dispatch(saveUniversalTrackerValue('reject', utrValue._id, {}));
    await reloadUtrvs?.();
  };

  const valueHistory = [...utrvs].sort((a, b) =>
    a.effectiveDate > b.effectiveDate ? -1 : b.effectiveDate > a.effectiveDate ? 1 : 0,
  );

  let aggregated = false;
  const columns = hideProvenance ? tableColumns.filter((c) => c.id !== 'actions') : tableColumns;

  const tableData = valueHistory.map((utrValue) => {
    const formattedValue = getFormattedUTRValue({ utr, utrv: utrValue, selectedColumnCode });
    aggregated = (utrValue.value && Boolean(utrValue.aggregationCount)) || aggregated;
    return {
      displayDate: formatDateUTC(utrValue.effectiveDate, DATE.MONTH_YEAR),
      shortDisplayDate: formatDateUTC(utrValue.effectiveDate, DATE.MONTH_YEAR_SHORT_2),
      sortableDate: formatDateUTC(utrValue.effectiveDate, DATE.SORTABLE),
      value: formattedValue,
      utrType: getDataTypeIcon(utrValue),
      utrvPeriod: getPeriodName(utrValue.period, false),
      actions: (
        <UtrvActions
          utrValue={utrValue}
          utr={utr}
          users={users}
          documents={documents}
          utrValueId={utrValue._id}
          currentUser={currentUser}
          handleReject={handleReject}
          handleSelectUtrv={handleSelectUtrv}
        />
      ),
    };
  });

  const noData = <div className='alert alert-primary mt-3'>There is no history for this tracker</div>;
  return (
    <div className='d-flex w-100 flex-column'>
      <Table className='data-table w-100 mt-2' columns={columns} data={tableData} noData={noData} />
      {aggregated ? <div className='text-right'> * data is aggregated from its children</div> : null}
    </div>
  );
};
