/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import NotFoundMetricIcon from '../../../../images/not-found-metric.svg';
import { generateUrl } from '../../../../routes/util';
import { ROUTES } from '@constants/routes';
import Dashboard, { DashboardSection } from '@components/dashboard';
import { SURVEY } from '@constants/terminology';

interface NotFoundMetricProps {
  initiativeId: string;
  surveyId?: string;
}

export const NotFoundMetric = ({ initiativeId, surveyId }: NotFoundMetricProps) => {
  const history = useHistory();

  const navigate = () => {
    // back to survey overview when question is removed
    if (surveyId) {
      return history.push(generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' }));
    }
    // otherwise back to report page when survey is removed
    return history.push(generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId }));
  };

  return (
    <Dashboard>
      <DashboardSection className='pt-2 mt-5' classes={{ whiteBoxContainer: 'p-5', whiteBoxWrapper: 'py-5 my-3' }}>
        <div className='d-flex w-100 gap-5'>
          <img src={NotFoundMetricIcon} alt='not found metric' />
          <div className='flex-1 ps-2'>
            <h1 className='text-ThemeTextMedium'>
              Oh no! <br /> We can’t find that metric in your {SURVEY.SINGULAR}
            </h1>
            <p className='text-ThemeTextMedium mt-2'>
              We looked but can’t find it. This might be because it was deleted recently.If you think there is a
              problem, please get in touch using the chat function at the bottom of the page.
            </p>
            <div className='mt-4'>
              <Button onClick={() => window.location.reload()}>Try again</Button>
              <Button color='primary' className='ms-4' onClick={() => navigate()}>
                {surveyId ? `Go to ${SURVEY.SINGULAR} overview` : `Go to ${SURVEY.PLURAL} list`}
              </Button>
            </div>
          </div>
        </div>
      </DashboardSection>
    </Dashboard>
  );
};
