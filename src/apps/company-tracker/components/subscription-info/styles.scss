@import '../../../../css/variables';

.subscription-info-dashboard {
  .badge {
    background-color: transparent !important;
    border: 1px solid var(--theme-AccentExtradark);
    color: var(--theme-AccentExtradark);
    padding: 0.25rem 1rem;
  }
  .table-component-container {
    .g17-table {
      border: 1px solid var(--theme-BorderDefault);
      border-radius: $border-radius;
      padding-right: 0px;
      .g17-tbody,
      .g17-thead {
        .g17-tr {
          .g17-th {
            border-color: transparent;
            padding: 1rem !important;
            color: var(--theme-TextDark);
          }
          .g17-td {
            padding: 1.5rem 2rem !important;
            border-bottom: 1px solid var(--theme-BorderDefault);
            &.name-col {
              width: 70%;
            }
          }
        }
      }
      .g17-tbody {
        .g17-tr {
          &:first-of-type {
            position: relative;
            min-height: 70px;
            height: 70px;
          }
        }
      }
    }
  }
}
