/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useMemo, useState } from 'react';
import Dashboard, { DashboardSection } from '@components/dashboard';
import { AIFeature, FeatureStability } from '@g17eco/molecules/feature-stability';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table } from '@g17eco/molecules/table';
import { Toggle } from '@g17eco/molecules/toggle';
import { RootState, useAppDispatch, useAppSelector } from '@reducers/index';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { Badge, Button, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import './styles.scss';
import { SubscriptionDetails } from './subscription-details';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { UserLimitUpgradeModal } from '../user-limit/UserLimitUpgradeModal';
import { FeatureCode, FeatureTag, getFeatureDetails } from '@g17eco/core';
import { AddonButton } from '../payment-details/AddonButton';
import { TrialModal } from '../payment-details/TrialModal';
import { CustomMetricUpgradeModal } from '../custom-metrics/CustomMetricUpgradeModal';
import { RootInitiativeData } from '@g17eco/types/initiative';
import { useAppConfig, useAppSettings } from '@hooks/app/useAppSettings';
import { ColumnDef } from '@tanstack/react-table';
import { useGetBetaFeatureTagsQuery, useUpdateBetaFeatureTagsMutation } from '@api/beta-feature-tags';
import { loadInitiativeTree } from '@actions/index';
import NotAuthorised from '@routes/not-authorised';
import { Action } from '@constants/action';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { canManageCurrentLevel, isStaff } from '@selectors/user';
import { skipToken } from '@reduxjs/toolkit/query';
import {
  useExportInitiativeDataFullMutation,
  useGetBundleSignedUrlMutation,
  useGetExportInitiativeDataFullQuery,
} from '@api/initiative-download-data';
import { canRetryJob, isFinishedJob, isProcessingJob } from '@utils/background-job';
import IconButton from '@components/button/IconButton';
import { AppCode } from '@g17eco/types/app';

const getAIFeatureMap = (appConfigCode?: AppCode) => [
  {
    name: `Data input: ${getFeatureDetails(FeatureTag.MetricAssistantAI)?.name}`,
    tag: FeatureTag.MetricAssistantAI,
    code: FeatureCode.MetricAssistantAI,
  },
  {
    name: `Data input: ${getFeatureDetails(FeatureTag.AIAccessDocumentLibrary)?.name}`,
    tag: FeatureTag.AIAccessDocumentLibrary,
    code: FeatureCode.AIAccessDocumentLibrary,
    staffOnly: true,
  },
  {
    name: `Data input: ${getFeatureDetails(FeatureTag.AIAutoAnswer)?.name}`,
    tag: FeatureTag.AIAutoAnswer,
    code: FeatureCode.AIAutoAnswer,
    staffOnly: true,
  },
  {
    name: `Data input: ${getFeatureDetails(FeatureTag.DraftFurtherExplanationAI)?.name}`,
    tag: FeatureTag.DraftFurtherExplanationAI,
    code: FeatureCode.DraftFurtherExplanationAI,
  },
  {
    name: `Insights: ${getFeatureDetails(FeatureTag.SDGInsightAI)?.name}`,
    tag: FeatureTag.SDGInsightAI,
    code: FeatureCode.SDGInsightAI,
  },
  {
    name: `BETA: Downloads: ${getFeatureDetails(FeatureTag.PPTXReportAI)?.name}`,
    tag: FeatureTag.PPTXReportAI,
    code: FeatureCode.PPTXReportAI,
    description:
      appConfigCode === AppCode.SGXESGenome
        ? `Designed to work with the SGX Core ${QUESTION.SINGULAR} ${PACK.SINGULAR}`
        : `Currently designed to work with EESG ${QUESTION.SINGULAR} ${PACK.SINGULAR}`,
  },
];

interface Row {
  name: string | JSX.Element;
  data: JSX.Element;
}

const getColumns = (title: string | React.JSX.Element): ColumnDef<Row>[] => {
  return [
    {
      header: () => title,
      accessorKey: 'name',
      enableSorting: false,
      meta: {
        cellProps: {
          className: 'name-col',
        },
      },
      cell: ({ row }) => row.original.name,
    },
    {
      header: '',
      accessorKey: 'data',
      enableSorting: false,
      meta: {
        cellProps: {
          className: 'text-right',
        },
      },
      cell: ({ row }) => row.original.data,
    },
  ];
};

interface Props {
  initiativeId: string;
  rootOrg: RootInitiativeData;
}

const FeatureConfirmationModal = ({
  isOpen,
  toggle,
  handleUpdate,
}: {
  isOpen: boolean;
  toggle: () => void;
  handleUpdate: () => void;
}) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>AI features confirmation</ModalHeader>
      <ModalBody>
        <span>
          By enabling AI features, your data will be processed by AI services (OpenAI, Anthropic) on servers in the USA.
          While these AI services would not use your data for AI training, their privacy standards, laws and regulations
          may differ from, and could provide less protection than that of your home country. We also strongly recommend
          that you carefully review and edit any generated content before sharing it.
        </span>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='primary' onClick={handleUpdate}>
          I understand
        </Button>
      </ModalFooter>
    </Modal>
  );
};

const limitsColumn = getColumns('Limits');

/**
 * NOTE: Any routes that use this component must ensure that the current user have the appropriate permissions.
 */
export const SubscriptionInfo = ({ initiativeId, rootOrg }: Props): JSX.Element => {
  const appConfig = useAppConfig();
  const { betaFeatures } = useAppSettings();
  const isRootOrg = rootOrg._id === initiativeId;
  const globalDataState = useAppSelector((state) => state.globalData);
  // To avoid re-renders, we're only passing what we need. (until FeaturePermissions is refactored)
  const state = { globalData: globalDataState } as RootState;

  const {
    data: { configTags, defaultTags } = { configTags: [], defaultTags: [] },
    isLoading: isLoadingTags,
    error: getTagsError,
  } = useGetBetaFeatureTagsQuery(rootOrg._id, { skip: !isRootOrg });
  const dispatch = useAppDispatch();

  const [updateTags, { isLoading: isUpdating, error: updateTagsError }] = useUpdateBetaFeatureTagsMutation();
  const [selectedFeatureTag, setSelectedFeatureTag] = useState<FeatureTag | undefined>(undefined);

  const limitReportingLevels = FeaturePermissions.getLimitReportingLevels(state);
  const limitUsers = FeaturePermissions.getLimitUsers(state);
  const limitSurveys = FeaturePermissions.getLimitSurveys(state);
  const limitCustomMetric = FeaturePermissions.getLimitCustomMetric(state);

  const isManager = useAppSelector(canManageCurrentLevel);
  const isStaffUser = useAppSelector(isStaff);
  const { data } = useGetExportInitiativeDataFullQuery(initiativeId ?? skipToken, { pollingInterval: 30000 });
  const [exportInitiativeDataFull, { data: createdJob, isLoading: loadingExport }] =
    useExportInitiativeDataFullMutation();
  const [getBundleSignedUrl] = useGetBundleSignedUrlMutation();

  const job = data?.job;
  const creatingJob = !job && (loadingExport || Boolean(createdJob));
  const processingJob = job && isProcessingJob(job);
  const finishedJob = job && isFinishedJob(job);
  const canRetry = job && canRetryJob(job);

  const getDisabledExport = () => {
    if (finishedJob) {
      return false;
    }
    if (canRetry) {
      return false;
    }
    return creatingJob || processingJob;
  };
  const disabledExport = getDisabledExport();

  const limitsRows = useMemo(() => {
    return [
      {
        name: 'Number of users',
        data: (
          <div className={'align-items-center d-flex justify-content-end'}>
            <AddonButton
              key={'add-users'}
              size={'xs'}
              rootOrg={rootOrg}
              appConfig={appConfig}
              featureCode={FeatureCode.Users}
              TrialModalComponent={({ toggle }) => (
                <TrialModal
                  title={'Additional user seats unavailable during trial'}
                  toggle={toggle}
                  initiativeId={rootOrg._id}
                >
                  During the free trial, the option to buy additional user seats is not available. To buy more seats,
                  first add credit card details or another payment method.
                </TrialModal>
              )}
              UpgradeModal={UserLimitUpgradeModal}
            >
              <i className={'fa fa-plus mr-1'} />
              Add user seats
            </AddonButton>
            <Limit getLimit={() => limitUsers} />
          </div>
        ),
      },
      {
        name: 'Number of subsidiaries',
        data: <Limit getLimit={() => limitReportingLevels} />,
      },
      {
        name: `Number of ${SURVEY.PLURAL}`,
        data: <Limit getLimit={() => limitSurveys} />,
      },
      {
        name: 'Number of custom metrics',
        data: (
          <div className={'align-items-center d-flex justify-content-end'}>
            <AddonButton
              key={'add-custom-metrics'}
              size={'xs'}
              rootOrg={rootOrg}
              appConfig={appConfig}
              featureCode={FeatureCode.CustomMetrics}
              TrialModalComponent={({ toggle }) => (
                <TrialModal toggle={toggle} initiativeId={rootOrg._id}>
                  During the free trial, the option to buy additional custom metrics is not available. To buy more
                  custom metrics, first add credit card details or another payment method.
                </TrialModal>
              )}
              UpgradeModal={CustomMetricUpgradeModal}
            >
              <i className={'fa fa-plus mr-1'} />
              Add custom {QUESTION.PLURAL}
            </AddonButton>
            <Limit getLimit={() => limitCustomMetric} />
          </div>
        ),
      },
      {
        name: 'Number of global standards',
        data: <Limit getLimit={() => 0} />,
      },
    ];
  }, [appConfig, limitCustomMetric, limitReportingLevels, limitSurveys, limitUsers, rootOrg]);

  if (!isRootOrg) {
    return <NotAuthorised />;
  }

  if (isLoadingTags) {
    return <LoadingPlaceholder height={300} isLoading={true} />;
  }

  const handleUpdate = async ({ tag, isAccepted }: { tag: FeatureTag; isAccepted: boolean }) => {
    setSelectedFeatureTag(undefined);
    if (defaultTags.includes(tag)) {
      return;
    }
    const action = configTags.includes(tag) ? Action.Remove : Action.Add;
    return updateTags({ tag, action, isAccepted, initiativeId: rootOrg._id })
      .unwrap()
      .then(async () => {
        // Only reload the tree if successfully update the tags
        dispatch(loadInitiativeTree({ organization: rootOrg, forceRefresh: true, blocking: false }));
      });
  };

  const downloadAllData = async () => {
    if (finishedJob) {
      const { url } = await getBundleSignedUrl({ initiativeId, jobId: job._id }).unwrap();
      window.open(url, '_blank');
      return;
    }

    exportInitiativeDataFull(initiativeId);
  };

  const dashboardRows = [
    {
      name: 'Access to Insights and Trends',
      data: <Tick isEnabled={() => true} />,
    },
    {
      name: 'Access to Downloads',
      data: <Tick isEnabled={() => FeaturePermissions.canAccessDownloads(state)} />,
    },
    {
      name: `Access to ${SURVEY.SINGULAR} admin`,
      data: <Tick isEnabled={() => FeaturePermissions.canAccessAdminDashboard(state)} />,
    },
  ];

  const featureRows = [
    {
      name: 'Bulk import data',
      data: <Tick isEnabled={() => FeaturePermissions.canAccessBulkImporting(state)} />,
    },
    {
      name: 'Enhanced reports: subsidiary & date comparison',
      data: <Tick isEnabled={() => FeaturePermissions.canAccessCombinedReport(state)} />,
    },
    {
      name: `Create combined ${SURVEY.PLURAL}`,
      data: <Tick isEnabled={() => FeaturePermissions.canAccessCombinedSurvey(state)} />,
    },
    {
      name: `Create ${SURVEY.SINGULAR} templates`,
      data: <Tick isEnabled={() => FeaturePermissions.canAccessSurveyTemplates(state)} />,
    },
    {
      name: `${QUESTION.CAPITALIZED_SINGULAR} verification workflow`,
      data: <Tick isEnabled={() => FeaturePermissions.canAccessVerification(state)} />,
    },
    {
      name: 'External assurance',
      data: <Tick isEnabled={() => FeaturePermissions.canAccessAssurance(state)} />,
    },
  ];

  const advancedFeatureRows = [
    {
      name: 'Single-Sign-On (SSO)',
      data: <PleaseContactUs />,
    },
    {
      name: 'API for data import',
      data: <PleaseContactUs />,
    },
    {
      name: 'Sustainability consulting',
      data: <PleaseContactUs />,
    },
  ];

  const AIFeatures = getAIFeatureMap(appConfig?.code).filter(({ tag, staffOnly }) => {
    if (staffOnly && !isStaffUser) {
      return false;
    }
    return betaFeatures?.includes(tag) || configTags.includes(tag);
  });
  const getTooltip = (disabled: boolean | undefined, disabledReason: string | undefined) => {
    if (initiativeId !== rootOrg._id) {
      return 'This feature can only be enabled from the root initiative';
    }
    if (disabled) {
      return disabledReason ?? '';
    }
    return '';
  };

  const AIFeatureRows = AIFeatures.length
    ? [
        {
          name: (
            <div className='text-center text-ThemeTextMedium w-75 position-absolute top-50 start-50 translate-middle'>
              By turning on AI features, you acknowledge that data entered by your company will be sent for AI
              processing to OpenAI (ChatGPT) servers in the USA
            </div>
          ),
          data: <></>,
        },
        ...AIFeatures.map(({ name, tag, code, description }) => {
          const availability = FeaturePermissions.getAvailability(state, code);
          return {
            name: (
              <div className='d-flex flex-column'>
                <span>{name}</span>
                {description && <span className='text-ThemeTextMedium'>{description}</span>}
              </div>
            ),
            data: (
              <div className='d-flex justify-content-end'>
                <SimpleTooltip text={getTooltip(availability.disabled, availability.disabledReason)}>
                  <Toggle
                    testId={`toggle-${tag}`}
                    disabled={
                      isUpdating || initiativeId !== rootOrg._id || defaultTags.includes(tag) || availability?.disabled
                    }
                    className={{ form: 'd-flex align-items-center pl-0 mb-0' }}
                    checked={availability?.active ?? configTags.some((item) => item === tag)}
                    onChange={() => {
                      if (configTags.length === 0) {
                        setSelectedFeatureTag(tag);
                      } else {
                        handleUpdate({ tag, isAccepted: false });
                      }
                    }}
                  />
                </SimpleTooltip>
              </div>
            ),
          };
        }),
      ]
    : [];

  return (
    <Dashboard className='flex-fill subscription-info-dashboard'>
      <BasicAlert hide={!getTagsError && !updateTagsError} type={'danger'}>
        {getTagsError?.message ?? updateTagsError?.message ?? ''}
      </BasicAlert>
      {isUpdating ? <BlockingLoader /> : null}
      <DashboardSection className='p-0' title='Your plan details:'>
        <div className='mx-auto w-100'>
          <SubscriptionDetails />
          {AIFeatureRows.length ? (
            <div className='mt-4'>
              <Table
                columns={getColumns(
                  <div className='d-flex align-items-center'>
                    <FeatureStability feature={AIFeature[0]} className='m-0' />
                    <span>AI features</span>
                  </div>
                )}
                data={AIFeatureRows}
              />
              <FeatureConfirmationModal
                isOpen={!!selectedFeatureTag}
                toggle={() => setSelectedFeatureTag(undefined)}
                handleUpdate={() => {
                  if (selectedFeatureTag) {
                    handleUpdate({ tag: selectedFeatureTag, isAccepted: true });
                  }
                }}
              />
            </div>
          ) : null}
          <div className='mt-4'>
            <Table columns={getColumns('Features')} data={featureRows} />
          </div>
          <div className='mt-4'>
            <Table columns={limitsColumn} data={limitsRows} />
          </div>
          <div className='mt-4'>
            <Table columns={getColumns('Dashboard/screens')} data={dashboardRows} />
          </div>
          <div className='mt-4'>
            <Table columns={getColumns('Advanced features')} data={advancedFeatureRows} />
          </div>
          {isManager ? (
            <div>
              <Button
                color={disabledExport ? 'secondary' : 'warning'}
                outline
                onClick={downloadAllData}
                disabled={disabledExport}
              >
                <i className='fa-light fa-database mr-2' />
                {disabledExport ? (
                  <span>
                    Generating export data <i className='fa-light fa-spin fa-spinner-scale ml-2' />
                  </span>
                ) : (
                  'Download all data'
                )}
              </Button>
              {finishedJob ? (
                <SimpleTooltip text='Regenerate export data'>
                  <IconButton
                    data-testid={'btn-regenerate'}
                    onClick={() => exportInitiativeDataFull(initiativeId)}
                    outline={false}
                    color='transparent'
                    className='text-ThemeAccentDark ms-2'
                    icon='fal fa-repeat'
                    disabled={disabledExport}
                  />
                </SimpleTooltip>
              ) : null}
            </div>
          ) : null}
        </div>
      </DashboardSection>
    </Dashboard>
  );
};

const Tick = ({ isEnabled }: { isEnabled: () => boolean }) => {
  if (isEnabled()) {
    return (
      <h4 className='m-0'>
        <i className='fal fa-check text-ThemeSuccessMedium' />
      </h4>
    );
  }
  return (
    <h4 className='m-0'>
      <i className='fal fa-times text-ThemeDangerMedium' />
    </h4>
  );
};

const Limit = ({ getLimit }: { getLimit: () => number }) => {
  const limit = getLimit();

  return <Badge pill>{limit <= 0 ? 'Unlimited' : limit}</Badge>;
};

const PleaseContactUs = () => {
  return <span className='text-lg text-ThemeTextLight'>Contact us</span>;
};
