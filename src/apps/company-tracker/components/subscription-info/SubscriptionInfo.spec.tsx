import { screen, waitFor } from '@testing-library/react';
import { expect, vi } from 'vitest';
import { SubscriptionInfo } from '.';
import type { RootInitiativeData } from '@g17eco/types/initiative';
import { getUrl, renderWithProviders } from '@fixtures/utils';
import { createUser, getCurrentUserState } from '@fixtures/user-factory';
import { faker } from '@faker-js/faker';
import { FeatureTag } from '@g17eco/core';
import { getGlobalData } from '@fixtures/global-data-fixture';
import { AppCode, AppConfig } from '@g17eco/types/app';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { setupServer } from 'msw/node';
import { http, PathParams } from 'msw';
import userEvent from '@testing-library/user-event';
import { Action } from '@constants/action';
import { ResponseSuccess } from '@fixtures/msw-fixtures';

describe('SubscriptionInfo Component', () => {
  const routePath = '/company-tracker/admin/:initiativeId/account-settings';
  type RequestData = {
    params: PathParams;
    body?: { tag: string };
  };

  const _id = faker.database.mongodbObjectId();
  const globalData = getGlobalData(
    {
      appConfig: {
        settings: {
          betaFeatures: [
            FeatureTag.MetricAssistantAI,
            FeatureTag.DraftFurtherExplanationAI,
            FeatureTag.SDGInsightAI
          ]
        }
      } as AppConfig
    },
    { _id, appConfigCode: AppCode.CompanyTrackerPro }
  );


  const betaTagsUrl = getUrl(`initiatives/${_id}/beta-features-tags`);
  const server = setupServer(
    http.get(getUrl('initiatives/:id/sponsorships'), async () => ResponseSuccess([])),
    http.get(getUrl('initiatives/:id/export/full'), async () => ResponseSuccess({ success: true, data: {} })),
    http.get(getUrl('initiative-tree/companies/:initiativeId'), async () => ResponseSuccess(globalData.data))
  );

  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    vi.clearAllMocks();
  });
  afterAll(() => server.close());

  const mockProps: { initiativeId: string; rootOrg: RootInitiativeData; } = {
    initiativeId: globalData.initiativeId,
    rootOrg: globalData.organization
  };

  const getRenderOptions = () => {
    return {
      route: {
        initialEntries: [`/company-tracker/admin/${_id}/account-settings`],
        path: routePath
      },
      store: reduxFixtureStore({
        currentUser: getCurrentUserState(createUser({ isStaff: true })),
        globalData,
      }),
    };
  };

  it('should render the confirmation modal on first toggle activation', { timeout: 500000 }, async () => {
    const renderOptions = getRenderOptions();

    const data: RequestData = { params: {}, body: undefined };
    server.use(
      http.get(betaTagsUrl, async () => {
        if (data.body) {
          return ResponseSuccess({ configTags: [data.body.tag], defaultTags: [] });
        }
        return ResponseSuccess({ configTags: [], defaultTags: [] });
      }),
      http.patch(betaTagsUrl, async ({ request }) => {
        const url = new URL(request.url);
        data.params = Object.fromEntries(url.searchParams.entries());
        const body = await request.json();
        data.body = body as RequestData['body'];
        return ResponseSuccess(body);
      }),
      http.get(betaTagsUrl, async () => {
        return ResponseSuccess({ configTags: data.body ? [data.body.tag] : [], defaultTags: [] });
      })
    );

    const user = userEvent.setup();

    renderWithProviders(<SubscriptionInfo {...mockProps} />, renderOptions);

    const testId = `toggle-${FeatureTag.DraftFurtherExplanationAI}`;
    await waitFor(() => expect(screen.getByTestId(testId)).toBeVisible());

    await user.click(await screen.findByTestId(testId));
    expect(await screen.findByText('AI features confirmation')).toBeInTheDocument();

    const byRole = screen.getByRole('button', { name: /I understand/i });
    expect(byRole).toBeInTheDocument();
    await user.click(byRole);
    await waitFor(() => {
      expect(screen.queryByText('AI features confirmation')).not.toBeInTheDocument();
    });

    expect(data.params.isAccepted).toBe('true');
    expect(data.body).toEqual({ tag: FeatureTag.DraftFurtherExplanationAI, action: Action.Add });

    expect(await screen.findByTestId(testId)).toBeChecked();
  });

  it('should not render the confirmation modal if not the first toggle activation', async () => {
    const renderOptions = getRenderOptions();
    const data: RequestData = { params: {}, body: undefined };
    server.use(
      http.get(betaTagsUrl, async () => {
        return ResponseSuccess({
          configTags: data.body ? [FeatureTag.MetricAssistantAI, data.body.tag] : [FeatureTag.MetricAssistantAI],
          defaultTags: []
        });
      }),
      http.patch(betaTagsUrl, async ({ request }) => {
        const url = new URL(request.url);
        data.params = Object.fromEntries(url.searchParams.entries());
        const body = await request.json();
        data.body = body as RequestData['body'];
        return ResponseSuccess(body);
      })
    );

    const user = userEvent.setup();
    renderWithProviders(<SubscriptionInfo key={'test-2'} {...mockProps} />, renderOptions);

    const testId = `toggle-${FeatureTag.DraftFurtherExplanationAI}`;
    await waitFor(() => expect(screen.getByTestId(testId)).toBeInTheDocument());

    const toggleSwitch = await screen.findByTestId(testId);
    expect(toggleSwitch).toBeInTheDocument();
    await user.click(toggleSwitch);

    expect(screen.queryByText('AI features confirmation')).not.toBeInTheDocument();

    expect(data.body).toEqual({ tag: FeatureTag.DraftFurtherExplanationAI, action: Action.Add });
    expect(data.params.isAccepted).toBe('false');

    expect(toggleSwitch).toBeChecked();
  });
});
