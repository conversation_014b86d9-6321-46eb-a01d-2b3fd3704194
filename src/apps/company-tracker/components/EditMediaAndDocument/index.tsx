/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { Modal, ModalBody, ModalHeader, Button, Form, FormGroup, Label, Input } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import G17Client from '@services/G17Client';
import { reloadInitiative } from '@actions/initiative';
import { SubmitButton } from '@components/button/SubmitButton';
import { InitiativeFile } from '@g17eco/types/initiative';
export const apiClient = G17Client;

interface MediaAndDocumentForm {
  title: string;
  description: string;
}

interface EditMediaAndDocumentProps {
  mediaDetails?: InitiativeFile;
  isOpen: boolean;
  toggle: () => void;
}

const trimParams = (params: MediaAndDocumentForm): MediaAndDocumentForm => {
  return {
    title: params.title.trim(),
    description: params.description.trim(),
  };
};

const EditMediaAndDocument = (props: EditMediaAndDocumentProps) => {
  const { mediaDetails, isOpen, toggle } = props;
  const [prevForm, setPrevForm] = useState<MediaAndDocumentForm>({ title: '', description: '' });
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  const initiativeId = useAppSelector((state) => (state.initiative.loaded ? state.initiative.data._id : undefined));
  const dispatch = useAppDispatch();

  const updateForm = (e: React.ChangeEvent<{ name: string; value: string | undefined }>) => {
    const { name, value } = e.target;
    setPrevForm({
      ...prevForm,
      [name]: value,
    });
  };

  useEffect(() => {
    if (mediaDetails) {
      setPrevForm((state) => {
        return {
          ...state,
          title: mediaDetails.title,
          description: mediaDetails.description,
        };
      });
    }
  }, [mediaDetails]);

  const handleSubmit = async (e: React.SyntheticEvent) => {
    setSaving(true);
    return apiClient
      .patch(`initiatives/${initiativeId}/documents/${mediaDetails?._id}`, trimParams(prevForm))
      .then(() => {
        setSaving(false);
        dispatch(reloadInitiative());
        toggle();
      })
      .catch((e) => {
        setSaving(false);
        setMessage(e.message);
      });
  };

  const handleDelete = (e: React.MouseEvent) => {
    return apiClient
      .delete(`initiatives/${initiativeId}/documents/${mediaDetails?._id}`)
      .then(() => {
        setSaving(false);
        dispatch(reloadInitiative());
        toggle();
      })
      .catch((e) => {
        setSaving(false);
        setMessage(e.message);
      });
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static' className='upload-media-documents-modal'>
      <ModalHeader toggle={toggle}>Edit Document Details</ModalHeader>
      <ModalBody>
        {saving && <Loader />}
        {message && <div className='alert alert-danger'>{message}</div>}
        <Form>
          <FormGroup>
            <Label className='strong' for='title'>
              Title
            </Label>
            <Input id='title' type='text' name='title' onChange={updateForm} value={prevForm.title ?? ''} />
          </FormGroup>
          <FormGroup>
            <Label className='strong' for='description'>
              Description
            </Label>
            <Input
              id='description'
              type='text'
              name='description'
              onChange={updateForm}
              value={prevForm.description ?? ''}
            />
          </FormGroup>
          <div className='d-flex justify-content-between'>
            <div>
              <Button onClick={handleDelete} color='danger'>
                Delete
              </Button>
            </div>
            <div>
              <Button color='link-secondary' className='mr-3' onClick={toggle}>
                Cancel
              </Button>
              <SubmitButton onClick={handleSubmit}>Submit</SubmitButton>
            </div>
          </div>
        </Form>
      </ModalBody>
    </Modal>
  );
};

export default EditMediaAndDocument;
