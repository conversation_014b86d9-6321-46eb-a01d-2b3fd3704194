@import '../../../../css/variables';
@import '../../../../css/functions';

$colBottomMargin: 6px;
$textSize: 13px;

.company-settings__wrapper {
  .text-label-uppercase {
    padding-top: 5px;
  }
  .industry-row__wrapper,
  .organization-row__wrapper,
  .text-row__wrapper,
  .month-day-row__wrapper {
    align-items: center;
    .col:first-child {
      color: var(--theme-TextMedium);
    }
    button {
      border: 0;
      font-size: $textSize;
    }
  }
  .industry-row__wrapper {
    p:last-child {
      margin-bottom: 0.5rem;
    }
    button {
      align-self: flex-end;
    }
  }
  .text-row__wrapper {
    .text-row__input {
      font-size: $textSize;
      padding: 8px 15px;
    }
    .text-pre-line {
      white-space: pre-line;
    }
    .text-ellipsis__paragraph {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .month-day-row__wrapper {
    .month-day-picker {
      & > div {
        flex: 1;
        max-width: unset;
      }
      margin-bottom: 0.5rem;
    }
    button {
      align-self: flex-end;
    }
  }
  @include media-breakpoint-down(md) {
    .dashboard-row {
      h2 {
        padding: 0rem 1rem;
      }
    }
    .industry-row__wrapper,
    .organization-row__wrapper,
    .month-day-row__wrapper {
      .col:first-child {
        margin-bottom: $colBottomMargin;
      }
    }
    .text-row__wrapper {
      .align-self-start {
        margin-bottom: $colBottomMargin;
      }
    }
    .month-day-row__wrapper {
      .month-day-picker {
        flex-direction: column;
        & > div {
          padding: 0;
        }
      }
    }
  }
  .FileDropZone:hover {
    text-decoration: none;
    .btn-link {
      text-decoration: underline;
    }
  }
  .fa-image {
    color: var(--theme-TextMedium);
  }

  .default-survey-config__header {
    font-weight: 400;
  }

  #enforce-config {
    width: 2em;
    height: 1em;
  }
}
