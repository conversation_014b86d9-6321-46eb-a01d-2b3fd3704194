import React from 'react';
import { vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { BulkOnboardingContainer } from './BulkOnboardingContainer';

const onboardingListsId = 'onboarding-lists-id';
// mock OnboardingLists
vi.mock('./OnboardingLists', () => ({
  __esModule: true,
  OnboardingLists: () => <div data-testid={onboardingListsId} />,
}));

const bulkOnboardingModalOpen = 'bulk-onboarding-modal-open';
const bulkOnboardingModalClose = 'bulk-onboarding-modal-close';
// mock BulkOnboardingModal
vi.mock('./BulkOnboardingModal', () => ({
  __esModule: true,
  BulkOnboardingModal: ({ isOpen }: { isOpen: boolean }) =>
    isOpen ? <div data-testid={bulkOnboardingModalOpen} /> : <div data-testid={bulkOnboardingModalClose} />,
}));

describe('BulkOnboardingContainer', () => {
  const mockBreadcrumbsComponent = <div data-testid='breadcrumbs'>Breadcrumbs</div>;
  const initiativeId = 'test-initiative-id';
  const userLimit = 10;

  it('renders correctly with given props', () => {
    render(
      <BulkOnboardingContainer
        BreadcrumbsComponent={mockBreadcrumbsComponent}
        initiativeId={initiativeId}
        userLimit={userLimit}
      />
    );

    expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
    expect(screen.getByText('Invite multiple users')).toBeInTheDocument();
    expect(screen.getByTestId('import-users-btn')).toBeInTheDocument();
  });

  it('toggles BulkOnboardingModal on button click', () => {
    render(
      <BulkOnboardingContainer
        BreadcrumbsComponent={mockBreadcrumbsComponent}
        initiativeId={initiativeId}
        userLimit={userLimit}
      />
    );

    const button = screen.getByTestId('import-users-btn');
    fireEvent.click(button);
    expect(screen.getByTestId(bulkOnboardingModalOpen)).toBeInTheDocument();

    fireEvent.click(button);
    expect(screen.getByTestId(bulkOnboardingModalClose)).toBeInTheDocument();
  });

  it('passes correct props to OnboardingLists', () => {
    render(
      <BulkOnboardingContainer
        BreadcrumbsComponent={mockBreadcrumbsComponent}
        initiativeId={initiativeId}
        userLimit={userLimit}
      />
    );

    expect(screen.getByText('Invite multiple users')).toBeInTheDocument();
    expect(screen.getByTestId(onboardingListsId)).toBeInTheDocument();
  });
});