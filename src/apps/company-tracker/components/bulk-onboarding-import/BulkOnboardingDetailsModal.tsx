import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalHeader } from 'reactstrap';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { OnboardingListExtended, OnboardingModelPlain } from '@g17eco/types/onboarding';
import { DATE, formatDate } from '@utils/date';
import { InitiativePlain } from '@g17eco/types/initiative';
import { capitaliseFirstLetter } from '@utils/index';
import { useState } from 'react';
import { UserInvitationInfoModal } from '../user-invitation-info/UserInvitationInfoModal';

interface Props {
  list: OnboardingListExtended | undefined;
  initiativeMap: Map<string, InitiativePlain>;
  isOpen: boolean;
  toggle: () => void;
}

export const BulkOnboardingDetailsModal = ({ isOpen, list, initiativeMap, toggle }: Props) => {
  const [onboarding, setOnboarding] = useState<OnboardingModelPlain>();
  const onboardings = list?.onboardings ?? [];

  const initiativeId = onboarding?.user.permissions.find((p) => initiativeMap.has(p.initiativeId))?.initiativeId;

  const columns: ColumnDef<OnboardingModelPlain>[] = [
    {
      header: 'Email',
      enableSorting: true,
      accessorFn: (onboarding) => onboarding.user.email,
    },
    {
      header: 'Companies',
      cell: ({ row }) => {
        const permissions = row.original.user.permissions;
        return permissions.length ? (
          <div>
            {permissions.map((permission) => (
              <p key={permission.initiativeId} className='mb-1'>{`${
                initiativeMap.get(permission.initiativeId)?.name
              }: ${permission.permissions.join(', ')}`}</p>
            ))}
          </div>
        ) : (
          '-'
        );
      },
    },
    {
      header: 'Created',
      enableSorting: true,
      accessorFn: (onboarding) => formatDate(onboarding.created, DATE.DEFAULT_SPACES_WITH_TIME),
    },
    {
      header: 'Status',
      enableSorting: true,
      cell: ({ row: { original: onboarding } }) => {
        return (
          <div className='d-flex gap-2 align-items-center'>
            {capitaliseFirstLetter(onboarding.status)}
            {onboarding.status === 'pending' ? (
              <Button size='sm' onClick={() => setOnboarding(onboarding)}>
                <i className='fas fa-arrow-rotate-right' />
              </Button>
            ) : null}
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Modal isOpen={isOpen} toggle={toggle} backdrop='static' returnFocusAfterClose={false} size='lg'>
        <ModalHeader toggle={toggle}>Bulk onboarding details</ModalHeader>
        <ModalBody>
          <Table columns={columns} data={onboardings} pageSize={10} />
        </ModalBody>
      </Modal>
      {initiativeId ? (
        <UserInvitationInfoModal
          onboardingId={onboarding?._id}
          initiativeId={initiativeId}
          toggle={() => setOnboarding(undefined)}
        />
      ) : null}
    </>
  );
};
