// OnboardingLists.test.tsx
import React from 'react';
import { vi, Mock } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { OnboardingLists } from './OnboardingLists';
import {
  useBulkOnboardListsQuery,
  useGetOnboardingEmailsQuery,
  useResendOnboardingEmailMutation,
} from '@api/bulk-onboarding-import';
import { useAppSelector } from '@reducers/index';

// Mock the useBulkOnboardListsQuery hook
vi.mock('@api/bulk-onboarding-import', () => ({
  useBulkOnboardListsQuery: vi.fn(),
  useGetOnboardingEmailsQuery: vi.fn(),
  useResendOnboardingEmailMutation: vi.fn(),
}));

// Mock the useAppSelector hook
vi.mock('@reducers/index', () => ({
  useAppSelector: vi.fn(),
}));

describe('OnboardingLists Component', () => {
  beforeEach(() => {
    // Set up default mock return values
    (useBulkOnboardListsQuery as Mock).mockReturnValue({
      data: [],
      isFetching: false,
    });

    (useGetOnboardingEmailsQuery as Mock).mockReturnValue({
      data: [],
      isFetching: false,
    });

    (useResendOnboardingEmailMutation as Mock).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);

    (useAppSelector as Mock).mockReturnValue({
      loaded: true,
      data: {
        initiativeTree: [],
      },
    });
  });

  it('renders loader when data is fetching', () => {
    (useBulkOnboardListsQuery as Mock).mockReturnValue({
      data: [],
      isFetching: true,
    });

    const view = render(<OnboardingLists initiativeId='test-initiative' />);
    expect(view.container.querySelector('#loader')).toBeInTheDocument();
  });

  it('renders message when there are no files imported', () => {
    render(<OnboardingLists initiativeId='test-initiative' />);
    expect(screen.getByText('There are no files imported.')).toBeInTheDocument();
  });

  it('renders onboarding lists when data is available', () => {
    const mockData = [
      {
        _id: '1',
        name: 'Test List',
        fileName: 'test-file.csv',
        user: { email: '<EMAIL>', firstName: 'John', lastName: 'Doe' },
        created: '2023-10-01T12:00:00Z',
        onboardings: [{ user: { email: '<EMAIL>' } }, { user: { email: '<EMAIL>' } }],
      },
    ];

    (useBulkOnboardListsQuery as Mock).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    render(<OnboardingLists initiativeId='test-initiative' />);
    expect(screen.getByText('test-file.csv')).toBeInTheDocument();
    expect(screen.getByText('UPLOADED: 2 users invited')).toBeInTheDocument();
  });

  it('opens details modal when a list is clicked', () => {
    const mockData = [
      {
        _id: '1',
        name: 'Test List',
        fileName: 'test-file.csv',
        user: { firstName: 'John', lastName: 'Doe' },
        created: '2023-10-01T12:00:00Z',
        onboardings: [
          { user: { email: '<EMAIL>', permissions: [] }, created: '2023-10-01T12:00:00Z', status: 'pending' },
          { user: { email: '<EMAIL>', permissions: [] }, created: '2023-10-01T12:00:00Z', status: 'complete' },
        ],
      },
    ];

    (useBulkOnboardListsQuery as Mock).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    render(<OnboardingLists initiativeId='test-initiative' />);
    fireEvent.click(screen.getByText('test-file.csv'));
    expect(screen.getByText('Bulk onboarding details')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Complete')).toBeInTheDocument();
  });
});
