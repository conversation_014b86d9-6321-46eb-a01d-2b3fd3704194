/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { useCallback, useMemo, useEffect, useState } from 'react';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '../../../../components/dashboard';
import { useAppSelector } from '../../../../reducers';
import { getCurrentUser } from '../../../../selectors/user';
import { BlockingLoader } from '@g17eco/atoms/loader';
import {
  MaterialitySurveyModelMinData,
  useDeleteSurveyMutation,
  useGetSurveysQuery,
  useLazyGetSurveyCreateStatusQuery,
} from '../../api/materiality-assessment';
import { Button, DropdownItem, DropdownMenu, DropdownToggle, UncontrolledDropdown } from 'reactstrap';
import { DATE, formatDateUTC } from '../../../../utils/date';
import { useNavigate } from 'react-router-dom-v5-compat';
import { useSiteAlert } from '../../../../hooks/useSiteAlert';
import { SiteAlertColors } from '../../../../slice/siteAlertsSlice';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { Table } from '@g17eco/molecules/table';
import { generateUrl } from '../../../../routes/util';
import { materialityAssessmentInitiativeRoutes } from '../../routes/MaterialityTrackerRoutes';
import { AssessmentProgress } from '../assessment/AssessmentProgress';
import { AssessmentCreateModal } from '../assessment-create-modal/partials/AssessmentCreateModal';
import { SurveyPermissions } from '../../../../services/permissions/SurveyPermissions';
import { ColumnDef } from '@tanstack/react-table';
import './styles.scss';
import { SURVEY } from '@constants/terminology';
import { useHistory, useLocation } from 'react-router-dom';

interface AssessmentsListProps {
  initiativeId: string;
}

export const AssessmentsList = (props: AssessmentsListProps) => {
  const { initiativeId } = props;

  const history = useHistory();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const checkout = queryParams.get('checkout');
  const session_id = queryParams.get('session_id');

  const { data: surveys, isFetching: isGettingSurveys } = useGetSurveysQuery({ initiativeId });
  const [deleteSurvey, { isLoading: isDeleting }] = useDeleteSurveyMutation();
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const { addSiteAlert } = useSiteAlert();
  const user = useAppSelector(getCurrentUser);

  const [startFetchSurveyCreateStatus, { data: surveyCreateStatusResult }] = useLazyGetSurveyCreateStatusQuery({
      pollingInterval: session_id ? 3000 : undefined,
    });

  const isSurveyCreating = checkout === 'success' && session_id;

  useEffect(() => {
    if (session_id) {
      startFetchSurveyCreateStatus({ initiativeId, sessionId: session_id });
    }
  }, [startFetchSurveyCreateStatus, initiativeId, session_id]);

  useEffect(() => {
    if (!surveyCreateStatusResult?.surveyId) {
      return;
    }

    const url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_SURVEY, {
      initiativeId,
      surveyId: surveyCreateStatusResult.surveyId,
    });
    history.push(url);

  }, [history, initiativeId, surveyCreateStatusResult]);

  const canDelete = useCallback(
    (surveyId: string) => {
      if (!surveys || !user) {
        return false;
      }

      const survey = surveys.find((s) => s._id === surveyId);
      if (!survey) {
        return false;
      }

      return SurveyPermissions.canManage(survey, user);
    },
    [surveys, user]
  );

  const navigate = useNavigate();

  useEffect(() => {
    if (surveys && surveys.length === 0 && !session_id) {
      setOpenCreateModal(true);
    }
  }, [session_id, surveys]);

  const columns: ColumnDef<MaterialitySurveyModelMinData>[] = useMemo(() => {
    const handleDelete = (surveyId: string) => {
      deleteSurvey({ surveyId })
        .then(() => {
          const url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_LIST, { initiativeId });
          navigate(url);
        })
        .catch(() => {
          addSiteAlert({
            color: SiteAlertColors.Danger,
            content: `Failed to delete ${SURVEY.SINGULAR}`,
          });
        });
    };

    const handleGoToSurvey = (surveyId: string) => {
      const url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_SURVEY, {
        initiativeId,
        surveyId,
      });
      navigate(url);
    };
    return [
      {
        header: 'Name',
        meta: {
          cellProps: {
            className: 'text-nowrap clickable',
          },
        },
        cell: ({ row }) => {
          const { _id, effectiveDate } = row.original;
          return (
            <Button color='link' onClick={() => handleGoToSurvey(_id)}>
              {formatDateUTC(effectiveDate, DATE.MONTH_YEAR)}
            </Button>
          );
        },
      },
      {
        header: 'Completion',
        meta: {
          cellProps: {
            className: 'text-nowrap completion-col clickable',
          },
        },
        cell: ({ row }) => (
          <div onClick={() => handleGoToSurvey(row.original._id)}>
            <AssessmentProgress utrvs={row.original.fragmentUniversalTrackerValues} showSummary={false} />
          </div>
        ),
      },
      {
        header: ' ',
        meta: {
          cellProps: {
            className: 'text-center',
          },
        },
        cell: ({ row }) =>
          canDelete(row.original._id) ? (
            <UncontrolledDropdown
              style={{
                position: 'inherit',
              }}
            >
              <DropdownToggle color='transparent' outline className='px-2'>
                <i className='fal fa-bars' />
              </DropdownToggle>
              <DropdownMenu>
                <DropdownItem onClick={() => handleDelete(row.original._id)}>
                  <i className='fa fa fa-trash me-2' />
                  Delete
                </DropdownItem>
              </DropdownMenu>
            </UncontrolledDropdown>
          ) : null,
      },
    ];
  }, [addSiteAlert, canDelete, deleteSurvey, initiativeId, navigate]);

  const isLoading = isDeleting || isGettingSurveys;
  return (
    <Dashboard>
      {isSurveyCreating ? <BlockingLoader /> : null}
      {isLoading ? <BlockingLoader /> : null}
      <LoadingPlaceholder isLoading={isGettingSurveys}>
        <DashboardSectionTitle
          buttons={[
            <Button onClick={() => setOpenCreateModal(true)} color='secondary' className='me-2' key={'new-assessment'}>
              Create assessment
            </Button>,
          ]}
        />
        <DashboardSection title='Assessments'>
          {surveys?.length ? (
            <Table<MaterialitySurveyModelMinData>
              key={initiativeId}
              className='assessments-list'
              data={surveys}
              showHeader={false}
              columns={columns}
            />
          ) : (
            <div className='text-center'>
              You have not created any assessments.
              <br />
              <Button className='mt-2' onClick={() => setOpenCreateModal(true)} color='primary'>
                Buy a new assessment
              </Button>
            </div>
          )}

          {openCreateModal ? (
            <AssessmentCreateModal
              surveys={surveys}
              initiativeId={initiativeId}
              toggle={() => setOpenCreateModal((c) => !c)}
            />
          ) : null}
        </DashboardSection>
      </LoadingPlaceholder>
    </Dashboard>
  );
};
