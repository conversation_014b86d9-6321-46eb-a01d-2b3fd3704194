import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from 'reactstrap';
import { useToggle } from '@hooks/useToggle';
import { InitiativePlain } from '@g17eco/types/initiative';
import config from '../../../../config';
import { useIntegrateCompanyTrackerMutation } from '@api/initiatives';
import { generateErrorToast } from '@components/toasts';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { companyTrackerCardConfig } from '@constants/app-card';
import { SettingStorage } from '@services/SettingStorage';
import { companyTrackerRouteMatcher } from '@routes/appRootMatcher';
import { useAppDispatch } from '@reducers/index';
import { reloadInitiativeTree } from '@actions/index';
import { FeatureIntro } from '@g17eco/molecules/feature-intro';

interface Props {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
}

export const CompanyTrackerIntegrationCard = ({ initiative }: Props) => {
  const [isOpen, toggle] = useToggle(false);

  const [integrateCT, { isLoading }] = useIntegrateCompanyTrackerMutation();
  const dispatch = useAppDispatch();

  const handleSubmit = async () => {
    try {
      await integrateCT({ initiativeId: initiative._id }).unwrap();

      // Reload root organization to have new appConfigCode
      dispatch(reloadInitiativeTree());
      // Avoid redirect to preselected CT
      SettingStorage.setItem(companyTrackerRouteMatcher.storageKey, initiative._id);
      window.open(generateUrl(ROUTES.COMPANY_TRACKER, { initiativeId: initiative._id }), '_blank');
    } catch (e) {
      generateErrorToast(e);
    } finally {
      toggle();
    }
  };

  return (
    <>
      <div className='d-flex flex-column justify-content-between align-items-center gap-2 h-100'>
        <img src={companyTrackerCardConfig.logo} alt='logo' style={{ maxHeight: '53px' }} />
        <div className='d-flex justify-content-between gap-2'>
          <Button color='link-secondary' onClick={() => window.open(config.brochureURL.companyTracker, '_blank')}>
            More info
          </Button>
          <Button color='primary' onClick={toggle}>
            Start free trial
          </Button>
        </div>
      </div>
      <Modal isOpen={isOpen} toggle={toggle} backdrop='static' returnFocusAfterClose={false}>
        <ModalHeader toggle={toggle}>Connect Company Tracker</ModalHeader>
        <ModalBody>
          {isLoading ? <BlockingLoader /> : null}
          <p>
            Unlock the full potential of your sustainability journey by connecting Company Tracker to {initiative.name}:
          </p>
          <p>Free trial period = 14 days</p>
          <FeatureIntro
            icon='fa-circle-nodes'
            header='Seamless integration across G17Eco'
            content='Effortlessly connect your G17Eco apps with a single click, streamlining everything from completing materiality assessments and gathering data for sustainability reporting to securing third-party assurance.'
          />
          <FeatureIntro
            icon='fa-code-fork'
            header='Workflow Automation'
            content='Streamline collaboration and data collection across complex organisations, and automate validation and tracking.'
          />
          <FeatureIntro
            icon='fa-chart-mixed'
            header='Analytics, insights and sustainability report generation'
            content='Design insightful digital dashboards to enable real-time monitoring of analytics, seamless sharing with internal and external stakeholders, and effortless generation of sustainability reports.'
          />
          <p>
            <a href={config.brochureURL.companyTracker} target='_blank'>
              Click here
            </a>{' '}
            to learn more about Company Tracker.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button color='link-secondary' onClick={toggle}>
            Cancel
          </Button>
          <Button color='primary' onClick={handleSubmit} disabled={isLoading}>
            Start free trial
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};
