/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button, FormGroup, Input, Label, Modal, ModalBody, ModalHeader, UncontrolledCollapse } from 'reactstrap';
import { SiteAlertColors, addSiteAlert } from '../../../../../slice/siteAlertsSlice';
import {
  CreateSurvey,
  MaterialitySurveyModelMinData,
  useCreateSurveyMutation,
  useGetBuySurveyContextQuery,
} from '../../../api/materiality-assessment';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useState } from 'react';
import { SURVEY } from '@constants/terminology';
import { getMonthNonJSOptions, getYearDropdownOptions } from '@utils/date';
import { getEffectiveDate } from '@utils/survey';
import { SelectFactory, SelectTypes } from '../../../../../molecules/select/SelectFactory';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { AssessmentFormGroup } from '../AssessmentFormGroup';
import { AssessmentType } from '@g17eco/types/survey';

interface AssessmentCreateModalProps {
  initiativeId: string;
  toggle: () => void;
  surveys: MaterialitySurveyModelMinData[] | undefined;
}

const YEARS_BACK = 6;
const YEARS_NEXT = 2;
const TOOLTIP =
  'Financial Materiality Assessments focus on how sustainability issues impact a company’s financial performance. Whilst a Double Materiality Assessment considers both financial materiality and the company\'s broader impact on the environment and society. If you are unsure which is right for you please contact us.';

const yearOptions = getYearDropdownOptions(YEARS_BACK, YEARS_NEXT);
const monthOptions = getMonthNonJSOptions(); // 1-12

const checkIsEffectiveDateConflict = ({
  month,
  year,
  surveys,
}: {
  month: number | undefined;
  year: number | undefined;
  surveys: Pick<MaterialitySurveyModelMinData, 'effectiveDate'>[] | undefined;
}) => {
  if (!month || !year || !surveys) {
    return false;
  }
  return surveys.some((survey) => survey.effectiveDate === getEffectiveDate(month, year));
};

const assessmentTypeMap: { [key in AssessmentType]: { label: string; icon: string } } = {
  [AssessmentType.FinancialMateriality]: {
    label: 'Financial Materiality',
    icon: 'fa-buildings',
  },
  [AssessmentType.DoubleMateriality]: {
    label: 'Double Materiality',
    icon: 'fa-earth-europe',
  },
};

interface AssessmentForm {
  month: number | undefined;
  year: number | undefined;
  answers: Partial<CreateSurvey['answers']>;
  referralCode: string | undefined;
  assessmentType: AssessmentType | undefined;
}

const defaultAssessmentForm: AssessmentForm = {
  answers: {},
  assessmentType: undefined,
  month: undefined,
  year: undefined,
  referralCode: undefined,
}

export const AssessmentCreateModal = (props: AssessmentCreateModalProps) => {
  const { surveys, initiativeId, toggle } = props;
  const [createSurvey, { isLoading: isCreating }] = useCreateSurveyMutation();
  const { data: createSurveyContext, isLoading } = useGetBuySurveyContextQuery({ initiativeId });

  const [{ month, year, answers, referralCode, assessmentType }, setAssessmentForm] =
    useState<AssessmentForm>(defaultAssessmentForm);

  const [message, setMessage] = useState<string | undefined>();

  const utrCodes = createSurveyContext?.utrs.map((utr) => utr.code) ?? [];
  const isAllUtrsAnswered = utrCodes.every((code) => answers[code]);
  const hasEffectiveDateConflict = checkIsEffectiveDateConflict({ month, year, surveys });
  const isFormValid = isAllUtrsAnswered && month && year && !hasEffectiveDateConflict && assessmentType;

  const updateAssessmentForm = (field: keyof AssessmentForm, value: AssessmentForm[keyof AssessmentForm]) =>
    setAssessmentForm((prev) => ({
      ...prev,
      [field]: value,
    }));

  const handleCreate = () => {
    if (!isFormValid) {
      return addSiteAlert({
        color: SiteAlertColors.Danger,
        content: `Invalid ${SURVEY.SINGULAR} configuration.`,
      });
    }

    if (!answers) {
      return; // Typescript
    }

    createSurvey({
      initiativeId,
      effectiveDate: getEffectiveDate(month, year),
      answers,
      referralCode,
      returnUrl: `${location.pathname}`,
      assessmentType,
    })
      .unwrap()
      .then((response) => {
        if (response.sessionUrl) {
          window.location.href = response.sessionUrl;
          return;
        }
      })
      .catch((e) => setMessage(e.data?.message ?? e.message));
  };

  const handleOptionChange = (key: string, value?: string) => {
    updateAssessmentForm('answers', {
      ...answers,
      [key]: value,
    });
  };

  if (isLoading) {
    return (
      <Modal isOpen={true} toggle={toggle}>
        <ModalHeader toggle={toggle}>Create a new assessment</ModalHeader>
        <BlockingLoader />;
      </Modal>
    );
  }

  if (!createSurveyContext || utrCodes.length === 0) {
    return (
      <Modal isOpen={true} toggle={toggle}>
        <ModalHeader toggle={toggle}>Create a new assessment</ModalHeader>
        <ModalBody>
          <BasicAlert type='danger'>Unable to create an assessment at this time. Please try again later.</BasicAlert>
        </ModalBody>
      </Modal>
    );
  }

  const { utrs } = createSurveyContext;

  return (
    <Modal isOpen={true}>
      <ModalHeader toggle={toggle}>Create a new assessment</ModalHeader>
      {isCreating ? <BlockingLoader /> : null}
      <ModalBody className='text-ThemeTextMedium'>
        <BasicAlert type='danger'>{message}</BasicAlert>
        <div>
          Materiality assessments are custom to each organisation. Please provide the following details so we can set up
          your assessment.
        </div>

        <div className='mt-4' />
        <BasicAlert hide={!hasEffectiveDateConflict} type={'warning'}>
          <div>
            <b>WARNING:</b> An assessment already exists for the selected date.
          </div>
        </BasicAlert>
        <FormGroup>
          <Label htmlFor='assessment-year' className='strong'>
            Assessment date
          </Label>
          <div className='d-flex gap-3'>
            <SelectFactory
              className='w-100'
              selectType={SelectTypes.SingleSelect}
              options={monthOptions}
              placeholder='Select month'
              onChange={(op) => (op ? updateAssessmentForm('month', op.value) : undefined)}
            />

            <SelectFactory
              className='w-100'
              selectType={SelectTypes.SingleSelect}
              options={yearOptions}
              placeholder='Select year'
              onChange={(op) => (op ? updateAssessmentForm('year', op.value) : undefined)}
            />
          </div>
        </FormGroup>

        <FormGroup>
          <Label htmlFor='assessment-year' className='strong'>
            <SimpleTooltip text={TOOLTIP}>
              <i className='fal text-xl fa-info-circle mr-2' />
            </SimpleTooltip>
            Assessment type
          </Label>
          <div className='d-flex gap-2'>
            {Object.values(AssessmentType).map((type) => (
              <Button
                active={assessmentType === type}
                outline={assessmentType === type}
                color='secondary'
                className={`btn-md ${assessmentType === type ? 'background-ThemeNeutralsDark' : ''}`}
                onClick={() => updateAssessmentForm('assessmentType', type)}
              >
                <i className={`fal ${assessmentTypeMap[type].icon} mr-2`} />
                {assessmentTypeMap[type].label}
              </Button>
            ))}
          </div>
        </FormGroup>

        {utrs.map((utr) => (
          <AssessmentFormGroup key={utr.code} utr={utr} onChange={handleOptionChange} tooltip={utr.instructions} />
        ))}

        <div className='mt-4 text-ThemeTextLight'>
          If you have been provided with a referral code, please{' '}
          <Button color='link' id={'collapsibleReferralCode'} className='px-0 pt-0' style={{ paddingBottom: '1px' }}>
            click here
          </Button>
          <UncontrolledCollapse toggler='#collapsibleReferralCode'>
            <FormGroup className='pt-4'>
              <Label htmlFor='referralCode' className='strong'>
                Referral code
              </Label>
              <Input
                type='text'
                name='referralCode'
                id='referralCode'
                onChange={(e) => updateAssessmentForm('referralCode', e.target.value)}
              />
            </FormGroup>
          </UncontrolledCollapse>
        </div>

        <div className='mt-3'>
          {isFormValid && !referralCode ? (
            <BasicAlert type='primary'>
              <strong>Product Purchase Required</strong>
              <p>You will be redirected to the payment page.</p>
            </BasicAlert>
          ) : null}
          <div className='text-right pt-4'>
            <Button color='link-secondary' onClick={toggle} className='me-3'>
              Cancel
            </Button>
            <Button color='primary' disabled={!isFormValid} onClick={handleCreate}>
              Buy assessment
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};
