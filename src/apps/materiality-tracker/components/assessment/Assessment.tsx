/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import Dashboard, { DashboardSection } from '../../../../components/dashboard';
import { materialityAssessmentApi, useGetSurveyQuery } from '../../api/materiality-assessment';
import { useSurveyGroups } from '../../../../utils/survey';
import { SurveyOverviewMode } from '../../../../slice/surveySettings';
import { AssessmentProgress } from './AssessmentProgress';
import { QueryWrapper } from '../../../../components/query/QueryWrapper';
import './styles.scss';
import { ExpandingSurveyQuestionList } from '@features/materiality-question-list/expanding-survey-question-list';
import { UtrvStatus } from '@constants/status';
import { NoData } from '@g17eco/molecules/no-data';
import { Option } from '@g17eco/molecules/select/SelectFactory';
import { FilterToggle } from '@g17eco/molecules/filter-toggle';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Filters, MinimalSurveyData } from './types';
import { AssessmentDropdown } from './AssessmentDropdown';
import { AssessmentToolbar } from './AssessmentToolbar';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { CompleteButton } from '../../../../components/survey/button/CompleteButton';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '../../../../routes/util';
import { ROUTES } from '../../../../constants/routes';
import { OnChangeValue } from 'react-select';
import { useToggle } from '@hooks/useToggle';
import { AssessmentFilters } from './AssessmentFilters';
import { useMemo, useState } from 'react';
import { useGetSurveyUsersQuery } from '@api/initiative-stats';
import { disableByDelegationStatus, disableBySearch, disableByStatus } from '@components/survey/utils/getDisableUtrs';
import { ScopeQuestionGroup } from '@g17eco/types/survey';
import G17Client from '@services/G17Client';
import { DownloadType } from '@g17eco/types/download';
import { SearchDebounce } from '../../../../components/survey/survey-question-list-toolbar/partials/SearchQuestions';
import { generateSurveyQuestionsFlexSearchMap } from '../../../../selectors/blueprint';
import { Document } from 'flexsearch';
import classnames from 'classnames';
import { useAppDispatch } from '@reducers/index';

interface AssessmentProps {
  initiativeId: string;
  surveys: MinimalSurveyData[];
  surveyId: string;
}

const useFilters = ({
  surveyGroups,
  searchIndex,
}: {
  surveyGroups: ScopeQuestionGroup[];
  searchIndex: Document<unknown>;
}) => {
  const [showFilters, toggleShowFilters] = useToggle(false);
  const [filters, setFilters] = useState<Filters>({});

  const handleChangeFilters = (key: keyof Filters, value?: string | string[]) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const generateDisabledUtrs = () => [
    ...disableBySearch({ surveyGroups, searchText: filters.searchText, searchIndex }),
    ...(filters.status ? disableByStatus({ surveyGroups, filterByStatus: [filters.status] }) : []),
    ...disableByDelegationStatus({ surveyGroups, filterByDelegationStatus: filters.delegation }),
  ];
  const disabledUTRs = generateDisabledUtrs();

  return {
    showFilters,
    toggleShowFilters,
    filters,
    handleChangeFilters,
    disabledUTRs,
  };
};

export const Assessment = (props: AssessmentProps) => {
  const { initiativeId, surveys, surveyId } = props;

  const surveyQuery = useGetSurveyQuery({ surveyId });
  const { data: survey } = surveyQuery;
  const surveyGroups = useSurveyGroups(SurveyOverviewMode.Universal, survey);
  const { data: surveyUsers = [] } = useGetSurveyUsersQuery({ surveyId, initiativeId });

  const searchIndex = useMemo(() => generateSurveyQuestionsFlexSearchMap(survey), [survey]);
  const { showFilters, toggleShowFilters, filters, handleChangeFilters, disabledUTRs } = useFilters({
    surveyGroups,
    searchIndex,
  });

  const history = useHistory();
  const goToInsights = () =>
    history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_INSIGHTS, { initiativeId, insightSurveyId: surveyId }));
  const dispatch = useAppDispatch();
  const handleReloadQuestion = async () => {
    dispatch(materialityAssessmentApi.util.invalidateTags(['survey', 'survey-list']));
  };

  const handleReloadOnCompleteAssessment = async () => {
    dispatch(materialityAssessmentApi.util.invalidateTags(['survey', 'survey-list']));
    goToInsights();
  };

  const hasUnansweredQuestions = survey
    ? survey.fragmentUniversalTrackerValues.some(
        (utrv) => utrv?.status !== UtrvStatus.Updated && utrv?.status !== UtrvStatus.Verified,
      )
    : true;

  const isComplete = Boolean(survey?.completedDate);
  const onClickItem = (op: OnChangeValue<Option<string>, false>) => {
    if (survey && op?.value === survey._id) {
      return;
    }
    history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_SURVEY, { initiativeId, surveyId: op?.value }));
  };

  const downloadLatestAssessmentCSV = async () => {
    return G17Client.downloadLatestAssessmentAnswers({
      surveyId,
      initiativeId,
      type: DownloadType.Csv,
    });
  };

  return (
    <Dashboard className='assessmentContainer'>
      <QueryWrapper
        query={surveyQuery}
        onFetching={BlockingLoader}
        onLoading={BlockingLoader}
        onError={() => <NoData text='There was an error fetching your assessments. Please try again.' />}
        onNoData={() => <NoData text='No assessments currently available.' />}
        onSuccess={(survey) => (
          <>
            <div className='d-flex align-items-center px-3 mb-3'>
              <AssessmentProgress
                utrvs={survey.fragmentUniversalTrackerValues}
                verifiedBgColor={
                  survey.completedDate ? 'background-ThemeSuccessMedium' : 'background-ThemeIconSecondary'
                }
                showVerifiedCount={false}
              />
              <SimpleTooltip
                key='submit-btn'
                text={hasUnansweredQuestions ? 'Please answer all questions before submitting' : ''}
              >
                {isComplete ? (
                  <Button color='primary' className='btn-lg ml-3' onClick={goToInsights}>
                    <i className='fal fa-square-poll-vertical mr-2' />
                    View results
                  </Button>
                ) : (
                  <CompleteButton
                    disabled={hasUnansweredQuestions}
                    isUserAdmin={true}
                    survey={survey}
                    color='secondary'
                    handleReload={handleReloadOnCompleteAssessment}
                    notCompletedText='Submit assessment'
                    notCompletedIcon=''
                    showPopup={false}
                    classes={{ btn: 'btn-lg ml-3' }}
                  />
                )}
              </SimpleTooltip>
            </div>
            <DashboardSection>
              <div className='d-flex align-items-center mb-3'>
                <AssessmentDropdown selectedSurvey={survey} surveys={surveys} onClickItem={onClickItem} />
                <div className='d-flex align-items-center ml-auto gap-1'>
                  <Button onClick={downloadLatestAssessmentCSV}>
                    <i className='fal fa-file-csv text-ThemeIconSecondary mr-2' />
                    Download assessment
                  </Button>
                </div>
              </div>
              <div className='d-flex gap-3'>
                <SearchDebounce
                  handleChange={(value: string) => handleChangeFilters('searchText', value)}
                  defaultValue={filters.searchText}
                />
                <FilterToggle
                  toggleFilters={toggleShowFilters}
                  showExtendedFilters={showFilters}
                  label='Filters'
                  reverse
                />
              </div>
              {showFilters ? (
                <AssessmentFilters filters={filters} onChangeFilters={handleChangeFilters} users={surveyUsers} />
              ) : null}
              <div className={classnames('mt-4', { 'completed-survey-group-wrapper': !!survey?.completedDate })}>
                <ExpandingSurveyQuestionList
                  survey={survey}
                  surveyId={surveyId}
                  initiativeId={initiativeId}
                  surveyGroups={surveyGroups}
                  handleReload={handleReloadQuestion}
                  Toolbar={AssessmentToolbar}
                  disabledUTRs={disabledUTRs}
                />
              </div>
            </DashboardSection>
          </>
        )}
      />
    </Dashboard>
  );
};
