/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { But<PERSON> } from 'reactstrap';
import Dashboard, { DashboardRow, DashboardSection } from '../../../../components/dashboard';
import { useLocation, useNavigate } from 'react-router-dom-v5-compat';
import { materialityAssessmentApi, useGetSurveyQuery } from '../../api/materiality-assessment';
import { useSurveyGroups } from '../../../../utils/survey';
import { SurveyOverviewMode } from '../../../../slice/surveySettings';
import { InitiativeData } from '../../../../types/initiative';
import { BlueprintContributions } from '../../../../types/survey';
import { useAppDispatch } from '../../../../reducers';
import { NavigationButtons } from '../../../../components/question/NavigationButtons';
import { getQuestionId } from '../../../../selectors/survey';
import { useQuestionIds } from '../../../../hooks/useQuestionIds';
import { AssessmentProgress } from './AssessmentProgress';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { QueryWrapper } from '../../../../components/query/QueryWrapper';
import { QuestionContainerStateless } from '../../../../components/survey/question/QuestionContainerStateless';
import { generateUrl } from '../../../../routes/util';
import { materialityAssessmentInitiativeRoutes } from '../../routes/MaterialityTrackerRoutes';
import { BasicAlert } from '@g17eco/molecules/alert';

interface AssessmentQuestionProps {
  initiativeId: string;
  surveyId: string;
  questionId: string;
  questionIndex?: string;
}

const NoData = ({ text }: { text: string }) => <div className='mr-3'><BasicAlert type='warning'>{text}</BasicAlert></div>;

export const AssessmentQuestion = (props: AssessmentQuestionProps) => {
  const { initiativeId, surveyId, questionId, questionIndex = '' } = props;

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const surveyQuery = useGetSurveyQuery({ surveyId });
  const { data: survey } = surveyQuery;
  const surveyGroups = useSurveyGroups(SurveyOverviewMode.Universal, survey);

  const disableUtrs: string[] = []; // No filtering for now on this page
  const { questionListIds, currentQuestionIndex } = useQuestionIds(surveyGroups, questionIndex, questionId, disableUtrs);

  const handleBackToList = () => {
    const url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_SURVEY, { initiativeId, surveyId });
    navigate(url);
  }

  const handleReload = async () => dispatch(materialityAssessmentApi.util.invalidateTags(['survey', 'survey-list']));
  const materiality: InitiativeData['materiality'] = {};
  const blueprint: BlueprintContributions = {};

  const handleGoToQuestion = ({ id, index }: { id: string, index?: number }) => {
    let url: string;
    if (index) {
      url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_QUESTION_INDEX, { initiativeId, surveyId, questionId: id, questionIndex: `${index}` });
    } else {
      url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_QUESTION, { initiativeId, surveyId, questionId: id });
    }
    const searchParams = new URLSearchParams(location.search);
    navigate({
      pathname: url,
      search: searchParams.toString()
    });
  };
  const goNext = () => handleGoToQuestion(getQuestionId(questionListIds, questionId, currentQuestionIndex, true));
  const goPrevious = () => handleGoToQuestion(getQuestionId(questionListIds, questionId, currentQuestionIndex, false));

  return (
    <Dashboard className='assessmentContainer question-view'>
      <DashboardRow>
        <div className='d-flex flex-fill align-items-center'>
          <div className='me-auto'>
            <Button color='link' onClick={handleBackToList}>
              <i className='fal fa-chevron-left me-2' />
              Back to assessment
            </Button>
          </div>
          <div>
            <NavigationButtons goPrevious={goPrevious} goNext={goNext} />
          </div>
        </div>
      </DashboardRow>
      <DashboardRow>
        <AssessmentProgress utrvs={survey?.fragmentUniversalTrackerValues ?? []} />
      </DashboardRow>
      <DashboardSection className='question-wrapper' padding={2}>
        <QueryWrapper
          query={surveyQuery}
          onError={() => <NoData text='There was an error fetching your assessments. Please try again.' />}
          onNoData={() => <NoData text='No assessments currently available.' />}
          onFetching={BlockingLoader}
          onLoading={BlockingLoader}
          onSuccess={(survey) => (
            <QuestionContainerStateless
              initiativeId={initiativeId}
              surveyId={surveyId}
              questionId={questionId}
              questionIndex={currentQuestionIndex}
              survey={survey}
              surveyGroups={surveyGroups}
              materiality={materiality}
              blueprint={blueprint}
              searchIndex={undefined}
              handleReload={handleReload}
              config={{
                enableFurtherExplanation: false,
                enableComments: false,
                enableQuestionSubtitle: false,
                enableEvidence: false,
                enableNANR: false,
                enableMakePrivate: false,
                enableAIAssistant: false
              }}
            />
          )} />
      </DashboardSection>
    </Dashboard >
  );
}
