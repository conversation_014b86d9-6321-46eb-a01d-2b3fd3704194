/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useState } from 'react';
import { loadSurvey, reloadSurveyListSummary } from '@actions/survey';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { getCurrentUser } from '@selectors/user';
import { SurveyPermissions } from '@services/permissions/SurveyPermissions';
import DelegationModal from '@components/delegation-modal';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { BulkButton, countQuestions } from '@components/survey-question-list/partials/ToggledButton';
import { FloatingToolbar } from '@components/floating-toolbar';
import {
  BulkActionToolbarProps,
  BulkActionToolbarSelectedUtrv,
} from '@components/survey-question-list/partials/BulkActionToolbar';

export const AssessmentToolbar = (props: BulkActionToolbarProps) => {
  const { selectedQuestions, handleClose, surveyId, surveyData, initiativeId } = props;
  const dispatch = useAppDispatch();

  const [delegationModal, setDelegationModal] = useState<BulkActionToolbarSelectedUtrv[]>([]);
  const [isLoading, setLoading] = useState(false);
  const currentUser = useAppSelector(getCurrentUser);

  if (!currentUser) {
    return null;
  }

  const selectedCount = countQuestions(selectedQuestions);
  const canManage = surveyData && SurveyPermissions.canManage(surveyData, currentUser);

  const buttons = canManage
    ? [
        <BulkButton
          key={'delegate-button'}
          className='d-flex gap-2'
          tooltip=''
          onClick={() => setDelegationModal(selectedQuestions)}
        >
          <i className='fal fa-chart-simple' />
          <>Delegate</>
        </BulkButton>,
      ]
    : [];

  const handleReload = async () => {
    return Promise.all([dispatch(loadSurvey(surveyId, false, true)), dispatch(reloadSurveyListSummary())]).then(() =>
      props.handleReload?.()
    );
  };

  if (buttons.length === 0) {
    return null;
  }

  return (
    <>
      {isLoading ? <BlockingLoader /> : null}
      <FloatingToolbar handleClose={handleClose} isOpen={selectedCount > 0} items={buttons} />
      {delegationModal.length > 0 ? (
        <DelegationModal
          isOpen={delegationModal.length > 0}
          survey={surveyData}
          handleSubmit={async () => {
            setLoading(true);
            await handleReload();
            setLoading(false);
          }}
          toggle={() => setDelegationModal([])}
          initiativeId={initiativeId}
          utrvs={delegationModal}
        />
      ) : null}
    </>
  );
};
