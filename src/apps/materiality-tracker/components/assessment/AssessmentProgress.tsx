/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import SurveyProgress, { AnimationVariant } from '@components/survey-progress';
import { UniversalTrackerValuePlain } from '../../../../types/surveyScope';
import { getSurveyQuestionProgressFromUtrvs } from '../../../../utils/survey';

interface AssessmentProgressProps {
  utrvs: Pick<UniversalTrackerValuePlain, 'status' | '_id'>[];
  showSummary?: boolean;
  verifiedBgColor?: string;
  showVerifiedCount?: boolean;
}

export const AssessmentProgress = ({
  utrvs,
  verifiedBgColor,
  showVerifiedCount,
  showSummary = true,
}: AssessmentProgressProps) => {
  const progressStats = getSurveyQuestionProgressFromUtrvs(utrvs);

  return (
    <div className='flex-fill'>
      <div className='w-100 d-flex'>
        <div className='flex-grow-1 pr-3'>
          <SurveyProgress
            {...progressStats}
            animationVariant={AnimationVariant.Animated}
            verifiedBgColor={verifiedBgColor}
            showVerifiedCount={showVerifiedCount}
          />
          {showSummary ? (
            <div className='text-sm text-right text-ThemeTextMedium mt-1'>
              answered:
              <span className='dont_translate ml-1'>
                {progressStats.completed}/{progressStats.count}
              </span>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
