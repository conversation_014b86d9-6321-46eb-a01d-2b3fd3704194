/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table } from '@g17eco/molecules/table';
import { ColumnDef } from '@tanstack/react-table';
import { AssessmentDataRow, assessmentDataColumnMap } from '../../utils';
import ClampLines from 'react-clamp-lines';

interface AssessmentResultsProps {
  data: AssessmentDataRow[];
}

export const AssessmentResults = (props: AssessmentResultsProps) => {
  const { data } = props;

  const scoresTableColumns: ColumnDef<AssessmentDataRow>[] = [
    {
      header: assessmentDataColumnMap.name,
      accessorKey: 'name',
    },
    {
      header: assessmentDataColumnMap.score,
      accessorFn: (row) => row.relativeScore !== undefined ? `${row.relativeScore}%` : row.score,
      meta: {
        cellProps: {
          className: 'text-center',
        },
      },
    },
    {
      header: assessmentDataColumnMap.utrMapping,
      cell: ({ row }) => (
        <SimpleTooltip text={row.original.utrMapping}>
          <ClampLines
            id={'utrMapping'}
            text={row.original.utrMapping}
            lines={2}
            moreText=''
            lessText=''
            ellipsis='...'
          />
        </SimpleTooltip>
      ),
    },
    {
      header: assessmentDataColumnMap.esgs,
      accessorKey: 'esgs',
    },
    {
      header: assessmentDataColumnMap.sdgs,
      cell: ({ row }) => (
        <SimpleTooltip text={row.original.sdgs}>
          <ClampLines id={'sdgs'} text={row.original.sdgs} lines={2} moreText='' lessText='' ellipsis='...' />
        </SimpleTooltip>
      ),
    },
    {
      header: assessmentDataColumnMap.materialPillars,
      cell: ({ row }) => (
        <SimpleTooltip text={row.original.materialPillars}>
          <ClampLines
            id={'materialPillars'}
            text={row.original.materialPillars}
            lines={2}
            moreText=''
            lessText=''
            ellipsis='...'
          />
        </SimpleTooltip>
      ),
    },
    {
      header: assessmentDataColumnMap.materialBoundaries,
      cell: ({ row }) => (
        <SimpleTooltip text={row.original.materialBoundaries}>
          <ClampLines
            id={'materialBoundaries'}
            text={row.original.materialBoundaries}
            lines={2}
            moreText=''
            lessText=''
            ellipsis='...'
          />
        </SimpleTooltip>
      ),
    },
  ];

  return <Table<AssessmentDataRow> data={data} columns={scoresTableColumns} />;
};
