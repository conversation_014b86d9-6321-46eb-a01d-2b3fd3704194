import { ROUTES } from '@constants/routes';
import { AdminBreadcrumbsProps, Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';

export const MTAdminBreadcrumbs = ({ breadcrumbs, initiativeId }: AdminBreadcrumbsProps) => {
  const rootUrl = generateUrl(ROUTES.MATERIALITY_TRACKER_ADMIN, { initiativeId });
  return <Breadcrumbs breadcrumbs={breadcrumbs} rootLabel={'Admin Settings'} rootUrl={rootUrl} />;
}
