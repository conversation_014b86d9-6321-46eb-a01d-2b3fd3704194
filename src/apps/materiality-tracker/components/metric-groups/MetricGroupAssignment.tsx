import { reloadCustomMetricGroupsByInitiativeId } from '@actions/initiative';
import { useRegenerateMetricGroupUtrsMutation } from '@api/metric-groups';
import {
  MaterialityAssessmentScope,
  useGenerateScoresQuery,
  useGetAssessmentSizeQuery,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { QuestionsAssignmentTable } from '@components/custom-metrics/QuestionsAssignmentTable';
import { DEFAULT_CUSTOM_METRICS_USAGE } from '@components/custom-metrics/utils';
import { generateErrorToast, generateToast } from '@components/toasts';
import { PACK, QUESTION } from '@constants/terminology';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { JobStatus } from '@g17eco/types/background-jobs';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { useAppDispatch } from '@reducers/index';
import { skipToken } from '@reduxjs/toolkit/query';
import { DATE, formatDateUTC } from '@utils/date';
import { useEffect, useState } from 'react';
import { Button } from 'reactstrap';
import { getAssessmentTypeLabel, topicLengthMap } from '../../utils';

const getTopicLengthOptions = (scope: MaterialityAssessmentScope | undefined) => {
  if (!scope) {
    return [];
  }
  const maxTopicLength = topicLengthMap[scope];
  const result = [];
  for (let i = 5; i <= maxTopicLength; i += 5) {
    result.push({ label: `Top ${i} topics`, value: i });
  }
  return result;
};

const getDefaultTopicLength = (metricGroup: MetricGroup, scope: MaterialityAssessmentScope | undefined) => {
  if (metricGroup.source?.topTopicsCount) {
    return metricGroup.source.topTopicsCount;
  }
  return scope ? topicLengthMap[scope] : undefined;
};

const generateOutdatedTooltip = (metricGroup: Pick<MetricGroup, 'survey'>) => {
  const { effectiveDate } = metricGroup.survey ?? {};
  if (!effectiveDate) {
    return '';
  }
  return (
    `Assessment scores for ${formatDateUTC(effectiveDate, DATE.MONTH_YEAR)} ${getAssessmentTypeLabel(metricGroup.survey)} has been updated.` +
    ` Please regenerate the ${PACK.SINGULAR} to reflect the latest changes.`
  );
};

interface Props {
  metricGroup: MetricGroup;
  handleReload: () => Promise<void>;
}

export const MetricGroupAssignment = ({ metricGroup, handleReload }: Props) => {
  const initiativeId = metricGroup.initiativeId;
  const assessmentId = metricGroup.survey?._id;

  const { data: latestScoreJob, isFetching: isLoadingScoreJob } = useGenerateScoresQuery(
    initiativeId && assessmentId ? { initiativeId, surveyId: assessmentId } : skipToken,
  );
  const { data, isFetching: isLoadingAssessmentSize } = useGetAssessmentSizeQuery(
    initiativeId && assessmentId ? { initiativeId, assessmentId } : skipToken,
  );
  const [regenerateMetricGroupUtrs] = useRegenerateMetricGroupUtrsMutation();
  const [selectedTopicLength, setSelectedTopicLength] = useState<number | undefined>(undefined);
  const options = getTopicLengthOptions(data?.sizeScope);
  const dispatch = useAppDispatch();

  useEffect(() => {
    setSelectedTopicLength(getDefaultTopicLength(metricGroup, data?.sizeScope));
  }, [data?.sizeScope, metricGroup]);

  const isOutdated =
    !!metricGroup.source?.jobId &&
    latestScoreJob?.status === JobStatus.Completed &&
    latestScoreJob?.jobId !== metricGroup.source?.jobId;

  const handleRegenerate = () => {
    if (selectedTopicLength) {
      regenerateMetricGroupUtrs({ initiativeId, groupId: metricGroup._id, topTopicsCount: selectedTopicLength })
        .unwrap()
        .then(() => {
          generateToast({
            color: 'success',
            message: 'Module regenerated successfully',
          });
          dispatch(reloadCustomMetricGroupsByInitiativeId(initiativeId));
        })
        .catch((error) => {
          generateErrorToast(error);
        });
    }
  };

  return (
    <>
      {isLoadingScoreJob || isLoadingAssessmentSize ? <BlockingLoader /> : null}
      <div className='fw-semibold'>{PACK.CAPITALIZED_SINGULAR} scope</div>
      <div className='mb-2'>
        Which topics would you like to use to generate {QUESTION.PLURAL} for this custom {PACK.SINGULAR}:
      </div>
      <SelectFactory
        selectType={SelectTypes.SingleSelect}
        options={options}
        value={options.find((option) => option.value === selectedTopicLength)}
        onChange={(option) => (option ? setSelectedTopicLength(option.value) : undefined)}
        isSearchable={false}
      />
      <div className='text-right my-3'>
        <Button className='me-2' disabled>
          <i className='fal fa-eye' /> Review changes before regenerating
        </Button>
        <Button color='primary' onClick={handleRegenerate}>
          <i className='fal fa-rotate mr-2' />
          Regenerate {PACK.SINGULAR}
        </Button>
        {isOutdated ? (
          <SimpleTooltip text={generateOutdatedTooltip(metricGroup)}>
            <i className='fal fa-info-circle text-ThemeWarningMedium ml-2' />
          </SimpleTooltip>
        ) : null}
      </div>
      <div className='divider' />
      <QuestionsAssignmentTable
        metricGroup={metricGroup}
        handleReload={handleReload}
        canEdit={false}
        readOnly={false}
        assignQuestionsUrl={''}
        editQuestionUrl={''}
        customMetricsUsage={DEFAULT_CUSTOM_METRICS_USAGE}
      />
    </>
  );
};
