import { reloadCustomMetricGroupsByInitiativeId } from '@actions/initiative';
import { useRegenerateMetricGroupUtrsMutation } from '@api/metric-groups';
import {
  MaterialityAssessmentScope,
  useGenerateScoresQuery,
  useGetAssessmentSizeQuery,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { QuestionsAssignmentTable } from '@components/custom-metrics/QuestionsAssignmentTable';
import { DEFAULT_CUSTOM_METRICS_USAGE } from '@components/custom-metrics/utils';
import { generateErrorToast, generateToast } from '@components/toasts';
import { PACK, QUESTION } from '@constants/terminology';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { JobStatus } from '@g17eco/types/background-jobs';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { useAppDispatch } from '@reducers/index';
import { skipToken } from '@reduxjs/toolkit/query';
import { useToggle } from '@hooks/useToggle';
import { useEffect, useState } from 'react';
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import { topicLengthMap } from '../../utils';

const getTopicLengthOptions = (scope: MaterialityAssessmentScope | undefined) => {
  if (!scope) {
    return [];
  }
  const maxTopicLength = topicLengthMap[scope];
  const result = [];
  for (let i = 5; i <= maxTopicLength; i += 5) {
    result.push({ label: `Top ${i} topics`, value: i });
  }
  return result;
};

const getDefaultTopicLength = (metricGroup: MetricGroup, scope: MaterialityAssessmentScope | undefined) => {
  if (metricGroup.source?.topTopicsCount) {
    return metricGroup.source.topTopicsCount;
  }
  return scope ? topicLengthMap[scope] : undefined;
};

const RegenerateModal = ({
  isOpen,
  toggle,
  handleRegenerate,
}: {
  isOpen: boolean;
  toggle: () => void;
  handleRegenerate: () => void;
}) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>
        <span className='text-ThemeWarningExtradark'>Regenerate {PACK.SINGULAR}</span>
      </ModalHeader>
      <ModalBody>
        <p className='text-ThemeTextMedium'>
          Changes have been made to the underlining material topics relevant to this {PACK.SINGULAR}, however the{' '}
          {PACK.SINGULAR} has not been update to reflect these changes. Would you like to regenerate the topics mapped
          within this {PACK.SINGULAR}?
        </p>
        <p className='fst-italic text-ThemeTextMedium mb-0'>
          Please note: Regenerating the {PACK.SINGULAR} will overwrite any previous edits you have made to it.
        </p>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>

        <Button color='warning' onClick={handleRegenerate}>
          Regenerate {PACK.SINGULAR}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

interface Props {
  metricGroup: MetricGroup;
  handleReload: () => Promise<void>;
}

export const MetricGroupAssignment = ({ metricGroup, handleReload }: Props) => {
  const initiativeId = metricGroup.initiativeId;
  const assessmentId = metricGroup.survey?._id;

  const { data: latestScoreJob, isFetching: isLoadingScoreJob } = useGenerateScoresQuery(
    initiativeId && assessmentId ? { initiativeId, surveyId: assessmentId } : skipToken,
  );
  const { data, isFetching: isLoadingAssessmentSize } = useGetAssessmentSizeQuery(
    initiativeId && assessmentId ? { initiativeId, assessmentId } : skipToken,
  );
  const [regenerateMetricGroupUtrs] = useRegenerateMetricGroupUtrsMutation();
  const [selectedTopicLength, setSelectedTopicLength] = useState<number | undefined>(undefined);
  const [regenerateModal, toggleRegenerateModal, setRegenerateModal] = useToggle(false);
  const options = getTopicLengthOptions(data?.sizeScope);
  const dispatch = useAppDispatch();

  useEffect(() => {
    setSelectedTopicLength(getDefaultTopicLength(metricGroup, data?.sizeScope));
  }, [data?.sizeScope, metricGroup]);

  const isOutdated =
    !!metricGroup.source?.jobId &&
    latestScoreJob?.status === JobStatus.Completed &&
    new Date(latestScoreJob.updatedAt) > new Date(metricGroup.updated);

  const hasChanged = metricGroup.source?.topTopicsCount !== selectedTopicLength;

  const handleRegenerate = () => {
    if (selectedTopicLength) {
      regenerateMetricGroupUtrs({ initiativeId, groupId: metricGroup._id, topTopicsCount: selectedTopicLength })
        .unwrap()
        .then(() => {
          generateToast({
            color: 'success',
            message: 'Module regenerated successfully',
          });
          dispatch(reloadCustomMetricGroupsByInitiativeId(initiativeId));
          setRegenerateModal(false);
        })
        .catch((error) => {
          generateErrorToast(error);
        });
    }
  };

  return (
    <>
      {isLoadingScoreJob || isLoadingAssessmentSize ? <BlockingLoader /> : null}
      <div className='fw-semibold'>{PACK.CAPITALIZED_SINGULAR} scope</div>
      <div className='mb-2'>
        Which topics would you like to use to generate {QUESTION.PLURAL} for this custom {PACK.SINGULAR}:
      </div>
      <SelectFactory
        selectType={SelectTypes.SingleSelect}
        options={options}
        value={options.find((option) => option.value === selectedTopicLength)}
        onChange={(option) => (option ? setSelectedTopicLength(option.value) : undefined)}
        isSearchable={false}
      />
      <div className='text-right my-3'>
        <Button className='me-2' disabled>
          <i className='fal fa-eye' /> Review changes before regenerating
        </Button>
        <Button
          color={isOutdated ? 'warning' : 'primary'}
          onClick={isOutdated ? toggleRegenerateModal : handleRegenerate}
          disabled={!isOutdated && !hasChanged}
        >
          <i className='fal fa-rotate mr-2' />
          Regenerate {PACK.SINGULAR}
        </Button>
      </div>
      <div className='divider' />
      <QuestionsAssignmentTable
        metricGroup={metricGroup}
        handleReload={handleReload}
        canEdit={false}
        readOnly={false}
        assignQuestionsUrl={''}
        editQuestionUrl={''}
        customMetricsUsage={DEFAULT_CUSTOM_METRICS_USAGE}
      />
      <RegenerateModal isOpen={regenerateModal} toggle={toggleRegenerateModal} handleRegenerate={handleRegenerate} />
    </>
  );
};
