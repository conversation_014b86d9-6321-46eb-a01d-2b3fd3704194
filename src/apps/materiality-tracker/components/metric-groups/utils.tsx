/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { MaterialitySurveyModelMinData } from '@apps/materiality-tracker/api/materiality-assessment';
import { getAssessmentTypeLabel } from '@apps/materiality-tracker/utils';
import { CardGridButtonType, CustomMetricCardProps } from '@components/custom-metrics/CustomMetricGroups';
import { CardGridButtonProps } from '@components/survey-scope/CardGrid';
import { getCardIcon } from '@components/survey-scope/CustomMetricCards';
import { PACK, QUESTION } from '@constants/terminology';
import { AccessType, MetricGroup } from '@g17eco/types/metricGroup';
import { DATE, formatDateUTC } from '@utils/date';
import { Button } from 'reactstrap';
import config from '../../../../config';

export interface MetricGroupsProps {
  metricGroups: MetricGroup[];
  handleEditMetricGroup: (groupId: string) => void;
}

export const getCustomMetricGroups = ({ metricGroups, handleEditMetricGroup }: MetricGroupsProps) => {
  const getButtons: (metricGroup: MetricGroup) => CardGridButtonProps[] = (metricGroup) => {
    return [
      metricGroup.source
        ? {
            type: CardGridButtonType.Edit,
            button: (
              <Button color='secondary' size='xs' onClick={() => handleEditMetricGroup(metricGroup._id)}>
                <i className='fal fa-pencil mr-2' />
                Edit {PACK.SINGULAR}
              </Button>
            ),
          }
        : {
            type: CardGridButtonType.Edit,
            button: (
              <Button disabled size='xs'>
                {metricGroup.survey?.completedDate ? 'Regenerate scores to view' : 'Complete assessment to view'}
              </Button>
            ),
          },
    ];
  };

  const cards: CustomMetricCardProps[] = metricGroups.map((metricGroup: MetricGroup) => {
    const card: CustomMetricCardProps = {
      key: `scope-cardgriditem-metricgroup-${metricGroup._id}`,
      title: <span>{metricGroup.groupName}</span>,
      sortTitle: metricGroup.groupName,
      displayGridSubtitles: true,
      subtitle: getAssessmentTypeLabel(metricGroup.survey),
      description: metricGroup.description ?? '',
      icon: getCardIcon(metricGroup),
      unitCount: metricGroup.universalTrackers?.length ?? 0,
      unitName: QUESTION.CAPITALIZED_PLURAL,
      buttons: getButtons(metricGroup),
      scopeTag: metricGroup._id,
      inScope: false,
      isPartial: false,
      accessType: AccessType.Custom,
    };
    return card;
  });

  return cards;
};

export const initializeMetricGroupFromSurvey = (survey: MaterialitySurveyModelMinData): MetricGroup => ({
  _id: `metric-group-${survey._id}`,
  groupName: formatDateUTC(survey.effectiveDate, DATE.MONTH_YEAR),
  groupData: {
    icon: `${config.media.logosBaseUrl}/materiality_tracker_logo.svg`,
  },
  initiativeId: survey.initiativeId,
  accessType: AccessType.Custom,
  survey,
  updated: new Date().toISOString(),
});
