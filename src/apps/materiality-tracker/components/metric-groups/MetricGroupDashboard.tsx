import { getAssessmentType } from '@apps/materiality-tracker/utils';
import { DashboardSection } from '@components/dashboard';
import CardGrid, { CardGridItem, CardGridItemProps, CardGridViewMode } from '@components/survey-scope/CardGrid';
import { ROUTES } from '@constants/routes';
import { PACK, QUESTION } from '@constants/terminology';
import { SearchBox } from '@g17eco/molecules/search';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { generateUrl } from '@routes/util';
import { assessmentTypeLabels } from '@utils/materiality';
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { getCustomMetricGroups } from './utils';

interface Props {
  initiativeId: string;
  metricGroups: MetricGroup[];
}

interface Filters {
  searchText: string;
  assessmentType: string;
}

const allOption = { value: 'all', label: 'All assessments' };

const assessmentTypeOptions = [
  ...Object.entries(assessmentTypeLabels).map(([value, label]) => ({ value, label })),
  allOption,
];

const DashboardToolbar = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return (
    <div
      className={`d-flex align-items-center justify-content-between gap-2 py-2 px-3 background-ThemeBgToolbar rounded ${className}`}
    >
      {children}
    </div>
  );
};

export const MetricGroupDashboard = ({ initiativeId, metricGroups }: Props) => {
  const [filters, setFilters] = useState<Filters>({ searchText: '', assessmentType: allOption.value });
  const history = useHistory();

  const handleChangeFilters = (newFilters: Partial<Filters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const filteredMetricGroups = metricGroups.filter((metricGroup) => {
    const assessmentType = getAssessmentType(metricGroup.survey);
    const isMatchingAssessmentType =
      filters.assessmentType === allOption.value || filters.assessmentType === assessmentType;

    const isMatchingSearchText =
      !filters.searchText || metricGroup.groupName.toLowerCase().includes(filters.searchText.toLowerCase());

    return isMatchingAssessmentType && isMatchingSearchText;
  });

  const handleEditMetricGroup = (groupId: string) => {
    const url = generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId, groupId });
    history.push(url);
  };

  const metricGroupCards = getCustomMetricGroups({
    metricGroups: filteredMetricGroups,
    handleEditMetricGroup,
  });

  return (
    <>
      <h3 className='ml-3'>
        Custom {QUESTION.SINGULAR} {PACK.PLURAL}
      </h3>
      <DashboardSection>
        <div>
          Below are the custom {PACK.PLURAL} generated from your completed assessments. {PACK.CAPITALIZED_PLURAL} are
          automatically created per assessment, with {QUESTION.PLURAL} tailored to your company's results based on key
          factors such as material topics, industry, company size, and location. To adjust mapped {QUESTION.PLURAL},
          click 'Edit {PACK.CAPITALIZED_SINGULAR}'.
        </div>
        <DashboardToolbar className='my-3'>
          <SearchBox
            handleOnChange={(e) => handleChangeFilters({ searchText: e.target.value })}
            searchText={filters.searchText}
            placeholder={`Search for custom ${PACK.SINGULAR}`}
            classNames={{ wrapper: 'w-100' }}
          />
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            options={assessmentTypeOptions}
            value={assessmentTypeOptions.find(({ value }) => value === filters.assessmentType)}
            onChange={(option) => handleChangeFilters({ assessmentType: option?.value ?? 'all' })}
            backgroundColor='var(--theme-BgExtralight)'
            className='col-4'
          />
        </DashboardToolbar>
        <CardGrid viewLayout={CardGridViewMode.gallery}>
          {metricGroupCards.map(({ key, ...cardProps }: CardGridItemProps) => (
            <CardGridItem key={key} {...cardProps} />
          ))}
        </CardGrid>
      </DashboardSection>
    </>
  );
};
