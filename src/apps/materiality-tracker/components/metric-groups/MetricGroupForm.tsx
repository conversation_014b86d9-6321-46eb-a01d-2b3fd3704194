import { MetricGroupAssignment } from '@apps/materiality-tracker/components/metric-groups/MetricGroupAssignment';
import { ViewMode } from '@components/custom-metrics/constants';
import { MetricGroupConfiguration, MetricGroupField } from '@components/custom-metrics/MetricGroupConfiguration';
import { DashboardSection } from '@components/dashboard';
import { Menu } from '@components/menu/Menu';
import { ROUTES } from '@constants/routes';
import { PACK, QUESTION } from '@constants/terminology';
import { CustomMetricRouteParams } from '@features/custom-metrics';
import { Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { FieldProps } from '@g17eco/molecules/form';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { generateUrl } from '@routes/util';
import { useHistory } from 'react-router-dom';
import { getAssessmentTypeLabel } from '../../utils';

interface Props {
  initiativeId: string;
  metricGroup: MetricGroup;
  viewMode?: ViewMode;
  handleReload: () => Promise<void>;
}

const metricGroupFields: FieldProps<MetricGroupField>[] = [
  {
    code: 'groupName',
    type: 'text',
    required: true,
    label: `${PACK.CAPITALIZED_SINGULAR} name`,
    disabled: true,
  },
  {
    code: 'description',
    type: 'textarea',
    required: false,
    label: `${PACK.CAPITALIZED_SINGULAR} description`,
    placeholder: 'Add description here...',
  },
];

export const MetricGroupForm = ({ initiativeId, viewMode = ViewMode.Edit, metricGroup, handleReload }: Props) => {
  const rootUrl = generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId });
  const history = useHistory();
  const groupId = metricGroup._id;

  const handleClickTab = (viewMode: ViewMode) => {
    const url = generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId, groupId, viewMode });
    history.push(url);
  };

  const handleChangeMetricGroup = (_: CustomMetricRouteParams) => {
    // back to metric group dashboard
    history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId }));
  };

  const renderView = () => {
    switch (viewMode) {
      case ViewMode.Edit:
        return (
          <MetricGroupConfiguration
            metricGroup={metricGroup}
            fields={metricGroupFields}
            readOnly={false}
            isIconEditable={false}
            isPreferredAltCodesDisplayed={false}
            handleReload={handleReload}
            handleChangeMetricGroup={handleChangeMetricGroup}
            editUrl={generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId, groupId, viewMode })}
          />
        );
      case ViewMode.AssignQuestions:
        return <MetricGroupAssignment metricGroup={metricGroup} handleReload={handleReload} />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className='ml-2'>
        <Breadcrumbs
          breadcrumbs={[]}
          rootUrl={rootUrl}
          rootLabel={
            <span>
              <i className='fal fa-caret-left' /> {`Custom ${QUESTION.SINGULAR} ${PACK.PLURAL}`}
            </span>
          }
        />
      </div>
      <h3 className='ml-3 my-3'>
        {metricGroup.groupName}: {getAssessmentTypeLabel(metricGroup.survey)}
      </h3>
      <DashboardSection>
        <Menu
          className='mb-4'
          items={[
            {
              active: viewMode === ViewMode.Edit,
              onClick: () => handleClickTab(ViewMode.Edit),
              label: 'Configuration',
            },
            {
              active: viewMode === ViewMode.AssignQuestions,
              onClick: () => handleClickTab(ViewMode.AssignQuestions),
              label: `Assign ${QUESTION.PLURAL}`,
            },
          ]}
        />
        {renderView()}
      </DashboardSection>
    </>
  );
};
