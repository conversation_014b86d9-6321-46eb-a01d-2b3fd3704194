import React from 'react';
import { Google<PERSON>hartWrapper, ReactGoogleChartEvent } from 'react-google-charts/dist/types';
import { DoubleMaterialityTopicChart } from './DoubleMaterialityTopicChart';
import {
  DoubleMaterialityAssessmentData,
  MaterialityAssessmentScope,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { topicLengthMap } from '../../../../utils';

export interface DataSource {
  loaded: boolean;
  width?: number;
  height?: number;
  chart?: string;
}

export interface DataSources {
  doubleMaterialityTopics: DataSource;
}

export const CHART_DEFAULTS = {
  OPTIONS: {
    vAxis: {
      format: 'decimal',
      gridlines: { count: 4 },
    },
    colors: ['#A6CFE5', '#D6F5DF', '#1A5A87'],
    pieSliceTextStyle: {
      color: '#0c3866',
    },
    legend: {
      position: 'none',
    },
    chartArea: {
      top: 50,
      bottom: 20,
      left: 80,
      right: 20,
    },
    titleTextStyle: {
      color: 'a1a3a4',
    },
  },
};

interface ChartReadyHandler {
  key: keyof DataSources;
  data: DataSource;
  handler: (key: string, data: DataSource) => void;
}

export const chartReadyEvent = (params: ChartReadyHandler): ReactGoogleChartEvent => {
  const { key, data, handler } = params;
  return {
    eventName: 'ready',
    callback: (ChartRef: { chartWrapper: GoogleChartWrapper }) => {
      data.chart = (ChartRef.chartWrapper.getChart().getImageURI() as unknown as string).split(',')[1];
      handler(key, data);
    },
  };
};

export interface GenerateChartsProps {
  updateData: (key: string, data: DataSource) => void;
  data: DoubleMaterialityAssessmentData[];
  sizeScope: MaterialityAssessmentScope;
}

export const GenerateCharts = React.memo(({ data, sizeScope, ...props }: GenerateChartsProps) => {
  const topTopicsData = data.slice(0, topicLengthMap[sizeScope]);
  return (
    <div style={{ height: 0, width: 0, overflow: 'hidden' }}>
      <DoubleMaterialityTopicChart {...props} data={topTopicsData} />
    </div>
  );
});
