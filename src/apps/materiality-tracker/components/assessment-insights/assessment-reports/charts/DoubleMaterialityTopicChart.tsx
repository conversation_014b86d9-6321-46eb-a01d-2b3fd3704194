import React from 'react';
import { Chart } from 'react-google-charts';
import { GenerateChartsProps, chartReadyEvent, DataSource } from './GenerateCharts';
import variables from '../../../../../../css/variables.module.scss';
import { truncate } from '@utils/string';

const WIDTH = 600;
const HEIGHT = 650;
const GROUP_WIDTH = '75%';
const MAX_TOPIC_LENGTH = 40;

const HEADERS = ['Topic', 'Financial', 'Impact'];
const COLORS = {
  BORDER: variables.BorderDefault,
  BASELINE: variables.BgExtralight  ,
  FINANCIAL_COLUMN: '691472',
  NONFINANCIAL_COLUMN: 'b4a7d6',
};

export const DoubleMaterialityTopicChart = React.memo((props: Omit<GenerateChartsProps, 'sizeScope'>) => {
  const { data, updateData } = props;

  const reportData: DataSource = {
    loaded: true,
    width: WIDTH,
    height: HEIGHT,
  };

  const chartData = data.map((topic) => [
    truncate(topic.name ?? '', MAX_TOPIC_LENGTH),
    topic.financialRelativeScore ?? NaN,
    topic.nonFinancialRelativeScore ?? NaN,
  ]);

  return (
    <Chart
      chartType='BarChart'
      data={[HEADERS, ...chartData]}
      options={{
        bar: {
          groupWidth: GROUP_WIDTH,
        },
        chartArea: {
          top: 40,
          left: '50%',
          width: '50%',
          height: '90%',
        },
        vAxis: {
          gridlines: {
            count: 0,
          },
        },
        legend: { position: 'top', maxLines: 1 },
        baselineColor: COLORS.BASELINE,
        colors: [COLORS.FINANCIAL_COLUMN, COLORS.NONFINANCIAL_COLUMN],
        isStacked: true,
      }}
      width={WIDTH}
      height={HEIGHT}
      chartEvents={[chartReadyEvent({ key: 'doubleMaterialityTopics', data: reportData, handler: updateData })]}
    />
  );
});
