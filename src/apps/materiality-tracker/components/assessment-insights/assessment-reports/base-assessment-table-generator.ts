import { getBorders, justifiedParagraph, spacer } from '@components/report-output/document-structure';
import { SafeTextRun } from '@utils/docx';
import {
  Paragraph,
  AlignmentType,
  TableRow,
  TableCell,
  Table,
  WidthType,
  IShadingAttributesProperties,
  VerticalAlign,
} from 'docx';
import {
  MaterialityAssessmentScope,
  MaterialityBoundary,
  MaterialPillar,
  UtrMapping,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { createArrayOfNumbers } from '@utils/array';
import { Framework, frameworks, Standard, standards } from '@g17eco/core';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { loggerMessage } from '../../../../../logger';
import { boundariesMap, MARGINS_SM, MAX_SUGGESTED_TOPIC, pillarShadingMap, STYLES, topTopicShadingMap } from './utils';
import { PrioritizedAssessmentData } from '../types';
import { capitaliseFirstLetter } from '@utils/index';
import { topicLengthMap } from '@apps/materiality-tracker/utils';
import { QUESTION } from '@constants/terminology';

export class BaseAssessmentTableGenerator<T extends PrioritizedAssessmentData> {
  /** Top x by scores topics for each materiality pillar based on sizeScope */
  protected topicsByPillarsMap: Map<string, T[]>;
  /** Unique topics sorted by scores */
  protected topRelevantTopics: T[];
  /** Top x by scores topics for each materiality boundary based on unique topics based on sizeScope */
  protected topicsByBoundariesMap: Map<string, T[]>;
  /** All topics scopes */
  protected topicScopes: Map<string, Standard | Framework>;
  /** Blueprint for question lookup */
  protected blueprintQuestions: Map<string, UniversalTrackerBlueprintMin>;
  /** Max topic length based on assessment's size scope */
  protected topicLength: number;
  /** Relevant suggested topics for each materiality pillar */
  private suggestedTopicsByPillarsMap: Map<string, T[]>;
  private initiativeName: string;

  constructor({
    result,
    questions,
    sizeScope,
    initiativeName,
  }: {
    readonly result: T[];
    readonly questions: UniversalTrackerBlueprintMin[];
    readonly sizeScope: MaterialityAssessmentScope;
    initiativeName: string;
  }) {
    this.initiativeName = initiativeName;
    this.topicLength = topicLengthMap[sizeScope];
    this.topRelevantTopics = this.getTopRelevantTopics(result);
    this.topicsByPillarsMap = this.getTopicsByPillarMap(this.topRelevantTopics);
    this.topicsByBoundariesMap = this.getTopicsByBoundaryMap(this.topRelevantTopics);
    this.suggestedTopicsByPillarsMap = this.getSuggestedTopicsByPillarsMap(this.getExcludedTopics(result));
    this.topicScopes = new Map<string, Standard | Framework>([
      ...Object.entries(standards),
      ...Object.entries(frameworks),
    ]);
    this.blueprintQuestions = new Map<string, UniversalTrackerBlueprintMin>(questions.map((q) => [q.code, q]));
  }

  protected getTopicsByPillar(result: T[], pillar: MaterialPillar) {
    return result.filter((topic) => topic.categories?.materialPillar?.includes(pillar));
  }

  protected getTopicsByPillarMap(result: T[]) {
    return new Map(
      Object.values(MaterialPillar).map<[string, T[]]>((pillar) => [pillar, this.getTopicsByPillar(result, pillar)]),
    );
  }

  protected getTopicsByBoundary(result: T[], boundary: MaterialityBoundary) {
    return result.filter((topic) => topic.categories?.boundary?.includes(boundary));
  }

  protected getTopicsByBoundaryMap(result: T[]) {
    return new Map(
      Object.values(MaterialityBoundary).map<[string, T[]]>((boundary) => [
        boundary,
        this.getTopicsByBoundary(result, boundary),
      ]),
    );
  }

  protected getSuggestedTopicsByPillarsMap(result: T[]) {
    return new Map(
      Object.values(MaterialPillar).map<[string, T[]]>((pillar) => [
        pillar,
        this.getTopicsByPillar(result, pillar).slice(0, MAX_SUGGESTED_TOPIC),
      ]),
    );
  }

  protected getTopRelevantTopics(result: T[]) {
    return result.slice(0, this.topicLength);
  }

  protected getExcludedTopics(result: T[]) {
    return result.slice(this.topicLength);
  }

  protected createTableCell({
    text,
    cellWidth,
    textStyles: { alignment, color, bold, italics } = {},
    cellStyles: { shading, verticalAlign = VerticalAlign.TOP, columnSpan } = {},
  }: {
    text?: string | string[];
    cellWidth: string;
    textStyles?: {
      alignment?: AlignmentType;
      color?: string;
      bold?: boolean;
      italics?: boolean;
    };
    cellStyles?: {
      shading?: IShadingAttributesProperties;
      verticalAlign?: VerticalAlign;
      columnSpan?: number;
    };
  }) {
    const mergedStyles = { ...STYLES.TABLE_CELL, ...(color ? { color } : {}) };
    const paragraph = new Paragraph({
      children: Array.isArray(text)
        ? text.map((subText) => new SafeTextRun({ ...mergedStyles, text: subText, bold, italics }))
        : [new SafeTextRun({ ...mergedStyles, text, bold, italics })],
      alignment,
    });
    return new TableCell({
      width: { type: WidthType.PERCENTAGE, size: cellWidth },
      children: [paragraph],
      margins: MARGINS_SM,
      borders: getBorders(STYLES.TABLE_BORDER_COLOUR),
      shading,
      verticalAlign,
      columnSpan,
    });
  }

  protected createMappedMetricCell({ cellWidth, utrMapping }: { cellWidth: string; utrMapping?: UtrMapping[] }) {
    if (!utrMapping || utrMapping.length === 0) {
      return new TableCell({
        width: { type: WidthType.PERCENTAGE, size: cellWidth },
        children: [
          new Paragraph({
            children: [
              new SafeTextRun({ ...STYLES.TABLE_CELL, text: 'No relevant metrics', italics: true, color: '666666' }),
            ],
          }),
        ],
        margins: MARGINS_SM,
        borders: getBorders(STYLES.TABLE_BORDER_COLOUR),
      });
    }
    const paragraphs = utrMapping.map((utr) => {
      const { title, description } = this.getQuestionInfo(utr);
      return new Paragraph({
        children: [
          new SafeTextRun({ ...STYLES.TABLE_CELL, text: title, bold: true }),
          new SafeTextRun({ ...STYLES.TABLE_CELL, text: description }),
        ],
      });
    });
    return new TableCell({
      width: { type: WidthType.PERCENTAGE, size: cellWidth },
      children: paragraphs,
      margins: MARGINS_SM,
      borders: getBorders(STYLES.TABLE_BORDER_COLOUR),
    });
  }

  protected createActionCell({ cellWidth, action }: { cellWidth: string; action?: string }) {
    if (!action) {
      return new TableCell({
        width: { type: WidthType.PERCENTAGE, size: cellWidth },
        children: [
          new Paragraph({
            children: [
              new SafeTextRun({ ...STYLES.TABLE_CELL, text: 'No relevant actions', italics: true, color: '666666' }),
            ],
          }),
        ],
        margins: MARGINS_SM,
        borders: getBorders(STYLES.TABLE_BORDER_COLOUR),
      });
    }
    const paragraphs = action
      .split('\n')
      .filter(Boolean)
      .map((text) => {
        return new Paragraph({
          children: [new SafeTextRun({ ...STYLES.TABLE_CELL, text })],
        });
      });
    return new TableCell({
      width: { type: WidthType.PERCENTAGE, size: cellWidth },
      children: paragraphs,
      margins: MARGINS_SM,
      borders: getBorders(STYLES.TABLE_BORDER_COLOUR),
    });
  }

  protected getTopicName(topic: T) {
    return topic.name || topic.code;
  }

  protected getLatestStandardCode(question: UniversalTrackerBlueprintMin) {
    const standard = this.topicScopes.get(question.type);
    const alternativeStandard = Object.keys(question.alternatives || {})
      .reduce<(Standard | Framework)[]>((acc, code) => {
        const alternative = this.topicScopes.get(code);
        if (alternative) {
          acc.push(alternative);
        }
        return acc;
      }, [])
      .find((s) => s.versionGroupCode === standard?.versionGroupCode);

    if (alternativeStandard?.version && standard?.version && alternativeStandard.version > standard.version) {
      return alternativeStandard.code;
    }

    return standard?.code || question.type;
  }

  protected getQuestionScope(utr: UtrMapping) {
    const question = this.blueprintQuestions.get(utr.code);
    if (!question) {
      return '';
    }
    const code = this.getLatestStandardCode(question);
    // Expected output: [UTR standard]
    return this.topicScopes.get(code)?.name || '';
  }

  protected getQuestionInfo(utr: UtrMapping) {
    const question = this.blueprintQuestions.get(utr.code);
    if (!question) {
      loggerMessage('Question not found', { level: 'error', questionCode: utr.code });
      return {
        title: '',
        description: '',
      };
    }
    // Expected output: [UTR standard]: [URT Title]
    return {
      title: `${this.getQuestionScope(utr)} [${question.typeCode}]: `,
      description: question.valueLabel,
    };
  }

  protected getSuggestedContent(pillar: MaterialPillar) {
    const suggested = this.suggestedTopicsByPillarsMap.get(pillar);
    const pillarName = capitaliseFirstLetter(pillar);
    if (!suggested || suggested.length === 0) {
      return justifiedParagraph(`Based on your assessment results, no highly relevant topics have been categorised under the ${pillarName} pillar for ${this.initiativeName}.`);
    }
    const firstSuggestion = suggested[0];
    const secondSuggestion = suggested[1];
    return justifiedParagraph([
      new SafeTextRun(
        `Based on your assessment results, no highly relevant topics have been categorised under the ${pillarName} pillar for ${this.initiativeName}.`,
      ),
      ...(firstSuggestion
        ? [
            new SafeTextRun(' To enhance your organisation\'s holistic approach, you may consider incorporating '),
            new SafeTextRun({ text: firstSuggestion.name, bold: true }),
            new SafeTextRun(
              `, which ranked ${firstSuggestion.priority} with a score of ${firstSuggestion.relativeScore ?? ''}%. `,
            ),
          ]
        : []),
      ...(secondSuggestion
        ? [
            new SafeTextRun('Alternatively, '),
            new SafeTextRun({ text: secondSuggestion.name, bold: true }),
            new SafeTextRun(
              `, ranked ${secondSuggestion.priority} with a score of ${secondSuggestion.relativeScore}%, could also provide valuable insights into this area.`,
            ),
          ]
        : []),
    ]);
  }

  public getStandardsTables() {
    return Object.values(MaterialPillar).flatMap((pillar) => {
      const { main, sub } = pillarShadingMap[pillar];
      const initialRows = [
        new TableRow({
          children: [
            this.createTableCell({
              cellWidth: '100%',
              text: pillar.toUpperCase(),
              textStyles: {
                alignment: AlignmentType.CENTER,
                color: 'ffffff',
              },
              cellStyles: {
                shading: { fill: main },
                columnSpan: 2,
              },
            }),
          ],
        }),
        new TableRow({
          children: [
            this.createTableCell({ text: 'MATERIAL TOPIC', cellWidth: '66%', cellStyles: { shading: { fill: sub } } }),
            this.createTableCell({
              text: 'STANDARDS & FRAMEWORKS',
              cellWidth: '34%',
              cellStyles: { shading: { fill: sub } },
            }),
          ],
        }),
      ];
      const topics = this.topicsByPillarsMap.get(pillar);
      const hasData = topics && topics.length > 0;
      const suggestedContent = hasData ? [] : [this.getSuggestedContent(pillar)];
      return [
        new Table({
          ...STYLES.TABLE,
          rows: initialRows.concat(
            hasData
              ? topics.map((topic) => {
                  const topicName = this.createTableCell({ cellWidth: '66%', text: topic?.name || topic?.code });
                  const uniqueStandards = (topic?.utrMapping || []).reduce<string[]>((acc, utr) => {
                    const standard = this.getQuestionScope(utr);
                    if (!standard || acc.includes(standard)) {
                      return acc;
                    }
                    acc.push(standard);
                    return acc;
                  }, []);
                  const displayText =
                    uniqueStandards.length > 0
                      ? this.createTableCell({ cellWidth: '34%', text: uniqueStandards.join(', ') })
                      : this.createTableCell({
                          cellWidth: '34%',
                          text: `No ${QUESTION.SINGULAR} mapped`,
                          textStyles: { color: '666666', italics: true },
                        });

                  return new TableRow({
                    children: [topicName, displayText],
                  });
                })
              : [],
          ),
        }),
        ...suggestedContent,
        spacer(),
      ];
    });
  }

  public getBoundariesTables() {
    return Object.values(MaterialPillar).flatMap((pillar) => {
      const { main, sub } = pillarShadingMap[pillar];
      const initialRows = [
        new TableRow({
          children: [
            this.createTableCell({
              cellWidth: '100%',
              text: pillar.toUpperCase(),
              textStyles: {
                alignment: AlignmentType.CENTER,
                color: 'ffffff',
              },
              cellStyles: {
                shading: { fill: main },
                columnSpan: 8,
              },
            }),
          ],
        }),
        new TableRow({
          children: [
            this.createTableCell({ cellWidth: '30%', text: 'MATERIAL TOPIC', cellStyles: { shading: { fill: sub } } }),
            ...Object.values(boundariesMap).map((boundary) =>
              this.createTableCell({
                cellWidth: '10%',
                text: boundary,
                textStyles: {
                  alignment: AlignmentType.CENTER,
                },
                cellStyles: {
                  shading: { fill: sub },
                },
              }),
            ),
          ],
        }),
      ];

      const topics = this.topicsByPillarsMap.get(pillar);
      const hasData = topics && topics.length > 0;
      const suggestedContent = hasData ? [] : [this.getSuggestedContent(pillar)];
      return [
        new Table({
          ...STYLES.TABLE,
          rows: initialRows.concat(
            hasData
              ? topics.map((topic) => {
                  const topicName = this.createTableCell({ cellWidth: '30%', text: topic?.name || topic?.code });

                  const boundaryValues = Object.keys(boundariesMap).map((boundary) => {
                    const isIncluded = topic?.categories?.boundary?.includes(boundary as MaterialityBoundary);
                    return this.createTableCell({
                      cellWidth: '10%',
                      text: isIncluded ? '✓' : '',
                      textStyles: { alignment: AlignmentType.CENTER },
                      cellStyles: { verticalAlign: VerticalAlign.CENTER },
                    });
                  });

                  return new TableRow({
                    children: [topicName, ...boundaryValues],
                  });
                })
              : [],
          ),
        }),
        ...suggestedContent,
        spacer(),
      ];
    });
  }

  public getTopTopicsPerBoundaryTable() {
    const topFiveScoreTopicCodes = this.topRelevantTopics.slice(0, 5).map((topic) => topic.code);
    const initialRows = [
      new TableRow({
        children: Object.values(boundariesMap).map((boundary) =>
          this.createTableCell({
            cellWidth: '14%',
            text: boundary,
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ),
      }),
    ];
    const maxRowCount = Math.max(...Array.from(this.topicsByBoundariesMap.values()).map((topics) => topics.length));

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        createArrayOfNumbers(0, maxRowCount - 1).map((index) => {
          return new TableRow({
            children: Object.keys(boundariesMap).map((boundary) => {
              const topic = this.topicsByBoundariesMap.get(boundary)?.[index];
              const isBoldText = topic?.code ? topFiveScoreTopicCodes.includes(topic.code) : false;
              return this.createTableCell({
                cellWidth: '14%',
                text: topic?.name || topic?.code,
                cellStyles: { shading: { fill: topTopicShadingMap[Math.floor(index / 5)] } },
                textStyles: { bold: isBoldText },
              });
            }),
          });
        }),
      ),
    });
  }

  public getTopicMappedMetricsTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '34%',
            text: 'TOPIC',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '66%',
            text: 'MAPPED METRIC',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics.map((topic) => {
          return new TableRow({
            children: [
              this.createTableCell({ cellWidth: '34%', text: this.getTopicName(topic) }),
              this.createMappedMetricCell({
                cellWidth: '66%',
                utrMapping: topic?.utrMapping,
              }),
            ],
          });
        }),
      ),
    });
  }

  public getTopicActionsTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '34%',
            text: 'TOPIC',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '66%',
            text: 'ACTION',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics.map((topic) => {
          return new TableRow({
            children: [
              this.createTableCell({ cellWidth: '34%', text: this.getTopicName(topic) }),
              this.createActionCell({ cellWidth: '66%', action: topic.action }),
            ],
          });
        }),
      ),
    });
  }
}
