import {
  TableRow,
  Table,
} from 'docx';
import {
  MaterialityAssessmentScope,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { BaseAssessmentTableGenerator } from './base-assessment-table-generator';
import { STYLES } from './utils';
import { PrioritizedAssessmentData } from '../types';

export class AssessmentTableGenerator extends BaseAssessmentTableGenerator<PrioritizedAssessmentData> {
  constructor({
    result,
    questions,
    sizeScope,
    initiativeName,
  }: {
    readonly result: PrioritizedAssessmentData[];
    readonly questions: UniversalTrackerBlueprintMin[];
    readonly sizeScope: MaterialityAssessmentScope;
    initiativeName: string;
  }) {
    super({ result, questions, sizeScope, initiativeName });
  }

  public getTopicScoresWithDescTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '10%',
            text: 'SCORE',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '24%',
            text: 'TOPIC',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '66%',
            text: 'DESCRIPTION',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics.map((topic) => {
          return new TableRow({
            children: [
              this.createTableCell({ cellWidth: '10%', text: `${topic?.relativeScore || 0}%` }),
              this.createTableCell({ cellWidth: '24%', text: this.getTopicName(topic) }),
              this.createTableCell({ cellWidth: '66%', text: topic.description }),
            ],
          });
        }),
      ),
    });
  }
}
