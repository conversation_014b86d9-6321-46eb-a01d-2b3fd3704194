import {
  MaterialityAssessmentScope,
  MaterialPillar,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { AssessmentTableGenerator } from './assessment-table-generator';
import { createAssessmentData, createAssessmentCategory } from '@fixtures/materiality-data';
import { Paragraph, Table } from 'docx';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { createUtr } from '@fixtures/utr/utrv-factory';
import { PrioritizedAssessmentData } from '../types';

const SUGGESTED_PARAGRAPH_KEY = 'w:r';
const TABLE_ROW_KEY = 'w:tr';

const STANDARDS_TABLE_HEADER_ROW_COUNT = 2;
const BOUNDARIES_TABLE_HEADER_ROW_COUNT = 2;
const TOP_TOPICS_PER_BOUNDARY_HEADER_ROW_COUNT = 1;
const TOPIC_SCORE_DESC_TABLE_HEADER_ROW_COUNT = 1;
const TOPIC_MAPPED_METRICS_TABLE_HEADER_ROW_COUNT = 1;
const TOPIC_ACTIONS_TABLE_HEADER_ROW_COUNT = 1;

const STANDARDS_TABLE_COLUMN_COUNT = 2;
const BOUNDARIES_TABLE_COLUMN_COUNT = 8;
const TOP_TOPICS_PER_BOUNDARY_COLUMN_COUNT = 7;
const TOPIC_SCORE_DESC_TABLE_COLUMN_COUNT = 3;
const TOPIC_MAPPED_METRICS_TABLE_COLUMN_COUNT = 2;
const TOPIC_ACTIONS_TABLE_COLUMN_COUNT = 2;

const getGenerator = ({
  result,
  questions,
}: {
  result: PrioritizedAssessmentData[];
  questions?: UniversalTrackerBlueprintMin[];
}) => {
  return new AssessmentTableGenerator({
    result,
    questions: questions ?? [],
    sizeScope: MaterialityAssessmentScope.Micro,
    initiativeName: 'Test Initiative',
  });
};

const parseDocxObject = (item: Paragraph | Table): { [key: string]: any; root: any[] } => {
  const stringifiedJson = JSON.stringify(item);
  return JSON.parse(stringifiedJson);
};

describe('AssessmentTableGenerator', () => {
  const result = [
    createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.People] }) }),
    createAssessmentData({
      categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Partnership] }),
    }),
    createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Planet] }) }),
    createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Principle] }) }),
    createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Prosperity] }) }),
  ];

  const questions: UniversalTrackerBlueprintMin[] = result
    .flatMap((assessmentData) => assessmentData.utrMapping ?? [])
    .map((utr) => ({
      ...createUtr(utr.code),
    }));

  const generator = getGenerator({
    result: [
      createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.People] }) }),
      createAssessmentData({
        categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Partnership] }),
      }),
      createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Planet] }) }),
      createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Principle] }) }),
      createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Prosperity] }) }),
    ],
    questions,
  });

  describe('getStandardsTables', () => {
    it('should return a table for each pillar', () => {
      const output = generator.getStandardsTables();
      const tables = output.filter((item) => {
        return item instanceof Table;
      });
      expect(tables).toHaveLength(Object.values(MaterialPillar).length);
      tables.forEach((table) => {
        const parsedTable = parseDocxObject(table);
        const tableRows = parsedTable.root.filter((item) => item.rootKey === TABLE_ROW_KEY);
        expect(tableRows).toHaveLength(STANDARDS_TABLE_HEADER_ROW_COUNT + 1);
        expect(tableRows[2].options.children).toHaveLength(STANDARDS_TABLE_COLUMN_COUNT);
      });
    });

    it('should return an empty table and a suggested paragraph if the topic does not have assessment data', () => {
      const generator = getGenerator({
        result: [
          createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.People] }) }),
          createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Planet] }) }),
          createAssessmentData({
            categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Principle] }),
          }),
          createAssessmentData({
            categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Prosperity] }),
          }),
        ],
      });
      const output = generator.getStandardsTables();
      const tables = output.filter((item) => {
        return item instanceof Table;
      });
      const suggestedParagraph = output.reduce((acc, item) => {
        if (item instanceof Paragraph) {
          const parsedItem = parseDocxObject(item);
          const isSuggested = Array.isArray(parsedItem.root)
            ? parsedItem.root.some((el: { rootKey: string }) => el.rootKey === SUGGESTED_PARAGRAPH_KEY)
            : false;
          return isSuggested ? [...acc, item] : acc;
        }
        return acc;
      }, [] as any[]);
      expect(tables).toHaveLength(Object.values(MaterialPillar).length);

      const noDataTables = tables.reduce((acc, table) => {
        const parsedTable = parseDocxObject(table);
        const isNoData = parsedTable.root.filter((item) => item.rootKey === TABLE_ROW_KEY).length === 2;
        return isNoData ? [...acc, table] : acc;
      }, [] as Table[]);

      expect(noDataTables).toHaveLength(1);
      expect(suggestedParagraph).toHaveLength(1);
    });
  });

  describe('getBoundariesTables', () => {
    it('should return a table for each pillar', () => {
      const output = generator.getBoundariesTables();
      const tables = output.filter((item) => {
        return item instanceof Table;
      });
      expect(tables).toHaveLength(Object.values(MaterialPillar).length);
      tables.forEach((table) => {
        const parsedTable = parseDocxObject(table);
        const tableRows = parsedTable.root.filter((item) => item.rootKey === TABLE_ROW_KEY);
        expect(tableRows).toHaveLength(BOUNDARIES_TABLE_HEADER_ROW_COUNT + 1);
        expect(tableRows[2].options.children).toHaveLength(BOUNDARIES_TABLE_COLUMN_COUNT);
      });
    });
    it('should return a suggested paragraph if topic does not have assessment data', () => {
      const generator = getGenerator({
        result: [
          createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.People] }) }),
          createAssessmentData({ categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Planet] }) }),
          createAssessmentData({
            categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Principle] }),
          }),
          createAssessmentData({
            categories: createAssessmentCategory({ materialPillar: [MaterialPillar.Prosperity] }),
          }),
        ],
      });
      const output = generator.getBoundariesTables();
      const tables = output.filter((item) => {
        return item instanceof Table;
      });
      const suggestedParagraph = output.reduce((acc, item) => {
        if (item instanceof Paragraph) {
          const parsedItem = parseDocxObject(item);
          const isSuggested = parsedItem.root.some((el: { rootKey: string }) => el.rootKey === SUGGESTED_PARAGRAPH_KEY);
          return isSuggested ? [...acc, item] : acc;
        }
        return acc;
      }, [] as any[]);
      expect(tables).toHaveLength(Object.values(MaterialPillar).length);
      expect(suggestedParagraph).toHaveLength(1);
    });
  });
  describe('getTopTopicsPerBoundaryTable', () => {
    it('should return a table with the correct number of rows', () => {
      const table = generator.getTopTopicsPerBoundaryTable();
      expect(table).toBeInstanceOf(Table);
      const parsedTable = parseDocxObject(table);
      const tableRows = parsedTable.root.filter((el) => el.rootKey === TABLE_ROW_KEY);
      expect(tableRows).toHaveLength(TOP_TOPICS_PER_BOUNDARY_HEADER_ROW_COUNT + result.length);
      expect(tableRows[0].options.children).toHaveLength(TOP_TOPICS_PER_BOUNDARY_COLUMN_COUNT);
    });
  });
  describe('getTopicScoresWithDescTable', () => {
    it('should return a table with the correct number of rows', () => {
      const table = generator.getTopicScoresWithDescTable();
      expect(table).toBeInstanceOf(Table);
      const parsedTable = parseDocxObject(table);
      const tableRows = parsedTable.root.filter((el) => el.rootKey === TABLE_ROW_KEY);
      expect(tableRows).toHaveLength(TOPIC_SCORE_DESC_TABLE_HEADER_ROW_COUNT + result.length);
      expect(tableRows[0].options.children).toHaveLength(TOPIC_SCORE_DESC_TABLE_COLUMN_COUNT);
    });
  });
  describe('getTopicMappedMetricsTable', () => {
    it('should return a table with the correct number of rows', () => {
      const table = generator.getTopicMappedMetricsTable();
      expect(table).toBeInstanceOf(Table);
      const parsedTable = parseDocxObject(table);
      const tableRows = parsedTable.root.filter((el) => el.rootKey === TABLE_ROW_KEY);
      expect(tableRows).toHaveLength(TOPIC_MAPPED_METRICS_TABLE_HEADER_ROW_COUNT + result.length);
      expect(tableRows[0].options.children).toHaveLength(TOPIC_MAPPED_METRICS_TABLE_COLUMN_COUNT);
    });
  });
  describe('getTopicActionsTable', () => {
    it('should return a table with the correct number of rows', () => {
      const table = generator.getTopicActionsTable();
      expect(table).toBeInstanceOf(Table);
      const parsedTable = parseDocxObject(table);
      const tableRows = parsedTable.root.filter((el) => el.rootKey === TABLE_ROW_KEY);
      expect(tableRows).toHaveLength(TOPIC_ACTIONS_TABLE_HEADER_ROW_COUNT + result.length);
      expect(tableRows[0].options.children).toHaveLength(TOPIC_ACTIONS_TABLE_COLUMN_COUNT);
    });
  });
});
