import {
  heading,
  heading2,
  heading3,
  imageWrapper,
  justifiedParagraph,
  letterList,
  numberList,
  pagebreak,
  paragraph,
  spacer,
  title,
} from '@components/report-output/document-structure';
import {
  AlignmentType,
  Document,
  Footer,
  HorizontalPositionAlign,
  HorizontalPositionRelativeFrom,
  ImageRun,
  PageNumber,
  PageOrientation,
  Paragraph,
  SectionType,
  Table,
  VerticalPositionRelativeFrom,
} from 'docx';
import G17EcoLogo from '@g17eco/images/g17Eco.svg';
import MTLogo from '@g17eco/images/apps/Materiality_Tracker_logo.svg';
import { SafeTextRun } from '@utils/docx';
import {
  DoubleMaterialityAssessmentData,
  MaterialityAssessmentScope,
  MaterialitySurveyModelMinData,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { DATE, formatDateUTC } from '@utils/date';
import { InitiativeData } from '@g17eco/types/initiative';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { getImageBase64FromSVGUrl } from '@utils/image';
import { detailedNumberingStyles, detailedStyles, getChart } from './utils';
import { DoubleMaterialityTableGenerator } from './double-materiality-table-generator';
import { SURVEY } from '@constants/terminology';
import { DataSource } from './charts/GenerateCharts';
import { topicLengthMap } from '@apps/materiality-tracker/utils';

interface Props {
  initiative: InitiativeData;
  survey: MaterialitySurveyModelMinData;
  doubleMaterialityResult: DoubleMaterialityAssessmentData[];
  mappedUtrs: UniversalTrackerBlueprintMin[];
  sizeScope: MaterialityAssessmentScope;
  dataSource: DataSource;
}

export const DoubleMaterialityReportGenerator = async (generatorData: Props): Promise<Document> => {
  const { survey, doubleMaterialityResult, initiative, mappedUtrs, sizeScope, dataSource } = generatorData;
  const G17EcoLogoBase64 = await getImageBase64FromSVGUrl(G17EcoLogo);
  const MTLogoBase64 = await getImageBase64FromSVGUrl(MTLogo);
  const tableGenerator = new DoubleMaterialityTableGenerator({
    result: doubleMaterialityResult,
    questions: mappedUtrs,
    sizeScope,
    initiativeName: initiative.name,
  });

  const g17ecoLogo = new ImageRun({
    data: G17EcoLogoBase64 ?? '',
    transformation: {
      width: 108,
      height: 28,
    },
  });

  const materialityTrackerLogo = new ImageRun({
    data: MTLogoBase64 ?? '',
    transformation: {
      width: 280,
      height: 102,
    },
    floating: {
      horizontalPosition: {
        relative: HorizontalPositionRelativeFrom.PAGE,
        align: HorizontalPositionAlign.CENTER,
      },
      verticalPosition: {
        offset: 1674250,
        relative: VerticalPositionRelativeFrom.PAGE,
      },
    },
  });

  const welcomePageContent = [
    imageWrapper(materialityTrackerLogo),
    spacer(5000),
    title('DOUBLE MATERIALITY ASSESSMENT', { alignment: AlignmentType.CENTER }),
    heading2(formatDateUTC(survey.effectiveDate, DATE.MONTH_YEAR).toUpperCase(), {
      alignment: AlignmentType.CENTER,
      textRun: { color: '434343' },
    }),
    spacer(400),
    heading2(initiative.name.toUpperCase(), { alignment: AlignmentType.CENTER, textRun: { color: '434343' } }),
    pagebreak(),
  ];

  const tableOfContentPageContent = [
    heading('TABLE OF CONTENTS'),
    numberList('numbering-letter-one', 'INTRODUCTION'),
    numberList('numbering-letter-one', 'G17ECO MATERIALITY TRACKER'),
    numberList('numbering-letter-one', `NAVIGATING THIS ${SURVEY.SINGULAR.toUpperCase()}`),
    numberList('numbering-letter-one', 'ASSESSMENT METHODOLOGY'),
    numberList('numbering-letter-one', 'DOUBLE MATERIALITY RESULTS'),
    letterList('numbering-letter-two', 'DOUBLE MATERIALITY SCORES AND DESCRIPTIONS'),
    letterList('numbering-letter-two', 'IDENTIFYING MATERIAL ASPECTS'),
    letterList('numbering-letter-two', 'DEFINING MATERIAL BOUNDARIES'),
    letterList('numbering-letter-two', 'MAPPING BY MATERIAL BOUNDARIES ONLY'),
    letterList('numbering-letter-two', 'FINANCIAL BREAKDOWN'),
    letterList('numbering-letter-two', 'IMPACT BREAKDOWN'),
    numberList('numbering-letter-one', 'SUMMARY'),
    numberList('numbering-letter-one', 'ABOUT G17ECO'),
    numberList('numbering-letter-one', 'APPENDIX'),
    letterList('numbering-letter-three', 'DEFINITIONS'),
    letterList('numbering-letter-three', 'DETAILED DESCRIPTION OF SCORING MECHANISMS'),
    letterList('numbering-letter-three', 'FINANCIAL IMPACT BASED ON SCORED DATA'),
    letterList('numbering-letter-three', 'STAKEHOLDER INTERACTIONS'),
    letterList('numbering-letter-three', 'BENEFITS OF MATERIALITY ASSESSMENT'),
    letterList('numbering-letter-three', 'TABLE A: TOPICS TO RECOMMENDED METRIC MAPPING'),
    letterList('numbering-letter-three', 'TABLE B: ACTIONS BASED ON RECOMMENDED TOPICS'),
    pagebreak(),
  ];

  const introductionPageContent = [
    heading('INTRODUCTION'),
    justifiedParagraph(
      `This report outlines the findings of the sustainability double materiality assessment conducted using the Materiality Tracker (MT) for ${initiative.name}. The assessment aims to pinpoint the most significant sustainability-related financial issues pertinent to the company’s operations. Leveraging advanced data analytics and industry benchmarks, and incorporating specific sustainability considerations relevant to ${initiative.name} sector, this analysis delivers critical insights into financially material topics. This report serves as a key resource for ${initiative.name}, guiding strategic financial planning and enhancing transparent reporting on sustainability performance and risks.`,
    ),
    heading('G17ECO MATERIALITY TRACKER'),
    justifiedParagraph(
      `Materiality Tracker’s double materiality assessment uses a rigorous methodology to identify and prioritise key financial issues for ${initiative.name}. Advanced data analytics examine diverse sources, including financial reports, market analyses, and regulatory documents. Industry benchmarks ensure accuracy and contextual relevance, delivering precise, actionable insights essential for strategic decision-making and financial reporting.`,
    ),
    justifiedParagraph(
      `The assessment highlights financially material topics critical to ${initiative.name}’s stability and growth, such as revenue growth, cost management, asset valuation, and financial compliance. Each issue is evaluated for its impact on financial performance, investor decisions, and long-term profitability.`,
    ),
    justifiedParagraph(
      'Proactively addressing these topics strengthens financial performance, mitigates risks, and enhances market competitiveness. The findings provide actionable insights for refining strategies, setting objectives, and ensuring transparent reporting. As a dynamic process, financial materiality assessment evolves with market trends, investor expectations, and regulatory changes.',
    ),
    justifiedParagraph(
      `By incorporating these insights into core strategies, ${initiative.name} reinforces financial resilience, optimises its market position, and drives sustainable growth. This proactive approach ensures alignment with changing conditions while laying a solid foundation for strategic financial planning and decision-making.`,
    ),
    pagebreak(),
    heading(`NAVIGATING THIS ${SURVEY.SINGULAR.toUpperCase()}`),
    justifiedParagraph(
      'Your materiality report consists of three sections. Section 5a outlines the double materiality assessment results, which are derived from two separate evaluations: a financial assessment and an impact assessment. The section 5e details the financial assessment results, while section 5f focuses on the impact assessment results. By combining these two assessments, the Materiality Tracker identifies the key material topics that form the basis of your double materiality assessment.',
    ),
    justifiedParagraph(
      'The appendices provide valuable insights to support your materiality assessment. They include detailed descriptions of each material topic, comprehensive metrics for reporting, and long-term strategies to guide your efforts.',
    ),
    justifiedParagraph(
      'This assessment plays a crucial role in refining your priorities, shaping strategic planning, and improving transparency. By clearly defining and categorizing key topics, you can adopt a focused and effective approach to decision-making that meets stakeholder and regulatory expectations.',
    ),
    heading('ASSESSMENT METHODOLOGY'),
    justifiedParagraph(
      `The financial and impact assessment conducted using the Materiality Tracker employed a robust methodology to evaluate the potential effects of identified material topics on both ${initiative.name}’s financial performance and its broader external impact. This process provides insight into ${initiative.name}’s overall sustainability performance. However, the exercise was not intended to evaluate the company’s current performance in these areas. Rather, it aimed to assess the current impact of the industry as a whole and identify opportunities for ${initiative.name} to contribute to improved industry-wide performance.`,
    ),
    justifiedParagraph(
      `Materiality Tracker reviewed the definitions of each material topic and identified relevant metrics to evaluate them. For each metric, reputable resources were used to rank the industry’s performance, with results normalized out of 100. Where ${initiative.name}’s industry underperforms on a metric, there is greater potential for ${initiative.name} to make a substantive impact, reflected in a higher score.`,
    ),
    justifiedParagraph(
      `After analyzing material details for ${initiative.name}, Materiality Tracker categorized these key topics into five pillars—People, Partnerships, Planet, Prosperity, and Principles—and aligned them to seven organizational boundaries. Reporting metrics aligned to globally recognized standards were also recommended, ensuring compliance with industry best practices. For detailed descriptions of these material topics and their recommended metrics, refer to the appendices at the end of this report.`,
    ),
    heading('DOUBLE MATERIALITY RESULTS'),
    justifiedParagraph(
      'The bar graph provides a visual comparison of scores across various topics within the "Impact Materiality" and "Financial Materiality" assessment. Each row represents a different topic, with bars indicating its score in both materiality dimensions. This side-by-side comparison helps stakeholders prioritise topics based on their significance to both financial outcomes and broader societal impacts. ',
    ),
    imageWrapper(getChart(dataSource), { alignment: AlignmentType.CENTER }),
  ];

  const resultPageContent = [
    heading2('DOUBLE MATERIALITY SCORES AND DESCRIPTIONS'),
    tableGenerator.getTopicScoresWithDescTable(),
    pagebreak(),
    heading2('IDENTIFYING MATERIAL ASPECTS'),
    justifiedParagraph(
      'The five sustainability pillars—People, Partnership, Planet, Prosperity, and Principle—focus on fostering social well-being, building collaborative relationships, protecting the environment, ensuring economic growth, and upholding ethical governance. Together, they provide a holistic approach to sustainable development, balancing social, environmental, & economic goals while promoting responsible and inclusive business practices.',
    ),
    ...tableGenerator.getStandardsTables(),
    pagebreak(),
    heading2('DEFINING MATERIAL BOUNDARIES'),
    justifiedParagraph(
      `To determine where each material topic can be influenced, Materiality Tracker helped ${initiative.name} define instrumental boundaries that impact key material topics. These boundaries assist ${initiative.name} in understanding where their scope of influence falls and how to measure, track, and report on the material topics identified in the materiality assessment.`,
    ),
    justifiedParagraph(
      `As part of this process, the topics were mapped across ${initiative.name}'s value chain. While many topics are relevant to multiple, or all, stages of the value chain, topics were assigned according to the ranking impacts associated with ${initiative.name} business and sphere of influence.`,
    ),
    heading3('Key material topics aligned with pillars and boundaries'),
    ...tableGenerator.getBoundariesTables(),
    pagebreak(),
    heading2('MAPPING BY MATERIAL BOUNDARIES ONLY'),
    justifiedParagraph(
      `Material topics were also mapped by material boundaries to better illustrate where ${initiative.name} influence is most needed. Please note material topics can appear across multiple material boundaries.`,
    ),
    justifiedParagraph(
      `As shown in the following table, internal focus would be best spent on ${tableGenerator.getBoundariesWithMostTopics()}. A topic can appear in more than one boundary.`,
    ),
    paragraph('*The top 5 scoring topics for your organisation have been highlighted in bold.', {
      textRun: { italics: true },
    }),
    tableGenerator.getTopTopicsPerBoundaryTable(),
  ];

  const breakdownPageContent = [
    heading('FINANCIAL BREAKDOWN'),
    justifiedParagraph(
      `As part of your financial assessment, the Materiality Tracker identified and evaluated the environmental, social and governance (ESG) factors, risks, and opportunities that have a significant impact on your financial performance and value creation. Below we have outlined the ${topicLengthMap[sizeScope]} most valuable material topics for ${initiative.name}.`,
    ),
    tableGenerator.getRelevantPillarTable(),
    spacer(),
    tableGenerator.getFinancialBreakdownTable(),
    pagebreak(),
    heading('IMPACT BREAKDOWN'),
    justifiedParagraph(
      `As part of your impact assessment, the Materiality Tracker identified and evaluated the ESG issues where ${initiative.name} has a significant impact on people, communities, and the environment, regardless of their financial impact on the company itself. Below we have outlined the ${topicLengthMap[sizeScope]} most valuable material topics for ${initiative.name}.`,
    ),
    tableGenerator.getRelevantPillarTable(),
    spacer(),
    tableGenerator.getNonFinancialBreakdownTable(),
  ];

  const summaryPageContent = [
    heading('SUMMARY'),
    justifiedParagraph(
      `This report, and the information gathered to support it, present the material topics relevant to ${initiative.name} business as determined by various stakeholder analysis and supplemental sources analyzed by G17Eco Materiality Tracker.`,
    ),
    justifiedParagraph(
      `This analysis satisfies the globally recognized standards requirement for conducting a materiality assessment as part of the sustainability reporting process, should ${initiative.name} wish to report externally according to these standards. G17Eco can continue to support ${initiative.name} efforts towards sustainability through assistance with strategy development, reporting, metrics, or other services as desired.`,
    ),
    heading('ABOUT G17ECO'),
    justifiedParagraph(
      'Worldwide Generation is an international team of purpose-driven sustainability and fintech experts united in driving a global movement for change. To enable this change, Worldwide Generation created the G17eco platform to accelerate the financing and delivery of the Sustainable Development Goals (SDGs).',
    ),
    justifiedParagraph(
      'Launched at the end of 2020, G17eco is the culmination of 300+ expert contributors to the platform over 5 years - including major support from the UK Government and the City of London.',
    ),
    pagebreak(),
    heading('APPENDIX - DEFINITIONS'),
    justifiedParagraph([
      new SafeTextRun({ text: 'Materiality Assessment: ', bold: true }),
      new SafeTextRun(
        'Utilising responses from your internal questionnaire, we identify your organisation’s distinct material topics, reflecting unique priorities and challenges.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Mapping to Pillars and Material Boundaries: ', bold: true }),
      new SafeTextRun(
        'These topics are systematically mapped to relevant sustainability pillars and boundaries, creating a structured framework to assess their influence on both your sustainability efforts and overall business operations.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Alignment with Global Standards: ', bold: true }),
      new SafeTextRun(
        'We align these topics with metrics from globally recognized standards, ensuring compliance and relevance. While standard suggestions are adaptable, you can modify, omit, or reprioritize these metrics based on your evolving organisational needs or strategic shifts. This flexibility ensures that the integration into our Company Tracker platform fully supports your specific reporting requirements.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Reporting and Sustainability Report: ', bold: true }),
      new SafeTextRun(
        'Completing your reporting cycle provides you with the essential data needed to craft and finalise your comprehensive sustainability report and refine your sustainability strategy and initiatives going forward.',
      ),
    ]),
    heading2('ENVIRONMENTAL, SOCIAL AND GOVERNANCE (ESG)'),
    justifiedParagraph([
      new SafeTextRun({ text: 'Environmental (E): ', bold: true }),
      new SafeTextRun(
        'This category addresses your company’s environmental impact, focusing on efficient resource management, waste reduction, pollution control, and conservation. It promotes practices that minimise environmental harm and enhance sustainability.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Social (S): ', bold: true }),
      new SafeTextRun(
        'This aspect covers your company\'s relationships with employees, suppliers, customers, and communities. It includes responsibilities like fair labour practices, diversity, inclusion, philanthropy, and active community involvement, stressing the importance of considering stakeholders in decision-making.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Governance (G): ', bold: true }),
      new SafeTextRun(
        'This segment covers your company’s governance structures, including executive pay, audits, internal controls, and shareholder rights. It includes governance practices that ensure compliance, integrity, and sustainable business success.',
      ),
    ]),
    heading2('FIVE PILLARS'),
    justifiedParagraph([
      new SafeTextRun({ text: 'Planet: ', bold: true }),
      new SafeTextRun(
        'Focuses on reducing ecological footprints and protecting ecosystems, emphasising sustainable environmental practices.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'People: ', bold: true }),
      new SafeTextRun(
        'Highlights the importance of ethical and beneficial business practices toward labour, the community, and regions of operation, aiming to improve inclusivity and quality of life.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Prosperity: ', bold: true }),
      new SafeTextRun(
        'Pertains to how the business contributes to economic development while ensuring financial stability and growth, fostering a stable and prosperous economy for all stakeholders.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Partnership: ', bold: true }),
      new SafeTextRun(
        'Encourages collaboration with various stakeholders, including NGOs and governments, to pursue sustainable goals collaboratively.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Principle: ', bold: true }),
      new SafeTextRun(
        'Stresses adherence to ethical standards, transparency, and legal compliance, ensuring all business operations maintain the highest integrity.',
      ),
    ]),
    heading2('BUSINESS CATEGORIES (BOUNDARIES)'),
    justifiedParagraph([
      new SafeTextRun({ text: 'Leadership: ', bold: true }),
      new SafeTextRun(
        'Focuses on strategic decision-making and leadership practices that influence the organisation’s direction and governance.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Research and Development (R&D): ', bold: true }),
      new SafeTextRun(
        'Involves activities aimed at developing or enhancing products, technologies, or services, underpinning the organisation’s innovation efforts.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Supply Chain Management (SCM): ', bold: true }),
      new SafeTextRun(
        'Examines all processes from raw material transformation to final product delivery, emphasising sustainable practices in procurement, resource use, and labour conditions.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Product or Services: ', bold: true }),
      new SafeTextRun(
        'Concerns the design, creation, and delivery of the company’s offerings, prioritising safety, quality, and sustainability.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Distribution: ', bold: true }),
      new SafeTextRun(
        'Manages the logistics of transporting and distributing goods, optimising routes, and methods to reduce environmental impacts.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Communities: ', bold: true }),
      new SafeTextRun(
        'Engages with local and global communities where the organisation operates, focusing on responsible, economic, and environmental contributions.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Experiences: ', bold: true }),
      new SafeTextRun(
        'Encompasses the company’s interactions with customers and employees, aiming to create value and enhance the workplace environment.',
      ),
    ]),
    heading2('CRITERIA FOR ASSESSMENTS'),
    justifiedParagraph(
      'MT utilizes a sophisticated scoring system designed to evaluate the significance of each material issue from both financial and impact perspectives, using a combination of qualitative and quantitative factors. This scoring mechanism is pivotal in quantifying and prioritizing both financial risks and opportunities, as well as broader environmental and social impacts.',
    ),
    justifiedParagraph(
      'Here\'s how it works:',
    ),
    justifiedParagraph([
      new SafeTextRun({ text: 'Quantitative Metrics: ', bold: true }),
      new SafeTextRun(
        'Material issues are scored on a scale that incorporates quantitative financial data, such as potential impact on revenue, costs, and assets. This includes both historical data and projected figures to assess potential future impacts.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Qualitative Assessments: ', bold: true }),
      new SafeTextRun(
        'Qualitative factors are also integrated into the scoring, including regulatory compliance risks, potential for reputational damage, and strategic importance. These are assessed through expert analysis and stakeholder feedback captured via questionnaires.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Weighting Factors: ', bold: true }),
      new SafeTextRun(
        `Each scoring criterion is weighted differently based on its relative importance to ${initiative.name} from both financial and impact perspectives. For example, issues that significantly influence the company's financial stability or have substantial environmental or social impacts may be given higher weight compared to those with less immediate or material consequences in either dimension.`,
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Threshold Levels: ', bold: true }),
      new SafeTextRun(
        'Thresholds are set to differentiate between levels of materiality. Issues that score above a certain threshold are classified as high priority and are flagged for immediate action or closer monitoring.',
      ),
    ]),
    heading2('IMPACT BASED ON SCORED DATA'),
    justifiedParagraph(
      'Once the material issues are scored, MT analyzes the aggregated data to determine their overall impact on the company and the broader environmental and social context. This analysis involves several steps:',
    ),
    justifiedParagraph([
      new SafeTextRun({ text: 'Impact Mapping: ', bold: true }),
      new SafeTextRun(
        'High-scoring issues are analysed to identify potential impacts on overall performance. This mapping helps visualize areas of risk and opportunity based on general industry trends and common strategic goals.',
      ),
    ]),
    justifiedParagraph([
      new SafeTextRun({ text: 'Sensitivity Analysis: ', bold: true }),
      new SafeTextRun(
        'A sensitivity analysis is conducted for high-priority issues to understand how changes in external conditions or typical strategic adjustments might affect the severity of these issues.',
      ),
    ]),
    pagebreak(),
    heading('STAKEHOLDER INTERACTIONS'),
    heading2('IDENTIFYING STAKEHOLDERS'),
    justifiedParagraph(
      `Although Materiality Tracker (MT) does not directly interact with external stakeholders through traditional face-to-face methods, it can still capture their perspectives. During the research and development phase of the tool, World Wide Generation designed the questionnaire by gathering comprehensive financial and sustainability data from a wide array of external stakeholders, including investors, financial analysts, and regulatory authorities. This provided critical insights into investor expectations, financial trends, market trends and financial strategies. In doing so, the MT is able to understand the financial strategies that influence ${initiative.name}.`,
    ),
    justifiedParagraph(
      'By analysing responses to these questions, the MT ensures that the material topics identified are closely aligned with stakeholder concerns and priorities. This method aims to synchronize the company’s financial planning and strategies with stakeholder expectations, ensuring responsiveness to market and regulatory dynamics while also minimizing unconscious bias.',
    ),
    justifiedParagraph(
      'The process of selecting and scoring material financial and impact issues is highly automated within the MT. This system uses advanced algorithms to analyse questionnaire data, identifying key financial issues based on their potential impact and relevance to stakeholders. The scoring mechanism assesses each issue’s significance and urgency, which helps prioritize financial actions and reporting.',
    ),
    heading2('STAKEHOLDERS ENGAGEMENT STRATEGIES'),
    justifiedParagraph(
      `MT leverages online data and publicly available financial and sustainability information alongside questionnaire insights to form a holistic view of stakeholder expectations, financial trends, and societal impacts. This strategy enriches the double materiality assessment by incorporating a broad spectrum of stakeholder and market perspectives without requiring direct engagement. The comprehensive analysis of this data ensures that ${initiative.name}’s sustainability and financial management practices are well-informed, transparent, and aligned with both stakeholder interests and regulatory requirements.`,
    ),
    heading2('BENEFITS OF MATERIALITY ASSESSMENT'),
    justifiedParagraph(
      'In today\'s dynamic economic and regulatory environment, organizations are increasingly recognizing the critical importance of robust financial and sustainability management practices. Both financial performance and environmental and social impacts can significantly affect a company’s long-term viability, stakeholder trust, and market position. To effectively navigate these challenges, companies must identify and prioritize issues that are most relevant to their business operations, financial stakeholders, and broader societal expectations.',
    ),
    justifiedParagraph(
      `The double materiality assessment conducted using G17eco Materiality Tracker for ${initiative.name} is a vital step in this process. By leveraging advanced data analytics and industry benchmarks, while considering specific financial factors relevant to our sector, the assessment provides a detailed understanding of the key material topics specific to ${initiative.name}. This enables the company to gain insights into areas where it can optimise performance, mitigate risks, and align its strategies with shareholder and market expectations.`,
    ),
    justifiedParagraph(
      'This report sets the context for the double materiality assessment, highlighting the growing importance of financial scrutiny and the need for organisations to integrate sophisticated sustainability analysis into their core business strategies. It recognises that managing financial materiality is not just about meeting regulatory requirements or managing market expectations, but also presents an opportunity for strategic financial planning, growth, and long-term profitability, while simultaneously managing environmental and social impacts.',
    ),
    justifiedParagraph(
      `By conducting this double materiality assessment, ${initiative.name} demonstrates its commitment to understanding and addressing the most significant material issues affecting its operations and external environment. The findings will provide a solid foundation for refining sustainability strategies, setting precise goals, and driving improvements in areas critical to the organization’s health.`,
    ),
    justifiedParagraph(
      'This report serves as a comprehensive guide to the double materiality assessment, offering insights into the methodology, data sources, and evaluation criteria used to identify key material topics. It provides a roadmap for integrating sophisticated financial management practices, enhancing stability, creating positive impacts, and contributing to sustainable economic performance.',
    ),
    justifiedParagraph(
      `By embracing the findings of this double materiality assessment, ${initiative.name} can navigate the complex sustainability landscape, enhance shareholder value, and position itself as a leader in sound financial and sustainability management.`,
    ),
  ];

  const appendixContent = [
    heading('APPENDIX SECTION'),
    heading2('TABLE A: TOPIC TO RECOMMENDED METRICS MAPPING'),
    tableGenerator.getTopicMappedMetricsTable(),
    pagebreak(),
    heading2('TABLE B: ACTIONS BASED ON RECOMMENDED TOPICS'),
    tableGenerator.getTopicActionsTable(),
  ];

  const footer = () => {
    return new Footer({
      children: [
        new Paragraph({
          children: [
            new SafeTextRun({
              children: [PageNumber.CURRENT],
            }),
          ],
        }),
        new Paragraph({
          children: [g17ecoLogo],
          alignment: AlignmentType.RIGHT,
        }),
      ],
    });
  };

  const portraitPage = (children: (Paragraph | Table)[]) => {
    return {
      properties: {
        type: SectionType.NEXT_PAGE,
      },
      footers: {
        default: footer(),
      },
      children,
    };
  };

  const landscapePage = (children: (Paragraph | Table)[]) => {
    return {
      properties: {
        type: SectionType.NEXT_PAGE,
        page: {
          size: {
            orientation: PageOrientation.LANDSCAPE,
          },
        },
      },
      footers: {
        default: footer(),
      },
      children,
    };
  };

  return new Document({
    features: {
      updateFields: true,
    },
    styles: {
      paragraphStyles: detailedStyles,
    },
    numbering: {
      config: detailedNumberingStyles,
    },
    sections: [
      portraitPage([...welcomePageContent, ...tableOfContentPageContent, ...introductionPageContent]),
      landscapePage(resultPageContent),
      portraitPage(breakdownPageContent),
      portraitPage(summaryPageContent),
      landscapePage(appendixContent),
    ],
  });
};
