import { TableRow, Table } from 'docx';
import {
  DoubleMaterialityAssessmentData,
  MaterialityAssessmentScope,
  MaterialityBoundary,
  MaterialPillar,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { BaseAssessmentTableGenerator } from './base-assessment-table-generator';
import { boundariesMap, pillarShadingMap, STYLES } from './utils';
import { capitalize } from '@utils/string';

export class DoubleMaterialityTableGenerator extends BaseAssessmentTableGenerator<DoubleMaterialityAssessmentData> {
  constructor({
    result,
    questions,
    sizeScope,
    initiativeName,
  }: {
    readonly result: DoubleMaterialityAssessmentData[];
    readonly questions: UniversalTrackerBlueprintMin[];
    readonly sizeScope: MaterialityAssessmentScope;
    initiativeName: string;
  }) {
    super({ result, questions, sizeScope, initiativeName });
  }

  public getTopicScoresWithDescTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '10%',
            text: 'SCORE',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '10%',
            text: 'FINANCIAL',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '10%',
            text: 'IMPACT',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '20%',
            text: 'TOPIC',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '50%',
            text: 'DESCRIPTION',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics.map((topic) => {
          return new TableRow({
            children: [
              this.createTableCell({ cellWidth: '10%', text: `${topic?.relativeScore || 0}%` }),
              this.createTableCell({ cellWidth: '10%', text: `${topic?.financialRelativeScore || 0}%` }),
              this.createTableCell({ cellWidth: '10%', text: `${topic?.nonFinancialRelativeScore || 0}%` }),
              this.createTableCell({ cellWidth: '20%', text: this.getTopicName(topic) }),
              this.createTableCell({ cellWidth: '50%', text: topic.description }),
            ],
          });
        }),
      ),
    });
  }

  public getRelevantPillarTable() {
    return new Table({
      ...STYLES.TABLE,
      rows: [
        new TableRow({
          children: [
            this.createTableCell({ text: 'RELEVANT PILLAR', cellWidth: '25%' }),
            ...Object.values(MaterialPillar).map((pillar) => {
              const { sub } = pillarShadingMap[pillar];
              return this.createTableCell({
                text: capitalize(pillar),
                cellWidth: '15%',
                cellStyles: { shading: { fill: sub } },
              });
            }),
          ],
        }),
      ],
    });
  }

  public getFinancialBreakdownTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '85%',
            text: 'Material Topic',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '15%',
            text: 'Score',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics
          .sort((a, b) => (b?.financialRelativeScore || 0) - (a?.financialRelativeScore || 0))
          .map((topic) => {
            const pillar = topic.categories?.materialPillar?.[0];
            const { sub } = pillar ? pillarShadingMap[pillar] : { sub: 'ffffff' };
            return new TableRow({
              children: [
                this.createTableCell({
                  cellWidth: '85%',
                  text: this.getTopicName(topic),
                  cellStyles: { shading: { fill: sub } },
                }),
                this.createTableCell({
                  cellWidth: '15%',
                  text: `${topic?.financialRelativeScore || 0}%`,
                  cellStyles: { shading: { fill: sub } },
                }),
              ],
            });
          }),
      ),
    });
  }

  public getNonFinancialBreakdownTable() {
    const initialRows = [
      new TableRow({
        children: [
          this.createTableCell({
            cellWidth: '85%',
            text: 'Material Topic',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
          this.createTableCell({
            cellWidth: '15%',
            text: 'Score',
            ...STYLES.APPENDIX_TABLE_HEADER,
          }),
        ],
      }),
    ];

    return new Table({
      ...STYLES.TABLE,
      rows: initialRows.concat(
        this.topRelevantTopics
          .sort((a, b) => (b?.nonFinancialRelativeScore || 0) - (a?.nonFinancialRelativeScore || 0))
          .map((topic) => {
            const pillar = topic.categories?.materialPillar?.[0];
            const { sub } = pillar ? pillarShadingMap[pillar] : { sub: 'ffffff' };
            return new TableRow({
              children: [
                this.createTableCell({
                  cellWidth: '85%',
                  text: this.getTopicName(topic),
                  cellStyles: { shading: { fill: sub } },
                }),
                this.createTableCell({
                  cellWidth: '15%',
                  text: `${topic?.nonFinancialRelativeScore || 0}%`,
                  cellStyles: { shading: { fill: sub } },
                }),
              ],
            });
          }),
      ),
    });
  }

  public getBoundariesWithMostTopics() {
    const maxTopic = Math.max(...Array.from(this.topicsByBoundariesMap.values()).map((topics) => topics.length));
    return Array.from(this.topicsByBoundariesMap.keys()).reduce((acc, key) => {
      const topicLength = this.topicsByBoundariesMap.get(key)?.length || 0;
      if (topicLength < maxTopic) {
        return acc;
      }
      if (!acc) {
        return boundariesMap[key as MaterialityBoundary];
      }
      return [acc, boundariesMap[key as MaterialityBoundary]].join(', ');
    }, '');
  }
}
