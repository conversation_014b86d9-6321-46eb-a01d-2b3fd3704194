import { KeyboardEvent, ChangeEvent, useState } from 'react';
import { Input, InputProps } from 'reactstrap';
import { TopicsRange } from './types';

export const DEFAULT_TOPICS_RANGE = {
  start: 1,
  end: 20,
};

const isValidTopicsRangeInput = ({
  validTopicsRange,
  topicsRange,
}: {
  validTopicsRange: TopicsRange;
  topicsRange: TopicsRange;
}) => {
  return (
    topicsRange.start >= validTopicsRange.start &&
    topicsRange.end <= validTopicsRange.end &&
    topicsRange.start <= topicsRange.end
  );
};

const NumberInput = (props: Partial<InputProps>) => {
  return (
    <Input type='number' className='border-ThemeBorderDefault' {...props} style={{ maxWidth: 50 }} bsSize={'sm'} />
  );
};

interface Props {
  topicsLength: number;
  onTopicsRangeChange: (topicsRange: TopicsRange) => void;
}

export const TopicsRangeInput = ({ topicsLength, onTopicsRangeChange }: Props) => {
  const [topicsRange, setTopicsRange] = useState<TopicsRange>({
    start: DEFAULT_TOPICS_RANGE.start,
    end: Math.min(topicsLength || DEFAULT_TOPICS_RANGE.start, DEFAULT_TOPICS_RANGE.end),
  });

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTopicsRange((prev) => ({ ...prev, [name]: value ? parseInt(value) : '' }));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      updateTopicsRange(e.currentTarget.name);
    }
  };

  const updateTopicsRange = (key: string) => {
    const validTopicsRange = {
      start: DEFAULT_TOPICS_RANGE.start,
      end: topicsLength || DEFAULT_TOPICS_RANGE.start,
    };

    const isValidTopicsRange = isValidTopicsRangeInput({ validTopicsRange, topicsRange: topicsRange });
    if (isValidTopicsRange) {
      onTopicsRangeChange(topicsRange);
      return;
    }

    const fixedTopicsRange = {
      ...topicsRange,
      ...{ [key]: validTopicsRange[key as keyof TopicsRange] },
    };
    setTopicsRange(fixedTopicsRange);
    onTopicsRangeChange(fixedTopicsRange);
  };

  return (
    <div className='d-flex justify-content-end align-items-center gap-2'>
      <div style={{ minWidth: 'fit-content' }}>Relevant topics:</div>
      <NumberInput
        name='start'
        value={topicsRange.start}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={() => updateTopicsRange('start')}
      />
      <span>-</span>
      <NumberInput
        name='end'
        value={topicsRange.end}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={() => updateTopicsRange('end')}
      />
      <div className='text-right text-ThemeHeadingLight text-sm'>(max. {topicsLength})</div>
    </div>
  );
};
