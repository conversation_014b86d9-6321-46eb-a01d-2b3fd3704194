import { SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { WorkInProgress } from '@g17eco/molecules/work-in-progress';
import { IN_PROGRESS_JOB_STATUSES, INVALID_JOB_STATUSES, JobStatus } from '@g17eco/types/background-jobs';
import { isDoubleMaterialitySurvey } from '@apps/materiality-tracker/utils';
import { AssessmentJobResult, PPTXReportJobResult } from '@apps/materiality-tracker/api/materiality-assessment';
import { AssessmentType } from '@g17eco/types/survey';
import { OverviewActions } from './OverviewActions';

interface Props {
  pptxReportJob?: Pick<PPTXReportJobResult, 'status' | 'completedDate'>;
  scoreJob?: Pick<AssessmentJobResult, 'status' | 'updatedAt'>;
  onExcelDownload: () => void;
  onPPTXDownload: () => void;
  onDocxDownload: () => Promise<void>;
  regenerateScores: () => void;
  assessmentType?: AssessmentType;
  onEditResults: () => void;
  isStaff: boolean;
}
export const AssessmentInsightsOverview = (props: Props) => {
  const {
    pptxReportJob: { status: pptxReportJobStatus, completedDate } = {},
    scoreJob: { status: scoreJobStatus, updatedAt } = {},
    onExcelDownload,
    onPPTXDownload,
    regenerateScores,
    onDocxDownload,
    assessmentType = AssessmentType.FinancialMateriality,
    onEditResults,
    isStaff,
  } = props;

  const isDoubleMateriality = isDoubleMaterialitySurvey({ assessmentType });

  if (!scoreJobStatus) {
    return null;
  }

  if (INVALID_JOB_STATUSES.includes(scoreJobStatus)) {
    return (
      <BasicAlert type='danger'>There was an error calculating the assessment scores. Please try again.</BasicAlert>
    );
  }

  const isGeneratingScore = IN_PROGRESS_JOB_STATUSES.includes(scoreJobStatus);

  if (isGeneratingScore) {
    return (
      <WorkInProgress
        title={`Your materiality ${SURVEY.SINGULAR} is currently generating and you will be notified once it is completed`}
      />
    );
  }

  const isPPTXReportOutdated = !!(updatedAt && completedDate && new Date(updatedAt) > new Date(completedDate));

  if (scoreJobStatus === JobStatus.Completed) {
    return (
      <OverviewActions
        isStaff={isStaff}
        isDoubleMateriality={isDoubleMateriality}
        isGeneratingScore={isGeneratingScore}
        regenerateScores={regenerateScores}
        onEditResults={onEditResults}
        onExcelDownload={onExcelDownload}
        onDocxDownload={onDocxDownload}
        pptxReportJobStatus={pptxReportJobStatus}
        onPPTXDownload={onPPTXDownload}
        isPPTXReportOutdated={isPPTXReportOutdated}
      />
    );
  }
};
