import IconButton from '@components/button/IconButton';
import { SURVEY } from '@constants/terminology';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { FeatureStability } from '@g17eco/molecules/feature-stability';
import { IN_PROGRESS_JOB_STATUSES, JobStatus } from '@g17eco/types/background-jobs';
import { Button } from 'reactstrap';

interface OverviewActionsProps {
  isStaff: boolean;
  isDoubleMateriality?: boolean;
  isGeneratingScore?: boolean;
  readOnly?: boolean;
  regenerateScores?: () => void;
  onEditResults?: () => void;
  onExcelDownload?: () => void;
  onDocxDownload?: () => Promise<void>;
  pptxReportJobStatus?: JobStatus;
  onPPTXDownload?: () => void;
  isPPTXReportOutdated?: boolean;
}

export const OverviewActions = (props: OverviewActionsProps) => {
  const {
    isStaff,
    isDoubleMateriality,
    isGeneratingScore,
    readOnly,
    regenerateScores,
    onEditResults,
    onExcelDownload,
    onDocxDownload,
    pptxReportJobStatus,
    onPPTXDownload,
    isPPTXReportOutdated,
  } = props;

  return (
    <div className='d-flex align-items-stretch gap-3 p-3'>
      <div className='w-50 d-flex flex-column whiteBoxContainer p-3'>
        <h4 className='m-0 mb-2'>
          <i className='fal fa-chart-simple mr-2' />
          Results
        </h4>
        <div className='mb-3'>
          These results have been uniquely generated based on your answers. We advise keeping them unchanged, but if you
          feel adjustments are required, you can modify them by clicking <span className='fw-bold'>Edit Results</span>.
          These changes will also be reflected on your insights and download reports.
        </div>
        <div className='mt-auto d-flex gap-2 justify-content-end align-items-center'>
          {isStaff ? (
            <SimpleTooltip
              text={
                  isGeneratingScore
                    ? 'Scores are being calculated. Cannot regenerate until complete'
                    : 'Regenerate scores'
              }
            >
              <FeatureStability stability='internal' />
              <IconButton
                data-testid={'btn-regenerate'}
                onClick={regenerateScores}
                outline={false}
                color='transparent'
                className='text-ThemeAccentDark'
                icon='fal fa-repeat'
                disabled={isGeneratingScore || readOnly}
              />
            </SimpleTooltip>
          ) : null}
          <Button color='link' onClick={onEditResults} disabled={readOnly}>
            <i className='fal fa-pencil mr-2' />
            Edit results
          </Button>
          <SimpleTooltip text={isDoubleMateriality ? 'Coming soon' : ''}>
            <Button disabled={isDoubleMateriality || readOnly} color='secondary' onClick={onExcelDownload}>
              <i className='fal fa-file-excel mr-2' />
              All results
            </Button>
          </SimpleTooltip>
        </div>
      </div>
      <div className='w-50 d-flex flex-column whiteBoxContainer p-3'>
        <h4 className='m-0 mb-2'>
          <i className='fal fa-file-chart-column mr-2' />
          Reports
        </h4>
        <div className='mb-3'>
          Download your {isDoubleMateriality ? 'double' : 'financial'} materiality assessment. The detailed report
          provides a comprehensive view, whilst the executive summary offers a high-level overview of your results.
        </div>
        <div className='mt-auto d-flex gap-2 justify-content-end align-items-center'>
          <Button color='secondary' onClick={onDocxDownload} disabled={readOnly}>
            <i className='fal fa-file-word mr-2' />
            Detailed report
          </Button>
          {pptxReportJobStatus && IN_PROGRESS_JOB_STATUSES.includes(pptxReportJobStatus) ? (
            <Button color='primary' disabled={true}>
              <i className='fal fa-circle mr-2' />
              Generating report
            </Button>
          ) : (
            <>
              <Button
                color='secondary'
                onClick={onPPTXDownload}
                disabled={!pptxReportJobStatus || pptxReportJobStatus !== JobStatus.Completed || readOnly}
              >
                <i className='fal fa-file-powerpoint mr-2' />
                Executive summary
              </Button>
              {isPPTXReportOutdated && !readOnly ? (
                <SimpleTooltip
                  className='text-center'
                  text={`Assessment results have changed, executive summary ${SURVEY.SINGULAR} is now outdated. Click the button to generate the latest ${SURVEY.SINGULAR}`}
                >
                  <i className='text-ThemeWarningMedium fas fa-exclamation-triangle' />
                </SimpleTooltip>
              ) : null}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
