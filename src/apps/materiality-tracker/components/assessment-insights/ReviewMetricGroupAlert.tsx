import { ROUTES } from '@constants/routes';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { generateUrl } from '@routes/util';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';

interface Props {
  initiativeId: string;
  assessmentId: string;
  metricGroups: MetricGroup[] | undefined;
}

export const ReviewMetricGroupAlert = ({ initiativeId, assessmentId, metricGroups }: Props) => {
  const history = useHistory();
  const metricGroup = metricGroups?.find((group) => group.survey?._id === assessmentId);

  const handleClick = () => {
    const url = generateUrl(ROUTES.MATERIALITY_TRACKER_MODULES, { initiativeId, groupId: metricGroup?._id });
    history.push(url);
  };

  return (
    <BasicAlert type='info' className='mx-3'>
      <div className='d-flex align-items-center justify-content-between'>
        <h4>Review relevant reporting {QUESTION.PLURAL} for this assessment</h4>
        {
          <SimpleTooltip text={!metricGroup ? 'Regenerate scores to view' : ''}>
            <Button color='primary' onClick={handleClick} disabled={!metricGroup}>
              View {QUESTION.PLURAL}
            </Button>
          </SimpleTooltip>
        }
      </div>
    </BasicAlert>
  );
};
