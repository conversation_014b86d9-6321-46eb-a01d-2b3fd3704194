import { MaterialTopicDetails } from '@apps/materiality-tracker/components/assessment-insights/MaterialTopicDetails';
import { DashboardSection } from '@components/dashboard';
import { useState } from 'react';
import { AssessmentInsightsChart } from './AssessmentInsightsChart';
import { NoData } from '@g17eco/molecules/no-data';
import { isDoubleMaterialityAssessmentData } from '@apps/materiality-tracker/utils';
import { AssessmentInsightsDoubleMaterialityChart } from './AssessmentInsightsDoubleMaterialityChart';
import { PrioritizedAssessmentData, TopicsRange } from './types';
import { CategoriesCharts } from './CategoriesCharts';

const filterDataWithRange = (data: PrioritizedAssessmentData[], range: TopicsRange) => {
  return data.slice(range.start - 1, range.end);
};

interface Props {
  data: PrioritizedAssessmentData[];
  topicsRange: TopicsRange;
}

export const AssessmentInsightsResult = ({ data, topicsRange }: Props) => {
  const filteredData = filterDataWithRange(data, topicsRange);
  const [selectedTopic, setSelectedTopic] = useState<PrioritizedAssessmentData | undefined>(filteredData[0]);

  if (data.length === 0) {
    return (
      <DashboardSection>
        <NoData text='No assessment results currently available.' />
      </DashboardSection>
    );
  }

  const selectedTopicCode = selectedTopic?.code ?? '';

  const updateDataOnColumnSelected = (selectedColumnIndex: number) => {
    setSelectedTopic(filteredData[selectedColumnIndex]);
  };

  return (
    <DashboardSection>
      {isDoubleMaterialityAssessmentData(filteredData) ? (
        <AssessmentInsightsDoubleMaterialityChart
          data={filteredData}
          selectedTopicCode={selectedTopicCode}
          columnClickHandler={updateDataOnColumnSelected}
        />
      ) : (
        <>
          <CategoriesCharts data={filteredData} />
          <AssessmentInsightsChart
            data={filteredData}
            selectedTopicCode={selectedTopicCode}
            columnClickHandler={updateDataOnColumnSelected}
          />
        </>
      )}
      {selectedTopic ? <MaterialTopicDetails data={selectedTopic} /> : null}
    </DashboardSection>
  );
};
