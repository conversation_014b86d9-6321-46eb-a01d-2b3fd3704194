import {
  OrderedTopic,
  useDeleteConfigMutation,
  useUpdateConfigMutation,
} from '@apps/materiality-tracker/api/materiality-assessment';
import IconButton from '@components/button/IconButton';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { InputRow, TableDraggableColumn, TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import { Toggle } from '@g17eco/molecules/toggle';
import { useState } from 'react';
import { Button, Input, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import { MaterialTopicDetails } from '../MaterialTopicDetails';
import { useToggle } from '@hooks/useToggle';
import { PrioritizedAssessmentData } from '../types';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { generateErrorToast, generateToast } from '@components/toasts';

interface Props {
  initiativeId: string;
  surveyId: string;
  onClickBack: () => void;
  originalTopics: PrioritizedAssessmentData[];
  customTopics: PrioritizedAssessmentData[];
}

const draggableColumns: TableDraggableColumn[] = [
  {
    header: '',
  },
  {
    header: 'Score',
  },
  {
    header: 'Material topic',
  },
  {
    header: '',
  },
];

interface DetailsModalProps {
  isOpen: boolean;
  toggle: () => void;
  topic: PrioritizedAssessmentData;
}
const DetailsModal = ({ isOpen, toggle, topic }: DetailsModalProps) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>{topic.name}</ModalHeader>
      <ModalBody>
        <MaterialTopicDetails data={topic} isCompact />
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Close
        </Button>
      </ModalFooter>
    </Modal>
  );
};

interface ExplanationModalProps {
  isOpen: boolean;
  toggle: () => void;
  onSubmit: (explanation: string) => void;
}
const ExplanationModal = ({ isOpen, toggle, onSubmit }: ExplanationModalProps) => {
  const [explanation, setExplanation] = useState('');
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Update topic relevance</ModalHeader>
      <ModalBody>
        <p>Provide an explanation for this change (required)</p>
        <Input type='textarea' value={explanation} onChange={(e) => setExplanation(e.currentTarget.value)} />
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='primary' disabled={!explanation} onClick={() => onSubmit(explanation)}>
          Update
        </Button>
      </ModalFooter>
    </Modal>
  );
};

const ResetModal = ({
  isOpen,
  toggle,
  onClickReset,
}: {
  isOpen: boolean;
  toggle: () => void;
  onClickReset: () => void;
}) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>
        <span className='text-ThemeWarningExtradark'>Reset results</span>
      </ModalHeader>
      <ModalBody>
        <p>Reset topics to the original order. This action cannot be undone.</p>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='warning' onClick={onClickReset}>
          Reset
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export const AssessmentResults = ({ initiativeId, surveyId, onClickBack, originalTopics, customTopics }: Props) => {
  const [detailingTopic, setDetailingTopic] = useState<PrioritizedAssessmentData | undefined>(undefined);
  const [viewingOriginalOrder, toggleViewingOriginalOrder, setViewingOriginalOrder] = useToggle(false);
  const [newOrderedTopics, setNewOrderedTopics] = useState<OrderedTopic[] | undefined>(undefined);
  const [reset, toggleReset] = useToggle(false);
  const [updateConfig, { isLoading: isUpdatingConfig }] = useUpdateConfigMutation();
  const [deleteConfig, { isLoading: isDeletingConfig }] = useDeleteConfigMutation();

  const topics = viewingOriginalOrder ? originalTopics : customTopics;
  const topicsMap = new Map(topics.map((topic) => [topic.code, topic]));

  const toggleDisabled = (toggledTopicCode: string) => {
    setNewOrderedTopics(
      customTopics.map(({ code, disabled }) =>
        code === toggledTopicCode ? { code, disabled: !disabled } : { code, disabled },
      ),
    );
  };

  const showDetails = (code: string) => {
    const topic = topicsMap.get(code);
    if (topic) {
      setDetailingTopic(topic);
      return;
    }
  };

  const rows: InputRow[] = topics.map(({ code, score, name, disabled }, index) => {
    const className = disabled ? 'text-ThemeBgDisabled' : '';
    return {
      id: code,
      cols: [
        <div key='topic-order' className={className}>
          {String(index + 1).padStart(3, '0')}
        </div>,
        <div key='topic-score' className={className}>
          {score}
        </div>,
        <div key='topic-name' className={`text-truncate ${className}`} style={{ maxWidth: '600px' }}>
          <SimpleTooltip text={name}>{name}</SimpleTooltip>
        </div>,
        <div key='topic-actions' className='d-flex gap-2 align-items-center justify-content-end'>
          <Button color='link' onClick={() => showDetails(code)}>
            More details
          </Button>
          <IconButton
            color='transparent'
            outline={false}
            icon={`fal fa-${disabled ? 'eye-slash' : 'eye'}`}
            onClick={() => toggleDisabled(code)}
            disabled={viewingOriginalOrder}
          />
        </div>,
      ],
      disabled,
    };
  });

  const handleArrange = (newColumns: string[]) => {
    if (!newColumns.some((code, index) => code !== topics[index].code)) {
      return;
    }

    setNewOrderedTopics(
      newColumns.reduce((orderedTopics, code) => {
        const topic = topicsMap.get(code);
        if (topic) {
          orderedTopics.push({ code: topic.code, disabled: topic.disabled });
        }
        return orderedTopics;
      }, [] as OrderedTopic[]),
    );
  };

  const handleSubmitExplanation = async (explanation: string) => {
    if (!newOrderedTopics) {
      return;
    }

    return updateConfig({
      initiativeId,
      surveyId,
      config: {
        orderedTopics: newOrderedTopics,
        explanation,
      },
    })
      .unwrap()
      .then(() => {
        generateToast({
          title: 'Assessment results updated',
          color: 'success',
          message: 'Assessment results have been updated',
        });
      })
      .catch(() => {
        generateErrorToast({
          message: 'There was an error updating your assessment results',
        });
      })
      .finally(() => {
        setNewOrderedTopics(undefined);
      });
  };

  const handleReset = () => {
    return deleteConfig({ initiativeId, surveyId })
      .unwrap()
      .then(() => {
        generateToast({
          title: 'Assessment results reset',
          color: 'success',
          message: 'Assessment results have been reset',
        });
        setViewingOriginalOrder(false);
      })
      .catch(() => {
        generateErrorToast({
          message: 'There was an error resetting your assessment results',
        });
      })
      .finally(() => {
        toggleReset();
      });
  };

  return (
    <>
      <Button color='link' className='text-left' onClick={onClickBack}>
        <i className='fal fa-caret-left mr-2' />
        Return to insights
      </Button>
      <div className='d-flex flex-column whiteBoxContainer mt-4 p-3'>
        {isUpdatingConfig || isDeletingConfig ? <BlockingLoader /> : null}
        <div className='d-flex justify-content-between gap-3 align-items-center'>
          <h4 className='text-ThemeTextDark me-auto'>Edit assessment results</h4>
          <Toggle label='View original order' onChange={toggleViewingOriginalOrder} checked={viewingOriginalOrder} />
          <Button color='warning' onClick={toggleReset}>
            <i className='fal fa-clock-rotate-left mr-2' />
            Reset
          </Button>
        </div>
        <p className='my-4'>
          Below is your full list of material topics, ordered by your assessment results. While we don’t recommend
          changes, you can reorder or hide topics if needed. Be mindful—updates will impact insights, reports, and
          metric recommendations from Company Tracker connections.
        </p>
        <TableDraggableRows
          columns={draggableColumns}
          data={rows}
          handleUpdate={handleArrange}
          disabled={viewingOriginalOrder}
        />
        {detailingTopic ? (
          <DetailsModal
            key={`details-modal-${detailingTopic ? 'open' : 'closed'}`}
            isOpen={!!detailingTopic}
            toggle={() => setDetailingTopic(undefined)}
            topic={detailingTopic}
          />
        ) : null}
        <ExplanationModal
          key={`explanation-modal-${newOrderedTopics ? 'open' : 'closed'}`}
          isOpen={!!newOrderedTopics}
          toggle={() => setNewOrderedTopics(undefined)}
          onSubmit={handleSubmitExplanation}
        />
        <ResetModal
          key={`reset-modal-${reset ? 'open' : 'closed'}`}
          isOpen={reset}
          toggle={toggleReset}
          onClickReset={handleReset}
        />
      </div>
    </>
  );
};
