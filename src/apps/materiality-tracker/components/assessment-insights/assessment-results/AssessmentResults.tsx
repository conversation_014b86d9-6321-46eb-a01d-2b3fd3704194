import { metricGroupsApi } from '@api/metric-groups';
import {
  OrderedTopic,
  UpdateImpactScope,
  useDeleteConfigMutation,
  useUpdateConfigMutation,
} from '@apps/materiality-tracker/api/materiality-assessment';
import IconButton from '@components/button/IconButton';
import { generateErrorToast, generateToast } from '@components/toasts';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { InputRow, TableDraggableColumn, TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import { Toggle } from '@g17eco/molecules/toggle';
import { useToggle } from '@hooks/useToggle';
import { useAppDispatch } from '@reducers/index';
import { useState } from 'react';
import { Button, Input, Modal, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'reactstrap';
import { MaterialTopicDetails } from '../MaterialTopicDetails';
import { PrioritizedAssessmentData } from '../types';
import { extendCustomTopics } from '../utils';

interface Props {
  initiativeId: string;
  surveyId: string;
  onClickBack: () => void;
  originalTopics: PrioritizedAssessmentData[];
  customTopics: PrioritizedAssessmentData[];
  hasCustomTopics: boolean;
}

const draggableColumns: TableDraggableColumn[] = [
  {
    header: '',
  },
  {
    header: 'Score',
  },
  {
    header: 'Material topic',
  },
  {
    header: '',
  },
];

interface DetailsModalProps {
  isOpen: boolean;
  toggle: () => void;
  topic: PrioritizedAssessmentData;
}
const DetailsModal = ({ isOpen, toggle, topic }: DetailsModalProps) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>{topic.name}</ModalHeader>
      <ModalBody>
        <MaterialTopicDetails data={topic} isCompact />
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Close
        </Button>
      </ModalFooter>
    </Modal>
  );
};

const SaveChangesModal = ({
  isOpen,
  toggle,
  onSubmit,
}: {
  isOpen: boolean;
  toggle: () => void;
  onSubmit: (explanation: string, impactScopes: UpdateImpactScope[]) => void;
}) => {
  const [explanation, setExplanation] = useState('');

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Save changes to topics</ModalHeader>
      <ModalBody>
        <p>
          You are updating your recommended material topics, which may affect your sustainability strategy, including
          the recommended {QUESTION.PLURAL} for your upcoming {SURVEY.ADJECTIVE} period. By continuing, you are choosing
          to regenerate your reports so that they reflect these changes. You may also choose to update the custom{' '}
          {PACK.SINGULAR}
          for this assessment.
        </p>
        <p className='fst-italic text-ThemeTextMedium'>
          Please note: Regenerating the {PACK.SINGULAR} will overwrite any previous edits you have made to it. If you
          choose to update only the reports now, you can update the module later by accessing it directly.
        </p>
        <p>Provide an explanation for this change (required)</p>
        <Input type='textarea' value={explanation} onChange={(e) => setExplanation(e.currentTarget.value)} />
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button
          color='secondary'
          onClick={() => onSubmit(explanation, [UpdateImpactScope.Reports])}
          disabled={!explanation}
        >
          Reports only
        </Button>
        <Button
          color='primary'
          onClick={() => onSubmit(explanation, [UpdateImpactScope.Reports, UpdateImpactScope.ModuleMetrics])}
          disabled={!explanation}
        >
          Reports and {QUESTION.PLURAL}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

const ResetModal = ({
  isOpen,
  toggle,
  onClickReset,
}: {
  isOpen: boolean;
  toggle: () => void;
  onClickReset: (impactScopes: UpdateImpactScope[]) => void;
}) => {
  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>
        <span className='text-ThemeWarningExtradark'>Reset results</span>
      </ModalHeader>
      <ModalBody>
        <p>
          You are resetting your topics to the original recommended order. This may affect your sustainability strategy,
          including the recommended {QUESTION.PLURAL} for your upcoming {SURVEY.ADJECTIVE} period. By resetting, you are
          choosing to regenerate your reports so that they reflect these changes. You may also choose to update the
          custom {PACK.SINGULAR} for this assessment. What would you like to reset?
        </p>
        <p className='fst-italic text-ThemeTextMedium mb-0'>
          Please note: Regenerating the {PACK.SINGULAR} will overwrite any previous edits you have made to it. If you
          choose to update only the reports now, you can update the {PACK.SINGULAR} later by accessing it directly.
        </p>
      </ModalBody>
      <ModalFooter>
        <Button color='link-secondary' onClick={toggle}>
          Cancel
        </Button>
        <Button color='secondary' onClick={() => onClickReset([UpdateImpactScope.Reports])}>
          Reports only
        </Button>
        <Button
          color='warning'
          onClick={() => onClickReset([UpdateImpactScope.Reports, UpdateImpactScope.ModuleMetrics])}
        >
          <i className='fal fa-clock-rotate-left mr-2' />
          Reports and {QUESTION.PLURAL}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

const generateCustomTopics = (topics: PrioritizedAssessmentData[]) => {
  return topics.map(({ code, disabled }) => ({ code, disabled }));
};

export const AssessmentResults = ({
  initiativeId,
  surveyId,
  onClickBack,
  originalTopics,
  customTopics,
  hasCustomTopics,
}: Props) => {
  const dispatch = useAppDispatch();
  const [detailingTopic, setDetailingTopic] = useState<PrioritizedAssessmentData | undefined>(undefined);
  const [viewingOriginalOrder, toggleViewingOriginalOrder, setViewingOriginalOrder] = useToggle(false);
  const [newOrderedTopics, setNewOrderedTopics] = useState<OrderedTopic[]>(generateCustomTopics(customTopics));
  const [hasChanged, setHasChanged] = useState(false);
  const [reset, toggleReset] = useToggle(false);
  const [saveModal, toggleSaveModal] = useToggle(false);
  const [updateConfig, { isLoading: isUpdatingConfig }] = useUpdateConfigMutation();
  const [deleteConfig, { isLoading: isDeletingConfig }] = useDeleteConfigMutation();

  const topics = viewingOriginalOrder ? originalTopics : extendCustomTopics(originalTopics, newOrderedTopics);
  const topicsMap = new Map(topics.map((topic) => [topic.code, topic]));

  const toggleDisabled = (toggledTopicCode: string) => {
    setNewOrderedTopics((prev) =>
      prev.map(({ code, disabled }) =>
        code === toggledTopicCode ? { code, disabled: !disabled } : { code, disabled },
      ),
    );
    setHasChanged(true);
  };

  const showDetails = (code: string) => {
    const topic = topicsMap.get(code);
    if (topic) {
      setDetailingTopic(topic);
      return;
    }
  };

  const rows: InputRow[] = topics.map(({ code, score, name, disabled }, index) => {
    const className = disabled ? 'text-ThemeBgDisabled' : '';
    return {
      id: code,
      cols: [
        <div key='topic-order' className={className}>
          {String(index + 1).padStart(3, '0')}
        </div>,
        <div key='topic-score' className={className}>
          {score}
        </div>,
        <div key='topic-name' className={`text-truncate ${className}`} style={{ maxWidth: '600px' }}>
          <SimpleTooltip text={name}>{name}</SimpleTooltip>
        </div>,
        <div key='topic-actions' className='d-flex gap-2 align-items-center justify-content-end'>
          <Button color='link' onClick={() => showDetails(code)}>
            More details
          </Button>
          <IconButton
            color='transparent'
            outline={false}
            icon={`fal fa-${disabled ? 'eye-slash' : 'eye'}`}
            onClick={() => toggleDisabled(code)}
            disabled={viewingOriginalOrder}
          />
        </div>,
      ],
      disabled,
    };
  });

  const handleArrange = (newColumns: string[]) => {
    if (!newColumns.some((code, index) => code !== topics[index].code)) {
      return;
    }

    setNewOrderedTopics(
      newColumns.reduce((orderedTopics, code) => {
        const topic = topicsMap.get(code);
        if (topic) {
          orderedTopics.push({ code: topic.code, disabled: topic.disabled });
        }
        return orderedTopics;
      }, [] as OrderedTopic[]),
    );
    setHasChanged(true);
  };

  const handleCancelChanges = () => {
    setNewOrderedTopics(generateCustomTopics(customTopics));
    setHasChanged(false);
  };

  const handleSubmitExplanation = async (explanation: string, impactScopes: UpdateImpactScope[]) => {
    if (!newOrderedTopics) {
      return;
    }

    return updateConfig({
      initiativeId,
      surveyId,
      config: {
        orderedTopics: newOrderedTopics,
        explanation,
      },
      impactScopes,
    })
      .unwrap()
      .then(() => {
        generateToast({
          title: 'Assessment results updated',
          color: 'success',
          message: 'Assessment results have been updated',
        });
      })
      .catch(() => {
        generateErrorToast({
          message: 'There was an error updating your assessment results',
        });
      })
      .finally(() => {
        toggleSaveModal();
        setHasChanged(false);
        if (impactScopes.includes(UpdateImpactScope.ModuleMetrics)) {
          dispatch(metricGroupsApi.util.invalidateTags(['metricGroups']));
        }
      });
  };

  const handleReset = (impactScopes: UpdateImpactScope[]) => {
    return deleteConfig({ initiativeId, surveyId, impactScopes })
      .unwrap()
      .then(() => {
        generateToast({
          title: 'Assessment results reset',
          color: 'success',
          message: 'Assessment results have been reset',
        });
        setViewingOriginalOrder(false);
        setNewOrderedTopics(generateCustomTopics(originalTopics));
        setHasChanged(false);
        if (impactScopes.includes(UpdateImpactScope.ModuleMetrics)) {
          dispatch(metricGroupsApi.util.invalidateTags(['metricGroups']));
        }
      })
      .catch(() => {
        generateErrorToast({
          message: 'There was an error resetting your assessment results',
        });
      })
      .finally(() => {
        toggleReset();
      });
  };

  return (
    <>
      <Button color='link' className='text-left' onClick={onClickBack}>
        <i className='fal fa-caret-left mr-2' />
        Return to insights
      </Button>
      <div className='d-flex flex-column whiteBoxContainer mt-4 p-3'>
        {isUpdatingConfig || isDeletingConfig ? <BlockingLoader /> : null}
        <div className='d-flex justify-content-between gap-3 align-items-center'>
          <h4 className='text-ThemeTextDark me-auto'>Edit assessment results</h4>
          <div>
            {hasChanged ? (
              <Button color='link-secondary' onClick={handleCancelChanges} className='me-2'>
                Cancel changes
              </Button>
            ) : null}
            <IconButton
              icon='fal fa-clock-rotate-left'
              color='warning'
              outline={false}
              onClick={toggleReset}
              disabled={!hasCustomTopics}
              className='mx-2'
            />
            <Button color='primary' disabled={!hasChanged} onClick={toggleSaveModal}>
              <i className='fal fa-floppy-disk mr-2' />
              Save changes
            </Button>
          </div>
        </div>
        <div className='d-flex justify-content-between gap-4 align-items-center'>
          <p className='my-4'>
            Whilst we don't recommend making changes, you can reorder or hide topics if needed. Be mindful—updates to
            this page will impact insights, reports, and metric recommendations from Company Tracker connections.
          </p>
          <Toggle
            label='View original order'
            onChange={toggleViewingOriginalOrder}
            checked={viewingOriginalOrder}
            style={{ minWidth: 'fit-content' }}
          />
        </div>
        <TableDraggableRows
          columns={draggableColumns}
          data={rows}
          handleUpdate={handleArrange}
          disabled={viewingOriginalOrder}
        />
        {detailingTopic ? (
          <DetailsModal
            key={`details-modal-${detailingTopic ? 'open' : 'closed'}`}
            isOpen={!!detailingTopic}
            toggle={() => setDetailingTopic(undefined)}
            topic={detailingTopic}
          />
        ) : null}
        <SaveChangesModal
          key={`save-changes-modal-${saveModal ? 'open' : 'closed'}`}
          isOpen={saveModal}
          toggle={toggleSaveModal}
          onSubmit={handleSubmitExplanation}
        />
        <ResetModal
          key={`reset-modal-${reset ? 'open' : 'closed'}`}
          isOpen={reset}
          toggle={toggleReset}
          onClickReset={handleReset}
        />
      </div>
    </>
  );
};
