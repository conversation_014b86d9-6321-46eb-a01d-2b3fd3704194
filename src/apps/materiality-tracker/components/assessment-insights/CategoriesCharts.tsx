import { AssessmentData, MaterialTopicCategories } from '@apps/materiality-tracker/api/materiality-assessment';
import { boundaryMap, esgMap, materialPillarMap } from '@apps/materiality-tracker/utils';
import Chart from 'react-google-charts';
import variables from '../../../../css/variables.module.scss';

interface Props {
  data: AssessmentData[];
}

const { ChartDark, ChartMedium, ChartLight, ChartBright, ChartPale, ChartPurple, ChartRed } = variables;
const options = {
  pieHole: 0.75,
  pieSliceText: 'none',
  legend: {
    position: 'bottom',
    alignment: 'center',
    textStyle: { fontSize: 9 },
    maxLines: 10,
  },
  chartArea: { width: '90%', height: '80%' },
  colors: [ChartDark, ChartMedium, ChartLight, ChartBright, ChartPale, ChartPurple, ChartRed],
};

type Category = {
  label: string;
};
type Categories = Record<string, Category>;
type ChartConfig = {
  code: keyof MaterialTopicCategories;
  label: string;
  categories: Categories;
};
const chartConfigs: ChartConfig[] = [
  {
    code: 'esg',
    label: 'ESG BREAKDOWN',
    categories: esgMap,
  },
  {
    code: 'boundary',
    label: 'MATERIAL BOUNDARIES',
    categories: boundaryMap,
  },
  {
    code: 'materialPillar',
    label: 'KEY PILLARS',
    categories: materialPillarMap,
  },
];

type CategoriesChartProps = Props & {
  config: ChartConfig;
};

const CategoriesChart = ({ data, config }: CategoriesChartProps) => {
  const { code, label, categories } = config;
  const chartData = [
    ['Category', ''],
    ...Object.entries(categories).map(([category, { label }]) => [
      label,
      data.filter((item) => (item.categories?.[code] as string[])?.includes(category)).length,
    ]),
  ];

  return (
    <div className='d-flex flex-column align-items-center'>
      <h5 className='fw-bold'>{label}</h5>
      <Chart chartType='PieChart' width='100%' height='auto' data={chartData} options={options} />
    </div>
  );
};

export const CategoriesCharts = ({ data }: Props) => {
  return (
    <div className='row mb-4'>
      {chartConfigs.map((chartConfig) => (
        <div key={chartConfig.code} className='col'>
          <CategoriesChart key={chartConfig.label} data={data} config={chartConfig} />
        </div>
      ))}
    </div>
  );
};
