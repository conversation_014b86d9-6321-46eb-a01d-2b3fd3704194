/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import { UserManagement } from '@routes/user-management';
import { useParams } from 'react-router-dom';
import { ROUTES } from '@constants/routes';
import { useAppSelector } from '@reducers/index';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { MTAdminBreadcrumbs } from '../components/breadcrumbs/MTAdminBreadcrumbs';

export const UserManagementRoute = () => {
  const { initiativeId, page } = useParams<{ page?: string; initiativeId: string }>();
  const userLimit = useAppSelector(FeaturePermissions.getLimitUsers);

  return (
    <UserManagement
      baseRoute={ROUTES.MATERIALITY_TRACKER_MANAGE_USERS}
      initiativeId={initiativeId}
      page={page}
      userLimit={userLimit}
      BreadcrumbsComponent={MTAdminBreadcrumbs}
    />
  );
};
