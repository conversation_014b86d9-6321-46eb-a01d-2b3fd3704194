/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { RouteErrorBoundary } from '@features/error';
import { AppRouteInterface, RouteInterface, RoutesInterface } from '../../../types/routes';
import Logo from '../../../images/apps/Materiality_Tracker_logo.svg';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { useCallback, useEffect } from 'react';
import { materialityTrackerRouteMatcher } from '../../../routes/appRootMatcher';
import { loadInitiativeById } from '../../../actions/initiative';
import { Route, Switch, matchPath, useHistory, useParams } from 'react-router-dom';
import Dashboard, { DashboardSection } from '@components/dashboard';
import { materialityAssessmentInitiativeRoutes, materialityTrackerRedirect } from './MaterialityTrackerRoutes';
import { SettingStorage } from '../../../services/SettingStorage';
import { AppRoute } from '../../../routes/AppRoute';
import { generateUrl } from '../../../routes/util';
import { OrganizationSwitcherRoute } from '../../../routes/OrganizationSwitcherRoute';
import { getInitiativeTree } from '@selectors/initiativeTree';
import UtrModal from '@components/utr-modal';
import { useLazyGetSurveysQuery } from '../api/materiality-assessment';
import { AgreementChecker } from '@features/agreement';
import { PACK } from '@constants/terminology';

export const materialityTrackerRouteEntry: RouteInterface = {
  id: 'materiality_tracker_entry',
  label: 'Materiality Tracker',
  path: '/materiality-tracker/:initiativeId?',
  icon: 'fa-folder-open',
  component: MaterialityTrackerRoute,
  exact: false,
  auth: true,
};

export const materialityTrackerRoutes: RoutesInterface = {
  MATERIALITY_TRACKER: materialityTrackerRouteEntry,
  ...materialityAssessmentInitiativeRoutes,
};

function MaterialityTrackerRoute() {
  const materialityIdpath = materialityTrackerRouteEntry.path;

  // above this level we are still using the old router, so need old useParams
  const { initiativeId } = useParams<{ initiativeId?: string }>();
  const dispatch = useAppDispatch();
  const initiativeTree = useAppSelector(getInitiativeTree);

  const { data: rootInitiatives } = useAppSelector((state) => state.rootInitiatives);
  const [getSurveys, { data: surveys }] = useLazyGetSurveysQuery();
  const organizations = materialityTrackerRouteMatcher.filterInitiatives(rootInitiatives);

  useEffect(() => {
    if (initiativeId) {
      dispatch(loadInitiativeById(initiativeId));
      getSurveys({ initiativeId });
    }
  }, [initiativeId, getSurveys, dispatch]);

  const history = useHistory();
  useEffect(() => {
    const isDefaultRoute = matchPath(history.location.pathname, materialityTrackerRouteEntry)?.isExact;
    if (initiativeId && !isDefaultRoute) {
      return;
    }

    let redirectInitiativeId;

    const selectedId = SettingStorage.getItem(materialityTrackerRouteMatcher.storageKey) ?? organizations[0]?._id;
    if (selectedId) {
      redirectInitiativeId = organizations.find((o) => o._id === selectedId)?._id;
    }
    if (!redirectInitiativeId && organizations.length === 1) {
      // If there is more than one org then it should show the selector so the user can choose the default?
      redirectInitiativeId = organizations[0]?._id;
    }

    if (!redirectInitiativeId) {
      return;
    }

    history.push(
      generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_LIST, {
        initiativeId: redirectInitiativeId,
      })
    );
  }, [history, organizations, initiativeId]);

  const redirectHandler = useCallback(
    (initiativeId: string, rootAppPath?: string) => {
      if (initiativeId) {
        history.push(materialityTrackerRouteMatcher.generateUrl(initiativeId, rootAppPath));
      }
    },
    [history]
  );

  const latestSurveyId = surveys?.[0]?._id;
  const completedSurveyId = surveys?.find((s) => s.completedDate)?._id;
  const defaultParams: AppRouteInterface['defaultParams'] = {
    initiativeId,
    surveyId: latestSurveyId,
    insightSurveyId: completedSurveyId,
  };

  const menuRoutes = [
    materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_SURVEY,
    materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_LIST,
    {
      ...materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_INSIGHTS,
      disabledTooltip: !completedSurveyId
        ? 'Complete your first assessment to view Insights'
        : materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_INSIGHTS.disabledTooltip,
    },
    {
      ...materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_MODULES,
      navItemPermissionId: !completedSurveyId ? 'menu_modules' : undefined,
      disabledTooltip: !completedSurveyId
        ? `Complete your first assessment to view ${PACK.CAPITALIZED_PLURAL}`
        : materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_MODULES.disabledTooltip,
    },
    materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_ADMIN,
  ];

  const materialityAssessmentAppRoute: AppRouteInterface = {
    appName: 'Materiality Tracker',
    appIcon: Logo,
    routes: menuRoutes,
  };

  return (
    <RouteErrorBoundary>
      <OrganizationSwitcherRoute
        appRoute={{
          ...materialityAssessmentAppRoute,
          defaultParams,
        }}
        routeMatcher={materialityTrackerRouteMatcher}
        changeReportingLevelRoute={materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER}
        redirectHandler={redirectHandler}
        initiativeTree={initiativeTree}
        pathMatch={materialityIdpath}
      >
        <AgreementChecker />
        <Switch>
          <Route {...materialityTrackerRedirect.MATERIALITY_TRACKER_REDIRECT} />
          {Object.entries(materialityAssessmentInitiativeRoutes).map(([k, route]) => (
            <Route
              key={`route_${route.id}`}
              path={route.path}
              exact={!!route.exact}
              render={() => <AppRoute route={route} />}
            />
          ))}
          <Route>
            <Dashboard>
              <DashboardSection icon={'fa-folder-open'} title='Materiality Tracker'>
                <p>You currently don't have any Materiality Trackers associated with this account.</p>
              </DashboardSection>
            </Dashboard>
          </Route>
        </Switch>
      </OrganizationSwitcherRoute>
      <UtrModal />
    </RouteErrorBoundary>
  );
}
