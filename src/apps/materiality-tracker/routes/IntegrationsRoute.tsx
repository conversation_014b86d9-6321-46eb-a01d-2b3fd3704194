/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { useParams } from 'react-router-dom';
import { ProviderCard, IntegrationsDashboard } from '@features/integrations';
import { MTAdminBreadcrumbs } from '../components/breadcrumbs/MTAdminBreadcrumbs';
import config from '../../../config';
import { companyTrackerCardConfig, materialityTrackerCardConfig } from '@constants/app-card';
import { createOnClickFromExternalUrl } from '@utils/click-handler';

export const IntegrationsRoute = () => {
  const { initiativeId } = useParams<{ initiativeId: string }>();
  const cards: ProviderCard[] = [
    {
      logo: companyTrackerCardConfig.logo,
      color: companyTrackerCardConfig.color,
      buttons: [
        { text: 'More info', onClick: createOnClickFromExternalUrl(config.brochureURL.companyTracker) },
        {
          text: 'Open',
          onClick: createOnClickFromExternalUrl(generateUrl(ROUTES.COMPANY_TRACKER, { initiativeId })),
        },
      ],
    },
    {
      logo: materialityTrackerCardConfig.logo,
      color: materialityTrackerCardConfig.color,
      buttons: [{ text: 'More info', onClick: createOnClickFromExternalUrl(config.brochureURL.materialityTracker) }],
    },
  ];

  return (
    <IntegrationsDashboard
      initiativeId={initiativeId}
      additionalCards={{ active: cards, available: [] }}
      BreadCrumbsComponent={MTAdminBreadcrumbs}
    />
  );
};
