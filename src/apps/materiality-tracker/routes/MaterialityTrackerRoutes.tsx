/*
 * Copyright (c) 2020-2024. World Wide Generation Ltd
 */

import { RoutesInterface } from '@g17eco/types/routes';
import { AssessmentRoute } from './assessment/AssessmentRoute';
import { AssessmentListRoute } from './assessment/AssessmentListRoute';
import { AdminSettingsRoute } from './AdminSettingsRoute';
import { UserManagementRoute } from '../../materiality-tracker/routes/UserManagementRoute';
import { AssessmentInsightRoute } from './assessment/AssessmentInsightRoute';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { CompanySettingsPages } from '../utils';
import { CompanySettingsRoute } from './CompanySettingsRoute';
import { BankingSettingsRoute } from './BankingSettingsRoute';
import { RedirectToMaterialityTracker } from './RedirectToMaterialityTracker';
import { StockExchangeSettingsRoute } from './StockExchangeSettingsRoute';
import { IntegrationsRoute } from './IntegrationsRoute';
import { PACK } from '@constants/terminology';
import { CustomMetricGroupRoute } from './CustomMetricGroupRoute';

export const materialityTrackerRedirect = {
  MATERIALITY_TRACKER_REDIRECT: {
    id: 'materiality_tracker_assessment_list_redirect',
    label: 'Assessment List Redirect',
    path: '/materiality-tracker/:initiativeId/redirect/:destination(assessment-list)',
    defaultParams: {
      destination: 'assessment-list',
    },
    component: RedirectToMaterialityTracker,
    auth: true,
  },
};

export const materialityAssessmentInitiativeRoutes: RoutesInterface = {
  MATERIALITY_TRACKER_COMPANY_SETTINGS: {
    id: 'materiality_tracker_company_settings',
    label: 'Company Settings',
    path: '/materiality-tracker/:initiativeId/admin/company-settings/:page?',
    component: CompanySettingsRoute,
    defaultParams: { page: CompanySettingsPages.Details },
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_MANAGE_USERS: {
    id: 'materiality_tracker_manage_users',
    label: 'Manage Users',
    path: '/materiality-tracker/:initiativeId/admin/manage-users/:page?',
    component: UserManagementRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_ADMIN: {
    id: 'materialityAssessment_admin',
    label: 'Admin',
    path: '/materiality-tracker/:initiativeId/admin',
    component: AdminSettingsRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_INSIGHTS: {
    id: 'materialityAssessment_insights',
    label: 'Insights',
    path: '/materiality-tracker/:initiativeId/insights/:insightSurveyId',
    component: AssessmentInsightRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
    disabledTooltip: 'Restricted users cannot view assessment insights. Please contact your administrator.',
  },
  MATERIALITY_TRACKER_MODULES: {
    id: 'materialityAssessment_modules',
    label: PACK.CAPITALIZED_PLURAL,
    path: '/materiality-tracker/:initiativeId/custom-metrics/:groupId?/:viewMode?',
    component: CustomMetricGroupRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_LIST: {
    id: 'materialityAssessment_surveys_list',
    label: 'All assessments',
    path: '/materiality-tracker/:initiativeId/assessment-list',
    component: AssessmentListRoute,
    exact: true,
    auth: true,
  },
  MATERIALITY_TRACKER_QUESTION_INDEX: {
    id: 'materialityAssessment_survey_question_index',
    label: 'Question Index',
    path: '/materiality-tracker/:initiativeId/assessment/:surveyId/:questionId/:questionIndex',
    component: AssessmentRoute,
    auth: true,
  },
  MATERIALITY_TRACKER_QUESTION: {
    id: 'materialityAssessment_survey_question',
    label: 'Question',
    path: '/materiality-tracker/:initiativeId/assessment/:surveyId/:questionId',
    component: AssessmentRoute,
    auth: true,
  },
  MATERIALITY_TRACKER_SURVEY: {
    id: 'materialityAssessment_survey',
    label: 'Latest assessment',
    path: '/materiality-tracker/:initiativeId/assessment/:surveyId?',
    component: AssessmentRoute,
    exact: true,
    auth: true,
  },
  MATERIALITY_TRACKER_BANKING_SETTINGS: {
    id: 'materiality_tracker_banking_settings',
    label: 'Banking Settings',
    path: '/materiality-tracker/:initiativeId/banking-settings',
    component: BankingSettingsRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_STOCK_EXCHANGE_SETTINGS: {
    id: 'materiality_tracker_stock_exchange_settings',
    label: 'Stock Exchange Settings',
    path: '/materiality-tracker/:initiativeId/stock-exchange-settings',
    component: StockExchangeSettingsRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MATERIALITY_TRACKER_INTEGRATIONS: {
    id: 'materiality_tracker_integrations',
    label: 'Integrations',
    path: '/materiality-tracker/:initiativeId/integrations',
    component: IntegrationsRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
};
