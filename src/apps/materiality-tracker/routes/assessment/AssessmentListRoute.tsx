/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { NotFound } from '../../../../routes/not-found/NotFound';
import { AssessmentsList } from '../../components/assessments-list/AssessmentsList';
import { useParams } from 'react-router-dom';

export const AssessmentListRoute = () => {
  const { initiativeId } = useParams<{
    initiativeId: string;
  }>();

  if (!initiativeId) {
    return <NotFound />;
  }
  return <AssessmentsList initiativeId={initiativeId} />;
};
