/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Assessment } from '../../components/assessment/Assessment';
import { NotFound } from '../../../../routes/not-found/NotFound';
import { useHistory, useParams } from 'react-router-dom';
import { AssessmentQuestion } from '../../components/assessment/AssessmentQuestion';
import { useGetSurveysQuery } from '../../api/materiality-assessment';
import { useEffect } from 'react';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { generateUrl } from '../../../../routes/util';
import { ROUTES } from '../../../../constants/routes';

export const AssessmentRoute = () => {
  const { initiativeId, surveyId, questionId, questionIndex } = useParams<{
    initiativeId: string;
    surveyId?: string;
    questionId?: string;
    questionIndex?: string;
  }>();
  const { data: surveys = [], isFetching: isGettingSurveys } = useGetSurveysQuery({ initiativeId });
  const history = useHistory();

  useEffect(() => {
    if (isGettingSurveys || surveyId || !initiativeId) {
      return;
    }

    const latestSurvey = surveys?.[0];
    if (latestSurvey) {
      const surveyId = latestSurvey._id;
      return history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_SURVEY, { initiativeId, surveyId }));
    }
    history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_LIST, { initiativeId }));
  }, [history, surveyId, initiativeId, isGettingSurveys, surveys]);

  if (!initiativeId) {
    return <NotFound />;
  }

  if (!surveyId) {
    if (isGettingSurveys) {
      return <BlockingLoader />;
    }
    return (
      <NotFound /> // Shouldn't get here, as it should in this case redirect to the survey list
    );
  }

  if (questionId) {
    return (
      <AssessmentQuestion
        initiativeId={initiativeId}
        surveyId={surveyId}
        questionId={questionId}
        questionIndex={questionIndex}
      />
    );
  }

  const minimalSurveys = surveys.map((survey) => ({
    _id: survey._id,
    name: survey.name,
    effectiveDate: survey.effectiveDate,
  }));

  return <Assessment initiativeId={initiativeId} surveys={minimalSurveys} surveyId={surveyId} />;
};
