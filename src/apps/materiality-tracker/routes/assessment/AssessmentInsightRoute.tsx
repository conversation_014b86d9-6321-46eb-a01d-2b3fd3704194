/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { NotFound } from '../../../../routes/not-found/NotFound';
import { useParams } from 'react-router-dom';
import { useGetSurveysQuery } from '../../api/materiality-assessment';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { AssessmentInsights } from '../../components/assessment-insights/AssessmentInsights';
import { useAppSelector } from '@reducers/index';
import { currentInitiative } from '@selectors/initiative';
import { canAccessInsightsAndDownloads } from '@selectors/user';
import NotAuthorised from '@routes/not-authorised';

export const AssessmentInsightRoute = () => {
  const { initiativeId, insightSurveyId } = useParams<{
    initiativeId: string;
    insightSurveyId: string;
  }>();

  const initiative = useAppSelector(currentInitiative);
  const { data: surveys, isFetching: isGettingSurveys } = useGetSurveysQuery({ initiativeId });
  const canAccessDownloadsByRole = useAppSelector((state) => canAccessInsightsAndDownloads(state, initiativeId));

  if (!canAccessDownloadsByRole) {
    return <NotAuthorised />;
  }

  if (isGettingSurveys || !initiative) {
    return <BlockingLoader />;
  }

  const survey = surveys?.find((s) => s._id === insightSurveyId);
  if (!surveys?.length || !survey) {
    return <NotFound />;
  }

  return <AssessmentInsights selectedSurvey={survey} surveys={surveys} initiative={initiative} />;
};
