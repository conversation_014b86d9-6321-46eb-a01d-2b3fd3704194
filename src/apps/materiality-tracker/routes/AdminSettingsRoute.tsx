/*
 * Copyright (c) 2024. Word Wide Generation Ltd
 */

import AdminSettings, { AdminSettingKey, CardSettings } from '@features/admin-settings';
import { ROUTES } from '@constants/routes';
import { useAppSelector } from '@reducers/index';
import NotAuthorised from '@routes/not-authorised';
import { generateUrl } from '@routes/util';
import { isUserManagerByInitiativeId } from '@selectors/user';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { isMaterialityTracker } from '@utils/apps';
import { getRootOrg, isRootOrg } from '@selectors/initiative';
import { CompanyTrackerIntegrationCard } from '../components/integration-card/CompanyTrackerIntegrationCard';

export const AdminSettingsRoute = () => {
  const history = useHistory();
  const match = useRouteMatch<{ initiativeId?: string }>();
  const { initiativeId } = match.params;

  const isManager = useAppSelector((state) => isUserManagerByInitiativeId(state, initiativeId));
  const isRootOrganization = useAppSelector(isRootOrg);
  const rootOrg = useAppSelector(getRootOrg);

  if (!isManager || !rootOrg || !isRootOrganization || initiativeId !== rootOrg._id) {
    return <NotAuthorised />;
  }

  const isPureMaterialityTracker = isMaterialityTracker(rootOrg);

  const cardSettings: CardSettings[] = [
    {
      key: AdminSettingKey.CompanySettings,
      handler: () => history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_COMPANY_SETTINGS, { initiativeId })),
      icon: 'fal fa-sliders fa-3x',
      text: 'Company settings',
    },
    {
      key: AdminSettingKey.ManageUsers,
      handler: () => history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_MANAGE_USERS, { initiativeId })),
      icon: 'fal fa-users-gear fa-3x',
      text: 'Manage users',
    },
    isPureMaterialityTracker
      ? {
          key: AdminSettingKey.AppIntegrations,
          text: <CompanyTrackerIntegrationCard initiative={rootOrg} />,
          className: 'p-3',
        }
      : {
          key: AdminSettingKey.AppIntegrations,
          handler: () => history.push(generateUrl(ROUTES.MATERIALITY_TRACKER_INTEGRATIONS, { initiativeId })),
          icon: 'fal fa-circle-nodes fa-3x',
          text: <>App Integrations</>,
        },
  ];

  return <AdminSettings cardSettings={cardSettings} />;
};
