import { useGetMetricGroupsQuery } from '@api/metric-groups';
import { cloneDeep } from '@apollo/client/utilities';
import { useGetSurveysQuery } from '@apps/materiality-tracker/api/materiality-assessment';
import { initializeMetricGroupFromSurvey } from '@apps/materiality-tracker/components/metric-groups/utils';
import { ViewMode } from '@components/custom-metrics/constants';
import CustomMetricContainer from '@components/custom-metrics/CustomMetricContainer';
import { DEFAULT_CUSTOM_METRICS_USAGE } from '@components/custom-metrics/utils';
import Dashboard from '@components/dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { MetricGroup } from '@g17eco/types/metricGroup';
import { useAppSelector } from '@reducers/index';
import { skipToken } from '@reduxjs/toolkit/query';
import NotAuthorised from '@routes/not-authorised';
import { isStaff, isUserManager } from '@selectors/user';
import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { MetricGroupDashboard } from '../components/metric-groups/MetricGroupDashboard';
import { MetricGroupForm } from '../components/metric-groups/MetricGroupForm';

interface RouteParams {
  initiativeId: string;
  groupId?: string;
  viewMode?: ViewMode;
}

export const CustomMetricGroupRoute = () => {
  const { initiativeId, groupId, viewMode } = useParams<RouteParams>();
  const isStaffUser = useAppSelector(isStaff);
  const isAdmin = useAppSelector(isUserManager);
  const canManageMetricGroup = isStaffUser && isAdmin;
  const { data: surveys, isFetching: isSurveysLoading } = useGetSurveysQuery(
    canManageMetricGroup ? { initiativeId } : skipToken,
  );
  const {
    data: metricGroups,
    isFetching: isMetricGroupsLoading,
    refetch,
  } = useGetMetricGroupsQuery(canManageMetricGroup ? initiativeId : skipToken);

  const isLoading = isSurveysLoading || isMetricGroupsLoading;

  const extendedMetricGroups = useMemo(() => {
    const existingSurveyIds = metricGroups?.map((s) => s.source?.surveyId).filter(Boolean) ?? [];
    return (surveys || []).reduce<MetricGroup[]>(
      (acc, survey) => {
        if (!existingSurveyIds.includes(survey._id)) {
          acc.push(initializeMetricGroupFromSurvey(survey));
        }
        return acc;
      },
      cloneDeep(metricGroups) || [],
    );
  }, [metricGroups, surveys]);

  if (!canManageMetricGroup) {
    return <NotAuthorised />;
  }

  const handleReload = async () => {
    refetch();
  };

  const renderView = () => {
    if (groupId) {
      const metricGroup = extendedMetricGroups.find((group) => group._id === groupId);
      return metricGroup ? (
        <MetricGroupForm
          initiativeId={initiativeId}
          metricGroup={metricGroup}
          viewMode={viewMode}
          handleReload={handleReload}
        />
      ) : null;
    }
    return <MetricGroupDashboard initiativeId={initiativeId} metricGroups={extendedMetricGroups} />;
  };

  return (
    <CustomMetricContainer
      initiativeId={initiativeId}
      view={viewMode}
      groupId={groupId}
      isPortfolioTracker={false}
      customMetricsUsage={DEFAULT_CUSTOM_METRICS_USAGE}
      metricGroups={extendedMetricGroups}
    >
      {isLoading ? <Loader /> : null}
      <Dashboard>{renderView()}</Dashboard>
    </CustomMetricContainer>
  );
};
