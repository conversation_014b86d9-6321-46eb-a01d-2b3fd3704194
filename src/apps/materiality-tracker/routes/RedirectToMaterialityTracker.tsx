/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Loader } from '@g17eco/atoms/loader';
import { generateUrl } from '@routes/util';
import { LocationDescriptor } from 'history';
import { useEffect, useState } from 'react';
import { Redirect, useRouteMatch } from 'react-router-dom';
import { materialityAssessmentInitiativeRoutes } from './MaterialityTrackerRoutes';

enum RedirectDestinations {
  ASSESSMENT_LIST = 'assessment-list',
}

export const RedirectToMaterialityTracker = () => {
  const [redirectUrl, setRedirectUrl] = useState<LocationDescriptor>('');
  const { params } = useRouteMatch<{ initiativeId: string; destination?: RedirectDestinations }>();
  const { initiativeId, destination } = params;

  useEffect(() => {
    if (!initiativeId) {
      return;
    }
    switch (destination) {
      case RedirectDestinations.ASSESSMENT_LIST: {
        const url = generateUrl(materialityAssessmentInitiativeRoutes.MATERIALITY_TRACKER_LIST, { initiativeId });
        setRedirectUrl({ pathname: url });
        break;
      }
      default:
        return;
    }
  }, [destination, initiativeId]);

  if (redirectUrl) {
    return <Redirect to={redirectUrl} />;
  }

  return <Loader />;
};
