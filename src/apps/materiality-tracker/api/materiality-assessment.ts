/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiResponse } from '../../../types/api';
import { axiosBaseQuery } from '../../../api/axiosQuery';
import { SurveyActionData, SurveyModelMinData, SurveyModelMinimalUtrv } from '../../../model/surveyData';
import { UniversalTrackerBase } from '../../../model/UniversalTracker';
import { JobStatus } from '../../../types/background-jobs';
import { DateString } from '@utils/date';
import { UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { PrioritizedAssessmentData } from '../components/assessment-insights/types';
import { AssessmentType } from '@g17eco/types/survey';

const transformResponse = <T>(response: ApiResponse<T>) => response.data;
const transformErrorResponse = <T>(response: { error: any, message: string; name: string; }) => response;

export interface UtrMapping {
  code: string;
  name?: string;
  score: number;
}

export interface MaterialTopicCategories {
  esg?: ESGCategory[];
  sdg?: string[];
  materialPillar?: MaterialPillar[];
  boundary?: MaterialityBoundary[];
}

export enum ESGCategory {
  Environmental = 'Environmental',
  Social = 'Social',
  Governance = 'Governance',
}

export enum MaterialPillar {
  People = 'people',
  Partnership = 'partnership',
  Planet = 'planet',
  Prosperity = 'prosperity',
  Principle = 'principle',
}

export enum MaterialityBoundary {
  Leadership = 'leadership',
  ResearchAndDevelopment = 'research-and-development',
  SupplyChain = 'supply-chain',
  ProductAndServices = 'product-and-services',
  Distribution = 'distribution',
  Communities = 'communities',
  Experiences = 'experiences',
}

export enum MaterialityAssessmentScope {
  Startup = 'startup',
  Solopreneur = 'solopreneur',
  Micro = 'micro',
  SME = 'sme',
  MidCap = 'mid-cap',
  MNC = 'mnc',
  Large = 'large',
}

export interface AssessmentJobResult {
  jobId: string;
  status: JobStatus;
  result?: AssessmentResult;
  config?: MaterialityAssessmentConfig;
  updatedAt: string;
}

export interface PPTXReportJobResult {
  status: JobStatus;
  jobId: string;
  taskId: string;
  completedDate: string | undefined;
}

export interface AssessmentResult {
  financial: AssessmentData[];
  nonFinancial: AssessmentData[];
}

export type AssessmentData = {
  code: string;
  name?: string;
  score: number;
  relativeScore?: number;
  utrMapping?: UtrMapping[];
  categories?: MaterialTopicCategories;
  description?: string;
  action?: string;
}

export type DoubleMaterialityAssessmentData = PrioritizedAssessmentData & {
  financialScore: number | undefined;
  nonFinancialScore: number | undefined;
  financialRelativeScore: number | undefined;
  nonFinancialRelativeScore: number | undefined;
}

export type OrderedTopic = Pick<AssessmentData, 'code'> & {
  disabled?: boolean;
};

export interface MaterialityAssessmentConfig {
  orderedTopics: OrderedTopic[];
  explanation: string;
}

export enum UpdateImpactScope {
  Reports = 'reports',
  ModuleMetrics = 'module-metrics',
}

export interface MaterialitySurveyModelMinData extends SurveyModelMinData {
  fragmentUniversalTrackerValues: SurveyModelMinimalUtrv[];
  /** @todo: apply migration for materiality surveys then change this to required */
  assessmentType?: AssessmentType;
}
export interface CreateSurveyContext {
  utrs: UniversalTrackerBase[];
}
export interface CreateSurvey {
  initiativeId: string;
  effectiveDate: DateString;
  answers: {
    [key: string]: string | undefined;
  };
  returnUrl: string;
  referralCode?: string;
  assessmentType: AssessmentType;
}

const SCORE_TAG = 'scores';
const SURVEY_TAG = 'survey';
const SURVEY_LIST_TAG = 'survey-list';
const BUY_SURVEY_CONTEXT_TAG = 'buy-survey-context';
const MT_PPTX_REPORT_TAG = 'mt-pptx-report';


export const materialityAssessmentApi = createApi({
  reducerPath: 'materialityAssessmentApi',
  baseQuery: axiosBaseQuery(),
  tagTypes: ['scores', SURVEY_LIST_TAG, SURVEY_TAG, BUY_SURVEY_CONTEXT_TAG, MT_PPTX_REPORT_TAG],
  endpoints: (builder) => ({
    generateScores: builder.query<AssessmentJobResult, { initiativeId: string; surveyId: string }>({
      transformResponse,
      query: ({ initiativeId, surveyId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores`,
        method: 'get',
      }),
      providesTags: [SCORE_TAG],
    }),
    regenerateScores: builder.mutation<AssessmentJobResult, { initiativeId: string; surveyId: string }>({
      transformResponse,
      query: ({ initiativeId, surveyId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores`,
        method: 'post',
      }),
      invalidatesTags: [SCORE_TAG],
    }),
    getSurveys: builder.query<MaterialitySurveyModelMinData[], { initiativeId: string }>({
      transformResponse,
      query: ({ initiativeId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys`,
        method: 'get',
      }),
      providesTags: [SURVEY_LIST_TAG],
    }),
    getBuySurveyContext: builder.query<CreateSurveyContext, { initiativeId: string }>({
      transformResponse,
      keepUnusedDataFor: 1,
      query: ({ initiativeId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/context`,
        method: 'get',
      }),
      providesTags: [BUY_SURVEY_CONTEXT_TAG],
    }),
    createSurvey: builder.mutation<{ sessionUrl?: string }, CreateSurvey>({
      transformResponse,
      transformErrorResponse,
      query: (data) => ({
        url: `/materiality-assessment/initiatives/${data.initiativeId}/create`,
        method: 'post',
        data,
      }),
      invalidatesTags: [SURVEY_LIST_TAG, BUY_SURVEY_CONTEXT_TAG],
    }),
    getSurvey: builder.query<SurveyActionData, { surveyId: string }>({
      transformResponse,
      query: ({ surveyId }) => ({
        url: `/surveys/${surveyId}`,
        method: 'get',
      }),
      providesTags: [SURVEY_TAG],
    }),
    getSurveyCreateStatus: builder.query<
      { status: string; surveyId: string },
      { initiativeId: string; sessionId: string }
    >({
      transformResponse,
      query: (data) => ({
        url: `/materiality-assessment/initiatives/${data.initiativeId}/create/${data.sessionId}`,
        method: 'get',
      }),
    }),
    deleteSurvey: builder.mutation<null, { surveyId: string }>({
      transformResponse,
      query: ({ surveyId }) => ({
        url: `/surveys/${surveyId}`,
        method: 'delete',
      }),
      invalidatesTags: [SURVEY_LIST_TAG, SURVEY_TAG, BUY_SURVEY_CONTEXT_TAG],
    }),
    generatePPTXReport: builder.query<
      PPTXReportJobResult,
      { initiativeId: string; surveyId: string; jobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, jobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${jobId}/report/pptx`,
        method: 'get',
      }),
      providesTags: [MT_PPTX_REPORT_TAG],
    }),
    regeneratePPTXReport: builder.mutation<
      PPTXReportJobResult,
      { initiativeId: string; surveyId: string; scoreJobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, scoreJobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${scoreJobId}/report/pptx`,
        method: 'post',
      }),
      invalidatesTags: [MT_PPTX_REPORT_TAG],
    }),
    getAssessmentSize: builder.query<
      { sizeScope: MaterialityAssessmentScope },
      { initiativeId: string; assessmentId: string }
    >({
      transformResponse,
      query: ({ initiativeId, assessmentId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${assessmentId}/size-scope`,
        method: 'get',
      }),
    }),
    getMappedUniversalTrackers: builder.query<
      UniversalTrackerBlueprintMin[],
      { initiativeId: string; surveyId: string; jobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, jobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${jobId}/questions`,
        method: 'get',
      }),
      providesTags: [SCORE_TAG, SURVEY_TAG],
    }),
    updateConfig: builder.mutation<
      void,
      { initiativeId: string; surveyId: string; config: MaterialityAssessmentConfig; impactScopes: UpdateImpactScope[] }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, config, impactScopes }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/config`,
        method: 'put',
        data: { config, impactScopes },
      }),
      invalidatesTags: [SCORE_TAG, MT_PPTX_REPORT_TAG],
    }),
    deleteConfig: builder.mutation<void, { initiativeId: string; surveyId: string; impactScopes: UpdateImpactScope[] }>({
      transformResponse,
      query: ({ initiativeId, surveyId, impactScopes }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/config`,
        method: 'delete',
        data: { impactScopes },
      }),
      invalidatesTags: [SCORE_TAG, MT_PPTX_REPORT_TAG],
    }),
  }),
});

export const {
  useGenerateScoresQuery,
  useRegenerateScoresMutation,
  useGetSurveysQuery,
  useLazyGetSurveyCreateStatusQuery,
  useLazyGetSurveysQuery,
  useCreateSurveyMutation,
  useGetSurveyQuery,
  useGetBuySurveyContextQuery,
  useDeleteSurveyMutation,
  useGeneratePPTXReportQuery,
  useLazyGetAssessmentSizeQuery,
  useGetAssessmentSizeQuery,
  useGetMappedUniversalTrackersQuery,
  useUpdateConfigMutation,
  useDeleteConfigMutation,
  useRegeneratePPTXReportMutation,
} = materialityAssessmentApi;
