/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getPortfolioById } from '@actions/portfolio-tracker';
import { g17ecoApi, transformResponse } from '@api/g17ecoApi';
import { InsightDashboard, InsightDashboardType } from '@g17eco/types/insight-custom-dashboard';
import { Portfolio } from '@g17eco/types/portfolio';
import { DataPeriods } from '@g17eco/types/universalTracker';

const PORTFOLIO_TRACKER_TAG = 'portfolio-tracker';
const PORTFOLIO_COMPANY_DASHBOARD_TAG = 'portfolio-company-dashboard';
const tags = [PORTFOLIO_TRACKER_TAG, PORTFOLIO_COMPANY_DASHBOARD_TAG];

interface PortfolioCompanyDashboardUrl {
  portfolioId: string;
  initiativeId: string;
  dashboardType: InsightDashboardType;
  params?: { period?: DataPeriods }
}

const getUrl = ({ portfolioId, initiativeId, dashboardType }: PortfolioCompanyDashboardUrl) => {
  return `/portfolios/${portfolioId}/initiatives/${initiativeId}/dashboard/${dashboardType}`;
}

export const portfolioTrackerApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: tags,
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getCompanyDashboard: builder.query<InsightDashboard, PortfolioCompanyDashboardUrl>({
        transformResponse,
        query: (params) => {
          return {
            url: getUrl(params),
            method: 'get',
          };
        },
        providesTags: [PORTFOLIO_COMPANY_DASHBOARD_TAG],
      }),
      getPortfolioById: builder.query<Portfolio, string>({
        transformResponse,
        query: (portfolioId) => {
          return {
            url: `portfolios/${portfolioId}`,
            method: 'get',
          };
        },
        providesTags: (_result, _error, agrs) => [{ type: PORTFOLIO_TRACKER_TAG, id: agrs }],
      }),
    }),
  });

export const { useGetCompanyDashboardQuery, useGetPortfolioByIdQuery } = portfolioTrackerApi;
