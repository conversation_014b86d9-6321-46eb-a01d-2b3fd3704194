/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */



import { UserManagement } from '../../../routes/user-management';
import { useParams } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { PTAdminBreadcrumbs } from '../components/breadcrumbs/PTAdminBreadcrumbs';

export const UserManagementRoute = () => {

  const { portfolioId, page } = useParams<{ page?: string, portfolioId: string }>();
  // Not limited for now in PT
  const userLimit = 999;

  return (
    <UserManagement
      baseRoute={ROUTES.PORTFOLIO_TRACKER_MANAGE_USERS}
      initiativeId={portfolioId} page={page} userLimit={userLimit}
      BreadcrumbsComponent={PTAdminBreadcrumbs}
      />
  )
}
