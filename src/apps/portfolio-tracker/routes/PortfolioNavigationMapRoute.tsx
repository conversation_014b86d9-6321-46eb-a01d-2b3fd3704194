/*
 * Copyright (c) 2020-2024. World Wide Generation Ltd
 */


import { useSelector } from 'react-redux';
import { getInitiativeGroupTree } from '../../../selectors/initiativeTree';

import { useCallback } from 'react';
import { RootState, useAppDispatch, useAppSelector } from '../../../reducers';
import { loadPortfolioTree } from '../../../slice/portfolioTreeSlice';
import { Loader } from '@g17eco/atoms/loader';
import { portfolioTrackerInitiativeRoutes } from './portfolioTrackerRoutes';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { InitiativeStructureFolders } from '@routes/initiative-structure/InitiativeStructureFolders';
import { getCurrentUser } from '@selectors/user';

export const PortfolioNavigationMapRoute = () => {
  const currentUser = useAppSelector(getCurrentUser);
  const dispatch = useAppDispatch();
  const history = useHistory();
  const initiativeTree = useSelector(getInitiativeGroupTree);

  const portfolioTree = useSelector((state: RootState) => state.portfolioTree);

  const onClick = useCallback(
    (portfolioId = '') => {
      const path = generateUrl(portfolioTrackerInitiativeRoutes.PORTFOLIO_TRACKER_PORTFOLIO, { portfolioId });
      history.push(path);
    },
    [history],
  );

  if (!currentUser) {
    return <Loader />;
  }

  if (!portfolioTree.loaded) {
    dispatch(loadPortfolioTree());
    return <Loader />;
  }

  return (
    <InitiativeStructureFolders
      currentUser={currentUser}
      archivedInitiatives={[]}
      onClick={onClick}
      customTree={initiativeTree}
      isPortfolioTracker
    />
  );
};
