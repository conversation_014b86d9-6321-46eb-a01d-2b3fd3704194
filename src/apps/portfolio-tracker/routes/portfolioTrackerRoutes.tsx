/*
 * Copyright (c) 2020-2024. World Wide Generation Ltd
 */

import { RoutesInterface } from '@g17eco/types/routes';
import { PortfolioRoute } from '@components/portfolio';
import { UserManagementRoute } from './UserManagementRoute';
import { CompanyManagement } from '@routes/company-management';
import { PortfolioTrackerCompanyRoute, PORTFOLIO_TRACKER_COMPANY } from './PortfolioTrackerCompanyRoute';
import { Benchmarking } from '@components/portfolio/benchmarking';
import { BenchmarkingTabCodes } from '@components/portfolio/benchmarking/BenchmarkingNavigation';
import { CompanySponsorship } from '@routes/company-sponsorship';
import { PortfolioInsightRoute } from './PortfolioInsightRoute';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { CustomMetricManagementRoute } from './CustomMetricManagementRoute';
import { AdminSettingsRoute } from './AdminSettingsRoute';
import { CompanySettingsRoute } from './CompanySettingsRoute';
import { SURVEY } from '@constants/terminology';

export const portfolioTrackerInitiativeRoutes: RoutesInterface = {
  PORTFOLIO_TRACKER_PORTFOLIO: {
    id: 'portfolio_tracker',
    label: 'Portfolios',
    tooltip: 'List of all portfolios',
    path: '/portfolio-tracker/portfolio/:portfolioId',
    icon: 'fa-folder-open',
    component: PortfolioRoute,
    appPermissionId: 'app_portfolio_tracker',
    exact: true,
    auth: true,
  },
  PORTFOLIO_TRACKER_MANAGE_SPONSORSHIPS: {
    id: 'portfolio_tracker_manage_sponsorships',
    label: 'Manage Sponsorships',
    path: '/portfolio-tracker/admin/:portfolioId/manage-sponsorships',
    component: CompanySponsorship,
    appPermissionId: 'app_portfolio_tracker',
    exact: true,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    parentId: 'portfolio_tracker'
  },
  PORTFOLIO_TRACKER_COMPANY_SETTINGS:
  {
    id: 'admin_company_settings',
    label: 'Company Settings',
    path: '/portfolio-tracker/admin/:portfolioId/company-settings',
    tooltip: 'Company Settings',
    component: CompanySettingsRoute,
    appPermissionId: 'app_portfolio_tracker',
    auth: true,
    hidden: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  PORTFOLIO_TRACKER_ADMIN_SETTINGS:
  {
    id: 'admin',
    label: 'Admin',
    path: '/portfolio-tracker/admin/:portfolioId',
    component: AdminSettingsRoute,
    appPermissionId: 'app_portfolio_tracker',
    tooltip: 'Admin',
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  PORTFOLIO_TRACKER_INSIGHTS:
  {
    id: 'insights',
    label: 'Insights',
    path: '/portfolio-tracker/insights/:portfolioId',
    component: PortfolioInsightRoute,
    appPermissionId: 'app_portfolio_tracker',
    tooltip: 'Insights',
    auth: true,
  },
  PORTFOLIO_TRACKER_INSIGHT_DASHBOARDS:
  {
    id: 'portfolio-insight-dashboards',
    label: 'Insight Dashboards',
    path: '/portfolio-tracker/insights/:portfolioId/custom-dashboard/:dashboardId',
    component: PortfolioInsightRoute,
    appPermissionId: 'app_portfolio_tracker',
    tooltip: 'Insight Dashboards',
    auth: true,
    hidden: true
  },
  PORTFOLIO_TRACKER_BENCHMARKING:
  {
    id: 'benchmarking',
    label: 'Benchmarking',
    path: '/portfolio-tracker/benchmarking/:portfolioId/:page?/:group?/:subGroup?/:leafGroup?/:leafChildGroup?',
    component: Benchmarking,
    appPermissionId: 'app_portfolio_tracker',
    tooltip: 'Benchmarking',
    auth: true,
    defaultParams: { page: BenchmarkingTabCodes.LatestSurveyProgress },
  },
  PORTFOLIO_TRACKER_CUSTOM_METRICS:
  {
    id: 'custom_metrics',
    label: 'Custom Metrics',
    path: '/portfolio-tracker/portfolio/:portfolioId/custom-metrics/:groupId?/:view?',
    component: CustomMetricManagementRoute,
    appPermissionId: 'app_portfolio_tracker',
    tooltip: 'Custom Metrics',
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hidden: true
  },
  PORTFOLIO_TRACKER_COMPANY,
  PORTFOLIO_TRACKER_MANAGE_USERS: {
    id: 'manage_users',
    label: 'Manage Users',
    path: '/portfolio-tracker/portfolio/:portfolioId/manage-users/:page?',
    icon: 'fa-user',
    tooltip: 'Manage Users and Permissions',
    component: UserManagementRoute,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hidden: true
  },
  PORTFOLIO_TRACKER_MANAGE_COMPANIES: {
    id: 'manage_companies',
    label: 'Companies',
    path: '/portfolio-tracker/portfolio/:portfolioId/manage-companies/:page?/:view?',
    icon: 'fa-building',
    tooltip: 'View a list of all companies across all portfolios',
    component: CompanyManagement,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hidden: true,
  },
  PORTFOLIO_TRACKER_SURVEY_OVERVIEW: {
    id: 'portfolio_tracker_survey_overview',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Overview`,
    path: '/portfolio-tracker/portfolio/:portfolioId/company/:companyId/overview/:questionId?/:index?',
    component: PortfolioTrackerCompanyRoute,
    appPermissionId: 'app_portfolio_tracker',
    exact: true,
    auth: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    parentId: 'portfolio_tracker',
  },
};
