/*
 * Copyright (c) 2022-2024. World Wide Generation Ltd
 */

import { Insights } from '@components/portfolio/insights';
import { Route, Switch, useParams } from 'react-router-dom';
import { portfolioTrackerRoutes } from './PortfolioTrackerEntryRoute';
import { PortfolioCustomDashboardRoute } from './PortfolioCustomDashboardRoute';

const PortfolioCustomDashboardRouteContainer = () => {
  const { dashboardId } = useParams<{ dashboardId?: string }>();
  return <PortfolioCustomDashboardRoute key={dashboardId} />
}

export const PortfolioInsightRoute = () => {
  return (
    <Switch>
      <Route path={portfolioTrackerRoutes.PORTFOLIO_TRACKER_INSIGHT_DASHBOARDS.path}>
        <PortfolioCustomDashboardRouteContainer />
      </Route>

      <Route>
        <Insights />
      </Route>
    </Switch>
  );
};
