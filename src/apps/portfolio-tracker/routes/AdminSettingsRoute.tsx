/*
 * Copyright (c) 2024. Word Wide Generation Ltd
 */

import AdminSettings, { AdminSettingKey, CardSettings } from '@features/admin-settings';
import { ROUTES } from '@constants/routes';
import { useAppSelector } from '@reducers/index';
import NotAuthorised from '@routes/not-authorised';
import { generateUrl } from '@routes/util';
import { isPortfolioTrackerExchange } from '@selectors/portfolio';
import { isUserManagerByInitiativeId } from '@selectors/user';
import { useHistory, useRouteMatch } from 'react-router-dom';

export const AdminSettingsRoute = () => {
  const history = useHistory();
  const match = useRouteMatch<{ portfolioId?: string }>();
  const { portfolioId } = match.params;

  const isManager = useAppSelector((state) =>
    isUserManagerByInitiativeId(state, portfolioId)
  );
  const isExchangeView = useAppSelector(isPortfolioTrackerExchange);

  if (!portfolioId || !isManager) {
    return <NotAuthorised />;
  }

  const cardSettings: CardSettings[] = [
    {
      key: AdminSettingKey.ManageUsers,
      handler: () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_MANAGE_USERS, { portfolioId })),
      icon: 'fal fa-users-gear fa-3x',
      text: 'Manage users',
    },
    {
      key: AdminSettingKey.ManageMetrics,
      handler: () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_CUSTOM_METRICS, { portfolioId })),
      icon: 'fal fa-file-circle-question fa-3x',
      text: 'Assigned metrics',
    },
    {
      key: AdminSettingKey.ManageSponsorships,
      handler: () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_MANAGE_SPONSORSHIPS, { portfolioId })),
      icon: 'fal fa-box-check fa-3x',
      text: 'Manage sponsorships',
    },
  ];

  if (isExchangeView) {
    cardSettings.unshift({
      key: AdminSettingKey.CompanySettings,
      handler: () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY_SETTINGS, { portfolioId })),
      icon: 'fal fa-sliders fa-3x',
      text: 'Company settings',
    });
  }

  return <AdminSettings cardSettings={cardSettings} />;
};
