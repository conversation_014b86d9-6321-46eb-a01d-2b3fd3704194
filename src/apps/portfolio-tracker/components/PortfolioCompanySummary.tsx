/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DashboardSectionTitle } from '@components/dashboard';
import { SdgContributionGraph } from '@components/impact-performance/SdgContributionGraph';
import { RatingsCarousel } from '@components/ratings-standards';
import { useGetScorecardByInitiativeIdQuery, useGetScorecardBySurveyIdQuery } from '@api/data-share';
import { RequesterType } from '@g17eco/types/dataShare';
import { getDefaultScorecardState } from '../../../reducers/scorecard';

interface PortfolioCompanySummaryProps {
  portfolioId: string;
  initiativeId: string;
  surveyId: string;
  hasFullScopeAccess: boolean;
}

const getScorecardState = ({ data, isSuccess, isLoading }: { data: any; isSuccess: boolean; isLoading: boolean }) => {
  const state = getDefaultScorecardState();
  if (isSuccess) {
    return {
      ...state,
      loaded: true,
      error: false,
      data: undefined,
      main: {
        data,
        loaded: true,
        loading: false,
      },
    };
  }
  if (isLoading) {
    return state;
  }
  return { ...state, errored: true };
};

export const PortfolioCompanySummary = (props: PortfolioCompanySummaryProps) => {
  const { initiativeId, portfolioId, surveyId, hasFullScopeAccess } = props;

  const {
    data: scSurvey,
    isSuccess: isScSurveySuccess,
    isFetching: isLoadingScSurvey,
  } = useGetScorecardBySurveyIdQuery(
    {
      initiativeId,
      surveyId,
      requesterId: portfolioId,
      requesterType: RequesterType.Portfolio,
    },
    { skip: !surveyId || !hasFullScopeAccess }
  );

  const {
    data: scInitiative,
    isSuccess: isScInitiativeSuccess,
    isFetching: isLoadingScInitiative,
  } = useGetScorecardByInitiativeIdQuery(
    {
      initiativeId,
      requesterId: portfolioId,
      requesterType: RequesterType.Portfolio,
    },
    { skip: !!surveyId || !hasFullScopeAccess }
  );

  const scorecardState = getScorecardState({
    data: surveyId ? scSurvey : scInitiative,
    isSuccess: surveyId ? isScSurveySuccess : isScInitiativeSuccess,
    isLoading: surveyId ? isLoadingScSurvey : isLoadingScInitiative,
  });

  return (
    <div>
      <SdgContributionGraph
        canAccess={false}
        showAITools={false}
        scorecardState={scorecardState}
        readOnly
        surveyId={surveyId}
        initiativeId={initiativeId}
      />
      <DashboardSectionTitle
        title='Media, Ratings and Certifications'
        headingStyle={2}
        className='text-ThemeHeadingLight'
      />
      <RatingsCarousel
        readOnly={true}
        showNoItems={true}
        surveyId={surveyId}
        initiativeId={initiativeId}
        options={{ showInfoButton: false }} />
    </div>
  );
};
