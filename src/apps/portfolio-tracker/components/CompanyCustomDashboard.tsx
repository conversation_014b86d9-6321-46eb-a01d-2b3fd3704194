/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { QueryWrapper } from '@components/query/QueryWrapper';
import { GridLayoutDashboard } from '@routes/custom-dashboard/GridLayoutDashboard';
import { InitiativeCompany } from '@g17eco/types/initiative';
import { useGetCompanyDashboardQuery } from '../api/portfolioTrackerApi';
import { Portfolio } from '@g17eco/types/portfolio';
import { InsightDashboardType } from '@g17eco/types/insight-custom-dashboard';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

interface CompanyDashboardProps {
  company: Pick<InitiativeCompany, '_id'>
  portfolio: Pick<Portfolio, '_id'>
  dashboardType: InsightDashboardType
  period?: DataPeriods
}

export const CompanyCustomDashboard = (props: CompanyDashboardProps) => {

  const { company, portfolio, dashboardType, period } = props;

  const query = useGetCompanyDashboardQuery({
    portfolioId: portfolio._id,
    initiativeId: company._id,
    dashboardType,
    params: { period },
  });

  return (
    <QueryWrapper
      query={query}
      onLoading={() => <LoadingPlaceholder isLoading={true} height={600} />}
      onSuccess={(dashboard) => {
        return <GridLayoutDashboard
          readOnly={true}
          isEditing={false}
          hideQuestionReference={true}
          gridItems={dashboard.items}
          utrsData={dashboard.utrsData}
          initiativeId={company._id}
          NoDataView={() => {
            return (
              <div className='flex-grow-1 d-flex justify-content-center align-items-center'>
                <span className='text-ThemeTextPlaceholder'>There is no company data.</span>
              </div>
            )
          }}
        />
      }}
    />
  )
}
