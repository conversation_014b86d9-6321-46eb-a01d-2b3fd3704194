/*
 * Copyright (c) 2023-2024. World Wide Generation Ltd
 */

import { useEffect, useMemo, useState } from 'react';
import { Button, ButtonGroup } from 'reactstrap';
import { useHistory, useLocation } from 'react-router-dom';
import Dashboard, { DashboardRow } from '@components/dashboard';
import { CompanyInsightsSidebar } from '@routes/summary/insights/partials/sidebar/CompanyInsightsSidebar';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { layoutOptions } from '@constants/layout';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { getMainDownloadCode, MainDownloadCode } from '../../../config/app-config';
import { getValidPage, isInsightLayoutPage } from '@routes/summary/insights/utils/helpers';
import { InitiativeCompany } from '@g17eco/types/initiative';
import { Portfolio } from '@g17eco/types/portfolio';
import { PortfolioCompanySummary } from './PortfolioCompanySummary';
import G17Client from '@services/G17Client';
import { RequesterType } from '@g17eco/types/dataShare';
import { SurveyListItem } from '@g17eco/types/survey';
import { hasFullScopeAccess } from '@utils/dataShare';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { getDashboardType } from '@g17eco/types/insight-custom-dashboard';
import { CompanyCustomDashboard } from './CompanyCustomDashboard';
import { SurveyPeriodDropdown } from '@components/survey-period-dropdown';
import { CustomDashboardTitle } from '@routes/custom-dashboard/CustomDashboardTitle';
import { usePTCompanyCustomDashboards } from '@hooks/usePTCompanyCustomDashboards';

interface Props {
  company: InitiativeCompany;
  className?: string;
  summaryPage: string | undefined;
  portfolio: Pick<Portfolio, '_id'>;
}

export const PortfolioCompanyInsights = (props: Props) => {
  const { portfolio, company, className } = props;

  const { addSiteError } = useSiteAlert();
  const location = useLocation();
  const history = useHistory();
  const period = (new URLSearchParams(location.search).get('period') ?? DataPeriods.Yearly) as DataPeriods;
  const mainDownloadCode = getMainDownloadCode(company.appConfigCode, company.permissionGroup); // Do not allow to change it
  const [selectedPack, setSelectedPack] = useState<MainDownloadCode>(mainDownloadCode);
  const summaryPage = getValidPage(props.summaryPage);
  const [item, setItem] = useState<SurveyListItem | undefined>();
  const [surveyList, setSurveyList] = useState<SurveyListItem[]>([]);

  const availablePeriods = useMemo(() => {
    return Array.from(new Set(surveyList.map((s) => s.period).filter(Boolean) as DataPeriods[]));
  }, [surveyList]);

  const { currentPage, options, handleClickOption } = usePTCompanyCustomDashboards({
    portfolioId: portfolio._id,
    initiativeId: company._id,
    summaryPage,
  });

  const commonSidebarProps = {
    initiativeId: portfolio._id,
    availablePeriods,
    currentPage,
    options,
    handleClickOption,
  };

  const dashboardType = getDashboardType(summaryPage, mainDownloadCode);

  useEffect(() => {
    G17Client.getRequesterDataShare({
      requesterId: portfolio._id,
      requesterType: RequesterType.Portfolio,
      initiativeId: company._id,
    })
      .then((data) => {
        const surveyList = data.list;
        setSurveyList(surveyList);
        setItem(surveyList[0]);
      })
      .catch((e: Error) => {
        addSiteError(e);
        setSurveyList([]);
      });
  }, [addSiteError, company._id, portfolio._id]);

  const buttons =
    mainDownloadCode === MainDownloadCode.SGX_Metrics && isInsightLayoutPage(summaryPage)
      ? [
          <ButtonGroup key={mainDownloadCode}>
            {layoutOptions.map((option) => {
              const isActive = selectedPack === option.code;
              return (
                <Button
                  key={option.code}
                  outline={!isActive}
                  active={isActive}
                  color={'primary'}
                  onClick={() => setSelectedPack(option.code)}
                >
                  {option.name}
                </Button>
              );
            })}
          </ButtonGroup>,
        ]
      : [];

  const hasButtons = buttons.length > 0;
  const isOverview = summaryPage === InsightPage.Overview;
  const title = isOverview ? <span className='text-ThemeHeadingLight'>Highlights</span> : undefined;

  return (
    <Dashboard className={`profile-dashboard ${className}`} hasSidebar={true}>
      <CompanyInsightsSidebar alignButtonRow={hasButtons} {...commonSidebarProps} />
      {hasButtons ? <DashboardRow className='topbar-container' buttons={buttons} mb={2} /> : null}
      <>
        <DashboardRow className='d-xxl-none mt-4'>
          <CustomDashboardTitle
            title={<h3 className='text-ThemeHeadingLight'>{currentPage}</h3>}
            currentPage={currentPage}
            options={options}
            handleClickOption={handleClickOption}
          />
        </DashboardRow>
        <DashboardRow
          title={title}
          headingStyle={2}
          className='text-ThemeHeadingLight'
          mb={2}
          buttons={
            !isOverview
              ? [
                  <SurveyPeriodDropdown
                    key='period'
                    availablePeriods={availablePeriods}
                    period={period}
                    setPeriod={(period) => {
                      history.push({
                        pathname: location.pathname,
                        search: `?period=${period}`,
                      });
                    }}
                  />,
                ]
              : []
          }
        />

        <CompanyCustomDashboard period={period} portfolio={portfolio} company={company} dashboardType={dashboardType} />

        {!isOverview ? null : (
          <PortfolioCompanySummary
            portfolioId={portfolio._id}
            initiativeId={company._id}
            surveyId={item?._id ?? ''}
            hasFullScopeAccess={hasFullScopeAccess(company)}
          />
        )}
      </>
    </Dashboard>
  );
};
