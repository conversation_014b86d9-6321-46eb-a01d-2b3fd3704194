/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import Dashboard, { DashboardSection } from '@components/dashboard';

interface NotSharedProps {
  title: string;
  children: string | React.JSX.Element;
}

export const NotShared = ({ title, children }: NotSharedProps) => {
  return (
    <Dashboard className='mt-0'>
      <DashboardSection title={title}>
        <div className='my-5 text-center'>
          <h3>{children}</h3>
        </div>
      </DashboardSection>
    </Dashboard>
  );
};
