import { ROUTES } from '@constants/routes';
import { AdminBreadcrumbsProps, Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';

export const PTAdminBreadcrumbs = ({ breadcrumbs, initiativeId }: AdminBreadcrumbsProps) => {
  const rootUrl = generateUrl(ROUTES.PORTFOLIO_TRACKER_ADMIN_SETTINGS, { portfolioId: initiativeId });
  return <Breadcrumbs breadcrumbs={breadcrumbs} rootLabel={'Admin Settings'} rootUrl={rootUrl} />;
}
