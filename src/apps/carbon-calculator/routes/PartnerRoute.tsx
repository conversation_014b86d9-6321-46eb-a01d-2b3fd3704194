/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { useParams } from 'react-router-dom';
import { useAppSelector } from '../../../reducers';
import { isStaff } from '../../../selectors/user';
import NotFound from '@components/not-found';
import Dashboard, { DashboardSection } from '@components/dashboard';
import { PartnerWrapper } from '../features/emission-partner/PartnerWrapper';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';

export const PartnerRoute = () => {

  // Don't actually have initiativeId, needs to move to CT route
  const { partnerCode, initiativeId = '-' } = useParams<{ partnerCode: string, initiativeId?: string }>();

  const isUserStaff = useAppSelector(isStaff)

  if (!isUserStaff) {
    return (
      <NotFound />
    )
  }

  const buttons = [<FeatureStability key={'stability'} feature={ExtraFeature.PartnerIntegration} />];

  return (
    <Dashboard>
      <DashboardSection buttons={buttons} title={'Emission Partner'} icon='fa-calculator'>
        <PartnerWrapper initiativeId={initiativeId} partnerCode={partnerCode} />
      </DashboardSection>
    </Dashboard>
  );
}
