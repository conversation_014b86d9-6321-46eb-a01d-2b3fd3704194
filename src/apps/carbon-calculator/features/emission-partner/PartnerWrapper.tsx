/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { QueryWrapper } from '@components/query/QueryWrapper';
import { useGetEmissionPartnerQuery } from '../../api';
import { PartnerView } from './PartnerView';

interface PartnerWrapperParams {
  initiativeId: string;
  partnerCode: string;
}

export const PartnerWrapper = (props: PartnerWrapperParams) => {

  const { initiativeId, partnerCode } = props;
  const query = useGetEmissionPartnerQuery({ initiativeId, partnerCode, });

  return (
    <div>
      <QueryWrapper query={query} onSuccess={(data) => <PartnerView {...props} partner={data} />} />
    </div>
  )
}
