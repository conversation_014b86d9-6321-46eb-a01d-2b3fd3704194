/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { CalculatorInfo } from '../../types';

interface PartnerViewProps {
  partner: CalculatorInfo,
  initiativeId: string
}

export const PartnerView = ({ partner }: PartnerViewProps) => {

  const { integration } = partner;

  if (!integration) {
    return (
      <div>
        <h1>Not available</h1>
      </div>
    )
  }

  if (integration.type === 'iframe') {
    return (
      <div>
        <h4>{partner.name}</h4>
        <p>{partner.description}</p>
        <div style={{ minHeight: '500px' }}>
          <iframe width={'100%'} height={'800'} title={partner.name} src={integration.iframe.src} />
        </div>
      </div>
    )
  }

  return (
    <div>
      <h1>Partner {partner.name}</h1>
      <div style={{ minHeight: '500px' }}>
        Not supported
      </div>
    </div>

  )
}
