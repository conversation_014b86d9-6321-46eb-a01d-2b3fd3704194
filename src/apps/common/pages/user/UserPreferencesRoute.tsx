/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useHistory } from 'react-router-dom';
import { useAppSelector } from '@reducers/index';
import { Loader } from '@g17eco/atoms/loader';
import { MySettings } from '@features/user-preferences';
import { useGetUserPreferencesQuery } from '@api/users';
import { QueryWrapper } from '@components/query/QueryWrapper';

export const UserPreferencesRoute = () => {

  const history = useHistory();
  const userState = useAppSelector((state) => state.currentUser);
  const query = useGetUserPreferencesQuery(undefined, {
    skip: userState.errored || !userState.loaded,
    refetchOnMountOrArgChange: true,
  });

  if (userState.errored) {
    history.push('/');
    return '';
  }

  if (!userState.loaded) {
    return <Loader />;
  }

  return (
    <QueryWrapper
      query={query}
     onSuccess={(preferences) => <MySettings preferences={preferences} user={userState.data} />}
    />
  );
}
