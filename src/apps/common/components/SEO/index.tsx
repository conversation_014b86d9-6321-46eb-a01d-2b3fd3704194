/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Helmet } from 'react-helmet'
import { useHistory } from 'react-router-dom';

interface SEOProps {
  canonicalUrl?: string;
}

export const SEO = (props: SEOProps) => {

  const history = useHistory();

  // By default strip out any GET params from canonical
  // This might be too simplistic, but since most of the website is blocked, then
  // the open pages should all by clean paths
  const defaultCanonicalUrl = `${window.origin}${history.location.pathname}`;

  return (
    <Helmet>
      <link rel='canonical' href={props.canonicalUrl ?? defaultCanonicalUrl} />
    </Helmet>
  );
}
