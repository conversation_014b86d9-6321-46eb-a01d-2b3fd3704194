/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { RouteErrorBoundary } from '@features/error';
import { AppRouteInterface, RouteInterface, RoutesInterface } from '@g17eco/types/routes';
import { AppLayout } from '../../../layout/AppLayout';
import Header from '@components/header';
import UTrModal from '@components/utr-modal';
import { AppHeader } from '@components/header/AppHeader';
import AssuranceTrackerLogo from '../../../images/apps/Assurance_Tracker_logo.svg';
import AssuranceList from '@components/assurer/assurance-list';
import AssurerAssurancePortfolio, {
  assurancePortfolioRoute,
} from '@components/assurer/assurer-assurance-portfolio';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@selectors/user';
import NotAuthorised from '@routes/not-authorised';
import { UserManagementRoute } from './UserManagementRoute';
import { useGetUserOrganizationQuery } from '../api/assurance';

export const assuranceRouteEntry: RouteInterface = {
  id: 'assurance_root',
  label: 'Assurance Tracker',
  tooltip: 'Assurance Tracker',
  path: '/assurance',
  icon: 'fa-folder-open',
  component: Assurance,
  appPermissionId: 'app_assurance',
  exact: false,
  auth: true,
};

const otherRoutes: RoutesInterface = {
  ASSURANCE_PORTFOLIO: {
    ...assurancePortfolioRoute,
    component: AssurerAssurancePortfolio,
  },
  ASSURANCE_MANAGE_USERS: {
    id: 'assurance_manage_users',
    label: 'Manage Users',
    path: '/assurance/manage-users/:page(invite)?',
    component: UserManagementRoute,
    auth: true,
    // Should come up with a way to check assurance permission roles per route
  }
}

export const assuranceRoutes: RoutesInterface = {
  ASSURANCE: assuranceRouteEntry,
  ...otherRoutes
}

const assuranceAppRoute: AppRouteInterface = {
  appName: 'Assurance Tracker',
  appIcon: AssuranceTrackerLogo,
  routes: [
    assuranceRoutes.ASSURANCE_PORTFOLIO
  ],
}

export function Assurance() {

  const { data: organization } = useGetUserOrganizationQuery();
  const currentUser = useSelector(getCurrentUser);

  // This check will likely be redundant because now we will show the brochure if users have no access
  const hasOrganization = Boolean(currentUser?.organizationId) || organization;

  return (
    <RouteErrorBoundary>
      <AppLayout Header={
        <Header>
          <AppHeader
            appRoute={assuranceAppRoute}
            route={assuranceRouteEntry} />
        </Header>
      }>
        {hasOrganization ?
          (
            <Switch>
              {Object.entries(otherRoutes).map(([_key, route]) => {
                return <Route
                  key={`route_${route.id}`}
                  {...route}
                  exact={route.exact ?? false} />
              })}
              <Route>
                <AssuranceList />
              </Route>
            </Switch>
          ) : <NotAuthorised />
        }
        <UTrModal />
      </AppLayout>
    </RouteErrorBoundary>
  )
}

export default Assurance;
