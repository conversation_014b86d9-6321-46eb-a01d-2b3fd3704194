/*
 * Copyright (c) 2024. Word Wide Generation Ltd
 */

import { useParams } from 'react-router-dom';
import { UserManagement } from '../components/user-management';
import { ROUTES } from '@constants/routes';
import { ATBreadcrumbs } from '../components/breadcrumbs/ATBreadcrumbs';
import { useAppSelector } from '@reducers/index';
import { getCurrentUser } from '@selectors/user';

export const UserManagementRoute = () => {
  const { page } = useParams<{ page?: string }>();
  const currentUser = useAppSelector(getCurrentUser);

  return (
    <UserManagement
      currentUser={currentUser}
      baseRoute={ROUTES.ASSURANCE_MANAGE_USERS}
      page={page}
      canInviteMultipleUsers
      BreadcrumbsComponent={ATBreadcrumbs}
    />
  );
};
