import { ROUTES } from '@constants/routes';
import { AdminBreadcrumbsProps, Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';

export const ATBreadcrumbs = ({ breadcrumbs }: Omit<AdminBreadcrumbsProps, 'initiativeId'>) => {
  const rootUrl = generateUrl(ROUTES.ASSURANCE);
  return <Breadcrumbs breadcrumbs={breadcrumbs} rootLabel={'Assurance Tracker'} rootUrl={rootUrl} />;
};
