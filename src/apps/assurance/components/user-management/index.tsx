/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { AdminBreadcrumbsProps } from '@g17eco/molecules/breadcrumbs';
import { BasicAlert } from '@g17eco/molecules/alert';
import { RouteInterfaceMin } from '@g17eco/types/routes';
import { useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import UserTable, { TableUser } from './UserTable';
import {
  OrganizationUsers,
  useGetAssuranceOrganizationMembersQuery,
  useGetUserOrganizationQuery,
} from '../../api/assurance';
import { getFullName } from '@utils/user';
import { Loader } from '@g17eco/atoms/loader';
import { generateUrl } from '@routes/util';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { Button } from 'reactstrap';
import UserInvitationFormContainer from './UserInvitationFormContainer';
import UserInvitationForm from './UserInvitationForm';
import { SerializedError } from '@reduxjs/toolkit';
import { UserFilters } from './types';
import { UserFilter } from './UserFilter';
import { CurrentUserData } from '@reducers/current-user';
import { OrganizationUserRole } from '@constants/user';
import { MAX_USER_LIMIT } from './constants';
import { AssuranceAction, hasOrganizationPermission } from '@components/assurer/permissions-utils';
import NotAuthorised from '@routes/not-authorised';
import { OnboardingStatus } from '@g17eco/types/onboarding';

interface UsersStateInterface {
  data: OrganizationUsers | undefined;
  isLoading: boolean;
  error:
    | {
        message: string;
        name: string;
      }
    | SerializedError
    | undefined;
  orgPermissionMap: Map<string, OrganizationUserRole[]>;
  organizationId: string;
}

interface ConsolidatedUsers {
  users: TableUser[];
  staff: TableUser[];
}

const getConsolidateUsers = ({
  data,
  isLoading,
  error,
  orgPermissionMap,
  organizationId,
}: UsersStateInterface): ConsolidatedUsers => {
  const consolidatedUsers: ConsolidatedUsers = { users: [], staff: [] };
  const tableUsers: TableUser[] = [];

  if (isLoading || !!error || !data) {
    return consolidatedUsers;
  }

  data.users.forEach((user) => {
    tableUsers.push({
      _id: user._id,
      isStaff: user.isStaff ?? false,
      isActivated: true,
      name: getFullName(user, user._id),
      email: user.email,
      roles: orgPermissionMap.get(user._id) || [OrganizationUserRole.RestrictedUser],
      lastUpdated: user.lastLogin,
      lastLogin: user.lastLogin,
      isRequestToJoin: false,
      user,
      invitedDate: user.invitedDate,
    });
  });

  data.invitations.forEach((invitation) => {
    const isRequestToJoin = invitation.status === OnboardingStatus.NotStarted;
    const user = invitation.user;
    const emailPrefix = user.email.split('@')[0];
    const roles = invitation.organizationRoles.find((role) => role.modelId === organizationId);
    tableUsers.push({
      _id: invitation._id,
      isStaff: false,
      isActivated: false,
      name: isRequestToJoin ? getFullName(user, emailPrefix) : emailPrefix,
      email: user.email,
      roles: roles ? roles.permissions : [],
      lastUpdated: invitation.created,
      lastLogin: '',
      isRequestToJoin,
      invitation,
    });
  });

  return tableUsers.reduce((accumulator, user) => {
    if (user.isStaff) {
      accumulator.staff.push(user);
      return accumulator;
    }
    accumulator.users.push(user);
    return accumulator;
  }, consolidatedUsers);
};

interface UserManagementProps {
  currentUser: CurrentUserData | undefined;
  page?: string;
  baseRoute: RouteInterfaceMin;
  canInviteMultipleUsers?: boolean;
  BreadcrumbsComponent: (props: Omit<AdminBreadcrumbsProps, 'initiativeId'>) => JSX.Element;
}

export const UserManagement = (props: UserManagementProps) => {
  const { currentUser, page, baseRoute, canInviteMultipleUsers = false, BreadcrumbsComponent } = props;
  const history = useHistory();
  const [userFilters, setUserFilters] = useState<UserFilters>({
    searchText: '',
    showPending: false,
    showAdminOnly: false,
  });
  const { data: organization, isLoading: isLoadingOrg, error: orgQueryError } = useGetUserOrganizationQuery();
  const { _id: organizationId = '' } = organization || {};
  const {
    data,
    isLoading: isLoadingUsers,
    error: usersQueryError,
  } = useGetAssuranceOrganizationMembersQuery({ organizationId }, { skip: isLoadingOrg || !organizationId });

  const orgPermissionMap = useMemo(() => {
    if (!organization) {
      return new Map<string, OrganizationUserRole[]>();
    }
    return new Map<string, OrganizationUserRole[]>(organization.permissions?.map((p) => [p.userId, p.permissions]));
  }, [organization]);

  const listUsers = useMemo(() => {
    return getConsolidateUsers({
      data,
      isLoading: isLoadingUsers,
      error: usersQueryError,
      orgPermissionMap,
      organizationId,
    });
  }, [isLoadingUsers, data, usersQueryError, orgPermissionMap, organizationId]);

  if (isLoadingOrg || isLoadingUsers) {
    return <Loader />;
  }

  const limitReached = listUsers.users.length >= MAX_USER_LIMIT;
  const canManage =
    organization && currentUser
      ? hasOrganizationPermission({ user: currentUser, action: AssuranceAction.CanManage, organization })
      : false;

  const openUserInvitationForm = () => {
    const invitationUrl = generateUrl(baseRoute, { organizationId, page: 'invite' });
    history.push(invitationUrl);
  };

  const baseBreadcrumb = {
    label: 'Manage assurance team',
    url: generateUrl(baseRoute),
  };

  if (!canManage) {
    return <NotAuthorised />;
  }

  if (page === 'invite') {
    return (
      <Dashboard className='userManagement'>
        <DashboardRow>
          <BreadcrumbsComponent breadcrumbs={[baseBreadcrumb, { label: 'Invite user' }]} />
        </DashboardRow>
        <DashboardSectionTitle title={'Invite users'} />
        <DashboardSection className='userManagement-container'>
          <UserInvitationFormContainer>
            <UserInvitationForm baseRoute={baseRoute} organizationId={organizationId} />
          </UserInvitationFormContainer>
        </DashboardSection>
      </Dashboard>
    );
  }

  const renderManageUserButtons = () => {
    if (!canInviteMultipleUsers) {
      return [];
    }
    return [
      <Button
        key={'invite-btn'}
        color='secondary'
        className={'text-nowrap'}
        onClick={openUserInvitationForm}
        disabled={limitReached}
        data-testid='add-user-btn'
      >
        <i className={'fa fa-plus mr-2'} />
        <span>Invite users</span>
      </Button>,
    ];
  };

  return (
    <Dashboard className='userManagement'>
      {orgQueryError ? (
        <BasicAlert key={orgQueryError.name} type={'danger'}>
          {orgQueryError.message}
        </BasicAlert>
      ) : null}
      {usersQueryError ? (
        <BasicAlert key={usersQueryError.name} type={'danger'}>
          {usersQueryError.message}
        </BasicAlert>
      ) : null}
      <DashboardRow>
        <BreadcrumbsComponent breadcrumbs={[baseBreadcrumb]} />
      </DashboardRow>
      <DashboardSectionTitle title={'Manage assurance team'} buttons={renderManageUserButtons()} />
      <UserFilter userFilters={userFilters} setUserFilters={setUserFilters} />
      <DashboardSection title={'Assurance team'} className='userManagement-container' padding={2}>
        <div data-testid='manage-users-table'>
          <UserTable
            organizationId={organizationId}
            currentUser={currentUser}
            users={listUsers.users}
            filters={userFilters}
          />
        </div>
      </DashboardSection>
      <DashboardSection title={'Support users (G17Eco staff)'} className='userManagement-container' padding={2}>
        <div data-testid='support-users-table'>
          <UserTable
            organizationId={organizationId}
            currentUser={currentUser}
            users={listUsers.staff}
            filters={userFilters}
          />
        </div>
      </DashboardSection>
    </Dashboard>
  );
};
