/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "../../../../css/variables";

.userManagement {
  .userManagement-container {
    .settings-btn {
      border-radius: 0px;
      padding: 1px;
      min-width: 30px;
      &:hover,
      &.active {
        text-decoration: none;
        background-color: var(--theme-AccentMedium);
        i {
          color: white !important;
        }
      }
    }
    .user-table-container {
      .table-component-container {
        overflow: visible;
        .emailCol {
          position: relative;
          top: 0px;
          height: 35px;
          width: 260px;
          max-width: 260px;
          .hover-expand {
            top: 0px;
            height: 35px;
            line-height: 20px;
            padding: 8px 11px;
            margin-left: -11px;
            z-index: 2;
          }
        }
      }
    }

    .user-invitation-form-container {
      .form-check {
        padding-left: 2rem;
        padding-right: 2rem;
        label {
          input {
            margin-top: 0.2rem;
          }
          &:hover {
            cursor: pointer;
          }
        }

        &.disabled {
          color: var(--theme-TextMedium);
          label {
            cursor:not-allowed;
            div, input {
              color: var(--theme-TextMedium);
              cursor:not-allowed;
            }
          }
        }
      }
      .email-chip {
        background-color: var(--theme-AccentExtradark);
        color: var(--theme-TextWhite);
        font-size: 16px;
        border-radius: 4px;
        height: 30px;
        padding: 0 4px 0 1rem;
        display: inline-flex;
        align-items: center;
        margin: 0 0.3rem 0.3rem 0;
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        -ms-border-radius: 4px;
        -o-border-radius: 4px;
        & > .chip-delete-button {
          background-color: transparent;
          color: var(--theme-TextWhite);
          width: 22px;
          height: 22px;
          border: none;
          cursor: pointer;
          font: inherit;
          margin-left: 10px;
          font-weight: bold;
          padding: 0;
          line-height: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          &:focus {
            outline: none;
          }
        }
      }
    }
    .btn-outline-secondary {
      &.disabled {
        background-color: var(--theme-BgDisabled) !important;
        border-color: var(--theme-BgDisabled) !important;
        color: var(--theme-HeadingLight) !important;
      }
    }
  }
}

.request-to-join-modal {
  .message-container {
    border: 1px solid var(--theme-NeutralsLight);
    border-radius: $borderRadius;
    padding: 1rem;
  }
}
