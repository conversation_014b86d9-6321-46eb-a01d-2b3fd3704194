/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useMemo } from 'react';
import { DATE, formatDateUTC } from '@utils/date';
import { assuranceTrackerUserRoles, OrganizationUserRole } from '@constants/user';
import { escapeRegexCharacters } from '@utils/string-format';
import { ManagementOrganizationInvitation, ManagementUser } from '@constants/users';
import './styles.scss';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { CurrentUserData } from '@reducers/current-user';
import { UserRolesDropdown } from './partials/UserRolesDropdown';
import { Button } from 'reactstrap';
import { PAGE_SIZE } from './constants';
import { OnboardingStatus } from '@g17eco/types/onboarding';

export interface TableUser {
  _id: string;
  name?: string;
  email?: string;
  roles: OrganizationUserRole[];
  lastUpdated?: string;
  lastLogin?: string;
  isActivated: boolean;
  isRequestToJoin: boolean;
  isStaff: boolean;
  user?: ManagementUser;
  invitation?: ManagementOrganizationInvitation;
  invitedDate?: string;
}

interface UserTableProps {
  organizationId: string;
  currentUser: CurrentUserData | undefined;
  users: TableUser[];
  isAddUserDisabled?: boolean;
  filters: {
    searchText: string;
    showPending: boolean;
    showAdminOnly: boolean;
  };
}

const getStatus = (user: TableUser): string => {
  if (user.isActivated) {
    return 'Active';
  }
  if (user.isRequestToJoin) {
    return 'Not Started';
  }
  return 'Pending';
};

const UserTable = (props: UserTableProps) => {
  const {
    organizationId,
    currentUser,
    users,
    filters: { searchText, showPending, showAdminOnly },
  } = props;

  const isCurrentUser = (user: TableUser) => user._id === currentUser?._id;

  const filteredUsers = useMemo(() => {
    const searchTextRegex = searchText
      ? new RegExp(escapeRegexCharacters(searchText.trim().toLowerCase()).replace(/ /g, '|'), 'g')
      : undefined;
    return users.filter((user) => {
      if (showPending) {
        return user.isActivated || user.isRequestToJoin || user.invitation?.status === OnboardingStatus.Pending;
      }
      if (showAdminOnly) {
        return user.roles.includes(OrganizationUserRole.Admin);
      }
      if (searchTextRegex) {
        return [user.name, user.email].some(
          (text) => text && (text.toLowerCase().match(searchTextRegex) || []).length > 0
        );
      }
      return user.isActivated;
    });
  }, [searchText, showAdminOnly, showPending, users]);

  const tableColumns: ColumnDef<TableUser>[] = [
    {
      accessorKey: 'name',
      header: 'User',
      meta: {
        cellProps: {
          className: 'dont_translate',
        },
      },
      cell: (c) => {
        const user = c.row.original;
        return <div className={`${isCurrentUser(user) ? 'text-ThemeBgDisabled' : ''}`}>{user.name}</div>;
      },
    },
    {
      id: 'email',
      accessorKey: 'email',
      header: 'Email address',
      meta: {
        cellProps: {
          className: 'emailCol dont_translate',
        },
      },
      cell: (c) => {
        const user = c.row.original;
        return (
          <SimpleTooltip text={user.email}>
            <div
              className={`${
                isCurrentUser(user) ? 'text-ThemeBgDisabled' : ''
              } hover-expand text-truncate dont_translate`}
            >
              {user.email}
            </div>
          </SimpleTooltip>
        );
      },
    },
    {
      id: 'roles',
      accessorKey: 'roles',
      header: 'Permission',
      cell: (c) => {
        if (!currentUser) {
          return;
        }
        const user = c.row.original;
        return (
          <UserRolesDropdown
            organizationId={organizationId}
            currentUser={currentUser}
            user={user}
            roles={assuranceTrackerUserRoles}
          />
        );
      },
    },
    {
      id: 'lastLogin',
      accessorFn: (originalRow) => (originalRow.lastLogin ? formatDateUTC(originalRow.lastLogin, DATE.SORTABLE) : null),
      header: 'Last login',
      cell: (c) => {
        const user = c.row.original;
        return (
          <span className={isCurrentUser(user) ? 'text-ThemeBgDisabled' : ''}>
            {user.isRequestToJoin ? undefined : user.lastLogin ? formatDateUTC(user.lastLogin, DATE.HUMANIZE) : 'never'}
          </span>
        );
      },
    },
    {
      id: 'status',
      accessorFn: (originalRow) => getStatus(originalRow),
      header: 'Status',
      cell: (c) => {
        const user = c.row.original;
        if (user.isRequestToJoin) {
          return (
            <SimpleTooltip text={'Coming soon'}>
              <Button className='text-nowrap p-0' color='link' size='sm' disabled>
                View request
              </Button>
            </SimpleTooltip>
          );
        }

        return <div className={`${isCurrentUser(user) ? 'text-ThemeBgDisabled' : ''}`}>{getStatus(user)}</div>;
      },
    },
    {
      id: 'actions',
      meta: {
        cellProps: {
          className: 'text-center',
        },
      },
      enableSorting: false,
      cell: () => (
        <SimpleTooltip text={'Coming soon'}>
          <i className='fal fa-trash text-danger' onClick={() => {}} />
        </SimpleTooltip>
      ),
    },
  ];

  const noResults = filteredUsers.length === 0;
  let noUsersMessage = '';
  if (noResults) {
    noUsersMessage =
      showPending || showAdminOnly || searchText
        ? 'No users match your criteria'
        : 'You have no users assigned to this assurance team';
  }

  return (
    <div className='user-table-container'>
      <Table columns={tableColumns} data={filteredUsers} pageSize={PAGE_SIZE} />
      {noResults ? (
        <BasicAlert type='secondary' className='mt-2'>
          {noUsersMessage}
        </BasicAlert>
      ) : null}
    </div>
  );
};

export default UserTable;
