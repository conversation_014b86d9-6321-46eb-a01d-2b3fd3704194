/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DashboardRow } from '@components/dashboard';
import { FormGroup, Input } from 'reactstrap';
import { UserFilters } from './types';

interface UserFilterProps {
  userFilters: UserFilters;
  setUserFilters: React.Dispatch<React.SetStateAction<UserFilters>>;
}

export const UserFilter = ({ userFilters, setUserFilters }: UserFilterProps) => {
  const changeSearchText = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserFilters({ ...userFilters, searchText: e.target.value });
  };

  const toggleFilters = (key: keyof Pick<UserFilters, 'showPending' | 'showAdminOnly'>) => () => {
    setUserFilters((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <DashboardRow>
      <div className='w-100 d-flex align-items-center gap-4'>
        <div className='flex-grow-1'>
          <Input placeholder={'Search for user...'} onChange={changeSearchText} value={userFilters.searchText} />
        </div>
        <FormGroup className='d-flex align-items-center gap-2 p-0 m-0' switch>
          <Input
            type='switch'
            className='m-0'
            name={'showPending'}
            checked={userFilters.showPending}
            id='showPendingSwitch'
            onChange={toggleFilters('showPending')}
          />
          <label htmlFor={'showPendingSwitch'}>Show pending users</label>
        </FormGroup>
        <FormGroup className='d-flex align-items-center gap-2 p-0 m-0' switch>
          <Input
            type='switch'
            className='m-0'
            name={'showAdminOnly'}
            checked={userFilters.showAdminOnly}
            id='showAdminSwitch'
            onChange={toggleFilters('showAdminOnly')}
          />
          <label htmlFor={'showAdminSwitch'}>Show organisation admins only</label>
        </FormGroup>
      </div>
    </DashboardRow>
  );
};
