/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { ReactNode, useContext, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { Button, FormGroup, Input, Label } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { assuranceTrackerUserRoles, OrganizationUserRole } from '@constants/user';
import { isValidEmail } from '@utils/index';
import { generateUrl } from '@routes/util';
import './styles.scss';
import { EmailChipsInput } from './partials/EmailChipsInput';
import { ACTION, UserInvitationFormContext } from './UserInvitationFormContainer';
import { SiteAlertColors } from '@g17eco/slices/siteAlertsSlice';
import { RouteInterfaceMin } from '@g17eco/types/routes';
import { MAX_USER_LIMIT } from './constants';
import {
  useCreateOrganizationUserOnboardingsMutation,
  useGetAssuranceOrganizationMembersQuery,
} from '../../api/assurance';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { BasicAlert } from '@g17eco/molecules/alert';

interface UserInvitationProps {
  organizationId: string;
  baseRoute: RouteInterfaceMin;
}

const UserInvitationForm = ({ organizationId, baseRoute }: UserInvitationProps) => {
  const { addSiteAlert } = useSiteAlert();
  const history = useHistory();
  const analytics = getAnalytics();
  const { emailInput, emails, errorMessage, dispatch } = useContext(UserInvitationFormContext);
  const [selectedRoles, setRoles] = React.useState<OrganizationUserRole[]>([OrganizationUserRole.Admin]);
  const {
    data,
    isFetching,
    error,
  } = useGetAssuranceOrganizationMembersQuery({ organizationId }, { skip: !organizationId });

  const [createMultipleOnboarding, { isLoading: isCreating, error: createError }] =
    useCreateOrganizationUserOnboardingsMutation();

  const addingEmailCount = emails.length + (isValidEmail(emailInput) ? 1 : 0);
  const hasValidEmails = emailInput ? isValidEmail(emailInput) : emails.length > 0;
  const canSubmit = !isCreating && hasValidEmails && selectedRoles.length > 0;

  const nonStaffUserCount = useMemo(() => {
    if (isFetching || !!error || !data) {
      return;
    }
    return data.users.reduce((acc, user) => acc + (user.isStaff ? 0 : 1), 0);
  }, [error, isFetching, data]);

  const isLimitReached = () => {
    if (nonStaffUserCount === undefined) {
      return true;
    }
    if (addingEmailCount) {
      return nonStaffUserCount + addingEmailCount > MAX_USER_LIMIT;
    }
    return nonStaffUserCount >= MAX_USER_LIMIT;
  };

  if (isFetching) {
    return <Loader />;
  }

  const backToUserManagement = () => {
    history.push(generateUrl(baseRoute, { organizationId }));
  };

  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const role = e.target.value as OrganizationUserRole;
    setRoles([role]);
  };

  const onChangeEmailInput = (e: React.ChangeEvent<any>) => {
    dispatch({
      type: ACTION.SET_EMAIL_INPUT,
      payload: e.target.value,
    });
  };

  const handleDelete = (item: string) => {
    dispatch({
      type: ACTION.DELETE_EMAIL,
      payload: emails.find((email) => email === item),
    });
  };

  const handleSubmit = async () => {
    if (isLimitReached()) {
      return addSiteAlert({
        content:
          'Cannot add more users as you have reached the limit on your account. Please contact support to increase your limit',
        color: SiteAlertColors.Danger,
      });
    }

    if (!hasValidEmails) {
      dispatch({
        type: ACTION.SET_ERROR_MESSAGE,
        payload: 'The e-mails you typed is invalid. Please check and try again.',
      });
      return;
    }
    dispatch({
      type: ACTION.SET_ERROR_MESSAGE,
      payload: '',
    });

    const inviteEmails = Array.from(new Set([...emails, emailInput])).filter((email) => email);

    try {
      const onboardings = await createMultipleOnboarding({
        organizationId,
        emails: inviteEmails,
        permissions: selectedRoles,
      });
      if (Array.isArray(onboardings)) {
        await Promise.all(
          onboardings
            .filter((onboarding) => onboarding._id)
            .map((onboarding) => {
              return analytics.track(AnalyticsEvents.UserInvited, {
                onboardId: onboarding._id,
                organizationId,
              });
            })
        );
      }
      backToUserManagement();
    } catch (error) {
      addSiteAlert({
        content: `It was not possible to send the invitation. ${error.message ? `(${error.message})` : ''}`,
        color: SiteAlertColors.Danger,
      });
    }
  };

  const renderRolesSelectors = () => {
    return (
      <>
        <hr className='mx-2 my-4 opacity-100 border-ThemeBorderDefault' />
        <div className='mt-4'>
          <h4 className='px-2'>User permissions</h4>
          {assuranceTrackerUserRoles.reduce((acc, role) => {
            if (role.disabled) {
              return acc;
            }
            const id = `role_${role.code}`;
            acc.push(
              <FormGroup check key={id} className='py-2'>
                <Input
                  type='radio'
                  id={id}
                  name='user_roles'
                  value={role.code}
                  onChange={handleRoleChange}
                  checked={selectedRoles.includes(role.code)}
                />
                <Label check for={id}>
                  <span className='strong'>{role.label}</span>
                  <div className='text-ThemeTextMedium'>{role.description}</div>
                </Label>
              </FormGroup>
            );
            return acc;
          }, [] as ReactNode[])}
        </div>
      </>
    );
  };

  return (
    <>
      {errorMessage && <div className='alert alert-danger'>{errorMessage}</div>}
      {isLimitReached() ? (
        <BasicAlert type='warning'>
          You have reached your user limit of {MAX_USER_LIMIT}. Please contact us to increase your limit.
        </BasicAlert>
      ) : null}
      {createError ? <BasicAlert type='danger'>{createError.message}</BasicAlert> : null}
      <div className='user-invitation-form-container'>
        <h4>Email address</h4>
        <FormGroup>
          <EmailChipsInput
            emailInput={emailInput}
            emails={emails}
            placeholder='Type user e-mail address and press Enter or Tab'
            onChangeEmailInput={onChangeEmailInput}
            handleDelete={handleDelete}
          />
        </FormGroup>
        {renderRolesSelectors()}
        <div className='mt-3 text-right'>
          <Button className='mr-3' disabled={isCreating} color='link-secondary' onClick={backToUserManagement}>
            Cancel
          </Button>
          <Button
            color='primary'
            disabled={!canSubmit || isCreating}
            onClick={handleSubmit}
            data-testid='invite-users-btn'
          >
            Invite user
          </Button>
        </div>
      </div>
    </>
  );
};

export default UserInvitationForm;
