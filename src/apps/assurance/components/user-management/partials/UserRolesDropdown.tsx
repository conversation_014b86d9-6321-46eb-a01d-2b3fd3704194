/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useState } from 'react';
import { DropdownItem, DropdownMenu, DropdownToggle, FormGroup, Input, Label, UncontrolledDropdown } from 'reactstrap';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { assuranceTrackerUserRoles, OrganizationUserRole, RoleInformation } from '@constants/user';
import { UserMin } from '@constants/users';
import { TableUser } from '../UserTable';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { useUpdateUserOrganizationPermissionsMutation } from '../../../api/assurance';
import { truncate } from '@utils/string';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

const getOrgPermissionChecked = (permission: OrganizationUserRole, selectedPermissions: OrganizationUserRole[]) => {
  return selectedPermissions.includes(permission);
};

export const getRoleName = (role: OrganizationUserRole, maxLength?: number) => {
  const userRole = assuranceTrackerUserRoles.find((r) => r.code === role);
  const name = userRole?.label ?? role;
  if (maxLength) {
    return truncate(name, maxLength);
  }
  return name;
};

const getLabel = (roles: TableUser['roles']): string | JSX.Element => {
  if (roles.length === 0) {
    // Legacy users will be treated as restricted user
    return 'Restricted user';
  }
  return getRoleName(roles[0]);
};

const getDisabledMessage = (user: TableUser) => {
  if (!user.isActivated) {
    return 'It is not possible to change roles for open invitations';
  }
};

export const UserRolesDropdown = ({
  organizationId,
  currentUser,
  user,
  roles,
}: {
  organizationId: string;
  currentUser: Pick<UserMin, '_id'>;
  user: TableUser;
  roles: RoleInformation<OrganizationUserRole>[];
}) => {
  const { addSiteError } = useSiteAlert();
  const [isOpenPermission, setIsOpenPermission] = useState(false);
  const isCurrentUser = user._id === currentUser._id;
  const disabledMessage = getDisabledMessage(user);

  const [updateUserPermission, { isLoading }] = useUpdateUserOrganizationPermissionsMutation();

  const isDisabled = Boolean(disabledMessage || isCurrentUser);

  const handleToggle = async (role: RoleInformation<OrganizationUserRole>) => {
    if (isDisabled || user.roles.includes(role.code)) {
      return;
    }
    return updateUserPermission({ organizationId, userId: user._id, permissions: [role.code] })
      .unwrap()
      .catch((e) => {
        addSiteError(e);
      });
  };

  const renderDropdownItem = (role: RoleInformation<OrganizationUserRole>) => {
    return (
      <DropdownItem key={`roles-${user._id}-${role.code}`} disabled={isDisabled}>
        <FormGroup disabled={isDisabled} onClick={(e) => e.stopPropagation()} check>
          <Input
            disabled={isDisabled}
            type={'radio'}
            id={`check-${user._id}-${role.code}`}
            checked={getOrgPermissionChecked(role.code, user.roles)}
            onChange={() => handleToggle(role)}
          />
          <Label check for={`check-${user._id}-${role.code}`}>
            {role.label}
          </Label>
        </FormGroup>
      </DropdownItem>
    );
  };

  return (
    <>
      {isLoading ? <BlockingLoader /> : null}
      <UncontrolledDropdown
        disabled={isDisabled}
        className='user-roles-dropdown'
        onToggle={(e, isOpen) => setIsOpenPermission(isOpen)}
      >
        <SimpleTooltip text={isOpenPermission ? '' : disabledMessage}>
          <DropdownToggle color='transparent' disabled={isDisabled} className={'text-left text-truncate'}>
            {getLabel(user.roles)}
          </DropdownToggle>
        </SimpleTooltip>
        <DropdownMenu>{roles.filter((role) => !role.disabled).map((role) => renderDropdownItem(role))}</DropdownMenu>
      </UncontrolledDropdown>
    </>
  );
};
