/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useMemo, useState } from 'react';
import { SurveySettings } from '@g17eco/types/survey';
import { DisableUtrsProps, generateDisabledUtrs } from '@components/survey/utils/getDisableUtrs';
import { useAppSelector } from '../../../reducers';
import { getCurrentUser } from '../../../selectors/user';
import { SurveyActionData } from '../../../model/surveyData';
import { generateSurveyQuestionsFlexSearchMap } from '../../../selectors/blueprint';
import { useGetBlueprintsQuery } from '@api/blueprints';

type Props = Pick<DisableUtrsProps, 'surveyGroups'>
  & { survey: Pick<SurveyActionData, 'sourceName' | 'questionGroups'> | undefined };

export const useFilterQuestions = ({ surveyGroups, survey }: Props) => {
  const user = useAppSelector(getCurrentUser);
  const { data: blueprint } = useGetBlueprintsQuery(survey?.sourceName);

  const [filters, setFilters] = useState({
    searchText: '',
  });

  const changeFilter = (key: keyof SurveySettings, setting: any) => {
    setFilters((filters) => {
      return { ...filters, [key]: setting };
    });
  };

  const disabledUTRs = useMemo(() => {
    return generateDisabledUtrs({
      surveyGroups,
      blueprint,
      searchText: filters.searchText,
      searchIndex: generateSurveyQuestionsFlexSearchMap(survey),
      user,
    });
  }, [blueprint, filters.searchText, survey, surveyGroups, user]);

  return { filters, changeFilter, disabledUTRs };
};
