<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.1.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 160 160" style="enable-background:new 0 0 160 160;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#C22033;}
</style>
<g>
	<g>
		<rect x="1" y="1" class="st0" width="158" height="158.01"/>
		<path class="st1" d="M158,2v156.01H2V2H158 M160,0H0v160.01h160V0L160,0z"/>
	</g>
	<path class="st1" d="M25.07,45.67h5.14v-5.23h2.19V36.2h-2.19V13.41h-5.33l-9.37,23.7v3.33h9.56V45.67z M20.26,36.2l4.85-13.28
		h0.05V36.2H20.26z"/>
	<g>
		<polygon class="st1" points="70.13,85.79 41.44,69.22 41.44,122.83 70.13,131.48 		"/>
		<polygon class="st1" points="73.29,85.9 73.29,131.5 102.17,122.82 102.17,69.23 		"/>
		<path class="st1" d="M124.7,67.87c0-1.88-1.52-3.4-3.39-3.4c-1.88,0-3.4,1.52-3.4,3.4v3.66h6.79V67.87z"/>
		<polygon class="st1" points="118.75,121.87 117.91,121.87 121.31,133.74 124.7,121.87 123.85,121.87 124.7,121.87 124.7,74.18 
			123.85,74.18 123.01,74.18 122.12,74.18 120.43,74.18 119.59,74.18 118.75,74.18 117.91,74.18 117.91,121.87 		"/>
		<polygon class="st1" points="108.33,71.53 105.26,69.1 105.26,124.28 73.37,134.58 71.9,134.58 71.61,134.58 70.13,134.58 
			38.39,125.71 38.39,69.1 35.31,71.53 35.31,127.87 69.99,137.47 71.61,137.47 71.9,137.47 73.52,137.47 105.51,127.31 
			108.33,126.48 		"/>
	</g>
	<g>
		<path class="st1" d="M45.69,32.57h5.25v1.79h-3.05v3.74h2.18v1.77h-2.18v3.97h3.05v1.79h-5.25V32.57z"/>
		<path class="st1" d="M58.85,36.08v6.05c0,2.01-0.83,3.51-3.18,3.51h-3.39V32.57h3.39C58.02,32.57,58.85,34.06,58.85,36.08z
			 M55.4,43.85c0.91,0,1.25-0.54,1.25-1.33v-6.85c0-0.77-0.35-1.31-1.25-1.31h-0.93v9.49H55.4z"/>
		<path class="st1" d="M66.68,32.57v9.89c0,2.02-0.87,3.34-3.1,3.34c-2.31,0-3.28-1.31-3.28-3.34v-9.89h2.2v10.1
			c0,0.77,0.31,1.29,1.08,1.29c0.77,0,1.08-0.52,1.08-1.29v-10.1H66.68z"/>
		<path class="st1" d="M68.13,42.27v-6.32c0-2.04,0.96-3.53,3.28-3.53c2.43,0,3.1,1.35,3.1,3.28v1.54h-2.01v-1.72
			c0-0.81-0.27-1.27-1.06-1.27c-0.79,0-1.12,0.56-1.12,1.35v7.02c0,0.79,0.33,1.35,1.12,1.35c0.79,0,1.06-0.5,1.06-1.27v-2.41h2.01
			v2.26c0,1.87-0.77,3.26-3.1,3.26C69.09,45.8,68.13,44.3,68.13,42.27z"/>
		<path class="st1" d="M75.21,45.65l2.47-13.07h2.51l2.49,13.07h-2.26l-0.4-2.39h-2.33l-0.39,2.39H75.21z M77.96,41.5h1.75
			l-0.87-5.24h-0.02L77.96,41.5z"/>
		<path class="st1" d="M82.38,32.57h5.84v1.79h-1.81v11.28h-2.22V34.37h-1.81V32.57z"/>
		<path class="st1" d="M89.44,32.57h2.2v13.07h-2.2V32.57z"/>
		<path class="st1" d="M93.11,42.27v-6.32c0-2.04,1-3.53,3.32-3.53c2.33,0,3.34,1.48,3.34,3.53v6.32c0,2.02-1,3.53-3.34,3.53
			C94.11,45.8,93.11,44.3,93.11,42.27z M97.56,42.62V35.6c0-0.79-0.35-1.35-1.14-1.35c-0.77,0-1.12,0.56-1.12,1.35v7.02
			c0,0.79,0.35,1.35,1.12,1.35C97.21,43.97,97.56,43.41,97.56,42.62z"/>
		<path class="st1" d="M103.06,37.43v8.21h-1.85V32.57h2.16l2.37,7.5v-7.5h1.83v13.07h-1.93L103.06,37.43z"/>
	</g>
	<g>
		<path class="st1" d="M48.2,27.35v-0.89c-1.75-0.31-2.51-1.6-2.51-3.37v-6.32c0-2.04,1-3.53,3.32-3.53c2.33,0,3.34,1.48,3.34,3.53
			v6.32c0,1.43-0.5,2.55-1.62,3.09l1.62,0.31v1.64L48.2,27.35z M50.15,23.55v-7.13c0-0.79-0.35-1.35-1.14-1.35
			c-0.77,0-1.12,0.56-1.12,1.35v7.13c0,0.77,0.35,1.35,1.12,1.35C49.8,24.9,50.15,24.33,50.15,23.55z"/>
		<path class="st1" d="M60.16,13.39v9.89c0,2.02-0.87,3.34-3.1,3.34c-2.31,0-3.28-1.31-3.28-3.34v-9.89h2.2v10.1
			c0,0.77,0.31,1.29,1.08,1.29c0.77,0,1.08-0.52,1.08-1.29v-10.1H60.16z"/>
		<path class="st1" d="M60.99,26.47l2.47-13.07h2.51l2.49,13.07H66.2l-0.4-2.39h-2.33l-0.39,2.39H60.99z M63.75,22.32h1.75
			l-0.87-5.24h-0.02L63.75,22.32z"/>
		<path class="st1" d="M69.52,13.39h2.2v11.28h2.62v1.79h-4.82V13.39z"/>
		<path class="st1" d="M75.36,13.39h2.2v13.07h-2.2V13.39z"/>
		<path class="st1" d="M78.58,13.39h5.84v1.79h-1.81v11.28H80.4V15.19h-1.81V13.39z"/>
		<path class="st1" d="M89.77,21.49v4.97h-2.2v-4.97l-2.45-8.1h2.29l1.33,4.96h0.02l1.33-4.96h2.1L89.77,21.49z"/>
	</g>
</g>
</svg>
