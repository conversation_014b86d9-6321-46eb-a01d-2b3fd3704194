/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */



import { UserEvent } from '@vitest/browser/context';


/**
 * Copied from playground
 * https://github.com/facebook/lexical/blob/main/packages/lexical-playground/__tests__/keyboardShortcuts/index.mjs
 */

export async function sleep(delay: number) {
  await new Promise((resolve) => setTimeout(resolve, delay));
}

export async function moveToLineBeginning(userEvent: UserEvent) {
  await userEvent.keyboard('{ControlOrMeta>}');
  await userEvent.keyboard('{ArrowLeft}');
  await userEvent.keyboard('{/ControlOrMeta}');
}

export async function selectAll(userEvent: UserEvent) {
  return userEvent.keyboard('{ControlOrMeta>}a{/ControlOrMeta}')
}

export async function moveLeft(userEvent: UserEvent, numCharacters = 1, delayMs?: number) {
  for (let i = 0; i < numCharacters; i++) {
    if (delayMs !== undefined) {
      await sleep(delayMs);
    }
    await userEvent.keyboard('{ArrowLeft}')
  }
}

export async function moveRight(userEvent: UserEvent, numCharacters = 1, delayMs?: number) {
  for (let i = 0; i < numCharacters; i++) {
    if (delayMs !== undefined) {
      await sleep(delayMs);
    }
    await userEvent.keyboard('{ArrowRight}')
  }
}

type Direction = 'left' | 'right';

export async function selectCharacters(userEvent: UserEvent, direction: Direction, numCharacters = 1) {
  const moveFunction = direction === 'left' ? moveLeft : moveRight;
  await userEvent.keyboard('{Shift>}')
  await moveFunction(userEvent, numCharacters);
  await userEvent.keyboard('{/Shift}')
}
