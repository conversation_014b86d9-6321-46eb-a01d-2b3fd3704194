/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { SurveyInitiative, SurveyType } from '@g17eco/types/survey';
import { faker } from '@faker-js/faker';
import { createInitiative } from '@fixtures/initiative-factory';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { Blueprints } from '@components/survey-configuration/types';

export const createSurveyInitiative = (surveyInitiative: Partial<SurveyInitiative>): SurveyInitiative => {

  const initiativeId = faker.database.mongodbObjectId();
  const initiative = createInitiative({ _id: initiativeId })
  return {
    _id: faker.database.mongodbObjectId(),
    initiativeId: initiative._id,
    initiative,
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    name: faker.company.name(),
    completedDate: undefined,
    aggregatedDate: undefined,
    period: DataPeriods.Yearly,
    type: SurveyType.Default,
    sourceName: Blueprints.Blueprint2022,
    unitConfig: undefined,
    ...surveyInitiative,
  }
}
