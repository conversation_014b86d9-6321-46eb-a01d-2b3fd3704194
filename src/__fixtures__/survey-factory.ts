/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { SurveyListItem, SurveyType } from '@g17eco/types/survey';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { faker } from '@faker-js/faker';
import { SurveyActionData } from '@models/surveyData';
import { UtrvType } from '@constants/status';
import { getDefaultScope } from '@utils/survey-scope';
import { Blueprints } from '@components/survey-configuration/types';
import { initiativeOne } from '@fixtures/initiative-factory';
import { CustomReportSubsidiarySurvey } from '@g17eco/types/custom-report';
import { SurveyConfig } from '@g17eco/slices/surveyConfig';
import { InitiativePlain } from '@g17eco/types/initiative';
import { getDefaultUnitConfig } from '@features/units-currency-settings';

export const createSurveyListItem = (survey: Partial<SurveyListItem> = {}): SurveyListItem => {

  return {
    _id: faker.database.mongodbObjectId(),
    completedDate: undefined,
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    name: undefined,
    period: DataPeriods.Yearly,
    scope: undefined,
    type: SurveyType.Default,
    ...survey,
  }
}


export const createCustomReportSubsidiarySurvey = (survey: Partial<CustomReportSubsidiarySurvey> = {}): CustomReportSubsidiarySurvey => {

  return {
    _id: faker.database.mongodbObjectId(),
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    initiativeId: initiativeOne._id,
    period: DataPeriods.Yearly,
    scope: undefined,
    type: SurveyType.Default,
    ...survey,
  }
}

export const createSurveyData = (survey: Partial<SurveyActionData> = {}): SurveyActionData => {
  return {
    _id: survey._id ?? faker.database.mongodbObjectId(),
    code: faker.string.alphanumeric(6),
    contributions: {},
    created: faker.date.past({ years: 2 }).toISOString(),
    customMetricGroups: [],
    evidenceRequired: false,
    fragmentUniversalTrackerValues: [],
    ignoredDate: '',
    initiativeId: '',
    initiatives: [],
    isPrivate: false,
    questionGroups: [],
    sourceName: Blueprints.Blueprint2022,
    stakeholders: {
      stakeholder: [],
      verifier: [],
      escalation: [],
    },
    utrvType: UtrvType.Actual,
    verificationRequired: false,
    completedDate: undefined,
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    name: undefined,
    period: DataPeriods.Yearly,
    scope: {
      ...getDefaultScope(),
      standards: ['gri']
    },
    type: SurveyType.Default,
    ...survey,
  }
}


export const createSurveyConfig = (survey: Partial<SurveyConfig> = {}): SurveyConfig => {
  return {
    _id: faker.database.mongodbObjectId(),
    name: faker.lorem.words(2),
    evidenceRequired: false,
    isPrivate: false,
    sourceName: Blueprints.Blueprint2022,
    verificationRequired: false,
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    initiative: initiativeOne,
    ...survey,
  } satisfies SurveyConfig
}


export const getDefaultSurveyConfig = (config: Partial<SurveyConfig> = {}): SurveyConfig => {
  return {
    _id: surveyOne._id,
    effectiveDate: faker.date.past({ years: 1 }).toISOString(),
    initiative: initiativeOne,
    noteInstructions: undefined,
    noteInstructionsEditorState: undefined,
    verificationRequired: true,
    evidenceRequired: true,
    noteRequired: false,
    isPrivate: false,
    unitConfig: getDefaultUnitConfig({}),
    ...config,
  };
};

export const surveyListOne = createSurveyListItem({
  name: 'Survey One',
  _id: '66c3fd327490ac186f5bd8a9'
});

export const surveyOne = createSurveyData({
  _id: surveyListOne._id,
  name: surveyListOne.name,
  initiativeId: initiativeOne._id,
});
