/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { UtrvHistory } from '@g17eco/types/universalTrackerValue';
import { getUrl } from '../msw-fixtures';
import { SurveyActionData } from '@models/surveyData';

export const getUpServer = (surveyActionData: SurveyActionData) => {

  const surveyId = surveyActionData._id
  const initiativeId = surveyActionData.initiativeId;

  return setupServer(
    http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
      success: true,
      data: surveyActionData
    })),
    http.get(getUrl('o/carbon-calculators'), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl(`blueprints/${surveyActionData.sourceName}`), async () => HttpResponse.json({
      success: true,
      data: {}
    })),
    http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl(`initiatives/${initiativeId}/metric-groups/tag`), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl(`bookmarks/universal-tracker-value/survey/${surveyId}`), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl(`assurances/portfolio/survey/${surveyId}`), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl('o/un/sdg'), async () => HttpResponse.json({
      success: true,
      data: { goals: [] }
    })),
    http.post('https://api-eu-central-1.graphcms.com/v2/:id/master', async () => HttpResponse.json({
      data: { glossaries: [] }
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/comments'), async () => HttpResponse.json({
      success: true,
      data: {
        items: [],
        users: []
      }
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/history'), async () => HttpResponse.json({
      success: true,
      data: {
        latestHistory: {
          documents: [],
          stakeholderHistory: undefined,
          verifierHistory: undefined
        }
      } satisfies UtrvHistory
    })),
    http.get(getUrl(`initiatives/${initiativeId}/documents`), async () => HttpResponse.json({
      success: true,
      data: {
        documents: [],
        nextCursor: undefined,
        hasNextPage: false
      }
    })),
    http.patch(getUrl('universal-tracker-values/:utrvId/update'), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/connections'), async () =>
      HttpResponse.json({
        success: true,
        data: {
          connections: [],
          utrvs: []
        }
      })
    ),
    http.get(getUrl('universal-tracker-values/:utrvId/disaggregation'), async () =>
      HttpResponse.json({
        success: true,
        data: []
      })
    ),
  );
};
