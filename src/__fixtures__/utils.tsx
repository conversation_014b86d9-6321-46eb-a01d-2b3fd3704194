import React, { PropsWithChildren } from 'react'
import { render } from '@testing-library/react'
import type { RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux'
import { RootState } from '../reducers'
import { MemoryRouter, Route } from 'react-router-dom';
import config from '../config'

export interface ExtendedRenderOptions extends Omit<RenderOptions, 'queries'> {
  preloadedState?: RootState;
  store?: any;
  route?: {
    initialEntries: string[];
    path: string;
  };
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    store,
    route,
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: PropsWithChildren): JSX.Element {
    if (route) {
      return (
        <Provider store={store}>
          <MemoryRouter initialEntries={route.initialEntries}>
            <Route path={route.path}>{children}</Route>
          </MemoryRouter>
        </Provider>
      )
    }

    return <Provider store={store}>{children}</Provider>
  }

  // Return an object with the store and all of RTL's query functions
  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

/** Must not prepend 'url' with slash symbol since apiUrl already has */
export const getUrl = (url: string = '') => `${config.apiUrl}${url}`;