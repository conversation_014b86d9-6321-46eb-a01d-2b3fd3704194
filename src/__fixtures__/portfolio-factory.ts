/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { faker } from '@faker-js/faker';
import { Portfolio, PortfolioScorecard } from '@g17eco/types/portfolio';
import { UserRoles } from '@constants/user';

export const createPortfolio = (p: Partial<Portfolio> = {}): Portfolio => {

  const initiativeId = faker.database.mongodbObjectId();
  const {
    _id = faker.database.mongodbObjectId(),
    ...rest
  } = p;

  return {
    _id,
    code: '',
    created: '',
    name: '',
    scorecard: {
      children: [{ initiativeId: initiativeId, weight: 2 }],
      weight: 2,
      scorecard: {
        goals: [],
      } as PortfolioScorecard['scorecard']
    } as PortfolioScorecard,
    usage: [],
    userPermissions: {
      initiativeId: _id,
      permissions: [UserRoles.Manager],
    },
    ...rest,
  };
};

export const portfolioOne = createPortfolio();
export const getPortfolioState = (data: Portfolio) => ({
  loaded: true,
  loading: false,
  errored: false,
  errorMessage: '',
  data,
});
