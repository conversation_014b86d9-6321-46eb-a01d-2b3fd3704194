/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { ManagementInvitation } from '@constants/users';
import { faker } from '@faker-js/faker';
import { initiativeOne } from '@fixtures/initiative-factory';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { ObType, OnboardingModelPlain, OnboardingStatus } from '@g17eco/types/onboarding';

export const createManagementInvitation = (invitation: Partial<ManagementInvitation>): ManagementInvitation => {
  const { _id = faker.database.mongodbObjectId(), ...rest } = invitation;
  const sexType =faker.person.sexType();
  const firstName =faker.person.firstName(sexType);
  const surname =faker.person.lastName(sexType);
  const initiativeId = invitation.initiativeId ?? initiativeOne._id;

  return {
    _id,
    type: ObType.Initiative,
    initiativeId: initiativeId,
    created: faker.date.recent({ days: 3 }).toISOString(),
    status: OnboardingStatus.Pending,
    user: {
      firstName,
      surname,
      email: faker.internet.email({ firstName, lastName: surname }),
      permissions: [{ initiativeId: initiativeId, permissions: InitiativePermissions.canContributeRoles }],
      complete: false,
    },
    ...rest,
  };
}


export const onboardingTokenOne: OnboardingModelPlain = {
  _id: '123',
  status: OnboardingStatus.Pending,
  user: {
    email: '<EMAIL>',
    firstName: 'John',
    surname: 'Doe',
    complete: false,
    permissions: []
  },
  initiativeId: 'init123',
  token: faker.string.alphanumeric(16),
  created: faker.date.recent({ days: 3 }).toISOString(),
  type: ObType.Initiative,
};
