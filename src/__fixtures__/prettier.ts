/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


// This function does not suppose to do anything, it's only used as a trigger
// for prettier auto-formatting (https://prettier.io/blog/2020/08/24/2.1.0.html#api)
import prettier from 'prettier';
import prettierHtml from 'prettier/plugins/html';

export function html(partials: any, ...params: string[]) {
  let output = '';
  for (let i = 0; i < partials.length; i++) {
    output += partials[i];
    if (i < partials.length - 1) {
      output += params[i];
    }
  }
  return output;
}

/** lexical-playground/__tests__/utils/index.mjs */
export async function prettifyHTML(
  string: string,
  { ignoreClasses, ignoreInlineStyles } = { ignoreClasses: false, ignoreInlineStyles: false },
) {
  let output = string;

  if (ignoreClasses) {
    output = output.replace(/\sclass="([^"]*)"/g, '');
  }

  if (ignoreInlineStyles) {
    output = output.replace(/\sstyle="([^"]*)"/g, '');
  }

  output = output.replace(/\s__playwright_target__="[^"]+"/, '');

  return await prettier.format(output, {
    attributeGroups: ['$DEFAULT', '^data-'],
    attributeSort: 'asc',
    bracketSameLine: true,
    htmlWhitespaceSensitivity: 'ignore',
    parser: 'html',
    plugins: [prettierHtml, 'prettier-plugin-organize-attributes'],
  });
}

export async function prepareHTMLCompare(text: string) {
  return prettifyHTML(text.replace(/\n/gm, ''), { ignoreClasses: true, ignoreInlineStyles: true });
}
