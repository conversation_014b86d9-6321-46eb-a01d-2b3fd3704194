/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import config from '../config';
import { http, HttpResponse, JsonBodyType } from 'msw';
import { ApiResponse } from '@g17eco/types/api';

export const getUrl = (url: string = '') => {

  const baseUrl = config.apiUrl

  if (url.startsWith('/')) {
    return `${baseUrl}${url.slice(1)}`;
  }
  return `${baseUrl}${url}`;
}

/** Success URL for GET request */
export const httpGet = (url: string, data: unknown[] | Record<string, unknown>) => {
  return http.get(getUrl(url), () => ResponseSuccess(data))
}


export const ResponseSuccess = (data: JsonBodyType, status = 200) => {
  return HttpResponse.json({
    success: true,
    data,
  } as ApiResponse, { status });
}

export const ResponseError = (errorDetails: { message: string, userMessage?: string, status?: number }) => {
  const { message, userMessage, status } = errorDetails
  return HttpResponse.json({
    success: false,
    message,
    userMessage,
  }, { status });
}
