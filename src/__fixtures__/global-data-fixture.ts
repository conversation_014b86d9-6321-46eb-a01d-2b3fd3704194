/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { GlobalDataStateLoaded } from '@reducers/global-data';
import { createRootInitiative } from '@fixtures/initiative-factory';
import { faker } from '@faker-js/faker';
import { RootInitiativeData } from '@g17eco/types/initiative';

export const getGlobalData = (data: Partial<GlobalDataStateLoaded['data']> = {}, org: Partial<RootInitiativeData> = {}): GlobalDataStateLoaded => {
  const _id = faker.database.mongodbObjectId();
  const organization = data.organization ?? createRootInitiative({ _id,  ...org });
  return {
    loaded: true,
    loading: false,
    errored: false,
    data: {
      initiativeTree: [],
      organization: organization,
      ...data,
    },
    organization: organization,
    lastUpdated: 123456,
    initiativeId: organization._id,

  }
}
