/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { configureStore, ConfigureStoreOptions } from '@reduxjs/toolkit';
import { reducer, RootState } from '@reducers/index';
import { ReduxReducer } from '@reducers/types';
import { g17ecoApi } from '@api/g17ecoApi';
import { getCurrentUserState, userOne } from '@fixtures/user-factory';
import { bookmarksApi } from '@api/bookmarks';
import { metricGroupsApi } from '@api/metric-groups';
import { carbonCalculatorsApi } from '@apps/carbon-calculator/api';
import { defaultSurveyConfigApi } from '@api/default-survey-config';
import { utrvApi } from '@api/utrv';

const currentUserState = getCurrentUserState(userOne);
export const reduxMiddleware: ConfigureStoreOptions['middleware'] = (getDefaultMiddleware) => {
  return getDefaultMiddleware({
    serializableCheck: false,
    immutableCheck: false,
  })
    .concat(g17ecoApi.middleware)
    .concat(metricGroupsApi.middleware)
    .concat(carbonCalculatorsApi.middleware)
    .concat(defaultSurveyConfigApi.middleware)
    .concat(bookmarksApi.middleware)
    .concat(utrvApi.middleware);
};

export const simpleUserStore = configureStore({
  reducer,
  devTools: false,
  preloadedState: {
    currentUser: currentUserState,
  },
  middleware: reduxMiddleware,
});

export const reduxFixtureStore = (preloadedState?: Partial<RootState>) => configureStore({
  reducer,
  devTools: false,
  preloadedState: {
    currentUser: currentUserState,
    ...preloadedState,
  },
  middleware: reduxMiddleware
});

export const createReduxLoadedState = <T = any>(data: T): ReduxReducer<T> => {
  return {
    loaded: true,
    loading: false,
    errored: false,
    errorMessage: '',
    data,
  }
}
