import {
  ESGCategory,
  MaterialityBoundary,
  MaterialPillar,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { PrioritizedAssessmentData } from '@apps/materiality-tracker/components/assessment-insights/types';
import { faker } from '@faker-js/faker';

export const createAssessmentCategory = (
  overrides?: Partial<PrioritizedAssessmentData['categories']>,
): PrioritizedAssessmentData['categories'] => ({
  esg: [ESGCategory.Social],
  sdg: [],
  materialPillar: [MaterialPillar.People],
  boundary: [
    MaterialityBoundary.Leadership,
    MaterialityBoundary.ResearchAndDevelopment,
    MaterialityBoundary.ProductAndServices,
  ],
  ...overrides,
});

export const createAssessmentData = (overrides?: Partial<PrioritizedAssessmentData>): PrioritizedAssessmentData => ({
  code: 'test-code',
  score: faker.number.float(),
  relativeScore: faker.number.float(),
  name: 'Test Name',
  utrMapping: [
    {
      code: 'utr-code-1',
      score: faker.number.float(),
    },
  ],
  categories: {
    esg: [ESGCategory.Social],
    sdg: [],
    materialPillar: [MaterialPillar.People],
    boundary: [
      MaterialityBoundary.Leadership,
      MaterialityBoundary.ResearchAndDevelopment,
      MaterialityBoundary.ProductAndServices,
    ],
  },
  description: 'Test description',
  action: 'Test action',
  priority: 1,
  ...overrides,
});
