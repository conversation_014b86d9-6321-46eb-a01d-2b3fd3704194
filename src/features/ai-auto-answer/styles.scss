.progress-background {
  height: 4px;
  border-radius: 2px;
  background-color: var(--theme-BorderDefault);
}

.progress-fill {
  border-radius: 2px;
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill-percent {
  min-width: 2.5rem;
  text-align: right;
}

.progress-glow-background, .progress-glow-animated {
  width: calc(100% - 6px);
  height: 6px;
  border-radius: 2px;
  overflow: hidden;
  position: absolute;
  z-index: 0;
  transform: translateX(-1px);
}

.progress-glow-background {
  background-color: var(--theme-BorderDefault);
}

.progress-glow-animated {
  display: none;
}

.progress-glow.processing {
  .progress-glow-animated {
    display: block;
  }

  .progress-glow-background {
    filter: blur(25px);
  }

  .progress-glow-animated::before, .progress-glow-background::before {
    content: '';
    width: 99999px;
    height: 99999px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(0deg);
    background-image: conic-gradient(rgba(0,0,0,0), #7c97ff, rgba(0,0,0,0) 25%);
    background-repeat: no-repeat;
    background-position: 0 0;
    z-index: -2;
    animation: glow 5s linear infinite;
  }

  .progress-glow-background::after {
    content: '';
    position: 'absolute';
    z-index: -1;
    left: 2px;
    top: 2px;
    width: calc(100% - 5px);
    height: calc(100% - 5px);
    background: #0d2ca7;
    border-radius: 2px;
  }
}

@keyframes glow {
  100% {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}