import classNames from 'classnames';
import './styles.scss';
import { AIAutoAnswerSurveyJobPlain } from '@g17eco/types/ai-auto-answer-job';
import { formatHumaniseDate } from '@utils/date';

interface Props {
  job: AIAutoAnswerSurveyJobPlain | undefined | null;
  isProcessing: boolean;
  processedUtrvs: number;
  totalUtrvs: number;
  classes?: {
    wrapper?: string;
    progressBar?: string;
    progressFill?: string;
    progressText?: string;
  };
}

export const ProgressBar = ({ job, isProcessing, processedUtrvs, totalUtrvs, classes }: Props) => {
  const progress = Math.round((processedUtrvs / totalUtrvs) * 100);

  return (
    <div className={classNames('d-flex align-items-center', classes?.wrapper)}>
      <div className={classNames('flex-grow-0 text-ThemeTextMedium text-sm ms-1', classes?.progressText)}>
        <span className='text-ThemeTextPlaceholder'>{processedUtrvs}/</span>
        {totalUtrvs} metrics
      </div>
      {job && isProcessing ? (
        <>
          <div className={classNames('flex-grow-1 ps-2', classes?.progressBar)}>
            <div className='progress-background w-100 pe-2'>
              <div className='progress-fill background-gradient' style={{ width: `${progress}%` }} />
            </div>
          </div>
          <div className='flex-grow-0 strong text-sm pl-2 progress-fill-percent'>{progress}%</div>
        </>
      ) : null}
      {job && !isProcessing ? (
        <div className='flex-grow-0 text-xs pl-2 text-ThemeTextMedium'>
          (last run: {formatHumaniseDate(job.updated)})
        </div>
      ) : null}
    </div>
  );
};
