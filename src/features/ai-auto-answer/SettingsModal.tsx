import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button, FormGroup, Input, Label, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';

export enum SettingOption {
  DocumentLibrary = 'document_library',
  OverwriteMetric = 'overwrite_metric',
}

interface Props {
  isOpen: boolean;
  toggle: () => void;
  initiativeId: string;
  handleSubmit: (options: SettingOption[]) => void;
}

export const SettingsModal = ({ isOpen, toggle, initiativeId, handleSubmit }: Props) => {
  const [selectedOptions, setSelectedOptions] = useState<SettingOption[]>([]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    const name = e.target.name as SettingOption;

    if (checked) {
      setSelectedOptions([...selectedOptions, name]);
    } else {
      setSelectedOptions(selectedOptions.filter((option) => option !== name));
    }
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
      <ModalHeader toggle={toggle}>Auto-Answer Settings</ModalHeader>
      <ModalBody className='pb-0'>
        <div>Answer using AI <span className='text-ThemeTextMedium text-sm'>(will draft answers even when no data is available)</span></div>

        <FormGroup check className='mt-2'>
          <Input
            type='checkbox'
            className='mt-2'
            id={SettingOption.DocumentLibrary}
            name={SettingOption.DocumentLibrary}
            onChange={onChange}
            disabled={true}
            checked={selectedOptions.includes(SettingOption.DocumentLibrary)}
          />
          <Label for={SettingOption.DocumentLibrary}>
            <div className='text-ThemeTextLight'>Answer using knowledge from Document Library</div>
            <div className='text-ThemeTextMedium text-sm'>
              (to use enable AI access in{' '}
              <Link to={generateUrl(ROUTES.ACCOUNT_SETTINGS, { initiativeId, page: 'account-management' })}>
                Admin/Account Settings/Account Management
              </Link>
              )
            </div>
          </Label>
        </FormGroup>

        <FormGroup check className='mt-1'>
          <Input
            type='checkbox'
            className='mt-2'
            id={SettingOption.OverwriteMetric}
            name={SettingOption.OverwriteMetric}
            onChange={onChange}
            checked={selectedOptions.includes(SettingOption.OverwriteMetric)}
            disabled={true}
          />
          <Label for={SettingOption.OverwriteMetric}>
            <div className='text-ThemeWarningDark'>Overwrite metrics that already have content/answers</div>
            <div className='text-ThemeTextMedium text-sm'>
              (be careful with this one, it could overwrite content someone else has added)
            </div>
          </Label>
        </FormGroup>
      </ModalBody>
      <ModalFooter className='pt-3'>
        <Button color='link-secondary' className='me-3' onClick={toggle}>
          Cancel
        </Button>
        <Button data-testid='submit-auto-answer' color='primary' disabled={false} onClick={() => handleSubmit(selectedOptions)}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
};
