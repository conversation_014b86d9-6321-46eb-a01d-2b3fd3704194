import { useEffect, useState } from 'react';
import { ButtonGlow, ButtonGradient } from '@g17eco/atoms/button';
import { ProgressBar } from './ProgressBar';
import { type SettingOption, SettingsModal } from './SettingsModal';
import { useToggle } from '@hooks/useToggle';
import { Button } from 'reactstrap';
import { VerticalDivider } from '@g17eco/atoms/divider';
import { useAiAutoAnswerSurveyMutation, useGetAIAutoAnswerSurveyQuery } from '@api/ai';
import { canRetryJob, isFinishedJob, isProcessingJob } from '@utils/background-job';
import { TaskStatus, TaskType } from '@g17eco/types/background-jobs';
import { ProgressStats } from '@utils/survey';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { FeatureStability } from '@g17eco/molecules/feature-stability';
import { SurveyActionData } from '@models/surveyData';

interface Props {
  survey: Pick<SurveyActionData, '_id' | 'completedDate'>;
  initiativeId: string;
  progressStats: ProgressStats;
}

export const AIAutoAnswer = ({ initiativeId, survey, progressStats }: Props) => {
  const { addSiteError } = useSiteAlert();

  const [openSettings, toggleSettings, setOpenSettings] = useToggle();
  const [skipPolling, setSkipPolling] = useState(false);

  const { data, isSuccess, error } = useGetAIAutoAnswerSurveyQuery(
    { initiativeId, surveyId: survey._id },
    { skip: skipPolling, pollingInterval: 10000 },
  );
  const [autoAnswerSurvey, { isLoading: creatingJob }] = useAiAutoAnswerSurveyMutation();

  useEffect(() => {
    // Skip polling when not found job or job is finished
    if (isSuccess && (!data?.job || isFinishedJob(data.job))) {
      setSkipPolling(true);
    }
  }, [data, isSuccess]);

  useEffect(() => {
    if (error && error.message) {
      addSiteError(error.message);
    }
  }, [error, addSiteError]);

  if (survey.completedDate) {
    return null;
  }

  const handleAutoAnswer = (options: SettingOption[]) => {
    // Trigger polling for processing data
    setSkipPolling(false);
    autoAnswerSurvey({ initiativeId, surveyId: survey._id })
      .unwrap()
      .catch((e) => addSiteError(e));
    setOpenSettings(false);
  };

  const job = isSuccess ? data.job : undefined;
  const processingJob = job && isProcessingJob(job);
  const finishedJob = job && isFinishedJob(job);
  const canRetry = job && canRetryJob(job);

  const getProcessingStatus = () => {
    if (finishedJob || canRetry) {
      return false;
    }
    return creatingJob || Boolean(processingJob);
  };
  const isProcessing = getProcessingStatus();

  const processedUtrvs =
    job?.tasks.filter(
      (task) =>
        task.type === TaskType.AIAutoAnswerProcess && [TaskStatus.Completed, TaskStatus.Error].includes(task.status),
    ).length ?? 0;

  const numOfJobProcessUtrvs = job?.tasks.filter((task) => task.type === TaskType.AIAutoAnswerProcess).length ?? 0;
  const totalUtrvs = Math.max(numOfJobProcessUtrvs, progressStats.created);

  return (
    <div className='w-100 d-flex align-items-center mb-2'>
      {isProcessing ? (
        <ButtonGlow color='secondary' disabled>
          <i className='fal fa-stars mr-2' />
          Auto Answering...
        </ButtonGlow>
      ) : (
        <ButtonGradient onClick={toggleSettings}>
          <i className='fal fa-stars mr-2' />
          Auto Answer
        </ButtonGradient>
      )}
      <Button color='transparent' className='ms-2 px-1' onClick={toggleSettings} disabled={isProcessing}>
        <i className='fal fa-gear text-ThemeIconSecondary' />
      </Button>
      <FeatureStability stability='internal' className='mx-1' />
      <VerticalDivider className='lh-lg mx-1' />
      <ProgressBar
        classes={{ wrapper: 'flex-grow-1', progressBar: 'processing' }}
        job={job}
        isProcessing={isProcessing}
        processedUtrvs={processedUtrvs}
        totalUtrvs={totalUtrvs}
      />
      <SettingsModal
        key={openSettings ? 'open' : 'closed'}
        isOpen={openSettings}
        toggle={toggleSettings}
        initiativeId={initiativeId}
        handleSubmit={handleAutoAnswer}
      />
    </div>
  );
};
