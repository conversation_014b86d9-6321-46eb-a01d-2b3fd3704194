/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button, Modal, ModalBody, Modal<PERSON>ooter, ModalHeader } from 'reactstrap';
import React from 'react';
import { CompanyTrackerServicesAgreementParams } from './agreement-types';


export const CompanyTrackerServicesAgreement = (props: CompanyTrackerServicesAgreementParams) => {
  const { requiredAgreement, handleAgree, handleDecline } = props;

  const servicesAgreementLink = <a
    href={'/legal-terms-and-conditions'}
    target='_blank' rel='noreferrer'>
    Platform Services Agreement
  </a>;


  const code = requiredAgreement.code;
  return (
    <Modal isOpen={true} toggle={() => handleDecline(code)} backdrop='static'>
      <ModalHeader data-testid={'company-tracker-service-agreement-header'}>
        Platform Services Agreement
      </ModalHeader>
      <ModalBody>
        I have read and understood the {servicesAgreementLink},
        which details the terms of my organisation's use of Company Tracker.
        I agree to comply with and be bound by the terms.
      </ModalBody>
      <ModalFooter>
        <Button color='danger' outline onClick={() => handleDecline(code)}>
          Decline
        </Button>
        <Button color='primary' onClick={() => handleAgree(code, 'company')}>
          Agree
        </Button>
      </ModalFooter>
    </Modal>
  )
}
