import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import { renderWithProviders } from '@fixtures/utils';
import { configureStore } from '@reduxjs/toolkit';
import { createUser, getCurrentUserState, userOne } from '@fixtures/user-factory';
import { reducer } from '@reducers/index';
import { AgreementChecker } from './AgreementChecker';
import { getGlobalData } from '@fixtures/global-data-fixture';
import { AppConfig, CustomAgreementConfig, DescriptionType } from '@g17eco/types/app';
import { faker } from '@faker-js/faker';
import { UserAgreement } from '@reducers/current-user';

describe('<AgreementChecker />', () => {

  const currentUser = getCurrentUserState(userOne);
  const customModalTestId = 'custom-agreement-modal-title';
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const agreement = {
    code: 'custom-agreement',
    type: 'sponsorship',
    data: {
      title: 'Custom Agreement',
      description: [
        {
          type: DescriptionType.Text,
          text: 'This is a custom agreement',
        },
        {
          type: DescriptionType.HyGraph,
          code: 'custom-agreement',
          text: 'This is a custom agreement using internal url',
        },
        {
          type: DescriptionType.ExternalLink,
          text: 'This is a custom agreement',
          url: 'https://example.com',
        }
      ],
    },
  } satisfies CustomAgreementConfig;

  it('should render custom sponsorship modal', () => {
    renderWithProviders(<AgreementChecker />, {
      store: configureStore({
        reducer,
        preloadedState: {
          currentUser,
          globalData: getGlobalData({
            companyAgreementsRequired: [agreement],
          }),
        },
      }),
    });

    const modal = screen.getByTestId(customModalTestId)
    expect(modal.textContent).toEqual(agreement.data.title);

    // Link
    expect(screen.getByText(agreement.data.description[1].text)).toBeInTheDocument();
  })

  it('should render CompanyTrackerServicesAgreement', () => {
    renderWithProviders(<AgreementChecker />, {
      store: configureStore({
        reducer,
        preloadedState: {
          currentUser,
          globalData: getGlobalData({}, { created: faker.date.recent({ days: 3 }).toISOString() }),
        },
      }),
    });

    const modal = screen.getByTestId('company-tracker-service-agreement-header')
    expect(modal.textContent).toEqual('Platform Services Agreement');
  });


  describe('SGX user acceptance', () => {

    const globalData = getGlobalData({
      appConfig: {
        settings: {
          userAgreementsRequired: [{ code: UserAgreement.ESGenomeTandC, fromDate: new Date('2021-01-01') }]
        },
      } as AppConfig
    }, { created: faker.date.recent({ days: 3 }).toISOString() });


    it('should show SGX User agreement', () => {
      renderWithProviders(<AgreementChecker />, {
        store: configureStore({
          reducer,
          preloadedState: {
            currentUser: getCurrentUserState(createUser({})),
            globalData: globalData,
          },
        }),
      });

      const modal = screen.getByTestId('sgx-agreement-header')
      expect(modal.textContent).toEqual('Welcome to SGX ESGenome\'s Terms of Use');
    })

    it('should not show if its already accepted SGX User agreement', () => {
      const sgxUser = createUser({
        registrationData: {
          agreements: { [UserAgreement.ESGenomeTandC]: faker.date.recent({ days: 1 }).toISOString() }
        }
      });

      const { container } = renderWithProviders(<AgreementChecker />, {
        store: configureStore({
          reducer,
          preloadedState: {
            currentUser: getCurrentUserState(sgxUser),
            globalData: globalData,
          },
        }),
      });

      expect(container.firstChild).toBeNull();
    })
  });
});
