/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { CustomAgreementConfig } from '@g17eco/types/app';
import { RootInitiativeData } from '@g17eco/types/initiative';

export type CompanyTrackerServicesAgreementParams = {
  requiredAgreement: CustomAgreementConfig;
  initiative: Pick<RootInitiativeData, '_id'>;
  handleAgree: (code: string, type: string) => void;
  handleDecline: (code: string) => void;
  /** Allow to control the priority of modal, default 1051 as it higher priority than others **/
  zIndex?: number;
};
