/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button, Modal, ModalBody, Modal<PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap';
import { handleRouteError } from '../../logger';
import { DescriptionElement } from '@g17eco/types/app';
import { generateUrl } from '@routes/util';
import { CompanyTrackerServicesAgreementParams } from './agreement-types';


const route = {
  path: '/agreements/:code'
}

function renderElement(element: DescriptionElement, index: number) {
  switch (element.type) {
    case 'external_link':
      return <a key={index} href={element.url} target='_blank' rel='noreferrer'>&nbsp;{element.text}&nbsp;</a>;
    case 'hygraph': {
      const href = generateUrl(route, { code: element.code });
      return <a key={index} href={href} target='_blank' rel='noreferrer'>&nbsp;{element.text}&nbsp;</a>;
    }
    case 'text':
    default:
      return <span key={index}>{element.text}</span>;
  }
}

export function CustomServiceAgreement(props: CompanyTrackerServicesAgreementParams) {

  const { requiredAgreement, handleAgree, handleDecline, zIndex = 1051 } = props;
  const { code, data, type } = requiredAgreement;

  if (!data || !type) {
    handleRouteError(new Error('Custom agreement is not defined or missing type'), { requiredAgreement })
    return null;
  }

  return (
    <Modal zIndex={zIndex} isOpen={true} toggle={() => handleDecline(code)} backdrop='static'>
      <ModalHeader data-testid={'custom-agreement-modal-title'}>{data.title}</ModalHeader>
      <ModalBody>
        {data.description.map((element, index) => renderElement(element, index))}
      </ModalBody>
      <ModalFooter>
        <Button color='danger' outline onClick={() => handleDecline(code)}>
          Decline
        </Button>
        <Button color='primary' onClick={() => handleAgree(code, type)}>
          Agree
        </Button>
      </ModalFooter>
    </Modal>
  )
}
