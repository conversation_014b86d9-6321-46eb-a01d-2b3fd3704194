/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { AgreementConfig } from '@g17eco/types/app';
import { UserAgreement } from '@reducers/current-user';
import { useHistory } from 'react-router-dom';
import config from '../../config';
import G17Client from '@services/G17Client';
import { reloadCurrentUser } from '@actions/user';
import { addSiteAlert } from '@g17eco/slices/siteAlertsSlice';
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
import React from 'react';
import { useAppDispatch } from '@reducers/index';

export const ESGenomeTandC = (props: { userId: string, requiredAgreement: AgreementConfig<UserAgreement> }) => {
  const { userId, requiredAgreement } = props;

  const history = useHistory();
  const dispatch = useAppDispatch();

  const sgxTandCLink = <a
    href={config.sgx.termsAndConditionsPdf}
    target='_blank' rel='noreferrer'>
    SGXNet User Terms & Conditions
  </a>;

  const esgAddendumLink = <a
    href={config.sgx.addendumTermsLink}
    target='_blank' rel='noreferrer'>
    ESG Portal Addendum to the SGXNet T&Cs
  </a>;

  const handleDecline = () => {
    history.push('/');
  }

  const handleAgree = () => {
    G17Client.acceptUserAgreement(userId, requiredAgreement.code)
      .then(() => dispatch(reloadCurrentUser()))
      .catch(() => dispatch(
        addSiteAlert({
          content: 'Could not update agreement. Please try again',
          timeout: 5000
        }))
      );
  }

  return (
    <Modal isOpen={true} toggle={handleDecline} backdrop='static'>
      <ModalHeader data-testid={'sgx-agreement-header'}>Welcome to SGX ESGenome's Terms of Use</ModalHeader>
      <ModalBody>
        I have read and understood the {sgxTandCLink} ("SGXNet T&Cs”) and the {esgAddendumLink}.
        I agree to comply with and be bound by the terms in relation to my access and use of this
        website and SGX ESGenome.
      </ModalBody>
      <ModalFooter>
        <Button color='danger' outline onClick={handleDecline}>Decline</Button>
        <Button color='primary' onClick={handleAgree}>Agree</Button>
      </ModalFooter>
    </Modal>
  )
}
