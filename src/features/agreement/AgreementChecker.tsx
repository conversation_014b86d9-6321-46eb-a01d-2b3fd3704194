/*
 * Copyright (c) 2022-2024. World Wide Generation Ltd
 */

import React from 'react';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { getCurrentUser } from '@selectors/user';
import G17Client from '@services/G17Client';
import { addSiteAlert, SiteAlertColors } from '@g17eco/slices/siteAlertsSlice';
import { CompanyAgreement, UserAgreement } from '@reducers/current-user';
import { getCompanyAgreements, getRootOrg } from '@selectors/initiative';
import { getDiff } from '@utils/date';
import { AgreementConfig } from '@g17eco/types/app';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { CompanyTrackerServicesAgreement } from './CompanyTrackerServicesAgreement';
import { reloadInitiativeTree } from '@actions/index';
import { ESGenomeTandC } from './ESGenomeTandC';
import { CustomServiceAgreement } from './CustomServiceAgreement';
import { useHistory } from 'react-router-dom';

function getRequiredAgreement<T extends string = UserAgreement>(
  configAgreements: AgreementConfig<T>[],
  created: string,
  agreements?: string[],
) {
  return configAgreements.find(config => {
    if (config.fromDate) {
      const diff = getDiff(created, config.fromDate);
      if (diff < 0) {
        return false;
      }
    }

    return !agreements?.includes(config.code);
  });
}

// @TODO: This current is shared with CT and MT
//  will require more refactoring to enabled it for PT or AT etc.
export const AgreementChecker = () => {

  const appSettings = useAppSettings();
  const currentUser = useAppSelector(getCurrentUser);
  const rootOrg = useAppSelector(getRootOrg);
  const rootOrgRequiredAgreements = useAppSelector(getCompanyAgreements);
  const dispatch = useAppDispatch();
  const history = useHistory();

  if (!currentUser || !rootOrg) {
    return null;
  }

  const handleDecline = () => {
    history.push('/');
  }

  const handleAgree = (code: string, type: string) => {
    // Can always use acceptCustomAgreement moving forward (release/3.32+)
    const promise = type === 'company' ?
      G17Client.acceptCompanyAgreement(rootOrg._id, code):
      G17Client.acceptCustomAgreement({ type, agreementCode: code, initiativeId: rootOrg._id, });

    promise.then(() => dispatch(reloadInitiativeTree()))
      .catch(() => dispatch(
        addSiteAlert({
          content: 'Could not update agreement. Please try again',
          color: SiteAlertColors.Danger,
          timeout: 5000
        }))
      );
  }

  const requiredUserAgreements = appSettings.userAgreementsRequired ?? [];
  const userAgreements = Object.keys(currentUser.registrationData?.agreements ?? {});
  const requiredUserAgreement = getRequiredAgreement<UserAgreement>(requiredUserAgreements, currentUser.created, userAgreements);
  if (requiredUserAgreement) {
    switch (requiredUserAgreement.code) {
      case UserAgreement.ESGenomeTandC:
        return <ESGenomeTandC requiredAgreement={requiredUserAgreement} userId={currentUser._id} />;
    }
  }

  const requiredCompanyAgreements = rootOrgRequiredAgreements.concat(appSettings.companyAgreementsRequired ?? []);
  const companyAgreements = Object.keys(rootOrg.metadata?.agreements ?? {});
  const requiredCompanyAgreement = getRequiredAgreement<string | CompanyAgreement>(requiredCompanyAgreements, rootOrg.created, companyAgreements);

  if (requiredCompanyAgreement) {
    switch (requiredCompanyAgreement.code) {
      case CompanyAgreement.CompanyTrackerServicesAgreement:
        return <CompanyTrackerServicesAgreement
          handleAgree={handleAgree}
          handleDecline={handleDecline}
          initiative={rootOrg}
          requiredAgreement={requiredCompanyAgreement} />;
      default:
        return <CustomServiceAgreement
          handleAgree={handleAgree}
          handleDecline={handleDecline}
          requiredAgreement={requiredCompanyAgreement}
          initiative={rootOrg} />
    }
  }

  return null;
}

