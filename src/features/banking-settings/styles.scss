.popular-banks {
  gap: 2rem;

  &__item {
    width: 170px;
    height: 170px;
    border: 1px solid var(--theme-BorderDefault);
    box-shadow: 0px 1px 3px rgba(38, 40, 43, 0.1), 0px 0px 1px rgba(38, 40, 43, 0.05);
    z-index: 1;

    &.selected {
      background-color: var(--theme-AccentExtralight) !important;
    }
  }

  &__logo {
    left: 5%;
    width: 90%;
    z-index: 0;
    object-fit: contain;
    object-position: center;
  }

  &__checkbox {
    bottom: 1rem;
    right: 1rem;
    z-index: 1;
  }
}

.other-banks {
  .isSaving {
    opacity: 0.6;
    pointer-events: none;
  }
}

.banks-table {
  // add spacing between header and body
  .g17-thead::after {
    content: "";
    display: block;
    height: 1.5rem;
    width: 100%;
    background: transparent;
  }
}

.manual-banks {
  .form-control {
    border-color: var(--theme-BorderDefault);
  }
}