/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { SiteAlertColors } from '../../slice/siteAlertsSlice';
import IconButton from '@components/button/IconButton';
import { Bank, BankingAction, BankingSetting, UpdateBankingSetting } from '@g17eco/types/banking-settings';
import './styles.scss';
import { naturalSort } from '@utils/index';
import { countriesDisplay, isManualBank } from '@utils/banking-settings';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { BasicAlert } from '@g17eco/molecules/alert';
import { Table } from '@g17eco/molecules/table';
import { ColumnDef } from '@tanstack/react-table';

interface BanksTableProps {
  selectedBanks: BankingSetting[];
  banks: Bank[];
  isLoading: boolean;
  updateBankingSettings: (params: UpdateBankingSetting) => Promise<void>;
  helperText?: string;
}

const BanksTable = (props: BanksTableProps) => {
  const { selectedBanks, banks, isLoading, updateBankingSettings, helperText } = props;
  const { addSiteAlert } = useSiteAlert();
  const [errorMessage, setMessage] = useState('');

  const isSelectedBank = (bank: Bank) => {
    return selectedBanks.some((item) => item.code === bank.code);
  };

  const handleAddBank = async (addBank: Bank) => {
    if (isLoading) {
      return;
    }

    try {
      const addBankSetting = {
        code: addBank.code,
        ...(isManualBank(addBank) ? { name: addBank.name, type: addBank.type } : {}),
      };
      await updateBankingSettings({
        bankingSettings: [...selectedBanks, addBankSetting],
        updateBank: addBankSetting,
        ...(!isManualBank(addBank) ? { action: BankingAction.Add } : {}),
      });
      addSiteAlert({
        content: (
          <>
            <strong>Success!</strong> {addBank.name} has been added
          </>
        ),
        color: SiteAlertColors.Primary,
      });
    } catch (e) {
      setMessage(e.message);
    }
  };

  const handleRemoveBank = async (removeBank: Bank) => {
    if (isLoading) {
      return;
    }

    try {
      const newValues = selectedBanks.filter((item) => item.code !== removeBank.code);
      await updateBankingSettings({
        bankingSettings: newValues,
        updateBank: removeBank,
        ...(!isManualBank(removeBank) ? { action: BankingAction.Remove } : {}),
      });
      addSiteAlert({
        content: (
          <>
            <strong>Success!</strong> {removeBank.name} has been removed
          </>
        ),
        color: SiteAlertColors.Primary,
      });
    } catch (e) {
      setMessage(e.message);
    }
  };

  const columns: ColumnDef<Bank>[] = [
    {
      accessorKey: 'name',
      header: () => 'Bank name',
      cell: ({ row }) => row.original.name,
      sortingFn: (a, b) => naturalSort(a.original.name ?? '', b.original.name ?? ''),

    },
    {
      accessorKey: 'country',
      header: () => 'Country',
      cell: ({ row }) => countriesDisplay(row.original.countries),
      sortingFn: (a, b) => naturalSort(countriesDisplay(a.original.countries), countriesDisplay(b.original.countries)),
    },
    {
      accessorKey: 'code',
      header: () => '',
      cell: ({ row }) => {
        if (isSelectedBank(row.original)) {
          return (
            <IconButton
              tooltip='Remove this bank from the company'
              disabled={isLoading}
              color='danger'
              onClick={() => handleRemoveBank(row.original)}
              icon='fa-times'
            />
          );
        }

        return (
          <IconButton
            tooltip='Add this bank to the company'
            disabled={isLoading}
            onClick={() => handleAddBank(row.original)}
            icon='fa-plus'
          />
        );
      },
      enableSorting: false,
    },
  ];

  return (
    <div className='w-100 mt-3 banks-table'>
      <BasicAlert type={'danger'} className='my-2'>
        {errorMessage}
      </BasicAlert>
      <Table responsive={true} className={isLoading ? 'isSaving' : ''} columns={columns} data={banks} pageSize={20} />
      {!banks.length ? <div className='text-center mt-4 pb-2'>{helperText}</div> : null}
    </div>
  );
};

export default BanksTable;
