/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Label, Button } from 'reactstrap';
import { UnitConfig } from '@models/surveyData';
import { InfoIcon } from '@g17eco/molecules/info-icon';
import { SURVEY } from '@constants/terminology';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { FormatOptionLabelContext, FormatOptionLabelMeta } from 'react-select';
import classNames from 'classnames';
import { UnitOption } from './types';
import { SurveyUnitsAndCurrencyText } from './utils';

export interface SurveyUnitsAndCurrencyFieldRowProps {
  code: keyof UnitConfig;
  options: UnitOption[];
  disabled?: boolean;
  canEnable?: boolean;
  unitConfig: Partial<UnitConfig>;
  defaultUnitConfig: UnitConfig;
  handleDropdownSelect: (code: string, abbr: string) => void;
  tooltip?: string;
  resetChangedUnit: (key: string) => void;
}

export const FieldRow = ({
  handleDropdownSelect,
  defaultUnitConfig,
  code,
  unitConfig,
  options,
  disabled = false,
  canEnable = false,
  tooltip,
  resetChangedUnit,
}: SurveyUnitsAndCurrencyFieldRowProps) => {
  const [isDisabled, setIsDisabled] = useState(disabled);

  if (options.length === 0) {
    return null;
  }

  const getOptionLabel = (props: Pick<UnitOption, 'singular' | 'abbr'> & { context: FormatOptionLabelContext }) => {
    const { singular, abbr, context } = props;
    return (
      <div className={classNames('row w-100', { strong: context === 'menu' && abbr === defaultUnitConfig[code] })}>
        <div className='col-8'>{singular}</div>
        <div className='col-4 dont_translate'>{abbr}</div>
      </div>
    );
  };

  const selectOptions = options.map(({ singular, abbr }) => ({
    label: getOptionLabel({ singular, abbr, context: 'menu' }),
    value: abbr,
  }));

  const currentSelected = unitConfig[code] ? unitConfig[code] : defaultUnitConfig[code];
  const currentOption = options.find((option: UnitOption) => option.abbr === currentSelected);
  const fieldTitle = SurveyUnitsAndCurrencyText[code].title;
  const fieldTooltip = tooltip || SurveyUnitsAndCurrencyText[code].tooltip;
  const enableButton = (
    <SimpleTooltip
      text={`(Staff Only) Unlock ${fieldTitle} configuration change, this has a high impact on ${SURVEY.SINGULAR} data`}
    >
      <Button
        onClick={() => {
          if (!isDisabled) {
            resetChangedUnit(code);
          }
          return !isDisabled || window.confirm(`Are you sure you want to enable ${fieldTitle} configuration change ?`)
            ? setIsDisabled(!isDisabled)
            : null;
        }}
        size='sm'
        className='ml-2'
      >
        <i className={'text-ThemeIconSecondary fas ' + (isDisabled ? 'fa-lock' : 'fa-unlock')} />
      </Button>
    </SimpleTooltip>
  );

  const formatOptionLabel = (option: Option | null, meta: FormatOptionLabelMeta<Option | null>) => {
    if (!option) {
      return <></>;
    }

    if (meta.context === 'value' && currentOption) {
      const { singular, abbr } = currentOption;
      return getOptionLabel({ singular, abbr, context: 'value' });
    }

    return option.label;
  };

  return (
    <div className='mb-2'>
      <div className='row'>
        <div className='col-3 align-self-center'>
          <Label className='col-form-label mb-0' for={code}>
            {fieldTitle}
            {isDisabled ? ' (Disabled)' : null}
          </Label>
          {fieldTooltip ? <InfoIcon text={fieldTooltip} className='info-icon' /> : null}

          {canEnable ? enableButton : null}
        </div>
        <div className='col-9'>
          <SelectFactory<UnitOption['abbr']>
            selectType={SelectTypes.SingleSelect}
            options={selectOptions}
            onChange={(op) => (op ? handleDropdownSelect(code, op.value) : undefined)}
            value={selectOptions.find((option) => option.value === currentSelected)}
            isSearchable={false}
            formatOptionLabel={formatOptionLabel}
            isDisabled={isDisabled}
          />
        </div>
      </div>
    </div>
  );
};
