/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { Button } from 'reactstrap';
import { SURVEY } from '@constants/terminology';
import { ConversionModal } from '@components/survey-configuration/partials/ConversionModal';
import { useToggle } from '@hooks/useToggle';
import { SurveyUnitsAndCurrencyForm, SurveyUnitsProps } from './SurveyUnitsAndCurrencyForm';
import { getDefaultUnitConfig } from './utils';
import { BasicAlert } from '@g17eco/molecules/alert';
import { CollapseGroup } from '@g17eco/molecules/collapse-panel';
import { Checkbox, CheckboxState } from '@g17eco/atoms/checkbox';

export const SurveyUnitsAndCurrency = (props: SurveyUnitsProps) => {
  const { unitConfig, additionalUnitConfig = {} } = props;
  const [isOpen, toggle] = useToggle(false);
  const defaultUnitConfig = getDefaultUnitConfig(additionalUnitConfig);

  return (
    <>
      <div className='d-flex justify-content-between'>
        <h5>Default {SURVEY.ADJECTIVE} units</h5>
        <Button color='link' className='text-sm' onClick={toggle}>
          View conversion rates
        </Button>
      </div>
      <SurveyUnitsAndCurrencyForm {...props} />
      <ConversionModal isOpen={isOpen} toggle={toggle} defaultsUnitConfig={unitConfig ?? defaultUnitConfig} />
    </>
  );
};

export const SurveyUnitsAndCurrencyCollapse = (props: SurveyUnitsProps) => {
  const {
    unitConfig,
    additionalUnitConfig = {},
    title = 'Unit and currency defaults',
    useInitiativeSettings,
    toggleUseInitiativeSettings,
    isDisabled,
  } = props;
  const [isOpen, toggle] = useToggle(false);
  const defaultUnitConfig = getDefaultUnitConfig(additionalUnitConfig);

  const toggleConversionRates = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggle();
  };

  return (
    <>
      <CollapseGroup
        initialValue={false}
        trigger={
          <div className='d-flex justify-content-between align-items-center'>
            <h5>{title}</h5>
            {useInitiativeSettings ? null : (
              <Button color='link' className='text-sm' onClick={toggleConversionRates}>
                <i className='fal fa-calculator text-ThemeAccentMedium text-sm mr-1' />
                <span className='text-ThemeAccentMedium'>View conversion rates</span>
              </Button>
            )}
          </div>
        }
      >
        {toggleUseInitiativeSettings ? (
          <>
            <Checkbox
              status={useInitiativeSettings ? CheckboxState.Checked : CheckboxState.Unchecked}
              onChange={toggleUseInitiativeSettings}
              disabled={isDisabled}
              className='cursor-pointer'
            />
            <span className='ml-2'>
              Use subsidiary specific default units and currencies. These can be managed within each subsidiaries{' '}
              <strong>Account settings</strong>
            </span>
          </>
        ) : null}
        {useInitiativeSettings ? (
          <BasicAlert type='info' className='mt-3'>
            Disabled: Subsidiary default will be used
          </BasicAlert>
        ) : (
          <SurveyUnitsAndCurrencyForm {...props} />
        )}
      </CollapseGroup>
      <ConversionModal isOpen={isOpen} toggle={toggle} defaultsUnitConfig={unitConfig ?? defaultUnitConfig} />
    </>
  );
};
