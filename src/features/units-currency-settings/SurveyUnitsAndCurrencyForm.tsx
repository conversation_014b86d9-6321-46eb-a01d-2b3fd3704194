/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { UnitTypes, getUnitsForType, getCurrencyList } from '@utils/units';
import { UnitConfig } from '@models/surveyData';
import { BasicAlert } from '@g17eco/molecules/alert';
import { FieldOptions } from './types';
import { FieldRow, SurveyUnitsAndCurrencyFieldRowProps } from './FieldRow';
import { defaultFieldOptions, getDefaultUnitConfig, SurveyUnitsAndCurrencyText } from './utils';

export interface SurveyUnitsProps {
  setUnitConfig?: (unitConfig: any) => void;
  unitConfig?: UnitConfig;
  isCreating?: boolean;
  isDisabled?: boolean;
  additionalUnitConfig?: Partial<UnitConfig>;
  warningText?: JSX.Element;
  title?: string;
  useInitiativeSettings?: boolean;
  toggleUseInitiativeSettings?: () => void;
  fieldOptions?: FieldOptions;
}

export const SurveyUnitsAndCurrencyForm = (props: SurveyUnitsProps) => {
  const {
    setUnitConfig = () => {},
    unitConfig,
    isCreating,
    isDisabled = false,
    additionalUnitConfig = {},
    warningText,
    fieldOptions = defaultFieldOptions,
  } = props;

  const defaultUnitConfig = getDefaultUnitConfig(additionalUnitConfig);

  const resetChangedUnit = (code: string) => {
    setUnitConfig({
      ...unitConfig,
      [code]: defaultUnitConfig[code as keyof UnitConfig],
    });
  };
  const [hasChanged, setChanged] = useState(false);

  const handleDropdownSelect = (code: string, abbr: string) => {
    setUnitConfig({
      ...unitConfig,
      [code]: abbr,
    });
    setChanged(true);
  };

  const fieldRowProps: Omit<SurveyUnitsAndCurrencyFieldRowProps, 'code' | 'options'> = {
    handleDropdownSelect,
    defaultUnitConfig,
    unitConfig: unitConfig ?? {},
    resetChangedUnit,
  };

  return (
    <>
      <div className='mt-3'>
        {(Object.keys(SurveyUnitsAndCurrencyText) as Array<keyof UnitConfig>).map((key: keyof UnitConfig) => {
          const props = {
            ...fieldRowProps,
            code: key,
            tooltip: fieldOptions[key]?.tooltip,
            disabled: isDisabled || fieldOptions[key]?.disabled,
            canEnable: fieldOptions[key]?.canEnable,
          };
          switch (key) {
            case 'currency':
              return <FieldRow key={key} {...props} options={getCurrencyList()} />;
            case 'numberScale':
              return <FieldRow key={key} {...props} options={getUnitsForType(UnitTypes.currency)} />;
            default:
              return <FieldRow key={key} {...props} options={getUnitsForType(key)} />;
          }
        })}
      </div>
      {!isCreating && warningText ? (
        <BasicAlert type={'warning'} hide={!hasChanged}>
          <div>{warningText}</div>
        </BasicAlert>
      ) : null}
    </>
  );
};
