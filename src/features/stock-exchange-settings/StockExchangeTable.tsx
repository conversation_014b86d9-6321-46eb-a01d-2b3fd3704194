/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import IconButton from '@components/button/IconButton';
import { naturalSort } from '@utils/index';
import { Table } from '@g17eco/molecules/table';
import { ColumnDef } from '@tanstack/react-table';
import { StockExchange } from '@g17eco/types/stock-exchange-settings';

interface StockExchangeTableProps {
  stockExchanges: StockExchange[];
  isLoading: boolean;
  helperText?: string;
  isSelectedStockExchange: (stockExchange: StockExchange) => boolean;
  handleSelectStockExchange: (stockExchange: StockExchange) => void;
}

export const StockExchangeTable = (props: StockExchangeTableProps) => {
  const { stockExchanges, isLoading, helperText, isSelectedStockExchange, handleSelectStockExchange } = props;

  const columns: ColumnDef<StockExchange>[] = [
    {
      accessorKey: 'name',
      header: () => 'Stock exchange name',
      cell: ({ row }) => row.original.name,
      sortingFn: (a, b) => naturalSort(a.original.name ?? '', b.original.name ?? ''),
    },
    {
      accessorKey: 'code',
      header: () => '',
      cell: ({ row }) => {
        if (isSelectedStockExchange(row.original)) {
          return (
            <IconButton
              tooltip='Remove this stock exchange from the company'
              disabled={isLoading}
              color='danger'
              onClick={() => handleSelectStockExchange(row.original)}
              icon='fa-times'
            />
          );
        }

        return (
          <IconButton
            tooltip='Add this stock exchange to the company'
            disabled={isLoading}
            onClick={() => handleSelectStockExchange(row.original)}
            icon='fa-plus'
          />
        );
      },
      enableSorting: false,
    },
  ];

  return (
    <div className='w-100 mt-3 stock-exchange-table'>
      <Table
        responsive={true}
        className={isLoading ? 'isSaving' : ''}
        columns={columns}
        data={stockExchanges}
        pageSize={20}
      />
      {!stockExchanges.length ? <div className='text-center mt-4 pb-2'>{helperText}</div> : null}
    </div>
  );
};
