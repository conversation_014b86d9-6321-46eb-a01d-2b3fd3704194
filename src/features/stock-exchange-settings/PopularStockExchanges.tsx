import classNames from 'classnames';
import { Input } from 'reactstrap';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { StockExchange } from '@g17eco/types/stock-exchange-settings';
import './styles.scss';

interface PopularStockExchangesProps {
  popularStockExchanges: StockExchange[];
  handleSelectStockExchange: (stockExchange: StockExchange) => void;
  isSelectedStockExchange: (stockExchange: StockExchange) => boolean;
}

export const PopularStockExchanges = (props: PopularStockExchangesProps) => {
  const { popularStockExchanges, handleSelectStockExchange, isSelectedStockExchange } = props;

  const renderStockExchangeItem = (stockExchange: StockExchange) => {
    const isSelected = isSelectedStockExchange(stockExchange);

    return (
      <SimpleTooltip key={`${stockExchange.name}_${stockExchange.code}`} text={stockExchange.name}>
        <div
          className={classNames(
            'position-relative d-flex justify-content-center align-items-center rounded-2 cursor-pointer bg-white popular-stock-exchanges__item',
            { selected: isSelected },
          )}
          onClick={() => handleSelectStockExchange(stockExchange)}
        >
          {stockExchange.logo ? (
            <img className='popular-stock-exchanges__logo' src={stockExchange.logo} alt={stockExchange.name} />
          ) : (
            <div className='w-100 h-100 d-flex justify-content-center align-items-center text-center p-3 text-black'>
              {stockExchange.name}
            </div>
          )}
          <Input
            type='checkbox'
            className='position-absolute cursor-pointer popular-stock-exchanges__checkbox'
            checked={isSelected}
            readOnly
          />
        </div>
      </SimpleTooltip>
    );
  };

  return (
    <div className='d-flex w-100 flex-wrap mt-4 popular-stock-exchanges'>
      {popularStockExchanges.map(renderStockExchangeItem)}
    </div>
  );
};
