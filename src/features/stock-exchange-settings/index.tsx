import { Col, Row } from 'reactstrap';
import { useAppSelector } from '@reducers/index';
import { generateUrl } from '@routes/util';
import { currentInitiative } from '@selectors/initiative';
import Dashboard, { DashboardRow, DashboardSection } from '@components/dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { AdminBreadcrumbsProps } from '@g17eco/molecules/breadcrumbs';
import { RouteInterface } from '@g17eco/types/routes';
import { OtherStockExchanges } from './OtherStockExchanges';
import { useGetListOfStockExchangesQuery } from '@api/stock-exchange-settings';
import { categorizeStockExchangesByLocation } from '@utils/stock-exchange-settings';
import { getDefaultConfig } from '../../config/app-config';
import { PopularStockExchanges } from './PopularStockExchanges';
import { useState } from 'react';
import { StockExchange } from '@g17eco/types/stock-exchange-settings';

interface Props {
  baseRoute: RouteInterface;
  BreadcrumbsComponent: (props: AdminBreadcrumbsProps) => JSX.Element;
}

const StockExchangeSettings = (props: Props) => {
  const location = getDefaultConfig().locationWhitelist ?? [];
  const initiative = useAppSelector(currentInitiative);
  const { data: listOfStockExchanges = [], isFetching } = useGetListOfStockExchangesQuery();

  /** @todo: this state is used for demo, in the future we should take from initiative stock exchange settings */
  const [selectedStockExchanges, setSelectedStockExchanges] = useState<StockExchange[]>([]);
  const isSelectedStockExchange = (stockExchange: StockExchange) => {
    return selectedStockExchanges.some((item) => item.code === stockExchange.code);
  };
  const handleSelectStockExchange = (stockExchange: StockExchange) => {
    if (!isSelectedStockExchange(stockExchange)) {
      setSelectedStockExchanges([...selectedStockExchanges, stockExchange]);
    } else {
      setSelectedStockExchanges(selectedStockExchanges.filter((item) => item.code !== stockExchange.code));
    }
  };

  if (!initiative || isFetching) {
    return <Loader />;
  }

  const { baseRoute, BreadcrumbsComponent } = props;
  const breadcrumbs = [
    { label: 'Company Settings', url: generateUrl(baseRoute, { initiativeId: initiative._id }) },
    { label: 'Stock Exchange Settings' },
  ];

  const { popularStockExchanges, otherStockExchanges } = categorizeStockExchangesByLocation(
    listOfStockExchanges,
    location,
  );

  return (
    <Dashboard className='stock-exchange-settings'>
      <DashboardRow>
        <BreadcrumbsComponent breadcrumbs={breadcrumbs} initiativeId={initiative?._id ?? ''} />
      </DashboardRow>
      <DashboardRow>
        <h3 className='pl-2'>Stock Exchange settings</h3>
      </DashboardRow>
      <DashboardSection>
        <Row className='justify-content-center'>
          <Col className='col-md-8 col-12'>
            <h3 className='text-strong text-black'>Select your stock exchange</h3>
            {popularStockExchanges.length > 0 ? (
              <>
                <h4 className='text-black mt-4'>Popular stock exchanges</h4>
                <PopularStockExchanges
                  popularStockExchanges={popularStockExchanges}
                  isSelectedStockExchange={isSelectedStockExchange}
                  handleSelectStockExchange={handleSelectStockExchange}
                />
              </>
            ) : null}
          </Col>
          <Col className='col-12 d-flex justify-content-center flex-wrap'>
            <OtherStockExchanges
              initiative={initiative}
              isLoading={false}
              otherStockExchanges={otherStockExchanges}
              isSelectedStockExchange={isSelectedStockExchange}
              handleSelectStockExchange={handleSelectStockExchange}
            />
          </Col>
        </Row>
      </DashboardSection>
    </Dashboard>
  );
};

export default StockExchangeSettings;
