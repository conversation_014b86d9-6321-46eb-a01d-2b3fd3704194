import { Button, Input, Modal, ModalBody, <PERSON>dal<PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap';

interface AddStockExchangeModalProps {
  isOpen: boolean;
  handleClose: () => void;
}

export const AddStockExchangeModal = (props: AddStockExchangeModalProps) => {
  const { isOpen, handleClose } = props;

  return (
    <Modal isOpen={isOpen} toggle={handleClose} backdrop='static'>
      <ModalHeader toggle={handleClose}>Add stock exchange</ModalHeader>
      <ModalBody>
        <Input name='name' className='w-100 text-md' placeholder='Enter stock exchange name' />
      </ModalBody>
      <ModalFooter className='pt-0'>
        <Button color='link-secondary' className='mr-3' onClick={handleClose}>
          Cancel
        </Button>
        <Button color='primary' onClick={handleClose}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
};
