import { useMemo, useState } from 'react';
import { Col } from 'reactstrap';
import type { InputType } from 'reactstrap/types/lib/Input';
import { useToggle } from '@hooks/useToggle';
import { InitiativeData } from '@g17eco/types/initiative';
import { naturalSort } from '@utils/index';
import { FormGenerator } from '@g17eco/molecules/form';
import { AddStockExchangeModal } from './AddStockExchangeModal';
import { StockExchangeTable } from './StockExchangeTable';
import { StockExchange } from '@g17eco/types/stock-exchange-settings';

const searchFields = [
  {
    code: 'name',
    type: 'text' as InputType,
    required: false,
    label: 'Search for your stock exchange name',
  },
];

const arrangeListOfStockExchanges = (
  stockExchanges: StockExchange[],
  selectedStockExchanges: StockExchange[] | undefined,
) => {
  const selectedCodes = (selectedStockExchanges ?? []).map((stockExchange) => stockExchange.code);

  return [...stockExchanges].sort((stockExchangeA, stockExchangeB) => {
    if (selectedCodes.includes(stockExchangeA.code) && !selectedCodes.includes(stockExchangeB.code)) {
      return -1;
    }
    if (selectedCodes.includes(stockExchangeB.code) && !selectedCodes.includes(stockExchangeA.code)) {
      return 1;
    }
    return naturalSort(stockExchangeA.name ?? '', stockExchangeB.name ?? '');
  });
};

interface OtherBanksProps {
  initiative: InitiativeData;
  otherStockExchanges: StockExchange[];
  isLoading: boolean;
  isSelectedStockExchange: (stockExchange: StockExchange) => boolean;
  handleSelectStockExchange: (stockExchange: StockExchange) => void;
}

const filterStockExchanges = ({ searchText, stockExchange }: { searchText: string; stockExchange: StockExchange }) => {
  return (stockExchange.name ?? '').toLowerCase().includes(searchText.toLowerCase());
};

export const OtherStockExchanges = (props: OtherBanksProps) => {
  const { initiative, otherStockExchanges, isLoading, isSelectedStockExchange, handleSelectStockExchange } = props;

  const [searchText, setSearchText] = useState('');
  const [isTableOpen, toggleTable] = useToggle(false);
  const [isModalOpen, toggleModal] = useToggle(false);

  const filteredStockExchanges = useMemo(() => {
    const filteredResults = searchText
      ? otherStockExchanges.filter((stockExchange) => filterStockExchanges({ searchText, stockExchange }))
      : otherStockExchanges;
    return arrangeListOfStockExchanges(filteredResults, initiative.bankingSettings);
  }, [otherStockExchanges, initiative, searchText]);

  const onChange = (e: React.ChangeEvent<any>) => {
    setSearchText(e.target.value);
  };

  const showTable = isTableOpen || searchText;

  return (
    <>
      <Col className='col-md-8 col-12'>
        <h4 className='text-ThemeTextDark mt-6'>Other stock exchanges</h4>
        <div className='mt-4'>
          <div className='col-sm-6 col-12 text-md'>
            <FormGenerator fields={searchFields} form={{ name: searchText }} updateForm={onChange} />
          </div>
          <div
            className='d-inline-block text-md text-ThemeHeadingLight cursor-pointer text-decoration-underline'
            onClick={() => toggleTable()}
          >
            Browse full stock exchange list
          </div>
          <div className='text-md mt-4'>
            Stock exchange not found?{' '}
            <span className='text-decoration-underline cursor-pointer' onClick={toggleModal}>
              Add it manually
            </span>
          </div>
        </div>
      </Col>
      {showTable ? (
        <StockExchangeTable
          stockExchanges={filteredStockExchanges}
          isLoading={isLoading}
          helperText='Search for a stock exchange to add...'
          isSelectedStockExchange={isSelectedStockExchange}
          handleSelectStockExchange={handleSelectStockExchange}
        />
      ) : null}
      <AddStockExchangeModal isOpen={isModalOpen} handleClose={toggleModal} />
    </>
  );
};
