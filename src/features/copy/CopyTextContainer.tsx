/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import React from 'react';
import './CopyTextContainer.scss';
import { CopyButton } from './CopyButton';

interface Props {
  text: string | undefined;
  className?: string;
}

export const CopyTextContainer = ({ text, className = '' }: Props) => {

  if (!text) {
    return null;
  }

  return <div className={`copy-text-container ${className}`}>
    <p>{text}</p>
    <CopyButton content={text} />
  </div>
}
