import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import React from 'react';
import { Card, CardTitle, CardText } from 'reactstrap';

export interface AdminSettingsCardProps {
  icon?: string;
  text: string | React.ReactNode;
  onClickHandler?: React.MouseEventHandler<HTMLElement>;
  disabledText?: string;
  className?: string;
}

export const AdminSettingsCard = ({
  icon,
  text = '',
  onClickHandler = () => {},
  disabledText = '',
  className = 'p-4',
}: AdminSettingsCardProps) => {
  return (
    <div className='settings-card-container'>
      <SimpleTooltip text={disabledText}>
        <Card
          className={`settings-card ${className} ${disabledText ? 'disabled' : 'active'}`}
          body
          onClick={disabledText ? undefined : onClickHandler}
        >
          {icon ? (
            <CardTitle className='settings-card-icon mb-3'>
              <i className={icon} />
            </CardTitle>
          ) : null}
          <CardText tag={'div'} className='settings-card-text'>
            {text}
          </CardText>
        </Card>
      </SimpleTooltip>
    </div>
  );
};
