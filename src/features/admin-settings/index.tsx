/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Col, Row } from 'reactstrap';
import { AdminSettingsCard, AdminSettingsCardProps } from './AdminSettingsCard';
import Dashboard from '@components/dashboard';
import './styles.scss';

export enum AdminSettingKey {
  CompanySettings = 'company-settings',
  ManageUsers = 'manage-users',
  ManageMetrics = 'manage-metrics',
  ManageSponsorships = 'manage-sponsorships',
  AdminDashboard = 'admin-dashboard',
  DataSharing = 'data-sharing',
  SystemLogs = 'system-logs',
  AppIntegrations = 'app-integrations',
  DocumentLibrary = 'document-library',
}

export interface CardSettings extends Omit<AdminSettingsCardProps, 'onClickHandler'> {
  key: AdminSettingKey;
  handler?: () => void;
}

interface Props {
  cardSettings?: CardSettings[];
}

const AdminSettings = ({ cardSettings = [] }: Props) => {
  return (
    <div>
      <Dashboard hasSidebar={false} className='mt-4'>
        <div className='w-100 px-3 px-lg-0'>
          <h3 className='text-xl mt-0 mb-4'>Admin settings</h3>
          <Row className='admin-settings-container'>
            {cardSettings.map((setting) => {
              return (
                <Col key={setting.key} className='col-12 col-sm-6 col-lg-4'>
                  <AdminSettingsCard
                    onClickHandler={setting.handler}
                    icon={setting.icon}
                    text={setting.text}
                    disabledText={setting.disabledText}
                    className={setting.className}
                  />
                </Col>
              );
            })}
          </Row>
        </div>
      </Dashboard>
    </div>
  );
};

export default AdminSettings;
