/*!
 * Copyright (c) 2021. World Wide Generation Ltd
 */

@import '../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';

.admin-settings-container {
  row-gap: 1rem;

  .col {
    padding: 0 0.5rem;
  }

  .settings-card-container {
    width: 100%;

    .settings-card {
      text-align: center;
      border-color: var(--theme-BorderDefault);

      &-text {
        font-weight: 500;
      }
    }

    .active {
      &:hover {
        background-color: var(--theme-AccentExtralight);
        border-color: var(--theme-AccentMedium);
        cursor: pointer;
        user-select: none;
      }

      .settings-card-icon,
      .settings-card-text {
        color: var(--theme-IconDark);
      }
    }

    .disabled {
      &:hover {
        cursor: not-allowed;
        user-select: none;
      }

      .settings-card-icon,
      .settings-card-text {
        color: var(--theme-BgDisabled);
      }
    }
  }
}