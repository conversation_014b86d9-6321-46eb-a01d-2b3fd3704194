import UniversalTracker from '@models/UniversalTracker';
import { SurveyModelMinimalUtrv } from '@models/surveyData';
import { DisaggregationResponse } from '@api/disaggregation';
import { ColumnDef } from '@tanstack/react-table';
import { ValueAggregation, valueAggregationMap } from '@g17eco/types/disaggregation';
import { MinimalUtrv, UniversalTrackerValueView } from '@features/universal-tracker-value';
import { Table } from '@g17eco/molecules/table';
import { Button } from 'reactstrap';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useHistory } from 'react-router-dom';
import { DATE, formatDateUTC } from '@utils/date';
import { getPeriodName } from '@utils/universalTracker';
import { useAppSelector } from '../../../../reducers';
import { getInitiativeTree } from '@selectors/initiativeTree';
import { getBranchInitiativeNameText } from '@routes/initiative-structure/utils';
import './styles.scss';
import { naturalSort } from '@utils/index';
import { getSupportedValueText, sortBySubsidiary } from './utils';
import { ExtendedHistoryData } from './types';
import { useMemo } from 'react';
import { getStatusIcon } from '@utils/universalTrackerValue';

interface Props {
  utr?: UniversalTracker;
  utrv?: SurveyModelMinimalUtrv;
  data?: DisaggregationResponse;
  aggregatedDate?: string;
}

export const AggregatedBreakdownDetail = (props: Props) => {
  const { utr, utrv, data, aggregatedDate } = props;
  const history = useHistory();
  const tree = useAppSelector(getInitiativeTree);

  const disaggregatedData: ExtendedHistoryData[] = useMemo(() => {
    if (!utr || !utrv || !data) {
      return [];
    }
    return data.disaggregatedData
      .map((rowData, index) => {
        let displayAnswer = '';

        if (index !== 0 && data.valueAggregation === ValueAggregation.LatestAggregator) {
          displayAnswer = 'Ignore';
        } else {
          displayAnswer = getSupportedValueText(rowData, utr);
        }

        return {
          ...rowData,
          displaySubsidiary: getBranchInitiativeNameText({
            initiativeTree: tree,
            initiativeId: rowData.initiativeId,
            showInitiativeId: true,
          }),
          displayDate: formatDateUTC(rowData.effectiveDate, DATE.MONTH_YEAR_SHORT),
          displayPeriod: getPeriodName(rowData.period),
          displayAnswer,
        };
      })
      .sort(sortBySubsidiary);
  }, [data, tree, utr, utrv]);

  if (!utr || !utrv || !data) {
    return null;
  }

  const columns: ColumnDef<ExtendedHistoryData>[] = [
    {
      header: 'Subsidiary',
      accessorFn: (row) => row.displaySubsidiary,
      sortingFn: (a, b) => sortBySubsidiary(a.original, b.original),
    },
    {
      header: 'Date',
      accessorFn: (row) => row.displayDate,
    },
    {
      header: 'Period',
      accessorFn: (row) => row.displayPeriod,
    },
    {
      header: 'Answer',
      accessorFn: (row) => row.displayAnswer,
      cell: ({ row }) => {
        if (row.index !== 0 && data.valueAggregation === ValueAggregation.LatestAggregator) {
          return <div className='text-danger'>Ignore</div>;
        }
        const minimalUtrv: MinimalUtrv = {
          _id: row.original.utrvId,
          status: utrv.status,
          value: row.original.value,
          valueData: row.original.valueData,
        };
        return (
          <div className='aggregated-breakdown__table-answer-col text-truncate'>
            <UniversalTrackerValueView utr={utr} utrv={minimalUtrv} valueType={utr.getValueType()} />
          </div>
        );
      },
      sortingFn: (a, b) =>
        naturalSort(a.original.displayAnswer, b.original.displayAnswer),
    },
    {
      id: 'status',
      cell: ({ row }) => {
        const { utrvId, action: updatedAction, latestHistory } = row.original;
        const action = latestHistory?.action ?? updatedAction;
        return (<i data-testid={`${utrvId}-status`} aria-label={action} className={`fa-light ${getStatusIcon({ status: action })}`} />);
      }
    },
    {
      id: 'view',
      header: '',
      cell: ({ row }) => {
        if (!row.original.surveyId) {
          return null;
        }
        const surveyPath = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
          initiativeId: row.original.initiativeId,
          surveyId: row.original.surveyId,
          page: 'question',
        });
        return (
          <Button color='link' onClick={() => history.push({ pathname: `${surveyPath}/${row.original.utrvId}` })}>
            View
          </Button>
        );
      },
    },
  ];

  const date = formatDateUTC(aggregatedDate ?? utrv.lastUpdated, DATE.AT_TIME)

  return (
    <>
      <p>
        <strong>Data Snapshot</strong>: This report shows a snapshot taken on <strong>{date}</strong>.
        If any metrics have changed since then, please refresh the report to see the latest data.
      </p>
      <p>Aggregation: {valueAggregationMap[data.valueAggregation]}</p>
      <Table responsive={true} columns={columns} data={disaggregatedData} className='aggregated-breakdown__table' />
    </>
  );
};
