/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useMemo } from 'react';
import { UtrvStatus } from '@constants/status';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';

type Props = Readonly<{
  sourceItems: UniversalTrackerValuePlain['sourceItems']
  className?: string
}>;

export function SourceItemWarning({ sourceItems, className }: Props) {
  const { otherStatus, total } = useMemo(() => {
    // We assume that if status is undefined, it must have been verified as it
    // was required to be in that state for it to be used as a source
    const items = sourceItems ?? [];
    return items.reduce((acc, { latestStatus = UtrvStatus.Verified }) => {
      if (latestStatus === UtrvStatus.Verified) {
        acc.isVerified++;
        return acc;
      }
      acc.otherStatus++;
      return acc;
    }, { otherStatus: 0, isVerified: 0, total: items.length });
  }, [sourceItems]);

  if (!otherStatus) {
    // No need to show the warning
    return null;
  }

  const text = total === otherStatus ?
    'All data points used in this metric\'s aggregation are in the submitted state and have not been fully verified' :
    'Partially verified: Some data points used in this metric\'s aggregation are still in the submitted state and have not been fully verified';

  return (
    <div data-testid={'source-item-warning'} className={className}>
      <span className={'text-ThemeWarningMedium'}>
        <i className={'fa-light fa-circle-exclamation'} /> {text}
      </span>
    </div>
  );
}
