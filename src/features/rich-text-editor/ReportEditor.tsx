/*
 * Copyright (c) 2024-2025. World Wide Generation Ltd
 */


import './ReportEditor.scss';
import React, { useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { InitialConfigType, LexicalComposer } from '@lexical/react/LexicalComposer';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { CollaborationPlugin } from '@lexical/react/LexicalCollaborationPlugin';
import { ProviderFactory } from '@g17eco/types/collaboration';
import { HeadingNode } from '@lexical/rich-text';
import { MarkNode } from '@lexical/mark';
import { ListItemNode, ListNode } from '@lexical/list';
import { BeautifulMentionNode, BeautifulMentionsPlugin } from 'lexical-beautiful-mentions';
import { ToolbarPlugin } from './plugins/ToolbarPlugin';
import { CommentPlugin } from './plugins/CommentPlugin';
import { FloatingToolbarPlugin } from './plugins/FloatingToolbarPlugin';
import { UserMin } from '@constants/users';
import { getFullName } from '@utils/user';
import { CustomMenu, CustomMenuItem } from './plugins/MentionPlugin/CustomMenu';
import { CustomTagPlugin } from './plugins/CustomTagPlugin';
import { defaultTheme } from './themes/defaultTheme';
import { beautifulMentionsTheme } from './themes/CommentEditorTheme';
import { handleRouteError } from '../../logger';
import { MappingListResponse } from '@g17eco/types/utr-external-mapping';
import { IXBRLNode } from './plugins/CustomTagPlugin/IXBRLNode';
import { TreeViewPlugin } from '@features/rich-text-editor/plugins/TreeViewPlugin';

interface Props {
  documentId: string;
  providerFactory?: ProviderFactory;
  username: string;
  placeholder?: (isEditable: boolean) => null | React.JSX.Element;
  disabled?: boolean;
  autoFocus?: boolean;
  className?: string;
  users?: UserMin[];
  externalMappings: MappingListResponse;
  debug?: boolean;
}

const editorConfig: InitialConfigType = {
  onError: (error: Error) => {
    handleRouteError(error);
  },
  theme: {
    ...defaultTheme,
    beautifulMentions: beautifulMentionsTheme,
    ixbrlNode: 'ixbrl-plugin-node',
  },
  editorState: null,
  namespace: 'ReportEditor',
  nodes: [
    ListNode,
    ListItemNode,
    HeadingNode,
    MarkNode,
    BeautifulMentionNode,
    IXBRLNode,
  ],
};


export const ReportEditor = (props: Props) => {
  const {
    disabled = false,
    placeholder,
    providerFactory,
    autoFocus,
    documentId,
    className,
    externalMappings,
    debug = false,
  } = props;

  const editorRef  = useRef<HTMLDivElement>(null);
  const [isTagsOpen, setIsTagsOpen] = useState(false);

  const tags = useMemo(() => {
    return externalMappings.mappings.map(mapping => ({
      tagName: mapping.mappingCode,
    }));
  }, [externalMappings.mappings]);


  return (
    <LexicalComposer key={documentId} initialConfig={editorConfig}>
      <div className='editor-container position-relative' data-testid={'report-editor'} ref={editorRef}>
        {disabled ? null : <ToolbarPlugin />}
        <div className={classNames('position-relative', className)} >
          {autoFocus ? <AutoFocusPlugin /> : null}
          <RichTextPlugin
            contentEditable={<ContentEditable data-testid={'report-editor-input'} className='editor-input position-relative px-2 pt-1 pb-3' />}
            ErrorBoundary={LexicalErrorBoundary}
            placeholder={placeholder}
          />
          <ListPlugin />
          <HistoryPlugin />
          {providerFactory ? <CollaborationPlugin
            id={documentId}
            username={props.username}
            providerFactory={providerFactory}
            shouldBootstrap={true}
          />: null}
          {props.users ? <BeautifulMentionsPlugin
            menuComponent={CustomMenu}
            menuItemComponent={CustomMenuItem}
            items={{
              '@': (props.users).map((user) => ({ value: getFullName(user, user._id) }))
            }}
          /> : null}
          <FloatingToolbarPlugin isTagOpen={isTagsOpen} />
          {providerFactory ? <CommentPlugin
            containerRef={editorRef}
            users={props.users}
            commentId={documentId}
            providerFactory={providerFactory}
          />: null}
          <CustomTagPlugin isOpen={isTagsOpen} toggle={(isOpen) => setIsTagsOpen(isOpen)} tags={tags} />
          {debug ? (
            <div className={'my-3'} style={{ overflow: 'auto', background: 'black', color: 'white' }}>
              <TreeViewPlugin />
            </div>
          ) : null}
        </div>
      </div>
    </LexicalComposer>
  );
};
