/*
 * Copyright (c) 2024-2025. World Wide Generation Ltd
 */


import React, { useRef } from 'react';
import classNames from 'classnames';
import { InitialConfigType, LexicalComposer } from '@lexical/react/LexicalComposer';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { CollaborationPlugin } from '@lexical/react/LexicalCollaborationPlugin';
import { ProviderFactory } from '@g17eco/types/collaboration';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { HeadingNode } from '@lexical/rich-text';
import { MarkNode } from '@lexical/mark';
import { ListItemNode, ListNode } from '@lexical/list';
import { BeautifulMentionNode, BeautifulMentionsPlugin } from 'lexical-beautiful-mentions';
import { ToolbarPlugin } from './plugins/ToolbarPlugin';
import { CommentPlugin } from './plugins/CommentPlugin';
import { FloatingToolbarPlugin } from './plugins/FloatingToolbarPlugin';
import { UserMin } from '@constants/users';
import { getFullName } from '@utils/user';
import { CustomMenu, CustomMenuItem } from './plugins/MentionPlugin/CustomMenu';
import { defaultTheme } from './themes/defaultTheme';
import { beautifulMentionsTheme } from './themes/CommentEditorTheme';
import { handleRouteError } from '../../logger';

export interface CollaborationEditorProps {
  documentId: string;
  providerFactory: ProviderFactory;
  username?: string;
  placeholder?: (isEditable: boolean) => null | React.JSX.Element;
  disabled?: boolean;
  autoFocus?: boolean;
  editorType?: 'plain' | 'rich';
  className?: string;
  users?: UserMin[];
}

const editorConfig: InitialConfigType = {
  onError: (error: Error) => {
    handleRouteError(error);
  },
  theme: {
    ...defaultTheme,
    beautifulMentions: beautifulMentionsTheme,
  },
  editorState: null,
  namespace: 'CollaborationEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, MarkNode, BeautifulMentionNode],
};


export const CollaborationEditor = (props: CollaborationEditorProps) => {
  const {
    disabled,
    placeholder,
    providerFactory,
    autoFocus,
    documentId,
    className,
    editorType = 'rich',
  } = props;

  const editorRef  = useRef<HTMLDivElement>(null);
  const EditorComponent = editorType === 'rich' ? RichTextPlugin : PlainTextPlugin;

  const mentionItems = {
    '@': (props.users ?? []).map((user) => ({ value: getFullName(user, user._id) }))
  };

  return (
    <LexicalComposer key={documentId} initialConfig={editorConfig}>
      <div className='editor-container position-relative' ref={editorRef}>
        {disabled || editorType !== 'rich' ? null : <ToolbarPlugin />}
        <div className={classNames('position-relative', className)} >
          {autoFocus ? <AutoFocusPlugin /> : null}
          <EditorComponent
            contentEditable={<ContentEditable className='editor-input position-relative px-2 pt-1 pb-3' />}
            ErrorBoundary={LexicalErrorBoundary}
            placeholder={placeholder}
          />
          <ListPlugin />
          <HistoryPlugin />
          <CollaborationPlugin
            id={documentId}
            username={props.username}
            providerFactory={providerFactory}
            shouldBootstrap={true}
          />
          {props.users ? <BeautifulMentionsPlugin
            menuComponent={CustomMenu}
            menuItemComponent={CustomMenuItem}
            items={mentionItems}
          /> : null}
          {editorRef.current ? <FloatingToolbarPlugin isTagOpen={false} anchorElem={editorRef.current} /> : null}
          <CommentPlugin
            containerRef={editorRef}
            users={props.users}
            commentId={documentId}
            providerFactory={providerFactory}
          />
        </div>
      </div>
    </LexicalComposer>
  );
};
