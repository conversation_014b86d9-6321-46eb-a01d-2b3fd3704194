@mixin editor-icon {
  background-color: var(--theme-NeutralsDark);
  border-color: var(--theme-NeutralsDark);
}

.editor-container {
  border: 1px solid var(--theme-BorderDefault);
  border-radius: 4px;

  &.read-only {
    border: none;
  }

  .ltr {
    text-align: left;
  }

  .rtl {
    text-align: right;
  }

  .editor-input {
    min-height: 150px;
    outline: 0;
  }

  .editor-placeholder {
    color: var(--theme-TextPlaceholder);
    overflow: hidden;
    text-overflow: ellipsis;
    top: 0.25rem;
    left: 0.5rem;
    user-select: none;
    display: inline-block;
    pointer-events: none;
  }

  .editor-text-bold {
    font-weight: bold;
  }

  .editor-text-italic {
    font-style: italic;
  }

  .editor-text-underline {
    text-decoration: underline;
  }

  .editor-paragraph {
    margin: 0;
    margin-bottom: 1rem;
    position: relative;
  }

  .editor-paragraph:last-child {
    margin-bottom: 0;
  }

  .editor-list-ol {
    padding: 0;
    margin: 0;
    margin-left: 16px;
  }

  .editor-list-ul {
    padding: 0;
    margin: 0;
    margin-left: 16px;
  }

  .editor-listitem {
    margin: 8px 32px 8px 32px;
  }

  .editor-nested-listitem {
    list-style-type: none;
  }

  .toolbar {
    button.active.btn.btn-transparent {
      @include editor-icon;
    }
  }

  h1,
  h3 {
    color: var(--theme-HeadingMedium);
  }
}

.editor__toolbar__submenu {
  button.active.btn.btn-transparent {
    @include editor-icon;
  }
}
