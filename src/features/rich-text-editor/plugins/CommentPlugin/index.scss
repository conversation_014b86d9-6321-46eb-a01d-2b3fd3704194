/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

@import "../../../../css/variables";


$zIndexCommentInputBox: 24;

.CommentPlugin_CommentInputBox {
  display: block;
  position: absolute;
  width: 250px;
  min-height: 80px;
  background-color: #fff;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: $zIndexCommentInputBox;
  animation: show-input-box 0.4s ease;

  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    margin-left: 0.5em;
    right: -1em;
    top: 0;
    left: calc(50% + 0.25em);
    box-sizing: border-box;
    border: 0.5em solid black;
    border-color: transparent transparent #fff #fff;
    transform-origin: 0 0;
    transform: rotate(135deg);
    box-shadow: -3px 3px 3px 0 rgba(0, 0, 0, 0.05);
  }

  .editor-placeholder {
    color: var(--theme-TextPlaceholder);
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
    padding: 0.5rem;
    display: inline-block;
    pointer-events: none;
  }

  .CommentPlugin_CommentInputBox_Editor {
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 5px;
    min-height: 80px;
    padding: 0.5rem;

    &:focus {
      outline: 1px solid rgb(66, 135, 245);
    }
  }
}

.CommentPlugin-mention-menu {
  z-index: $zIndexCommentInputBox + 1;
  position: relative;
  min-width: 200px;

  border: 1px solid #ccc;
  background-color: $colorColorsWhite;
  border-radius: 5px;

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
      padding: 0.5rem;
      cursor: pointer;
      transition: background-color 0.2s linear;

      &:hover {
        background-color: $colorColorsGrey100;
      }

      &.active {
        background-color: $colorColorsGrey100;
      }
    }
  }
}

@keyframes show-input-box {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.CommentPlugin_ShowCommentsButton {
  position: absolute;
  top: 4px;
  right: -40px;
  @media (max-width: 1024px) {
    display: none;
  }
}

.CommentPlugin_CommentsPanel {
  position: absolute;
  right: -310px;
  width: 300px;
  top: 0;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  animation: show-comments 0.2s ease;
  z-index: 25;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1rem;

  .CommentPlugin_CommentsPanel_Empty {
    display: flex;
    color: #777;
    padding: 1rem;
  }

  .CommentPlugin_CommentsPanel_List {
    padding: 0;
    list-style-type: none;
    margin: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    color: var(--theme-TextDark);
  }
}

@keyframes show-comments {
  0% {
    opacity: 0;
    transform: translateX(300px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.CommentPlugin_CommentsPanel_Container {

  display: none;
  &.active {
    display: inherit;
  }

  .CommentPlugin_CommentsPanel_Editor {
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 5px;
    caret-color: rgb(5, 5, 5);
    position: relative; // for the placeholder
    padding: 0.5rem;
    display: block;
    width: 100%;
  }

  .editor-placeholder {
    color: var(--theme-TextPlaceholder);
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
    padding-top: 0.25rem;
    padding-left: 0.2rem;
    display: inline-block;
    pointer-events: none;
  }
}

.CommentPlugin_CommentsPanel_List_Comment {

  position: relative;

  .comment-delete-wrapper {
    background: rgb(140 140 140 / 20%);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(5px);
    pointer-events: visible;
    display: flex;
    align-items: center;

    &-content {
      padding: 1rem;
      width: 100%;
    }
  }


  .comment-delete-button {
    cursor: pointer;
    align-self: flex-end;
  }

  &:hover {
    .comment-delete-button {
      display: inline-block;
    }
  }

  .comment-content {
    word-break: break-word;
  }
}

.CommentPlugin_CommentsPanel_List_Thread {
  padding: 0.5rem;
  border-radius: 12px; // All $border-radius are the same in overrides? bug?
  margin: 0.5rem 0;
  border: 1px solid #eee;
  position: relative;
  transition: all 0.2s linear;

  &.interactive {
    cursor: pointer;
    background-color: $colorThemeAccentExtralight;

    &:hover {
      background-color: $colorThemeBgMedium;
    }

    &.active {
      background-color: $colorColorsWhite;
    }
  }

  &.active {
    background-color: $colorColorsWhite;
    cursor: inherit;

    .CommentPlugin_CommentsPanel_List_Comment:hover {
      background-color: inherit;
    }
  }


  blockquote {
    min-width: 0; // Makes text-truncate work
  }

  .delete-wrapper {
    background: rgb(140 140 140 / 20%);
    position: absolute;
    top: 0;
    left: 0;
    margin-top: 0.5rem;
    margin-left: 0.5rem;
    height: calc(100% - 1rem);
    width: calc(100% - 1rem);
    border-radius: 12px;
    box-sizing: border-box;
    z-index: 30;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;

    &-content {
      padding: 1rem;
      width: 100%;
    }
  }

  .CommentPlugin_CommentsPanel_List_Thread_Comments {
    padding-left: 10px;
    list-style-type: none;

    .CommentPlugin_CommentsPanel_List_Comment {
      border-left: 5px solid $colorThemeAccentExtradark;
      margin-left: 5px;
      padding: 15px 0 15px 15px;
      transition: all 0.2s linear;

      &:first-child {
        border: none;
        padding-left: 0;
        margin-left: 0;
      }
    }
  }
}
