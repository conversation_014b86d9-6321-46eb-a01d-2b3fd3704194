/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useRef } from 'react';
import { CommentsPanelList, PanelListProps } from './CommentsPanelList';
import IconButton from '@components/button/IconButton';


type Props = Omit<PanelListProps, 'listRef'> & {
  closePanel: () => void;
};

export function CommentsPanel(props: Props): React.JSX.Element {
  const { activeIDs, comments, deleteCommentOrThread, submitAddComment, markNodeMap } = props;

  const listRef = useRef<HTMLUListElement>(null);
  const isEmpty = comments.length === 0;

  const className = 'd-flex align-items-center py-2 justify-content-between border-bottom border-ThemeNeutralsLight';
  return (
    <div className='CommentPlugin_CommentsPanel'>
      <div className={className}>
        <h5 className='ps-3 flex-grow-1 fw-bold'>Comments</h5>
        <IconButton
          color={'link-secondary'}
          outline={false}
          tooltip={'Close'}
          icon={'fal fa-times'}
          onClick={props.closePanel} />
      </div>
      {isEmpty ? (
        <div className='CommentPlugin_CommentsPanel_Empty'>No Comments</div>
      ) : (
        <CommentsPanelList
          activeIDs={activeIDs}
          comments={comments}
          deleteCommentOrThread={deleteCommentOrThread}
          listRef={listRef}
          submitAddComment={submitAddComment}
          markNodeMap={markNodeMap}
        />
      )}
    </div>
  );
}
