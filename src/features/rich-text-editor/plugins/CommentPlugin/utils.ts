/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useCallback } from 'react';
import { EditorState, LexicalEditor } from 'lexical';
import { $isRootTextContentEmpty, $rootTextContent } from '@lexical/text';
import { Comment, Thread } from './types';

export function useOnChange(
  setContent: (text: string) => void,
  setCanSubmit: (canSubmit: boolean) => void,
) {
  return useCallback(
    (editorState: EditorState, _editor: LexicalEditor) => {
      editorState.read(() => {
        setContent($rootTextContent());
        setCanSubmit(!$isRootTextContentEmpty(_editor.isComposing(), true));
      });
    },
    [setCanSubmit, setContent],
  );
}

interface CommentCreate extends Partial<Omit<Comment, 'content' | 'author'>> {
  content: string;
  author: string;
}

export function createUID(): string {
  return Math.random()
    .toString(36)
    .replace(/[^a-z]+/g, '')
    .substr(0, 5);
}

export function createComment({ content, author, id, timeStamp, deleted, userId }: CommentCreate): Comment {
  return {
    author,
    content,
    deleted: deleted === undefined ? false : deleted,
    id: id === undefined ? createUID() : id,
    timeStamp: timeStamp === undefined ? performance.timeOrigin + performance.now() : timeStamp,
    type: 'comment',
    userId,
  };
}

export function cloneThread(thread: Thread): Thread {
  return {
    comments: Array.from(thread.comments),
    id: thread.id,
    quote: thread.quote,
    type: 'thread',
  };
}

export function createThread(
  quote: string,
  comments: Array<Comment>,
  id?: string,
): Thread {
  return {
    comments,
    id: id === undefined ? createUID() : id,
    quote,
    type: 'thread',
  };
}

export function markDeleted(comment: Comment): Comment {
  return {
    ...comment,
    content: '[Deleted Comment]',
    deleted: true,
  };
}
