/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import * as React from 'react';
import { Button } from 'reactstrap';
import { Comment, Thread } from './types';

type Props = {
  commentOrThread: Comment | Thread;
  deleteCommentOrThread: (comment: Comment | Thread, thread?: Thread) => void;
  onClose: () => void;
  thread?: Thread;
};

export function ShowDeleteCommentOrThreadDialog(props: Props): React.JSX.Element {
  const { commentOrThread, deleteCommentOrThread, onClose, thread } = props;

  return (
    <>
      <div className={'mb-3 text-center'}>
        Delete {commentOrThread.type === 'thread' ? 'comment thread' : 'comment'}?
      </div>
      <div className='d-flex justify-content-evenly'>
        <Button
          className={'me-2'}
          color={'secondary'}
          onClick={() => {
            onClose();
          }}>
          Cancel
        </Button>
        <Button
          color={'danger'}
          onClick={() => {
            deleteCommentOrThread(commentOrThread, thread);
            onClose();
          }}>
          Delete
        </Button>
      </div>
    </>
  );
}
