/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useContext } from 'react';
import { DropdownItem, DropdownMenu, DropdownToggle, UncontrolledDropdown } from 'reactstrap';
import classNames from 'classnames';

import { ShowDeleteCommentOrThreadDialog } from './ShowDeleteCommentOrThreadDialog';
import { DATE, formatDateNonUTC, formatDateUTC } from '@utils/date';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { CollaborationAvatar } from '@g17eco/atoms/avatar';
import { CommentContext } from './CommentContext';
import { getFullName } from '@utils/user';
import { Comment, Thread } from './types';

interface Props {
  comment: Comment;
  deleteComment: (commentOrThread: Comment | Thread, thread?: Thread) => void;
  thread?: Thread;
}

export function CommentsPanelListComment({ comment, deleteComment, thread }: Props): React.JSX.Element | null {
  const seconds = Math.round((comment.timeStamp - (performance.timeOrigin + performance.now())) / 1000);
  const [toDeleteComment, setDeleteComment] = React.useState<Comment | undefined>(undefined);
  const { users } = useContext(CommentContext)

  const user = comment.userId ?
    users.find((user) => user._id === comment.userId) :
    undefined;
  const fullName = getFullName(user ?? {}, comment.author);

  if (comment.deleted) {
    return null; // Do not display deleted
  }

  return (
    <li className='m-0 CommentPlugin_CommentsPanel_List_Comment'>
      <div className='d-flex pb-2 align-items-center justify-content-between'>
        <CollaborationAvatar className={'me-2'} name={fullName} profile={user?.profile} />
        <div className={'d-flex flex-column flex-grow-1'}>
          <span className='fw-bold'>{fullName}</span>
          <div>
            <SimpleTooltip className={'text-sm'} text={`${formatDateUTC(comment.timeStamp, DATE.SORTABLE)} UTC`}>
              <span className='text-ThemeNeutralsExtradark'>
                {seconds > -10 ? 'Just now' : formatDateNonUTC(comment.timeStamp, DATE.HUMANIZE)}
              </span>
            </SimpleTooltip>
          </div>
        </div>
        {!comment.deleted && (
          <UncontrolledDropdown>
            <DropdownToggle color='transparent'>
              <i className={'fal fa-ellipsis-vertical'} />
            </DropdownToggle>
            <DropdownMenu>
              <DropdownItem disabled={true}>
                <span><i className={'fal fa-pencil-alt me-2'} /> Edit</span>
              </DropdownItem>
              <DropdownItem onClick={() => setDeleteComment(comment)}>
                <i className={'fal fa-trash-alt me-2'} /> Delete
              </DropdownItem>
            </DropdownMenu>
          </UncontrolledDropdown>
        )}
      </div>
      <p className={classNames('m-0 p-2 comment-content', { 'text-ThemeBgExtradark' : comment.deleted })}>
        {comment.content}
      </p>
      {toDeleteComment === comment  ? (
        <div key={comment.id} className={'comment-delete-wrapper'}>
          <div className={'delete-wrapper-content'}>
            <ShowDeleteCommentOrThreadDialog
              commentOrThread={comment}
              thread={thread}
              deleteCommentOrThread={deleteComment}
              onClose={() => setDeleteComment(undefined)} />
          </div>
        </div>
      ) : null}
    </li>
  );
}
