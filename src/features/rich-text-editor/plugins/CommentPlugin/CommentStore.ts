/*
 * Copyright (c) 2024-2025. World Wide Generation Ltd
 */

import type { LexicalEditor } from 'lexical';
import { COMMAND_PRIORITY_LOW } from 'lexical';
import { TOGGLE_CONNECT_COMMAND } from '@lexical/yjs';
import { Array as YArray, Map as YMap, Transaction, YArrayEvent, YEvent, } from 'yjs';
import { HocuspocusProvider } from '@hocuspocus/provider';
import { cloneThread, createComment, createThread, markDeleted } from './utils';
import { Comment, Comments, Thread } from './types';


// Alias in case we change it
type CollabProvider = HocuspocusProvider;
type CollabStorageMap = YMap<any>;

export function triggerOnChange(commentStore: CommentStore): void {
  const listeners = commentStore._changeListeners;
  for (const listener of listeners) {
    listener();
  }
}

export class CommentStore {
  _editor: LexicalEditor;
  _comments: Comments;
  _changeListeners: Set<() => void>;
  _collabProvider: null | CollabProvider;

  constructor(editor: LexicalEditor) {
    this._comments = [];
    this._editor = editor;
    this._collabProvider = null;
    this._changeListeners = new Set();
  }

  isCollaborative(): boolean {
    return this._collabProvider !== null;
  }

  getComments(): Comments {
    return this._comments;
  }

  getCollabDocument() {
    if (!this._collabProvider) {
      return null;
    }

    return this._collabProvider?.document
  }

  addComment(
    commentOrThread: Comment | Thread,
    thread?: Thread,
    offset?: number,
  ): void {
    const nextComments = Array.from(this._comments);
    const sharedCommentsArray = this._getCollabComments();

    if (thread !== undefined && commentOrThread.type === 'comment') {
      for (let i = 0; i < nextComments.length; i++) {
        const comment = nextComments[i];
        if (comment.type === 'thread' && comment.id === thread.id) {
          const newThread = cloneThread(comment);
          nextComments.splice(i, 1, newThread);
          const insertOffset = offset !== undefined ? offset : newThread.comments.length;

          if (this.isCollaborative() && sharedCommentsArray !== null) {
            const parentSharedArray = sharedCommentsArray.get(i).get('comments');
            this._withRemoteTransaction(() => {
              const sharedMap = this._createCollabSharedMap(commentOrThread);
              parentSharedArray.insert(insertOffset, [sharedMap]);
            });
          }

          newThread.comments.splice(insertOffset, 0, commentOrThread);
          break;
        }
      }
    } else {
      const insertOffset = offset !== undefined ? offset : nextComments.length;
      if (this.isCollaborative() && sharedCommentsArray !== null) {
        this._withRemoteTransaction(() => {
          const sharedMap = this._createCollabSharedMap(commentOrThread);
          sharedCommentsArray.insert(insertOffset, [sharedMap]);
        });
      }
      nextComments.splice(insertOffset, 0, commentOrThread);
    }
    this._comments = nextComments;
    triggerOnChange(this);
  }

  deleteCommentOrThread(
    commentOrThread: Comment | Thread,
    thread?: Thread,
  ): { markedComment: Comment; index: number } | null {
    const nextComments = Array.from(this._comments);
    const sharedCommentsArray = this._getCollabComments();
    let commentIndex: number | null = null;

    if (thread !== undefined) {
      for (let i = 0; i < nextComments.length; i++) {
        const nextComment = nextComments[i];
        if (nextComment.type === 'thread' && nextComment.id === thread.id) {
          const newThread = cloneThread(nextComment);
          nextComments.splice(i, 1, newThread);
          const threadComments = newThread.comments;
          commentIndex = threadComments.indexOf(commentOrThread as Comment);
          if (this.isCollaborative() && sharedCommentsArray !== null) {
            const parentSharedArray = sharedCommentsArray
              .get(i)
              .get('comments');
            this._withRemoteTransaction(() => {
              parentSharedArray.delete(commentIndex);
            });
          }
          threadComments.splice(commentIndex, 1);
          break;
        }
      }
    } else {
      commentIndex = nextComments.indexOf(commentOrThread);
      if (this.isCollaborative() && sharedCommentsArray !== null) {
        this._withRemoteTransaction(() => {
          sharedCommentsArray.delete(commentIndex as number);
        });
      }
      nextComments.splice(commentIndex, 1);
    }
    this._comments = nextComments;
    triggerOnChange(this);

    if (commentOrThread.type === 'comment') {
      return {
        index: commentIndex as number,
        markedComment: markDeleted(commentOrThread),
      };
    }

    return null;
  }

  registerOnChange(onChange: () => void): () => void {
    const changeListeners = this._changeListeners;
    changeListeners.add(onChange);
    return () => {
      changeListeners.delete(onChange);
    };
  }

  _withRemoteTransaction(fn: () => void): void {
    const doc = this.getCollabDocument();
    if (doc) {
      doc.transact(fn, this);
    }
  }

  _withLocalTransaction(fn: () => void): void {
    const collabProvider = this._collabProvider;
    try {
      this._collabProvider = null;
      fn();
    } finally {
      this._collabProvider = collabProvider;
    }
  }

  private _getCollabComments() {
    const doc = this.getCollabDocument();
    const comments = doc?.getArray('comments');
    if (comments) {
      return comments as YArray<any>;
    }
    return null;
  }

  private _createCollabSharedMap(commentOrThread: Comment | Thread): YMap<any> {
    const sharedMap = new YMap();
    const type = commentOrThread.type;
    const id = commentOrThread.id;
    sharedMap.set('type', type);
    sharedMap.set('id', id);
    if (type === 'comment') {
      sharedMap.set('author', commentOrThread.author);
      sharedMap.set('userId', commentOrThread.userId);
      sharedMap.set('content', commentOrThread.content);
      sharedMap.set('deleted', commentOrThread.deleted);
      sharedMap.set('timeStamp', commentOrThread.timeStamp);
    } else {
      sharedMap.set('quote', commentOrThread.quote);
      const commentsArray = new YArray();
      commentOrThread.comments.forEach((comment, i) => {
        const sharedChildComment = this._createCollabSharedMap(comment);
        commentsArray.insert(i, [sharedChildComment]);
      });
      sharedMap.set('comments', commentsArray);
    }
    return sharedMap;
  }

  registerCollaboration(provider: CollabProvider): () => void {
    this._collabProvider = provider;
    const sharedCommentsArray = this._getCollabComments();

    const connect = () => {
      provider.connect();
    };

    const disconnect = () => {
      try {
        provider.disconnect();
      } catch (e) {
        // Do nothing
      }
    };

    const unsubscribe = this._editor.registerCommand(
      TOGGLE_CONNECT_COMMAND,
      (payload) => {
        if (connect !== undefined && disconnect !== undefined) {
          if (payload) {
            console.log('Comments connected!');
            connect();
          } else {
            console.log('Comments disconnected!');
            disconnect();
          }
        }
        return false;
      },
      COMMAND_PRIORITY_LOW,
    );

    if (sharedCommentsArray === null) {
      return () => null;
    }

    const onSharedCommentChanges = (
      events: Array<YEvent<any>>,
      transaction: Transaction,
    ) => {
      if (transaction.origin === this) {
        return;
      }

      for (const event of events) {
        if (!(event instanceof YArrayEvent)) {
          continue;
        }

        const target = event.target;
        const deltas = event.delta;
        let offset = 0;
        for (const delta of deltas) {
          const insert = delta.insert;
          const retain = delta.retain;
          const del = delta.delete;
          const parent = target.parent;
          const parentThread =
            target === sharedCommentsArray
              ? undefined
              : parent instanceof YMap &&
              (this._comments.find((t) => t.id === parent.get('id')) as
                | Thread
                | undefined);

          if (Array.isArray(insert)) {
            insert
              .slice()
              .reverse()
              .forEach((map: YMap<any>) => {
                const id = map.get('id');
                const type = map.get('type');

                const commentOrThread =
                  type === 'thread'
                    ? createThread(
                      map.get('quote'),
                      map
                        .get('comments')
                        .toArray()
                        .map((innerComment: CollabStorageMap) => this.getComment(innerComment)),
                      id,
                    )
                    : this.getComment(map);

                this._withLocalTransaction(() => {
                  this.addComment(
                    commentOrThread,
                    parentThread as Thread,
                    offset,
                  );
                });
              });
          } else if (typeof retain === 'number') {
            offset += retain;
          } else if (typeof del === 'number') {
            for (let d = 0; d < del; d++) {
              const commentOrThread =
                parentThread === undefined || parentThread === false
                  ? this._comments[offset]
                  : parentThread.comments[offset];

              this._withLocalTransaction(() => {
                this.deleteCommentOrThread(commentOrThread, parentThread as Thread);
              });
              offset++;
            }
          }
        }
      }
    };

    sharedCommentsArray.observeDeep(onSharedCommentChanges);

    connect();

    return () => {
      sharedCommentsArray.unobserveDeep(onSharedCommentChanges);
      unsubscribe();
      this._collabProvider = null;
    };
  }

  private getComment = (map: YMap<any>) => {
    return createComment({
      content: map.get('content') as string,
      userId: map.get('userId') as string,
      author: map.get('author') as string,
      id: map.get('id') as string,
      timeStamp: map.get('timeStamp') as number,
      deleted: map.get('deleted') as boolean,
    });
  }
}

