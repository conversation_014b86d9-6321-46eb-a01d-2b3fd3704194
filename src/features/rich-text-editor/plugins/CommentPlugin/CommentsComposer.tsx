/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useRef, useState } from 'react';
import classnames from 'classnames';
import { Button } from 'reactstrap';
import { CLEAR_EDITOR_COMMAND, LexicalEditor } from 'lexical';
import { useCollaborationContext } from '@lexical/react/LexicalCollaborationContext';
import { createComment, useOnChange } from './utils';
import { CommentTextEditor } from './CommentTextEditor';
import { useCommentContext } from './CommentContext';
import type { SubmitAddComment, Thread } from './types';


type CommentsComposerProps = {
  submitAddComment: SubmitAddComment;
  thread?: Thread;
  active?: boolean;
};

export function CommentsComposer({ submitAddComment, thread, active }: CommentsComposerProps) {
  const [content, setContent] = useState('');
  const [canSubmit, setCanSubmit] = useState(false);
  const editorRef = useRef<LexicalEditor>(null);
  const { name: author } = useCollaborationContext();
  const { currentUser } = useCommentContext();

  const onChange = useOnChange(setContent, setCanSubmit);

  const submitComment = () => {
    if (canSubmit) {
      submitAddComment(createComment({ content: content, author: author, userId: currentUser?._id }), false, thread);
      const editor = editorRef.current;
      if (editor !== null) {
        editor.dispatchCommand(CLEAR_EDITOR_COMMAND, undefined);
      }
    }
  };

  return (
    <div className={classnames('m-2 CommentPlugin_CommentsPanel_Container', { active })}>
      <CommentTextEditor
        className='mb-3 text-sm CommentPlugin_CommentsPanel_Editor'
        onEscape={() => true}
        onChange={onChange}
        editorRef={editorRef}
      />
      <div className='text-right'>
        <Button color={'transparent'} outline={true} onClick={submitComment} disabled={!canSubmit}>
          Reply
        </Button>
      </div>
    </div>
  );
}
