/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import { $getNodeByKey, NodeKey } from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $isMarkNode, MarkNode } from '@lexical/mark';
import { ShowDeleteCommentOrThreadDialog } from './ShowDeleteCommentOrThreadDialog';
import { CommentsPanelListComment } from './CommentsPanelListComment';
import { CommentsComposer } from './CommentsComposer';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import IconButton from '@components/button/IconButton';
import classnames from 'classnames';
import { Comment, Comments, SubmitAddComment, Thread } from './types';


export type PanelListProps = {
  activeIDs: Array<string>;
  comments: Comments;
  deleteCommentOrThread: (commentOrThread: Comment | Thread, thread?: Thread) => void;
  listRef: { current: null | HTMLUListElement };
  markNodeMap: Map<string, Set<NodeKey>>;
  submitAddComment: SubmitAddComment;
};

export function CommentsPanelList(props: PanelListProps): React.JSX.Element {

  const {
    activeIDs,
    comments,
    deleteCommentOrThread,
    listRef,
    submitAddComment,
    markNodeMap,
  } = props;

  const [editor] = useLexicalComposerContext();
  const [counter, setCounter] = useState(0);
  const [deleteThread, setDeleteThread] = useState<Thread>();

  useEffect(() => {
    // Used to keep the time stamp up to date
    const id = setTimeout(() => {
      setCounter(counter + 1);
    }, 10000);

    return () => {
      clearTimeout(id);
    };
  }, [counter]);

  return (
    <ul className='CommentPlugin_CommentsPanel_List' ref={listRef}>
      {comments.map((commentOrThread) => {
        const id = commentOrThread.id;
        if (commentOrThread.type === 'thread') {
          const handleClickThread = () => {
            const markNodeKeys = markNodeMap.get(id);
            if (markNodeKeys !== undefined && activeIDs.indexOf(id) === -1) {
              const activeElement = document.activeElement;
              // Move selection to the start of the mark, so that we
              // update the UI with the selected thread.
              editor.update(
                () => {
                  const markNodeKey = Array.from(markNodeKeys)[0];
                  const markNode = $getNodeByKey<MarkNode>(markNodeKey);
                  if ($isMarkNode(markNode)) {
                    markNode.selectStart();
                  }
                },
                {
                  onUpdate() {
                    // Restore selection to the previous element
                    if (activeElement !== null) {
                      (activeElement as HTMLElement).focus();
                    }
                  },
                },
              );
            }
          };

          const isActive = activeIDs.indexOf(id) !== -1;
          return (
            <li
              key={id}
              onClick={handleClickThread}
              className={classnames('CommentPlugin_CommentsPanel_List_Thread shadow-md-alt', {
                interactive: markNodeMap.has(id),
                active: isActive,
              })}>
              <div style={{ color: '#ccc' }} className='d-flex align-items-center justify-content-between pt-1'>
                <blockquote className='d-flex align-items-center px-2 mb-0 w-100'>
                  <span
                    style={{ backgroundColor: '#FFD40066', padding: '0.1rem' }}
                    className={'mx-2 px-1 text-truncate fw-bold text-dark'}>
                    {commentOrThread.quote}
                  </span>
                </blockquote>
                {/* INTRODUCE RESOLVE THREAD HERE - @TODO change to resolve*/}
                <SimpleTooltip text={'Mark as resolved'} className={'pe-1'}>
                  <IconButton
                    color={'transparent-link'}
                    icon={'fa-solid fa-check text-success cursor-pointer'}
                    onClick={() => {
                      setDeleteThread(commentOrThread);
                    }}
                  />
                </SimpleTooltip>
              </div>
              <ul className='CommentPlugin_CommentsPanel_List_Thread_Comments mb-1'>
                {commentOrThread.comments.map((comment) => (
                  <CommentsPanelListComment
                    key={comment.id}
                    comment={comment}
                    deleteComment={deleteCommentOrThread}
                    thread={commentOrThread}
                  />
                ))}
              </ul>
              <CommentsComposer active={isActive} submitAddComment={submitAddComment} thread={commentOrThread} />
              {deleteThread === commentOrThread  ? (
                <div key={deleteThread.id} className={'delete-wrapper'}>
                  <div className={'delete-wrapper-content'}>
                    <ShowDeleteCommentOrThreadDialog
                      commentOrThread={deleteThread}
                      deleteCommentOrThread={deleteCommentOrThread}
                      onClose={() => setDeleteThread(undefined)} />
                  </div>
                </div>
              ) : null}
            </li>
          );
        }
        return (
          <CommentsPanelListComment
            key={id}
            comment={commentOrThread}
            deleteComment={deleteCommentOrThread}
          />
        );
      })}
    </ul>
  );
}
