/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { $getSelection, $isRangeSelection, LexicalEditor, RangeSelection } from 'lexical';
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { CommentTextEditor } from './CommentTextEditor';
import { Button } from 'reactstrap';
import { createDOMRange, createRectsFromDOMRange } from '@lexical/selection';
import { createComment, createThread, useOnChange } from './utils';
import { useCollaborationContext } from '@lexical/react/LexicalCollaborationContext';
import { useCommentContext } from './CommentContext';
import { Comment, Thread } from './types';

type SubmitAddCommentFn = (
  commentOrThread: Comment | Thread,
  isInlineComment: boolean,
  thread?: Thread,
  selection?: RangeSelection | null,
) => void;

type CommentInputBoxProps = {
  cancelAddComment: () => void;
  editor: LexicalEditor;
  submitAddComment: SubmitAddCommentFn;
};

export function CommentInputBox({ editor, cancelAddComment, submitAddComment }: CommentInputBoxProps) {
  const [content, setContent] = useState('');
  const [canSubmit, setCanSubmit] = useState(false);
  const boxRef = useRef<HTMLDivElement>(null);
  const selectionState = useMemo(
    () => ({
      container: document.createElement('div'),
      elements: [],
    }),
    [],
  );
  const selectionRef = useRef<RangeSelection | null>(null);
  const { name: author } = useCollaborationContext();
  const { currentUser } = useCommentContext();

  const updateLocation = useCallback(() => {
    editor.getEditorState().read(() => {
      const selection = $getSelection();

      if ($isRangeSelection(selection)) {
        selectionRef.current = selection.clone();
        const anchor = selection.anchor;
        const focus = selection.focus;
        const range = createDOMRange(
          editor,
          anchor.getNode(),
          anchor.offset,
          focus.getNode(),
          focus.offset,
        );
        const boxElem = boxRef.current;
        if (range !== null && boxElem !== null) {
          const { left, bottom, width } = range.getBoundingClientRect();
          const selectionRects = createRectsFromDOMRange(editor, range);
          let correctedLeft =
            selectionRects.length === 1 ? left + width / 2 - 125 : left - 125;
          if (correctedLeft < 10) {
            correctedLeft = 10;
          }
          boxElem.style.left = `${correctedLeft}px`;
          boxElem.style.top = `${
            bottom +
            20 +
            (window.pageYOffset || document.documentElement.scrollTop)
          }px`;
          const selectionRectsLength = selectionRects.length;
          const { container } = selectionState;
          const elements: Array<HTMLSpanElement> = selectionState.elements;
          const elementsLength = elements.length;

          for (let i = 0; i < selectionRectsLength; i++) {
            const selectionRect = selectionRects[i];
            let elem: HTMLSpanElement = elements[i];
            if (elem === undefined) {
              elem = document.createElement('span');
              elements[i] = elem;
              container.appendChild(elem);
            }
            const color = '255, 212, 0';

            const top = selectionRect.top + (window.pageYOffset || document.documentElement.scrollTop);

            // @TODO: Investigate why need to add 5 (something is adjusting coordinates) @KK
            const left = selectionRect.left + 5;
            elem.style.cssText = `position:absolute;top:${top}px;left:${left}px;height:${
              selectionRect.height
            }px;width:${
              selectionRect.width
            }px;background-color:rgba(${color}, 0.3);pointer-events:none;z-index:5;`;
          }
          for (let i = elementsLength - 1; i >= selectionRectsLength; i--) {
            const elem = elements[i];
            container.removeChild(elem);
            elements.pop();
          }
        }
      }
    });
  }, [editor, selectionState]);

  useLayoutEffect(() => {
    updateLocation();
    const container = selectionState.container;
    const body = document.body;
    if (body !== null) {
      body.appendChild(container);
      return () => {
        body.removeChild(container);
      };
    }
  }, [selectionState.container, updateLocation]);

  useEffect(() => {
    window.addEventListener('resize', updateLocation);

    return () => {
      window.removeEventListener('resize', updateLocation);
    };
  }, [updateLocation]);

  const onEscape = (event: KeyboardEvent): boolean => {
    event.preventDefault();
    cancelAddComment();
    return true;
  };

  const submitComment = () => {
    if (canSubmit) {
      let quote = editor.getEditorState().read(() => {
        const selection = selectionRef.current;
        return selection ? selection.getTextContent() : '';
      });
      if (quote.length > 100) {
        quote = quote.slice(0, 99) + '…';
      }
      submitAddComment(
        createThread(quote, [createComment({content : content, author : author, userId: currentUser?._id })]),
        true,
        undefined,
        selectionRef.current,
      );
      selectionRef.current = null;
    }
  };

  const onChange = useOnChange(setContent, setCanSubmit);

  return (
    <div className='CommentPlugin_CommentInputBox p-3' ref={boxRef}>
      <CommentTextEditor
        className='d-flex flex-grow-1 mb-3 text-sm CommentPlugin_CommentInputBox_Editor'
        onEscape={onEscape}
        onChange={onChange}
      />
      <div className='d-flex justify-content-between'>
        <Button color={'link-secondary'} onClick={cancelAddComment}>
          Close
        </Button>
        <Button color={'transparent'} outline={true} onClick={submitComment} disabled={!canSubmit}>
          Comment
        </Button>
      </div>
    </div>
  );
}
