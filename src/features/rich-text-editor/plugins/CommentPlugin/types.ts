/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export interface Comment {
  author: string;
  userId?: string;
  content: string;
  deleted: boolean;
  id: string;
  timeStamp: number;
  type: 'comment';
}

export interface Thread {
  comments: Array<Comment>;
  id: string;
  quote: string;
  type: 'thread';
}

export type SubmitAddComment = (commentOrThread: Comment, isInlineComment: boolean, thread?: Thread) => void;
export type Comments = Array<Thread | Comment>;
