/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useEffect, useState } from 'react';
import { CommentStore } from './CommentStore';
import { Comments } from './types';


export function useCommentStore(commentStore: CommentStore): Comments {
  const [comments, setComments] = useState<Comments>(commentStore.getComments());

  useEffect(() => {
    return commentStore.registerOnChange(() => {
      setComments(commentStore.getComments());
    });
  }, [commentStore]);

  return comments;
}
