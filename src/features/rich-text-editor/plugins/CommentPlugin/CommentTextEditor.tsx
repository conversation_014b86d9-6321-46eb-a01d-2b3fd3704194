/*
 * Copyright (c) 2024-2025. World Wide Generation Ltd
 */

import React from 'react';
import type { EditorState, LexicalEditor } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin';
import { EditorRefPlugin } from '@lexical/react/LexicalEditorRefPlugin';
import { BeautifulMentionNode, BeautifulMentionsPlugin } from 'lexical-beautiful-mentions';
import { EscapeHandlerPlugin } from '../EscapeHandlerPlugin';
import { CommentEditorTheme } from '../../themes/CommentEditorTheme';
import { EditorPlaceholder } from '../../EditorPlaceholder';
import { CustomMenu, CustomMenuItem } from '../MentionPlugin/CustomMenu';
import { useCommentContext } from './CommentContext';
import { getFullName } from '@utils/user';

type PlainTextEditorProps = {
  autoFocus?: boolean;
  className?: string;
  editorRef?: { current: null | LexicalEditor };
  onChange: (editorState: EditorState, editor: LexicalEditor) => void;
  onEscape: (e: KeyboardEvent) => boolean;
  placeholder?: () => React.JSX.Element;
};

const defaultPlaceholder = (): React.JSX.Element => <EditorPlaceholder placeholder={'Write a comment...'} />;


export function CommentTextEditor(props: PlainTextEditorProps) {

  const {
    className,
    autoFocus,
    onEscape,
    onChange,
    editorRef,
    placeholder = defaultPlaceholder,
  } = props;

  const initialConfig = {
    namespace: 'Commenting',
    nodes: [BeautifulMentionNode],
    onError: (error: Error) => {
      throw error;
    },
    theme: CommentEditorTheme,
  };

  const { users } = useCommentContext();
  const mentionItems = {
    '@': users.map((user) => ({ value: getFullName(user, user._id) }))
  };

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <div className='d-flex flex-grow-1 border-radius position-relative'>
        <PlainTextPlugin
          contentEditable={<ContentEditable className={className} />}
          placeholder={placeholder}
          ErrorBoundary={LexicalErrorBoundary}
        />
        <BeautifulMentionsPlugin
          menuComponent={CustomMenu}
          menuItemComponent={CustomMenuItem}
          items={mentionItems}
        />
        <OnChangePlugin onChange={onChange} />
        <HistoryPlugin />
        {autoFocus !== false && <AutoFocusPlugin />}
        <EscapeHandlerPlugin onEscape={onEscape} />
        <ClearEditorPlugin />
        {editorRef !== undefined && <EditorRefPlugin editorRef={editorRef} />}
      </div>
    </LexicalComposer>
  );
}
