/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { UserMin } from '@constants/users';
import { CurrentUserData } from '@reducers/current-user';

type CommentContextState = { users: UserMin[], currentUser?: CurrentUserData };

export const CommentContext = React.createContext<CommentContextState>({
  users: [],
  currentUser: undefined
});

CommentContext.displayName = 'CommentContext';


export const useCommentContext = () => {
  const context = React.useContext(CommentContext);
  if (!context) {
    throw new Error('useCommentContext must be used within a CommentContextProvider');
  }
  return context;
}
