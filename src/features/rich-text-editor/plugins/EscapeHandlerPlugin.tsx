/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';
import { KEY_ESCAPE_COMMAND } from 'lexical';

interface Props {
  onEscape: (e: KeyboardEvent) => boolean;
}

export const EscapeHandlerPlugin = ({ onEscape, }: Props): null => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerCommand(
      KEY_ESCAPE_COMMAND,
      (event: KeyboardEvent) => {
        return onEscape(event);
      },
      2,
    );
  }, [editor, onEscape]);

  return null;
};
