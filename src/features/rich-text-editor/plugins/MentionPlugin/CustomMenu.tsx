/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BeautifulMentionsMenuItemProps, BeautifulMentionsMenuProps } from 'lexical-beautiful-mentions';
import React, { forwardRef } from 'react';

export function CustomMenu({ loading, ...props }: BeautifulMentionsMenuProps) {

  return (
    <div className='CommentPlugin-mention-menu'>
      <ul className='m-0' {...props} />
    </div>
  )
}

export const CustomMenuItem = forwardRef<HTMLLIElement, BeautifulMentionsMenuItemProps>((props, ref) => {
  const { selected, item, ...rest } = props

  return (
    <li className={`${selected ? 'active' : ''}`} {...rest} ref={ref} />
  );
});
