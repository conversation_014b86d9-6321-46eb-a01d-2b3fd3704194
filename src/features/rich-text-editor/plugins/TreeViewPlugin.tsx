/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { TreeView } from '@lexical/react/LexicalTreeView';
import { $isXbrlNode } from '@features/rich-text-editor/plugins/CustomTagPlugin/IXBRLNode';

export function TreeViewPlugin() {
  const [editor] = useLexicalComposerContext();
  return (
    <TreeView
      viewClassName='tree-view-output'
      treeTypeButtonClassName='debug-treetype-button'
      timeTravelPanelClassName='debug-timetravel-panel'
      timeTravelButtonClassName='debug-timetravel-button'
      timeTravelPanelSliderClassName='debug-timetravel-panel-slider'
      timeTravelPanelButtonClassName='debug-timetravel-panel-button'
      editor={editor}
      customPrintNode={(node) => {
        if ($isXbrlNode(node)) {
          const { name, tag } = node.getXbrl();
          return `<${tag} name="${name}" />`;
        }
        // Return an empty string to use the default print node function
        return '';
      }}
    />
  );
}
