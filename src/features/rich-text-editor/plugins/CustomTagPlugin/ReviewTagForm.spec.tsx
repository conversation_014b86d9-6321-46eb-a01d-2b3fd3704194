
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ReviewTagForm } from './ReviewTagForm';
import userEvent from '@testing-library/user-event';

describe('ReviewTagForm', () => {
  const mockTags = [
    { tagName: 'ESRS 2-1' },
    { tagName: 'ESRS 2-2' },
    { tagName: 'ESRS 2-3' }
  ];

  const setup = (props = { tags: mockTags }) => {
    return {
      user: userEvent.setup(),
      ...render(<ReviewTagForm onAction={() => {}} {...props} />)
    };
  };

  it('displays correct number of tags with page separators', () => {
    setup();

    mockTags.forEach((_, index) => {
      expect(screen.getByText(`PAGE ${index + 1}`)).toBeInTheDocument();
      expect(screen.getByText(`TAG ${index + 1}`)).toBeInTheDocument();
    });
  });

  it('renders tag view buttons for each tag', () => {
    setup();

    const tagViewButtons = screen.getAllByTestId('tag-view-icon-button-tag');
    expect(tagViewButtons).toHaveLength(mockTags.length);
  });

  it('handles search box input', async () => {
    const { user } = setup();
    const searchBox = screen.getByPlaceholderText('Search');

    await user.type(searchBox, 'ESRS');
    expect(searchBox).toHaveValue('ESRS');
  });

  it('allows toggling select all checkbox', async () => {
    const { user } = setup();
    const selectAllCheckbox = screen.getByLabelText('Select all');

    await user.click(selectAllCheckbox);
    expect(selectAllCheckbox).toBeChecked();
  });
});
