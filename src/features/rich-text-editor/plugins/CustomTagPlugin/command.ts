/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import {
  $createTextNode,
  $getSelection,
  $isRangeSelection,
  $isTextNode,
  createCommand,
  LexicalEditor,
} from 'lexical';
import { $createIxbrlNode, IXBRLNodeProps } from './IXBRLNode';
import { $wrapSelectionInIxbrlNode } from './wrapSelectionInIxbrlNode';

export interface CustomTag {
  tagName: string;

  [k: string]: string | undefined;
}

interface TagTogglePayload {
  open: boolean;
}

interface TagCreatePayload {
  tagName: 'ix:nonFraction';
  properties: IXBRLNodeProps;
}

export const TOGGLE_CUSTOM_TAG_COMMAND = createCommand<TagTogglePayload>('TOGGLE_CUSTOM_TAG_COMMAND');

export const WRAP_IXBRL_COMMAND = createCommand<TagCreatePayload>();
export const UNWRAP_IXBRL_COMMAND = createCommand<string[] | null>();
export const TOGGLE_IXBRL_COMMAND = createCommand<IXBRLNodeProps>();

export function $handleCustomTagCommand(editor: LexicalEditor, payload: TagCreatePayload) {
  const { properties } = payload;

  editor.update(() => {
    const selection = $getSelection();
    if (!$isRangeSelection(selection)) {
      console.warn('No valid range selection found.');
      return;
    }

    if (selection.isCollapsed()) {
      console.warn('Selection is collapsed, nothing to tag.');
      return;
    }

    // Extract important selection information
    const anchorNode = selection.anchor.getNode();
    const focusNode = selection.focus.getNode();
    const anchorOffset = selection.anchor.offset;
    const focusOffset = selection.focus.offset;

    // Simple case: selection is within a single text node
    if (anchorNode === focusNode && $isTextNode(anchorNode)) {
      const parent = anchorNode.getParentOrThrow();

      const text = anchorNode.getTextContent();
      const startOffset = Math.min(anchorOffset, focusOffset);
      const endOffset = Math.max(anchorOffset, focusOffset);

      // Insert text before selection if needed
      if (startOffset > 0) {
        parent.append($createTextNode(text.substring(0, startOffset)));
      }

      // Create IXBRL node with selected text
      const ixbrlNode = $createIxbrlNode(properties);
      const textNode = $createTextNode(text.substring(startOffset, endOffset));
      ixbrlNode.append(textNode);

      // Insert the IXBRL node with selected text
      parent.append(ixbrlNode);

      // We can now safely remove the original text node
      anchorNode.remove();

      // Insert text after selection if needed
      let afterTextNode = null;
      if (endOffset < text.length) {
        afterTextNode = $createTextNode(text.substring(endOffset));
        parent.append(afterTextNode);
      }

      // Create an empty text node if there's no text after selection
      // This ensures we have a place to put the cursor outside the IXBRL tag
      if (!afterTextNode) {
        afterTextNode = $createTextNode('');
        parent.append(afterTextNode);
      }

      // Always position cursor in the after text node
      afterTextNode.select(0, 0);
      return;
    }

    $wrapSelectionInIxbrlNode(properties);
  });
}
