/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React, { useCallback, useState } from 'react';
import { Menu } from '@components/menu/Menu';
import { CustomTag } from '@features/rich-text-editor/plugins/CustomTagPlugin/command';
import { ReferenceViewer } from '@components/survey/question/mapping/ReferenceViewer';
import { ButtonGroup, Input, Label } from 'reactstrap';
import IconButton from '@components/button/IconButton';
import { SearchBox } from '@g17eco/molecules/search';


enum ReviewTagFormView {
  ESRS = 'esrs',
  Custom = 'custom',
}

interface Props extends Pick<TagViewParams, 'onAction'> {
  tags: CustomTag[];
}

const buttons = [
  { icon: 'fa-light fa-tags', view: 'tag' }
] as const;


interface TagViewParams {
  tagNumber: number;
  tag: CustomTag;
  isSelected: boolean;
  onAction: (action: { action: 'remove'; tag: CustomTag, shouldClose?: boolean }) => void;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const TagView = (props: TagViewParams) => {
  const { tag, tagNumber, isSelected, onAction, onChange } = props;

  const [view, setView] = useState<'text' | 'tag' | 'info'>('tag');

  const viewContent = () => {
    switch (view) {
      case 'text':
        return tag.tagName;
      case 'tag':
        return tag.tagName;
      case 'info': {
        return <ReferenceViewer view={'compact'} references={[]} />;
      }
    }
  };

  return (
    <div className={'w-100 border-radius p-2 my-3 text-break background-ThemeBgMedium shadow-sm'}>
      <div className='d-flex justify-content-between align-items-center'>
        <div className='d-flex align-items-center'>
          <Label className='m-0'>
            <Input type='checkbox' name={tag.tagName} onChange={onChange} checked={isSelected}  />
            <span className={'ms-2'}>TAG {tagNumber}</span>
          </Label>
        </div>
        <div className='d-flex'>
          <IconButton
            size={'sm'}
            aria-label={'Remove tag'}
            onClick={() => onAction({ action: 'remove', tag, shouldClose: true })}
            className={'border-0'}
            color={'danger'}
            outline={true}
            icon={'fa-light fa-times-circle'}
          />
          <IconButton
            size={'sm'}
            color={'primary'}
            className={'border-0'}
            outline={true}
            icon={'fa-light fa-tags'}
          />
          <IconButton
            size={'sm'}
            color={'success'}
            className={'border-0'}
            outline={true}
            icon={'fa-light fa-circle-check'}
          />
        </div>
      </div>

      <div className={'d-flex flex-row'}>
        <ButtonGroup className={'align-self-center d-inline'} vertical>
          {buttons.map((button) => {
            return (
              <IconButton
                key={button.icon}
                data-testid={`tag-view-icon-button-${button.view}`}
                color={'transparent'}
                disabled={true}
                outline={false}
                onClick={() => setView(button.view)}
                icon={button.icon}
              />
            );
          })}
        </ButtonGroup>
        <div
          style={{ minWidth: '200px' }}
          className={'w-100 border border-radius  border-2 p-2 text-break'}>
          {viewContent()}
        </div>
      </div>
    </div>
  );
};

export const ReviewTagForm = (props: Props) => {
  const { tags, onAction } = props;
  const [view, setView] = useState(ReviewTagFormView.ESRS);
  const [searchText, setSearchText] = useState('');
  const onChange = useCallback(() => {
    // Handle checkbox change in the future
  }, []);

  return (
    <div>
      <Menu
        items={[
          {
            active: view === ReviewTagFormView.ESRS,
            onClick: () => setView(ReviewTagFormView.ESRS),
            label: 'ESRS',
          },
          {
            active: view === ReviewTagFormView.Custom,
            onClick: () => setView(ReviewTagFormView.Custom),
            label: 'Custom',
            disabled: true,
          },
        ]}
      />
      <hr />
      <div>
        {view === ReviewTagFormView.ESRS ? (
          <div>
            <SearchBox
              searchText={searchText}
              handleOnChange={(value) => {
                setSearchText(value.currentTarget.value);
              }}
              classNames={{ wrapper: 'mb-2' }}
            />
            <Label className={'d-flex align-items-center ps-2'}>
              <Input type='checkbox' defaultChecked={false} />
              <span className={'ms-1'}>Select all</span>
            </Label>

            {tags.slice(0, 20).map((tag, i) => {
              return (
                <div key={tag.tagName}>
                  <div className={'d-flex align-items-center'}>
                    <hr style={{ minWidth: '40%' }} className={'d-flex'} />
                    <div className={'my-2'}>PAGE {i + 1}</div>
                    <hr style={{ minWidth: '40%' }} className={'d-flex'} />
                  </div>

                  <TagView isSelected={false} onChange={onChange} onAction={onAction} tagNumber={i + 1} key={tag.tagName} tag={tag} />
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    </div>
  );
};
