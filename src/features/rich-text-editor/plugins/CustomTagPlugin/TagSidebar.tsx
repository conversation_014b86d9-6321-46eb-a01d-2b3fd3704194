/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import React, { useMemo, useState } from 'react';
import { Button, Offcanvas, OffcanvasBody, OffcanvasHeader } from 'reactstrap';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { CustomTag, UNWRAP_IXBRL_COMMAND, WRAP_IXBRL_COMMAND } from './command';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { VirtualMenuList } from '@g17eco/molecules/select/partials/VirtualMenuList';
import IconButton from '@components/button/IconButton';
import { ReviewTagForm } from '@features/rich-text-editor/plugins/CustomTagPlugin/ReviewTagForm';

interface Props {
  tags: CustomTag[];
  isOpen: boolean;
  toggle: () => void;
  selectionTags?: string[];
}

export function TagSidebar({ isOpen, tags, toggle, selectionTags = [] }: Readonly<Props>) {

  const [tagName, setTagName] = useState('');
  const [editor] = useLexicalComposerContext();
  const options = useMemo(() => {
    return tags.map(tag => ({ value: tag.tagName, label: tag.tagName }));
  }, [tags]);

  // These are mocks and may not be needed, as it should be reusable
  const [factId, setFactId] = useState(1);
  const [unitRef, setUnitRef] = useState(1);
  const [contextRef, setContextRef] = useState(1);

  const selectedTags = tags.filter(tag => selectionTags.includes(tag.tagName));

  const handleApplyTag = () => {
    const newFactId = factId + 1;
    const newContextRef = contextRef + 1;
    const newUnitRef = unitRef + 1;

    setFactId(newFactId);
    setContextRef(newContextRef);
    setUnitRef(newUnitRef);
    // This should be decided based on the tag information
    const xbrlTag = 'ix:nonFraction';

    editor.dispatchCommand(WRAP_IXBRL_COMMAND, {
      tagName: xbrlTag,
      properties: {
        name: tagName,
        tag: xbrlTag,
        unitRef: `u-${newUnitRef}`,
        contextRef: `c-${newContextRef}`,
        factId: `fact-${newFactId}`
      }
    });
    setTagName('');
    toggle();
  };

  const handleRemoveTag = (tagName: string | null) => {
    editor.dispatchCommand(UNWRAP_IXBRL_COMMAND, tagName ? [tagName] : null);
  };

  const value = options.find(option => option.value === tagName) ?? null;
  const closeBtn = (
    <IconButton
      color={'secondary'}
      icon={'fa-light fa-sidebar-flip'}
      onClick={toggle}
      className='border-0' />
  );


  return (
    <Offcanvas
      direction={'end'}
      returnFocusAfterClose={false}
      isOpen={isOpen}
      toggle={toggle}>
      <OffcanvasHeader close={closeBtn} toggle={toggle}>
        Review Tags
      </OffcanvasHeader>
      <OffcanvasBody>
        {selectedTags.length > 0 ? (
          <div className='mb-3'>
            <h6>Selected Tags</h6>
            <ReviewTagForm
              tags={selectedTags}
              onAction={({ action, tag, shouldClose }) => {
                // Only remove action is available
                if (action === 'remove') {
                  handleRemoveTag(tag.tagName);
                }

                if (shouldClose) {
                  toggle();
                }
            }}
            />
            <hr />
          </div>
        ) : null}
        <div>
          <h6>Available Tags</h6>
          <SelectFactory
            className={'mb-3'}
            selectType={SelectTypes.SingleSelect}
            options={options}
            onChange={(option) => {
              setTagName(option?.value ?? '');
            }}
            components={{ MenuList: VirtualMenuList }}
            value={value}
          />
          <Button color='primary' disabled={!tagName} onClick={() => handleApplyTag()}>Apply Tag</Button>
        </div>
      </OffcanvasBody>
    </Offcanvas>
  );
}
