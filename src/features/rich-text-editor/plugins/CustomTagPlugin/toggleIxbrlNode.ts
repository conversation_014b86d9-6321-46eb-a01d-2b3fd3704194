/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import {
  $getSelection,
  $isRangeSelection,
  LexicalNode,
} from 'lexical';
import { $wrapSelectionInIxbrlNode } from './wrapSelectionInIxbrlNode';
import { $unwrapIxbrlNode } from './unwrapIxbrlNode';
import { $isXbrlNode } from './IXBRLNode';
import type { IXBRLNodeProps } from './IXBRLNode';

/**
 * Toggles an IXBRL wrapper:
 * - If inside an IXBRLNode → unwrap
 * - If outside → wrap selection
 */
export function $toggleIxbrlNode(props: IXBRLNodeProps): void {
  const selection = $getSelection();

  if (!$isRangeSelection(selection)) {
    console.warn('[$toggleIxbrlNode] Not a range selection. Aborting.');
    return;
  }

  const selectedNodes = selection.getNodes();

  for (const node of selectedNodes) {
    let current: LexicalNode | null = node;

    while (current !== null) {
      if ($isXbrlNode(current)) {
        // Already inside an IXBRL wrapper — unwrap instead
        $unwrapIxbrlNode();
        return;
      }
      current = current.getParent();
    }
  }

  // No IXBRL wrapper found — wrap it
  $wrapSelectionInIxbrlNode(props);
}
