/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_EDITOR,
  COMMAND_PRIORITY_LOW,
  LexicalNode,
  SELECTION_CHANGE_COMMAND
} from 'lexical';
import { useEffect, useState } from 'react';
import {
  $handleCustomTagCommand,
  CustomTag,
  WRAP_IXBRL_COMMAND,
  TOGGLE_CUSTOM_TAG_COMMAND,
  UNWRAP_IXBRL_COMMAND,
  TOGGLE_IXBRL_COMMAND
} from './command';
import { TagSidebar } from './TagSidebar';
import { mergeRegister } from '@lexical/utils';
import { $unwrapIxbrlNode } from './unwrapIxbrlNode';
import { $toggleIxbrlNode } from './toggleIxbrlNode';
import { $isXbrlNode } from './IXBRLNode';


interface Props {
  tags: CustomTag[];
  isOpen: boolean;
  toggle: (isOpen: boolean) => void;
}

export function CustomTagPlugin(props: Readonly<Props>) {
  const { tags, isOpen, toggle } = props;
  const [editor] = useLexicalComposerContext();

  const [selectionTags, setSelectionTags] = useState<string[]>([]);

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand(
        TOGGLE_CUSTOM_TAG_COMMAND,
        (payload) => {
          toggle(payload.open);
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        TOGGLE_IXBRL_COMMAND,
        (payload) => {
          editor.update(() => {
            $toggleIxbrlNode(payload);
          });
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        UNWRAP_IXBRL_COMMAND,
        () => {
          editor.update(() => {
            $unwrapIxbrlNode();
          });
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        WRAP_IXBRL_COMMAND,
        (payload) => {
          $handleCustomTagCommand(editor, payload);
          return true;
        },
        COMMAND_PRIORITY_EDITOR
      ),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          editor.getEditorState().read(() => {
            const selection = $getSelection();

            if (!$isRangeSelection(selection)) {
              setSelectionTags([]);
              return;
            }

            const seen = new Set<string>();
            const found: string[] = [];

            const nodes = selection.getNodes();
            for (const node of nodes) {
              let current: LexicalNode | null = node;
              while (current != null) {
                if ($isXbrlNode(current)) {
                  const key = current.getKey();
                  if (!seen.has(key)) {
                    const xbrl = current.getXbrl();
                    found.push(xbrl.name);
                    seen.add(key);
                  }
                }
                current = current.getParent();
              }
            }

            setSelectionTags(found);
          });

          return false;
        },
        COMMAND_PRIORITY_LOW
      )
    );
  }, [editor, toggle]);

  return (
    <TagSidebar
      isOpen={isOpen}
      toggle={() => toggle(!isOpen)}
      tags={tags}
      selectionTags={selectionTags}
    />
  );
}
