/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode } from '@lexical/rich-text';
import { defaultTheme } from '@features/rich-text-editor/themes/defaultTheme';
import { handleRouteError } from '../../logger';

export const defaultConfig = {
  namespace: 'RichTextEditor',
  theme: defaultTheme,
  onError: (error: Error) => {
    handleRouteError(error);
  },
  nodes: [ListNode, ListItemNode, HeadingNode],
};

