import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import {
  getInitialUnitConfig,
  getUnitTypeMap,
  GLOBAL_UPDATE_CODE,
  hasDataChanged,
  validateData,
  prepareUpdateData,
} from './utils';
import {
  InputOverrideType,
  InputOverrideDataType,
  InputOverrideFormType,
  VariationInput,
  UnitTypeMapValue,
  ErrorDecimalType
} from './types';
import { TableColumnType, UtrValueType, VariationDataSource } from '@g17eco/types/universalTracker';
import { generateObjectId } from '@utils/object-id';
import { TableColumn } from '@components/survey/form/input/table/InputInterface';

const initiativeId = generateObjectId();

const tableQuestion = {
  _id: 'tableId',
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      columns: [
        { code: 'unsupportedCol', name: 'Unsupported Col', type: TableColumnType.Text },
        { code: 'baseCol', name: 'Col without unit', type: TableColumnType.Number },
        { code: 'unitCol', name: ' Col with unit', type: TableColumnType.Number, unitType: 'volume', unit: 'm3' },
        {
          code: 'currencyCol',
          name: 'Col has currency',
          type: TableColumnType.Number,
          unitType: 'currency',
          unit: 'USD',
          numberScale: 'millions',
        },
      ],
    },
  },
};

const overriddenTableQuestion = {
  _id: '2',
  initiativeId,
  universalTrackerId: 'tableId',
  valueValidation: {
    table: {
      columns: [
        { code: 'unsupportedCol', name: 'Unsupported Col', type: TableColumnType.Text },
        { code: 'baseCol', name: 'Col without unit', type: TableColumnType.Number },
        { code: 'unitCol', name: ' Col with unit', type: TableColumnType.Number, unitInput: 'bbl' },
        {
          code: 'currencyCol',
          name: 'Col has currency',
          type: TableColumnType.Number,
          numberScaleInput: 'hundreds',
        },
      ],
    },
  },
};

const variationParams = {
  isEnforced: true,
  value: 15,
  dataSource: VariationDataSource.LastMonth,
  confirmationRequired: true,
};

const variationResetParams = {
  isEnforced: true,
  value: undefined,
  dataSource: undefined,
  confirmationRequired: undefined,
};

describe('getUnitTypeMap function', () => {
  it('should return empty Map if unsupported questions are selected', () => {
    const selectedQuestions = [
      { _id: '1', valueType: UtrValueType.Text },
      { _id: '2', valueType: UtrValueType.Date },
      { _id: '2', valueType: UtrValueType.TextValueList },
    ];
    const overriddenQuestions = [] as InitiativeUniversalTracker[];

    const expectedResult = { [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([]) };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.SingleInput,
    });
    expect(Array.from(result[GLOBAL_UPDATE_CODE].entries())).toEqual(
      Array.from(expectedResult[GLOBAL_UPDATE_CODE].entries()),
    );
  });

  it('should return Map with numberScale if selected questions has no unitType', () => {
    const selectedQuestions = [
      { _id: '1', valueType: UtrValueType.Number },
      { _id: '2', valueType: UtrValueType.NumericValueList },
    ];
    const overriddenQuestions = [] as InitiativeUniversalTracker[];

    const expectedResult = {
      [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
        ['numberScale', { defaultValue: undefined, overriddenValue: undefined, isLocked: true }],
      ]),
    };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.SingleInput,
    });

    expect(Array.from(result[GLOBAL_UPDATE_CODE].entries())).toEqual(
      Array.from(expectedResult[GLOBAL_UPDATE_CODE].entries()),
    );
  });

  it('should return the expected Map object when single question is selected', () => {
    const selectedQuestions = [{ _id: '1', unitType: 'volume', unit: 'm3', valueType: UtrValueType.Number }];
    const overriddenQuestions = [] as InitiativeUniversalTracker[];

    const expectedResult = {
      [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
        ['numberScale', { defaultValue: undefined, overriddenValue: undefined, isLocked: true }],
        ['volume', { defaultValue: 'm3', overriddenValue: undefined, isLocked: true }],
      ]),
    };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.SingleInput,
    });

    expect(Array.from(result[GLOBAL_UPDATE_CODE].entries())).toEqual(
      Array.from(expectedResult[GLOBAL_UPDATE_CODE].entries()),
    );
  });

  it('should return the expected Map object when single question has overrides is selected', () => {
    const selectedQuestions = [{ _id: '1', unitType: 'volume', unit: 'm3', valueType: UtrValueType.Number }];
    const overriddenQuestions = [
      {
        _id: '_',
        initiativeId,
        universalTrackerId: '1',
        numberScaleInput: 'hundreds',
        unitInput: 'gal',
        unitLocked: false,
        // old data does NOT have numberScaleLocked
      },
    ];

    const expectedResult = {
      [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
        ['numberScale', { defaultValue: undefined, overriddenValue: 'hundreds', isLocked: false }],
        ['volume', { defaultValue: 'm3', overriddenValue: 'gal', isLocked: false }],
      ]),
    };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.SingleInput,
    });

    expect(Array.from(result[GLOBAL_UPDATE_CODE].entries())).toEqual(
      Array.from(expectedResult[GLOBAL_UPDATE_CODE].entries()),
    );
  });

  it('should return the expected Map object when single table question is selected', () => {
    const selectedQuestions = [tableQuestion];
    const overriddenQuestions = [overriddenTableQuestion];
    const expectedResult: Record<string, Map<string, UnitTypeMapValue>> = {
      baseCol: new Map<string, UnitTypeMapValue>([
        [
          'numberScale',
          {
            defaultValue: undefined,
            overriddenValue: undefined,
            isLocked: true,
          },
        ],
      ]),
      unitCol: new Map<string, UnitTypeMapValue>([
        [
          'numberScale',
          {
            defaultValue: undefined,
            overriddenValue: undefined,
            isLocked: true,
          },
        ],
        [
          'volume',
          {
            defaultValue: 'm3',
            overriddenValue: 'bbl', // old data has override set
            isLocked: false, // old data does NOT have unitLocked -> default will be false
          },
        ],
      ]),
      currencyCol: new Map<string, UnitTypeMapValue>([
        [
          'numberScale',
          {
            defaultValue: 'millions',
            overriddenValue: 'hundreds', // old data has override set
            isLocked: false, // old data does NOT have numberScaleLocked -> default will be false
          },
        ],
      ]),
    };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.Table,
    });

    Object.keys(result).map((columnCode) => {
      expect(Array.from(result[columnCode].entries())).toEqual(Array.from(expectedResult[columnCode].entries()));
    });
  });

  it('should return the expected Map object when multiple numeric questions are selected', () => {
    const selectedQuestions = [
      { _id: 'numberId', unitType: 'volume', unit: 'm3', valueType: UtrValueType.Number },
      tableQuestion,
      {
        _id: 'numericValueListId',
        unitType: 'currency',
        unit: 'USD',
        numberScale: 'millions',
        valueType: UtrValueType.NumericValueList,
      },
    ];

    // old data has overrides, but does NOT have unitLocked & numberScaleLocked => isLocked: false
    const overriddenQuestions = [
      { _id: '1', initiativeId, universalTrackerId: 'numberId', unitInput: 'gal' },
      overriddenTableQuestion,
      {
        _id: '3',
        initiativeId,
        universalTrackerId: 'numericValueListId',
        numberScaleInput: 'thousands',
      },
    ];

    const expectedResult = {
      [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
        [
          'numberScale',
          {
            defaultValue: 'millions',
            overriddenValue: 'hundreds',
            isLocked: false,
          },
        ],
        ['volume', { defaultValue: 'm3', overriddenValue: 'gal', isLocked: false }],
      ]),
    };

    const result = getUnitTypeMap({
      selectedQuestions,
      overriddenQuestions,
      inputType: InputOverrideFormType.SingleInput,
    });

    expect(Array.from(result[GLOBAL_UPDATE_CODE].entries())).toEqual(
      Array.from(expectedResult[GLOBAL_UPDATE_CODE].entries()),
    );
  });

  describe('isLocked', () => {
    const selectedQuestions = [
      { _id: 'numberId1', valueType: UtrValueType.Number, numberScale: 'millions' },
      { _id: 'numberId2', valueType: UtrValueType.Number, numberScale: 'hundreds' },
    ];

    const testCases = [
      {
        label: 'isLocked should be true if selected questions has no overrides',
        overriddenQuestions: [],
        expectedResult: {
          [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
            [
              'numberScale',
              {
                defaultValue: 'millions',
                overriddenValue: undefined,
                isLocked: true,
              },
            ],
          ]),
        },
      },
      {
        label: 'isLocked should be false if selected questions has overrides without lock properties (old data)',
        overriddenQuestions: [
          { _id: '1', initiativeId, universalTrackerId: 'numberId1', numberScaleInput: 'hundreds' },
        ],
        expectedResult: {
          [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
            [
              'numberScale',
              {
                defaultValue: 'millions',
                overriddenValue: 'hundreds',
                isLocked: false,
              },
            ],
          ]),
        },
      },
      {
        label: 'isLocked should be false if there is only 1 selected question with overrides unlocked',
        overriddenQuestions: [
          {
            _id: '1',
            initiativeId,
            universalTrackerId: 'numberId1',
            numberScaleInput: 'hundreds',
            numberScaleLocked: true,
          },
          {
            _id: '2',
            initiativeId,
            universalTrackerId: 'numberId2',
            numberScaleInput: 'thousands',
            numberScaleLocked: false,
          },
        ],
        expectedResult: {
          [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
            [
              'numberScale',
              {
                defaultValue: 'millions',
                overriddenValue: 'hundreds',
                isLocked: false,
              },
            ],
          ]),
        },
      },
      {
        label: 'isLocked should be false if all selected questions have overrides & unlocked',
        overriddenQuestions: [
          {
            _id: '1',
            initiativeId,
            universalTrackerId: 'numberId1',
            numberScaleInput: 'hundreds',
            numberScaleLocked: false,
          },
          {
            _id: '2',
            initiativeId,
            universalTrackerId: 'numberId2',
            numberScaleInput: 'thousands',
            numberScaleLocked: false,
          },
        ],
        expectedResult: {
          [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
            [
              'numberScale',
              {
                defaultValue: 'millions',
                overriddenValue: 'hundreds',
                isLocked: false,
              },
            ],
          ]),
        },
      },
      {
        label: 'isLocked should be true if all selected questions have overrides & locked',
        overriddenQuestions: [
          {
            _id: '1',
            initiativeId,
            universalTrackerId: 'numberId1',
            numberScaleInput: 'hundreds',
            numberScaleLocked: true,
          },
          {
            _id: '2',
            initiativeId,
            universalTrackerId: 'numberId2',
            numberScaleInput: 'thousands',
            numberScaleLocked: true,
          },
        ],
        expectedResult: {
          [GLOBAL_UPDATE_CODE]: new Map<string, UnitTypeMapValue>([
            [
              'numberScale',
              {
                defaultValue: 'millions',
                overriddenValue: 'hundreds',
                isLocked: true,
              },
            ],
          ]),
        },
      },
    ];

    testCases.forEach(({ label, overriddenQuestions, expectedResult }) => {
      it(label, () => {
        const result = getUnitTypeMap({
          selectedQuestions,
          overriddenQuestions: overriddenQuestions,
          inputType: InputOverrideFormType.SingleInput,
        });
        expect(result).toEqual(expectedResult);
      });
    });
  });
});

describe('getInitialUnitConfig', () => {
  // no overrides
  const defaultMap = new Map<string, UnitTypeMapValue>([
    ['numberScale', { defaultValue: undefined, overriddenValue: undefined, isLocked: false }],
    ['volume', { defaultValue: 'm3', overriddenValue: undefined, isLocked: false }],
  ]);

  const expectedDefaults = {
    numberScale: { value: undefined, isEnforced: false, isLocked: false },
    volume: { value: 'm3', isEnforced: false, isLocked: false },
  };

  // has overrides
  const overrideMap = new Map<string, UnitTypeMapValue>([
    ['numberScale', { defaultValue: undefined, overriddenValue: 'hundreds', isLocked: false }],
    ['volume', { defaultValue: 'm3', overriddenValue: 'bbl', isLocked: true }],
  ]);

  const expectedOverrides = {
    numberScale: { value: 'hundreds', isEnforced: true, isLocked: false },
    volume: { value: 'bbl', isEnforced: true, isLocked: true },
  };

  it('single simple numeric question', () => {
    const unitTypeMap = { [GLOBAL_UPDATE_CODE]: defaultMap };
    const result = getInitialUnitConfig({
      inputType: InputOverrideFormType.SingleInput,
      tableColumns: [],
      unitTypeMap,
    });

    expect(result).toEqual({ [GLOBAL_UPDATE_CODE]: expectedDefaults });
  });

  it('single table question', () => {
    const tableColumns = [{ code: 'baseCol' }, { code: 'overriddenCol' }] as TableColumn[];

    const tableUnitTypeMap = {
      ['baseCol']: defaultMap,
      ['overriddenCol']: overrideMap,
    };
    const result = getInitialUnitConfig({
      inputType: InputOverrideFormType.Table,
      tableColumns,
      unitTypeMap: tableUnitTypeMap,
    });

    expect(result).toEqual({ baseCol: expectedDefaults, overriddenCol: expectedOverrides });
  });
});

describe('hasDataChanged', () => {
  const testCases: {
    label: string;
    defaultValue: Record<string, InputOverrideDataType<number> | VariationInput>;
    value: Record<string, InputOverrideDataType<number> | VariationInput>;
    expected: boolean;
  }[] = [
    {
      label: 'should return false if value is unchanged - undefined',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false } },
      value: { [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false } },
      expected: false,
    },
    {
      label: 'should return false if value is unchanged',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true } },
      value: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true } },
      expected: false,
    },
    {
      label: 'should return true if value has changed',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true } },
      value: { [GLOBAL_UPDATE_CODE]: { value: 2, isEnforced: true } },
      expected: true,
    },
    {
      label: 'should return true if value has changed to undefined',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true } },
      value: { [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false } },
      expected: true,
    },
    {
      label: 'should return true if confirmationRequired has changed',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true, confirmationRequired: true } },
      value: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true, confirmationRequired: false } },
      expected: true,
    },
    {
      label: 'should return true if dataSource has changed',
      defaultValue: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true, dataSource: VariationDataSource.LastMonth } },
      value: { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true, dataSource: VariationDataSource.LastYear } },
      expected: true,
    },
  ];
  testCases.forEach(({ label, defaultValue, value, expected }) => {
    it(label, () => {
      expect(hasDataChanged(defaultValue, value)).toEqual(expected);
    });
  });
});

describe('validateData', () => {
  const testCases: {
    label: string;
    error: ErrorDecimalType;
    data: Record<string, InputOverrideDataType<number>>;
    expected: boolean;
  }[] = [
    {
      label: 'should return true if toggle is off',
      error: { [GLOBAL_UPDATE_CODE]: undefined },
      data: {
        [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false },
      },
      expected: true,
    },
    {
      label: 'should return true if toggle is on and value is undefined',
      error: { [GLOBAL_UPDATE_CODE]: undefined },
      data: {
        [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: true },
      },
      expected: false,
    },
    {
      label: 'should return true if toggle is on and value is defined',
      error: { [GLOBAL_UPDATE_CODE]: undefined },
      data: {
        [GLOBAL_UPDATE_CODE]: { value: 3, isEnforced: true },
      },
      expected: true,
    },
    {
      label: 'should return false if toggle is on, value is defined, but has error',
      error: { [GLOBAL_UPDATE_CODE]: 'Invalid decimal, must be between 0 and 5' },
      data: {
        [GLOBAL_UPDATE_CODE]: { value: 6, isEnforced: true },
      },
      expected: false,
    },
    {
      label: 'should return false - invalid table data',
      error: { 'col-1': undefined, 'col-2': undefined },
      data: {
        'col-1': { value: undefined, isEnforced: true }, // invalid
        'col-2': { value: 1, isEnforced: true },
      },
      expected: false,
    },
    {
      label: 'should return false - invalid table data',
      error: { 'col-1': 'Invalid decimal, must be between 0 and 5', 'col-2': undefined }, // has at least 1 error
      data: {
        'col-1': { value: 6, isEnforced: true },
        'col-2': { value: 1, isEnforced: true },
      },
      expected: false,
    },
  ];
  testCases.forEach(({ label, error, data, expected }) => {
    it(label, () => {
      expect(validateData({ error, data })).toEqual(expected);
    });
  });
});

describe('prepareUpdateData', () => {
  describe('global update - singe simple question', () => {
    it('set overrides', () => {
      const decimal = { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true } };
      const unitConfig = {
        [GLOBAL_UPDATE_CODE]: { numberScale: { value: 'hundreds', isEnforced: true, isLocked: true } },
      };
      const variation = { [GLOBAL_UPDATE_CODE]: variationParams };

      const dataToUpdate = {
        decimal: { [GLOBAL_UPDATE_CODE]: 1 },
        unitConfig: { [GLOBAL_UPDATE_CODE]: { numberScale: 'hundreds', numberScaleLocked: true } },
        variation,
      };
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: undefined })).toEqual(dataToUpdate);
    });
    it('reset overrides', () => {
      const decimal = { [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false } }; // reset decimal
      const unitConfig = {
        [GLOBAL_UPDATE_CODE]: { numberScale: { value: 'millions', isEnforced: false, isLocked: false } },
      }; // reset unitConfig
      const variation = { [GLOBAL_UPDATE_CODE]: variationResetParams };

      const dataToUpdate = {
        decimal: { [GLOBAL_UPDATE_CODE]: null },
        unitConfig: { [GLOBAL_UPDATE_CODE]: { numberScale: '', numberScaleLocked: false } },
        variation,
      };
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: undefined })).toEqual(dataToUpdate);
    });
  });
  describe('global update - multiple questions', () => {
    const decimal = { [GLOBAL_UPDATE_CODE]: { value: 1, isEnforced: true, isLocked: false } };
    const unitConfig = {
      [GLOBAL_UPDATE_CODE]: {
        volume: { value: 'bbl', isEnforced: true, isLocked: false },
        numberScale: { value: 'hundreds', isEnforced: true, isLocked: true },
      },
    };
    const variation = { [GLOBAL_UPDATE_CODE]: variationParams };

    it('set partial overrides - decimal only', () => {
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.Decimal })).toEqual({
        decimal: { [GLOBAL_UPDATE_CODE]: 1 },
      });
    });

    it('set partial overrides - numberScale only', () => {
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.NumberScale })).toEqual({
        unitConfig: { [GLOBAL_UPDATE_CODE]: { numberScale: 'hundreds', numberScaleLocked: true } },
      });
    });

    it('set partial overrides - unit only', () => {
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.Unit })).toEqual({
        unitConfig: { [GLOBAL_UPDATE_CODE]: { volume: 'bbl', unitLocked: false } },
      });
    });
    it('set partial overrides - variation detection only', () => {
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.Variation })).toEqual({
        variation,
      });
    });
    it('reset partial overrides', () => {
      const decimal = { [GLOBAL_UPDATE_CODE]: { value: undefined, isEnforced: false } }; // reset decimal
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.Decimal })).toEqual({
        decimal: { [GLOBAL_UPDATE_CODE]: null },
      });
    });
    it('reset partial overrides - variation detection only', () => {
      const variation = { [GLOBAL_UPDATE_CODE]: variationResetParams };
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: InputOverrideType.Variation })).toEqual({
        variation,
      });
    });
  });
  describe('single table question', () => {
    it('should prepare update data correctly', () => {
      const decimal = {
        'col-1': { value: 1, isEnforced: true }, // set decimal
        'col-2': { value: undefined, isEnforced: false }, // unset decimal
      };
      const unitConfig = {
        'col-1': { numberScale: { value: 'hundreds', isEnforced: true, isLocked: true } }, // set unitConfig
        'col-2': { numberScale: { value: 'millions', isEnforced: false, isLocked: false } }, // reset unitConfig to default
      };

      const variation = {
        'col-1': variationParams, // set variation
        'col-2': variationResetParams, // reset variation
      };
      expect(prepareUpdateData({ decimal, unitConfig, variation, type: undefined })).toEqual({
        decimal: { 'col-1': 1, 'col-2': null },
        unitConfig: {
          'col-1': { numberScale: 'hundreds', numberScaleLocked: true },
          'col-2': { numberScale: '', numberScaleLocked: false },
        },
        variation,
      });
    });
  });
});
