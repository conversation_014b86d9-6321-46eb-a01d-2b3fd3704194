import classNames from 'classnames';
import React from 'react';
import { FormFeedback, Input, InputGroupText, InputProps } from 'reactstrap';

interface Props extends InputProps {
  value: number | undefined;
  suffix?: string;
  errorMessage?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const NumberInput = (props: Props) => {
  const {  errorMessage = '', min = 0, max = 5, suffix, ...inputProps } = props;
  return (
    <div
      className={classNames('input-group', {
        'has-validation': true,
      })}
    >
      <Input
        {...inputProps}
        type='number'
        invalid={!!errorMessage}
        className='border-ThemeBorderDefault fs-6'
        placeholder='Key in value'
        min={min}
        max={max}
        value={inputProps.value ?? ''}
      />
      {suffix ? (
        <InputGroupText className='px-3 border-ThemeBorderDefault background-ThemeBgMedium'>{suffix}</InputGroupText>
      ) : null}
      <FormFeedback className='mt-2'>{errorMessage}</FormFeedback>
    </div>
  );
};
