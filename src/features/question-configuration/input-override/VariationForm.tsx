import { Checkbox, CheckboxState } from '@g17eco/atoms/checkbox';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { VARIATION_DATA_SOURCE_LABEL_MAP } from '@utils/universalTracker';
import { constrainNumber } from '@utils/number';
import { FormGroup, Input, Label } from 'reactstrap';
import { InputOverrideType, VariationInputMap } from '../types';
import { GLOBAL_UPDATE_CODE } from '../utils';
import { NumberInput } from './NumberInput';
import { FeatureStability } from '@g17eco/molecules/feature-stability';

const dataSourceOptions = Object.entries(VARIATION_DATA_SOURCE_LABEL_MAP).map(([value, label]) => ({
  label,
  value,
}));

const VARIATION_RANGE = {
  MIN: 0,
  MAX: 100,
};

export interface VariationProps {
  variation: VariationInputMap;
  columnCode?: string;
  onToggleSwitch: (props: { type: InputOverrideType; code: string }) => void;
  handleVariationChange: (variation: VariationInputMap) => void;
}

export const VariationForm = (props: VariationProps) => {
  const { variation, columnCode, onToggleSwitch, handleVariationChange } = props;
  const code = columnCode ?? GLOBAL_UPDATE_CODE;
  const { value, isEnforced, dataSource, confirmationRequired } = variation[code];

  const update = (property: string, value: number | string | boolean | undefined) => {
    handleVariationChange({ [code]: { ...variation[code], [property]: value } });
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    update('value', constrainNumber({ value: rawValue, min: VARIATION_RANGE.MIN, max: VARIATION_RANGE.MAX }));
  };

  return (
    <div className='row gx-0 mb-3'>
      <FormGroup switch className='col-6 d-flex align-items-center' id={'variation-toggle'}>
        <Input
          type='switch'
          className='mr-2'
          onChange={() => onToggleSwitch({ type: InputOverrideType.Variation, code })}
          checked={isEnforced}
        />
        <label htmlFor={'variation-toggle'}>Variation detection</label>
        <FeatureStability stability='beta' />
      </FormGroup>
      <div className='d-flex align-items-center justify-content-between'>
        <label className='pl-2'>Variation amount</label>
        <div className='col-6'>
          <NumberInput disabled={!isEnforced} value={value} onChange={onInputChange} min={0} max={100} suffix={'%'} />
        </div>
      </div>
      <div>
        <div className='d-flex align-items-center justify-content-between mt-2'>
          <label className='pl-2'>Compare variation against</label>
          <div className='col-6'>
            <SelectFactory
              selectType={SelectTypes.SingleSelect}
              options={dataSourceOptions}
              isDisabled={!isEnforced}
              onChange={(op) => (op ? update('dataSource', op.value) : null)}
              value={dataSourceOptions.find((op) => op.value === dataSource) ?? dataSourceOptions[0]}
              isMenuPortalTargetBody
            />
          </div>
        </div>
        <div className={`text-right ${isEnforced ? 'text-ThemeTextMedium' : 'text-ThemeBgDisabled'}`}>
          *Only compares against reports of the same period
        </div>
      </div>
      <FormGroup check inline className={'mt-2 ms-2'}>
        <Checkbox
          status={confirmationRequired ? CheckboxState.Checked : CheckboxState.Unchecked}
          id={`confirm-checkbox-${code}`}
          disabled={!isEnforced}
          onChange={() => update('confirmationRequired', !confirmationRequired)}
        />
        <Label
          check
          for={`confirm-checkbox-${code}`}
          className={`user-select-none ${isEnforced ? '' : 'text-ThemeTextLight'}`}
        >
          Two step confirmation when variation detected
        </Label>
      </FormGroup>
    </div>
  );
};
