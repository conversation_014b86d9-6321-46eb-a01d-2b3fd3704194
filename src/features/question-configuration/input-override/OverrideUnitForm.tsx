import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { FormGroup, Input, Label } from 'reactstrap';
import { GLOBAL_UPDATE_CODE, getUnitByTypeOptions, getUnitLabel } from '../utils';
import { InputOverrideType, UnitConfigValueType, UnitConfigType, UnitTypeMapValue } from '../types';
import { UnitTypes } from '@utils/units';
import { Checkbox, CheckboxState } from '@g17eco/atoms/checkbox';
import { QUESTION } from '@constants/terminology';

export interface UnitOverrideProps {
  unitTypeMap: Map<string, UnitTypeMapValue>;
  unitConfig: UnitConfigType;
  columnCode?: string;
  type: InputOverrideType;
  onSelectUnit: (props: { code: string; unitType: string; value: string | undefined }) => void;
  onToggleSwitch: (props: { code: string; type: InputOverrideType }) => void;
  handleCheckboxToggle: (props: { code: string; type: InputOverrideType }) => void;
}

export const getInputLabel = (type: InputOverrideType) => {
  switch (type) {
    case InputOverrideType.NumberScale:
      return 'Number scale override';
    case InputOverrideType.Unit:
      return 'Unit override';
    default:
      return '';
  }
};

const OverrideUnitSelect = (props: UnitOverrideProps & { isDisabled: boolean }) => {
  const { unitTypeMap, unitConfig, columnCode, onSelectUnit, isDisabled } = props;
  const code = columnCode ?? GLOBAL_UPDATE_CODE;
  const selectedUnitTypes = [...unitTypeMap.keys()];

  if (!selectedUnitTypes.length || !unitConfig) {
    return null;
  }

  if (selectedUnitTypes.length === 1) {
    const [unitType] = selectedUnitTypes;
    const options = getUnitByTypeOptions(unitType);
    const { defaultValue } = unitTypeMap.get(unitType) ?? {};

    return (
      <div className='col-6 order-2'>
        <SelectFactory
          key={unitType}
          placeholder={defaultValue ?? 'Not set'}
          selectType={SelectTypes.SingleSelect}
          options={options}
          value={options.find((op) => op.value === unitConfig[code][unitType].value) ?? null}
          onChange={(op) => onSelectUnit({ code, unitType, value: op?.value })}
          isMenuPortalTargetBody
          isDisabled={isDisabled}
        />
      </div>
    );
  }

  return (
    <div className='col-12 order-3'>
      {selectedUnitTypes.map((unitType) => {
        const options = getUnitByTypeOptions(unitType);
        const { defaultValue } = unitTypeMap.get(unitType) ?? {};

        return (
          <div key={unitType} className='row g-0 mt-2'>
            <Label for={unitType} className='col-4 mt-2'>
              {getUnitLabel(unitType)}
            </Label>
            <SelectFactory
              key={unitType}
              placeholder={defaultValue ?? 'Not set'}
              selectType={SelectTypes.SingleSelect}
              options={options}
              value={options.find((op) => op.value === unitConfig[code][unitType].value) ?? null}
              onChange={(op) => onSelectUnit({ code, unitType, value: op?.value })}
              className='col-8'
              menuPlacement='top'
              isMenuPortalTargetBody
              isDisabled={isDisabled}
            />
          </div>
        );
      })}
    </div>
  );
};

export const OverrideUnitForm = (props: UnitOverrideProps) => {
  const { type, columnCode, onToggleSwitch, unitConfig, unitTypeMap, handleCheckboxToggle } = props;
  const code = columnCode ?? GLOBAL_UPDATE_CODE;
  const { isEnforced, isLocked } = (() => {
    // get number scale toggle and checkbox state
    if (type === InputOverrideType.NumberScale) {
      return unitConfig[code][UnitTypes.numberScale];
    }
    // get unit toggle and checkbox state, only 1 toggle & checkbox for all unit, therefore, just need to get the first one value
    const firstUnitType = Object.keys(unitConfig[code]).find((key) => key !== UnitTypes.numberScale);
    return firstUnitType ? unitConfig[code][firstUnitType] : {} as UnitConfigValueType;
  })();

  return (
    <div className='row gx-0 mb-3'>
      <FormGroup switch className='col-6 d-flex align-items-center order-1' id={`${type}-${code}`}>
        <Input type='switch' className='mr-2' onChange={() => onToggleSwitch({ type, code })} checked={isEnforced} />
        <label htmlFor={`${type}-${code}`}> {getInputLabel(type)}</label>
      </FormGroup>
      <OverrideUnitSelect {...props} isDisabled={!isEnforced} />
      <FormGroup check inline className={`mt-1 ms-2 ${unitTypeMap.size === 1 ? 'order-3' : 'order-2'}`}>
        <Checkbox
          status={isEnforced && !isLocked ? CheckboxState.Checked : CheckboxState.Unchecked}
          id={`allow-checkbox-${type}-${code}`}
          disabled={!isEnforced}
          onChange={() => handleCheckboxToggle({ type, code })}
          className={isEnforced ? 'cursor-pointer' : ''}
        />
        <Label check for={`allow-checkbox-${type}-${code}`} className='user-select-none'>
          Allow users to choose other options on {QUESTION.SINGULAR} page
        </Label>
      </FormGroup>
    </div>
  );
};
