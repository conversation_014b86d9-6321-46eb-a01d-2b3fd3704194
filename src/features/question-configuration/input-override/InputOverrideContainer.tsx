import {
  InputOverrideParams,
  useGetInitiativeUniversalTrackersQuery,
  useUpdateInitiativeUniversalTrackerMutation,
} from '@api/initiative-universal-trackers';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { isDefined } from '@utils/index';
import { isSimpleNumericColumnType } from '@utils/universalTracker';
import { InputOverrideForm } from './InputOverrideForm';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import {
  getDefaultDecimal,
  getInputType,
  isSingleTableQuestion,
  getDefaultVariation,
  getInitialUnitConfig,
  getUnitTypeMap,
  hasOverriddenVariations,
} from '../utils';
import { MetricOverrideProps } from '../types';

const hasOverriddenProperty = (utr: InitiativeUniversalTracker, type: 'decimal' | 'unitInput' | 'numberScaleInput' | 'variations') => {
  if (type === 'decimal') {
    return (
      isDefined(utr.valueValidation?.[type]) ||
      utr.valueValidation?.table?.columns.some((col) => isDefined(col.validation?.[type]))
    );
  }
  if (type === 'variations') {
    return hasOverriddenVariations(utr);
  }
  return isDefined(utr[type]) || utr.valueValidation?.table?.columns.some((col) => isDefined(col[type]));
};

export const InputOverrideContainer = (props: MetricOverrideProps) => {
  const { handleReload, selectedQuestions, initiativeId, numericSelectedQuestions, handleCloseModal } = props;
  const selectedUtrIds = selectedQuestions.map((q) => q._id);
  const { addSiteError } = useSiteAlert();

  const { data: initiativeUtrs = [], isFetching: isInitiativeUtrsLoading } = useGetInitiativeUniversalTrackersQuery(
    initiativeId,
    {
      skip: !initiativeId || selectedQuestions.length === 0,
    }
  );

  const [updateInitiativeUtrs, { isLoading: isUpdating }] = useUpdateInitiativeUniversalTrackerMutation();

  const { overriddenQuestions, firstDecimalValidation, firstVariationOverride } = initiativeUtrs.reduce(
    (acc, utr) => {
      if (!selectedUtrIds.includes(utr.universalTrackerId)) {
        return acc;
      }

      const hasOverriddenDecimal = hasOverriddenProperty(utr, 'decimal');
      const hasOverriddenUnit =
        hasOverriddenProperty(utr, 'unitInput') || hasOverriddenProperty(utr, 'numberScaleInput');
      const hasOverriddenVariation = hasOverriddenProperty(utr, 'variations');  

      if (!hasOverriddenDecimal && !hasOverriddenUnit && !hasOverriddenVariation) {
        return acc;
      }

      if (hasOverriddenDecimal && !acc.firstDecimalValidation) {
        acc.firstDecimalValidation = utr;
      }
      if (hasOverriddenVariation && !acc.firstVariationOverride) {
        acc.firstVariationOverride = utr;
      }
      acc.overriddenQuestions.push(utr);
      return acc;
    },
    { overriddenQuestions: [], firstDecimalValidation: undefined, firstVariationOverride: undefined } as {
      overriddenQuestions: InitiativeUniversalTracker[];
      firstDecimalValidation: InitiativeUniversalTracker | undefined;
      firstVariationOverride: InitiativeUniversalTracker | undefined;
    }
  );

  const tableColumns = isSingleTableQuestion(selectedQuestions)
    ? selectedQuestions[0].valueValidation?.table?.columns?.filter((c) => isSimpleNumericColumnType(c)) || []
    : [];

  const inputType = getInputType(selectedQuestions);

  const defaultDecimal = getDefaultDecimal({
    inputType,
    tableColumns,
    valueValidation: firstDecimalValidation?.valueValidation,
  });

  const unitTypeMap = getUnitTypeMap({ selectedQuestions, overriddenQuestions, inputType });
  const defaultUnitConfig = getInitialUnitConfig({ inputType, tableColumns, unitTypeMap });
  
  const defaultVariation = getDefaultVariation({
    inputType,
    tableColumns,
    valueValidation: firstVariationOverride?.valueValidation,
  });

  const handleUpdate = ({ decimal, unitConfig, variation }: Pick<InputOverrideParams, 'decimal' | 'unitConfig' | 'variation'>) => {
    const data = {
      decimal,
      unitConfig,
      variation,
      initiativeId,
      utrIds: numericSelectedQuestions.map((q) => q._id),
    };
    updateInitiativeUtrs(data)
      .unwrap()
      .then(() => {
        handleReload({ reloadSurvey: true, closeModal: selectedQuestions.length === 1 });
      })
      .catch((err) => {
        addSiteError(err);
        handleReload({ closeModal: true });
      });
  };

  return (
    <InputOverrideForm
      key={`${JSON.stringify(defaultDecimal)}-${JSON.stringify(defaultUnitConfig)}-${JSON.stringify(defaultVariation)}`}
      isLoading={[isInitiativeUtrsLoading, isUpdating].some((loading) => loading)}
      selectedQuestions={selectedQuestions}
      tableColumns={tableColumns}
      handleUpdate={handleUpdate}
      overriddenQuestions={overriddenQuestions}
      defaultDecimal={defaultDecimal}
      defaultUnitConfig={defaultUnitConfig}
      defaultVariation={defaultVariation}
      unitTypeMap={unitTypeMap}
      handleCloseModal={handleCloseModal}
    />
  );
};
