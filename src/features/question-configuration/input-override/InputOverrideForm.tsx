import { TableColumn } from '@components/survey/form/input/table/InputInterface';
import {
  hasDataChanged,
  validateData,
  hasUnitDataChanged,
  validateUnitData,
  prepareUpdateData,
  DECIMAL_MAX,
  DECIMAL_MIN,
  invalidInputMessage,
  isMultipleUtrUpdate,
} from '../utils';
import { useContext, useState } from 'react';
import { Button, ModalBody, ModalFooter } from 'reactstrap';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { InputOverrideGroup } from './InputOverrideGroup';
import { UnitTypes } from '@utils/units';
import { isSimpleNumericColumnType } from '@utils/universalTracker';
import { cloneDeep } from '@apollo/client/utilities';
import { validateMinMax } from '@utils/universalTrackerValue';
import { Loader } from '@g17eco/atoms/loader';
import { BulkActionUtr } from '@components/survey-question-list/partials/BulkActionToolbar';
import { VariationDataSource } from '@g17eco/types/universalTracker';
import { InputOverrideParams } from '@api/initiative-universal-trackers';
import { MultipleInputOverrideGroup } from './MultipleInputOverrideGroup';
import { QuestionManagementContext } from '../../../apps/company-tracker/components/admin-dashboard/questions/QuestionManagementContainer';
import {
  VariationInput,
  DecimalType,
  ErrorDecimalType,
  UnitConfigType,
  VariationInputMap,
  InputOverrideType,
  UnitTypeMapValue,
} from '../types';

interface InputOverrideFormProps {
  isLoading: boolean;
  tableColumns: TableColumn[];
  selectedQuestions: BulkActionUtr[];
  overriddenQuestions: InitiativeUniversalTracker[];
  handleUpdate: (props: Pick<InputOverrideParams, 'decimal' | 'unitConfig' | 'variation'>) => void;
  defaultDecimal: DecimalType;
  defaultUnitConfig: UnitConfigType;
  defaultVariation: VariationInputMap;
  unitTypeMap: Record<string, Map<string, UnitTypeMapValue>>;
  handleCloseModal: () => void;
}

const defaultState = { data: undefined };

export const InputOverrideForm = ({
  isLoading,
  selectedQuestions,
  overriddenQuestions,
  tableColumns,
  handleUpdate,
  defaultDecimal,
  defaultUnitConfig,
  defaultVariation,
  unitTypeMap,
  handleCloseModal,
}: InputOverrideFormProps) => {
  const [decimal, setDecimal] = useState<DecimalType>(defaultDecimal);
  const [unitConfig, setUnitConfig] = useState<UnitConfigType>(defaultUnitConfig);
  const [variation, setVariation] = useState<VariationInputMap>(defaultVariation);
  const [error, setError] = useState<ErrorDecimalType>(defaultState);
  const { inputOverrideType } = useContext(QuestionManagementContext);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setError((prev) => ({
      ...prev,
      [name]: validateMinMax(value, DECIMAL_MIN, DECIMAL_MAX).errored ? invalidInputMessage : '',
    }));
    setDecimal((prev) => ({
      ...prev,
      [name]: { ...prev[name], value: value === '' ? undefined : parseInt(value) },
    }));
  };

  const getUnitOrNumberScaleValue = ({
    isEnforced,
    unitType,
    code,
  }: {
    isEnforced: boolean;
    unitType: string;
    code: string;
  }) => {
    const utrValue = unitTypeMap[code].get(unitType)?.defaultValue;
    // toggle is off
    if (!isEnforced) {
      return utrValue;
    }
    // toggle is on, default to 'single' if utr's numberScale is undefined
    if (unitType === UnitTypes.numberScale && !utrValue) {
      return 'single';
    }
    return utrValue;
  };

  const toggleDecimalEnforcement = (code: string) => {
    setDecimal((prev) => ({ ...prev, [code]: { value: undefined, isEnforced: !prev[code].isEnforced } }));
    setError((prev) => ({ ...prev, [code]: undefined }));
  };

  const toggleNumberScaleEnforcement = (code: string) => {
    const unitType = UnitTypes.numberScale;
    setUnitConfig((prev) => {
      const isNowEnforced = !prev[code][unitType].isEnforced;
      return {
        ...prev,
        [code]: {
          ...prev[code],
          [unitType]: {
            value: getUnitOrNumberScaleValue({ isEnforced: isNowEnforced, unitType, code }),
            isEnforced: isNowEnforced,
            isLocked: true,
          },
        },
      };
    });
  };

  const toggleUnitEnforcement = (code: string) => {
    setUnitConfig((prev) => {
      const currentUnits = cloneDeep(prev[code]);
      delete currentUnits.numberScale;
      for (const unitType in currentUnits) {
        const isNowEnforced = !currentUnits[unitType].isEnforced;
        currentUnits[unitType].isEnforced = isNowEnforced;
        currentUnits[unitType].value = getUnitOrNumberScaleValue({ isEnforced: isNowEnforced, unitType, code });
        currentUnits[unitType].isLocked = true;
      }
      return { ...prev, [code]: { ...prev[code], ...currentUnits } };
    });
  };

  const toggleVariation = (code: string) => {
    setVariation((prev) => {
      const isNowEnforced = !prev[code].isEnforced;
      return {
        ...prev,
        [code]: {
          value: undefined,
          isEnforced: isNowEnforced,
          dataSource: isNowEnforced ? VariationDataSource.LastMonth : undefined,
          confirmationRequired: isNowEnforced ? false : undefined,
        },
      };
    });
  };

  const onToggleSwitch = ({ type, code }: { type: InputOverrideType; code: string }) => {
    switch (type) {
      case InputOverrideType.Decimal:
        return toggleDecimalEnforcement(code);
      case InputOverrideType.NumberScale:
        return toggleNumberScaleEnforcement(code);
      case InputOverrideType.Unit:
        return toggleUnitEnforcement(code);
      case InputOverrideType.Variation:
        return toggleVariation(code);
      default:
        return;
    }
  };

  const handleNumberScaleCheckbox = (code: string) => {
    const unitType = UnitTypes.numberScale;
    setUnitConfig((prev) => {
      const isNowLocked = !prev[code][unitType].isLocked;
      return {
        ...prev,
        [code]: { ...prev[code], [unitType]: { ...prev[code][unitType], isLocked: isNowLocked } },
      };
    });
  };

  const handleUnitCheckbox = (code: string) => {
    setUnitConfig((prev) => {
      const currentUnits = cloneDeep(prev[code]);
      delete currentUnits.numberScale;
      for (const unitType in currentUnits) {
        const isNowLocked = !currentUnits[unitType].isLocked;
        currentUnits[unitType].isLocked = isNowLocked;
      }
      return { ...prev, [code]: { ...prev[code], ...currentUnits } };
    });
  };

  const handleCheckboxToggle = ({ type, code }: { type: InputOverrideType; code: string }) => {
    switch (type) {
      case InputOverrideType.NumberScale:
        return handleNumberScaleCheckbox(code);
      case InputOverrideType.Unit:
        return handleUnitCheckbox(code);
      default:
        return;
    }
  };

  const handleVariationChange = (overrides: VariationInputMap) => {
    setVariation((prev) => ({ ...prev, ...overrides }));
  };

  const isFormValid =
    validateData<number>({ data: decimal, error }) &&
    validateUnitData({ data: unitConfig }) &&
    validateData<number>({ data: variation });
  const isMultipleUpdate = isMultipleUtrUpdate({ selectedQuestions, overriddenQuestions });
  const hasAnythingChanged =
    hasDataChanged(defaultDecimal, decimal) ||
    hasUnitDataChanged(defaultUnitConfig, unitConfig) ||
    hasDataChanged<VariationInput>(defaultVariation, variation);
  const isAllowedToUpdate = isFormValid && (hasAnythingChanged || isMultipleUpdate);

  const onClickUpdate = () => {
    if (!isAllowedToUpdate) {
      return;
    }
    const dataToUpdate = prepareUpdateData({
      type: inputOverrideType,
      decimal,
      unitConfig,
      variation
    });
    handleUpdate(dataToUpdate);
  };

  const onSelectUnit = ({ code, unitType, value }: { code: string; unitType: string; value: string | undefined }) => {
    setUnitConfig((prev) => ({
      ...prev,
      [code]: { ...prev[code], [unitType]: { ...prev[code][unitType], value } },
    }));
  };

  return (
    <>
      <ModalBody className='pt-3 overflow-scroll' style={{ maxHeight: '500px' }}>
        {isLoading ? <Loader /> : null}
        {selectedQuestions.length > 1 ? (
          <MultipleInputOverrideGroup
            hasAnythingChanged={hasAnythingChanged}
            unitTypeMap={unitTypeMap}
            unitConfig={unitConfig}
            decimal={decimal}
            onChange={onChange}
            error={error}
            onSelectUnit={onSelectUnit}
            onToggleSwitch={onToggleSwitch}
            handleCheckboxToggle={handleCheckboxToggle}
            handleVariationChange={handleVariationChange}
            variation={variation}
          />
        ) : (
          <InputOverrideGroup
            numericColumns={tableColumns.filter((col) => isSimpleNumericColumnType(col))}
            unitTypeMap={unitTypeMap}
            unitConfig={unitConfig}
            decimal={decimal}
            onChange={onChange}
            error={error}
            onSelectUnit={onSelectUnit}
            onToggleSwitch={onToggleSwitch}
            handleCheckboxToggle={handleCheckboxToggle}
            variation={variation}
            handleVariationChange={handleVariationChange}
          />
        )}
      </ModalBody>
      <ModalFooter>
        <Button color='transparent' onClick={handleCloseModal}>
          Cancel
        </Button>
        <Button color='primary' disabled={!isAllowedToUpdate || isLoading} onClick={onClickUpdate}>
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
