import { DecimalInputProps } from './DecimalInput';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Label } from 'reactstrap';
import { UnitOverrideProps } from './OverrideUnitForm';
import { GLOBAL_UPDATE_CODE } from '../utils';
import { InputOverrideType, UnitConfigType, UnitTypeMapValue } from '../types';
import { InputOverrideField } from './InputOverrideField';
import { useContext } from 'react';
import { QuestionManagementContext } from '../../../apps/company-tracker/components/admin-dashboard/questions/QuestionManagementContainer';
import { VariationProps } from './VariationForm';

const inputOverrideTypeOptions: Option<InputOverrideType>[] = [
  {
    label: 'Number scale',
    value: InputOverrideType.NumberScale,
  },
  {
    label: 'Decimals',
    value: InputOverrideType.Decimal,
  },
  {
    label: 'Units',
    value: InputOverrideType.Unit,
  },
  {
    label: 'Variation detection',
    value: InputOverrideType.Variation,
  },
];

interface Props extends DecimalInputProps, VariationProps, Omit<UnitOverrideProps, 'unitTypeMap' | 'type'> {
  unitTypeMap: Record<string, Map<string, UnitTypeMapValue>>;
  unitConfig: UnitConfigType;
  hasAnythingChanged: boolean;
}

export const MultipleInputOverrideGroup = (props: Props) => {
  const { unitTypeMap, hasAnythingChanged, ...rest } = props;
  const { inputOverrideType, setInputOverrideType } = useContext(QuestionManagementContext);
  if (!inputOverrideType) {
    return null;
  }

  return (
    <div>
      <Label className='mb-1'>
        <span className='fw-semibold'>Override</span>
        {hasAnythingChanged ? (
          <SimpleTooltip text='Please save or cancel your change before making additional input overrides'>
            <i className='fal fa-bell-exclamation text-ThemeWarningMedium ml-2' />
          </SimpleTooltip>
        ) : null}
      </Label>
      <SelectFactory
        selectType={SelectTypes.SingleSelect}
        options={inputOverrideTypeOptions}
        onChange={(op) => op?.value && setInputOverrideType(op.value)}
        isSearchable={false}
        value={inputOverrideTypeOptions.find((op) => op.value === inputOverrideType) ?? null}
        className='mb-3'
        isDisabled={hasAnythingChanged}
        isMenuPortalTargetBody
      />

      <InputOverrideField {...rest} showEmptyAlert type={inputOverrideType} unitTypeMap={unitTypeMap[GLOBAL_UPDATE_CODE]} />
    </div>
  );
};
