import { BulkActionUtr } from '@components/survey-question-list/partials/BulkActionToolbar';
import type { VariationDataSource } from '@g17eco/types/universalTracker';

export interface MetricOverrideProps {
  handleReload: (props?: { reloadSurvey?: boolean; closeModal?: boolean }) => Promise<void | undefined>;
  selectedQuestions: BulkActionUtr[];
  numericSelectedQuestions: BulkActionUtr[];
  initiativeId: string;
  handleCloseModal: () => void;
}

export enum InputOverrideType {
  Unit = 'unit',
  NumberScale = 'numberScale',
  Decimal = 'decimal',
  Variation = 'variation',
}

export enum ConfigurationType {
  MetricOverrides = 'Metric overrides',
  InputOverrides = 'Input overrides',
  Tags = 'Assign tags',
}

export enum InputOverrideFormType {
  SingleInput = 'single-input',
  Table = 'table',
}

export type InputOverrideDataType<T = string> = { value: T | undefined; isEnforced: boolean };

export type DecimalType = Record<string, InputOverrideDataType<number>>;

export interface ErrorDecimalType {
  [key: string]: string | undefined;
}

export type UnitConfigValueType = InputOverrideDataType & { isLocked: boolean };

// key can be __DATA__ or column's code
export type UnitConfigType = Record<string, Record<string, UnitConfigValueType>>;

export type VariationInput = InputOverrideDataType<number> & {
  dataSource: VariationDataSource | undefined;
  confirmationRequired: boolean | undefined;
};

// key can be __DATA__ or column's code
export type VariationInputMap = Record<string, VariationInput>;

export type UnitTypeMapValue = {
  defaultValue: string | undefined;
  overriddenValue: string | undefined;
  isLocked: boolean;
};