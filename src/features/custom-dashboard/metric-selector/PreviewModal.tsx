import { <PERSON>dal, <PERSON><PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { GridItemProps } from '@routes/custom-dashboard/GridItem';
import { HistoricalUtrsState, PreviewChartData } from '../types';
import { MultiUtrsChartWidget } from '@routes/custom-dashboard/items/charts/multi-utrs-chart/MultiUtrsChartWidget';
import {
  ChartSubType,
  GridDashboardChartItem,
  GridDashboardIntegrationItem,
  GridDashboardSDGChartItem,
  InsightDashboardItemType,
} from '@g17eco/types/insight-custom-dashboard';
import { ChartWidget } from '@routes/custom-dashboard/items/charts/ChartWidget';
import { transformVariables } from '@routes/custom-dashboard/utils';
import { isMultipleMetricsChart, getChartCalculation } from '../utils/dashboard-utils';
import { NoData } from '@routes/custom-dashboard/items/charts/common/NoData';
import { generateId } from '@utils/index';
import { SingleValueIntegration } from '@routes/custom-dashboard/items/charts/multi-utrs-chart/SingleValueIntegration';
import { IntegrationUtrData } from '@g17eco/types/integration';
import { HistoricalUtrs } from '@api/insights';
import { BasicAlert } from '@g17eco/molecules/alert';
import { DisaggregatedChartWidget } from '../items/disagregated-data-chart/DisaggregatedChartWidget';

interface PreviewModalProps extends Pick<GridItemProps, 'survey'> {
  initiativeId: string;
  isDisaggregated?: boolean;
  isOpen: boolean;
  toggleOpen: () => void;
  chartData: PreviewChartData;
  historicalUtrsState: HistoricalUtrsState;
}

export const PreviewModal = ({
  initiativeId,
  isOpen,
  toggleOpen,
  chartData,
  survey,
  historicalUtrsState,
  isDisaggregated,
}: PreviewModalProps) => {
  const { historicalUtrs = [], isLoading, errorMessage } = historicalUtrsState;

  const isSDGTracker = chartData.type === InsightDashboardItemType.SDGTracker;
  const altUnitText = isSDGTracker ? 'contribution' : '';
  const utrs = historicalUtrs.map((utr) => utr.utr);
  const noData = historicalUtrs.every(({ utrvs }) => !utrvs.length);

  const tempVariables = transformVariables(chartData.metrics);
  const tempItem: GridDashboardChartItem | GridDashboardSDGChartItem | GridDashboardIntegrationItem = {
    ...chartData,
    variables: tempVariables,
    calculation: getChartCalculation({
      metrics: chartData.metrics,
      variables: tempVariables,
      subType: chartData.subType,
      chartType: chartData.type,
      questionsMap: new Map(utrs.map((utr) => [utr.code, utr])),
    }),
    // This is added for displaying the chart placeholder
    _id: generateId(),
    gridSize: {
      x: 0,
      y: 0,
      w: 0,
      h: 0,
    },
  };

  if (isLoading) {
    return <Loader />;
  }

  if (!chartData.metrics.length) {
    return null;
  }

  const previewChart = () => {
    if (noData) {
      return <NoData item={tempItem} />;
    }

    if (isDisaggregated) {
      // historicalUtrs here must be HistoricalUtrs not IntegrationUtrData
      // TODO: May need to support integration data
      return <DisaggregatedChartWidget item={tempItem} utrsData={historicalUtrs as HistoricalUtrs[]} />;
    }

    if (isMultipleMetricsChart(chartData)) {
      return (
        <MultiUtrsChartWidget
          altUnitText={altUnitText}
          initiativeId={initiativeId}
          item={tempItem}
          readOnly
          utrsData={historicalUtrs}
        />
      );
    }

    if (chartData.type === InsightDashboardItemType.Integration) {
      return (
        <SingleValueIntegration
          item={tempItem}
          integrationData={{ utrsData: historicalUtrs as IntegrationUtrData[] }}
          hasSparkLine={chartData.subType === ChartSubType.SparkLine}
        />
      );
    }

    return (
      <ChartWidget
        utrData={(historicalUtrs as HistoricalUtrs[])[0]}
        selectedColumnCode={chartData.metrics[0].valueListCode}
        initiativeId={initiativeId}
        type={chartData.type}
        subType={chartData.subType}
        readOnly
        survey={survey}
      />
    );
  };

  return (
    <Modal fade={false} isOpen={isOpen} toggle={toggleOpen} backdrop='static' keyboard>
      <ModalHeader toggle={toggleOpen}>Preview chart</ModalHeader>
      <ModalBody style={{ height: '260px' }}>
        <BasicAlert type={'danger'} className='mt-2'>
          {errorMessage}
        </BasicAlert>
        {previewChart()}
      </ModalBody>
    </Modal>
  );
};
