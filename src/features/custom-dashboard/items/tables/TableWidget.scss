@import '../../../../css/clamp-lines';

.table-widget__container {
  .table-component-container {
    .pagination {
      margin: 0 !important;
    }

    &.table-unresponsive {
      .g17-table {
        .g17-thead {
          height: 48px;
          max-height: 48px;

          .g17-th {
            &:not(:first-of-type) {
              text-align: center;
            }

            i {
              display: none;
            }

            div:first-child {
              justify-content: center;
            }
          }
        }

        .g17-tbody {
          .g17-tr {
            height: 60px;
            max-height: 60px;
            background-color: var(--theme-BgExtralight);

            .g17-td {

              border-left: 1px solid var(--theme-BorderDefault);
              border-bottom: 1px solid var(--theme-BorderDefault);
              padding: 3px 12px !important;

              &:last-of-type {
                border-right: 1px solid var(--theme-BorderDefault);
              }

              &:first-of-type {
                .cell__value-label {
                  cursor: pointer;
                  @include text-clamp-lines(2);
                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}