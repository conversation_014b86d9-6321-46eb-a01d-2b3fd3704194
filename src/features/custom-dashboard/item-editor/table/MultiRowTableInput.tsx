/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useEffect, useRef, useState } from 'react';
import { Button, Collapse, Input } from 'reactstrap';
import { TableData, TableRowDataInfo } from '../../types';
import { QuestionSelectingFilter } from '@features/custom-dashboard';
import { QuestionData, defaultExtendedUtrVariable } from '@routes/custom-dashboard/utils';
import { generateId } from '@utils/index';
import { MAX_LENGTH_TABLE_TITLE, removeRow, updateRow } from '../../utils/table-utils';
import IconButton from '@components/button/IconButton';
import { useGetInitiativeBlueprintQuestionsQuery } from '@api/admin-dashboard';
import { ExtendedUtrVariable } from '@routes/custom-dashboard/types';
import { InputRow, TableDraggableColumn, TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import './MultiRowTableInput.scss';

const commonBtnClasses = 'w-100 rounded-0 d-flex justify-content-center align-items-center';

interface Props {
  initiativeId: string;
  tableData: TableData;
  setTableData: React.Dispatch<React.SetStateAction<TableData>>;
}

export function MultiRowTableInput(props: Props) {
  const { initiativeId, tableData, setTableData } = props;
  const { data: blueprintQuestions = [] } = useGetInitiativeBlueprintQuestionsQuery({ initiativeId });
  const [rowData, setRowData] = React.useState<ExtendedUtrVariable>(defaultExtendedUtrVariable);
  const [isAdding, setIsAdding] = useState(true);
  const addBtnDisabled = !rowData.code;
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (!(rowData.code || tableData.editRowId)) {
      return;
    }
    inputRef.current?.focus();
  }, [rowData.code, tableData.editRowId]);

  const onCollapseEnterEnded = () => {
    inputRef.current?.focus();
  };

  const updateTableData = (data: Partial<TableData>) => setTableData((prev) => ({ ...prev, ...data }));
  const setRowEditId = (id: string) => updateTableData({ editRowId: id });

  const updateRowData = (data: Partial<ExtendedUtrVariable>) => {
    setRowData((prev) => ({
      ...prev,
      ...data,
    }));
  };

  const clearData = () => {
    setRowEditId('');
    setRowData(defaultExtendedUtrVariable);
    setIsAdding(false);
  };

  const onAdd = () => {
    if (!rowData.code || !blueprintQuestions) {
      return;
    }

    const { name, ...utrData } = rowData;
    const newRowData: TableRowDataInfo = {
      id: generateId(),
      data: {
        value: {
          name: name || blueprintQuestions.find((question) => question.code === rowData.code)?.name || '',
        },
        variables: [
          {
            ...utrData,
          },
        ],
      },
    };

    updateTableData({ rowData: [...tableData.rowData, newRowData], editRowId: '' });
    clearData();
  };

  function onEdit() {
    if (!blueprintQuestions) {
      return;
    }
    const { name, ...utrData } = rowData;
    const newRowData: Partial<TableRowDataInfo> = {
      id: tableData.editRowId,
      data: {
        value: {
          name: name || blueprintQuestions.find((question) => question.code === rowData.code)?.name || '',
        },
        variables: [
          {
            ...utrData,
          },
        ],
      },
    };
    updateTableData({
      rowData: updateRow(tableData.rowData, newRowData),
    });
    clearData();
  }

  const columns: TableDraggableColumn[] = [
    { header: 'Metric titles', className: 'col-metric' },
    { header: '' },
  ];
  const rows: InputRow[] = tableData.rowData.map(({ id, data }, index) => {
    const { value } = data;
    return {
      id,
      cols: [
        <div key={id} className='text-truncate'>{value.name}</div>,
        <div key={`${index}-${id}`} className='d-flex align-items-center'>
          <IconButton
            icon='fal fa-pencil-alt'
            disabled={id === tableData.editRowId}
            color='link'
            size='sm'
            onClick={() => handleEditRow({ id, data })}
          />
          <IconButton
            icon='fal fa-trash-alt text-danger'
            color='link'
            size='sm'
            onClick={() => handleDeleteRow({ id, data })}
          />
        </div>,
      ],
      disabled: !!tableData.editRowId,
      active: id === tableData.editRowId,
    };
  });

  const handleEditRow = (info: TableRowDataInfo) => {
    setIsAdding(true);
    setRowData({
      ...info.data.variables[0],
      name: info.data.value.name,
    });
    setRowEditId(info.id);
  };

  const handleDeleteRow = (info: TableRowDataInfo) => {
    updateTableData({
      rowData: removeRow(tableData.rowData, info.id),
    });
    clearData();
  };

  const orderRows = (orderedIds: string[]) => {
    const newOrderedRowData = orderedIds.reduce((acc, id) => {
      const rowData = tableData.rowData.find((r) => r.id === id);
      if (rowData) {
        acc.push(rowData);
        return acc;
      }
      return acc;
    }, [] as TableRowDataInfo[]);

    updateTableData({ rowData: newOrderedRowData, editRowId: '' });
  };

  const handleSelectQuestion = ({ code, ...rest }: QuestionData) => {
    if (!code) {
      updateRowData({ ...rest, code: '', name: '' });
      return;
    }
    const name = blueprintQuestions.find((question) => question.code === code)?.valueLabel || '';
    updateRowData({ ...rest, code, name });
  };

  return (
    <div className=' mt-4'>
      <div className='table-input__container'>
        <TableDraggableRows
          columns={columns}
          data={rows}
          handleUpdate={orderRows}
          classes={{ wrapper: 'default-theme w-100 overflow-hidden' }}
        />
      </div>
      <Collapse isOpen={isAdding} onEntered={onCollapseEnterEnded}>
        <div key={Number(isAdding)} className='table-input__form-container'>
          <QuestionSelectingFilter
            key={tableData.editRowId}
            questionData={rowData}
            handleSelect={handleSelectQuestion}
            initiativeId={initiativeId}
          />
          <Input
            innerRef={inputRef}
            type='text'
            className='text-md mt-3'
            disabled={addBtnDisabled}
            maxLength={MAX_LENGTH_TABLE_TITLE}
            value={rowData.name ?? ''}
            onChange={(e) => updateRowData({ name: e.target.value })}
            placeholder='Metric title to appear on table (optional)'
          />
        </div>
        <>
          {tableData.editRowId === '' ? (
            <Button
              color='primary'
              className={`${commonBtnClasses} border-0 table-input__save-btn`}
              disabled={addBtnDisabled}
              onClick={onAdd}
            >
              <i className='fa fa-plus mr-2' />
              Save to table
            </Button>
          ) : (
            <Button
              color='primary'
              className={`${commonBtnClasses} border-0 table-input__save-btn`}
              disabled={addBtnDisabled}
              onClick={onEdit}
            >
              <i className='fa fa-plus mr-2' />
              Update row
            </Button>
          )}
          <Button outline onClick={clearData} className={`${commonBtnClasses} table-input__cancel-btn`}>
            <i className='fa-solid fa-angle-up mr-2'></i>
            Cancel
          </Button>
        </>
      </Collapse>
      {!isAdding && (
        <Button outline onClick={() => setIsAdding(true)} className={`${commonBtnClasses} table-input__add-btn`}>
          <i className='fas fa-plus fa-2x mr-2' />
          Add additional metric
        </Button>
      )}
    </div>
  );
}
