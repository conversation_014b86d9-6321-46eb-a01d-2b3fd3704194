/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { CHART_COLORS } from '../../../routes/custom-dashboard/utils';
import {
  CalculationType,
  ChartSubType,
  GridDashboardChartItem,
  InsightDashboardItemType,
  InsightDashboardTempItemType,
} from '../../../types/insight-custom-dashboard';
import { UniversalTrackerBlueprintMin } from '../../../types/universalTracker';
import { checkIsCustomTitle, getChartCalculation, getUniqueUtrVariables, removeMetric } from './dashboard-utils';

describe('removeMetric', () => {
  it('should match by reference', () => {
    const variableA = { code: 'metric1' };
    const metrics = [variableA, { code: 'metric2' }];
    const result = removeMetric(metrics, variableA, 0);
    expect(result).eqls([{ code: 'metric2' }]);
  });

  it('should match by passed in index', () => {
    const variableA = { code: 'metric1' };
    const metrics = [{ code: 'metric1' }, { code: 'metric2' }];
    const result = removeMetric(metrics, variableA, 0);
    expect(result).eqls([{ code: 'metric2' }]);
  });

  it('should remove metric from the list, where index does not match', () => {
    const metrics = [{ code: 'metric1' }, { code: 'metric2' }];
    const result = removeMetric(metrics, { code: 'metric1' }, 2);
    expect(result).eqls([{ code: 'metric2' }]);
  });

  it('should remove metric from the list with valueListCode', () => {
    const metrics = [{ code: 'metric1', valueListCode: 'a' }, { code: 'metric2' }];
    const result = removeMetric(metrics, { code: 'metric1', valueListCode: 'a' }, 0);
    expect(result).eqls([{ code: 'metric2' }]);
  });

  it('should remove metric only the specified index', () => {
    const metrics = [
      { code: 'metric1', valueListCode: 'a' },
      { code: 'metric2' },
      { code: 'metric1', valueListCode: 'a' },
      { code: 'metric1', valueListCode: 'a' },
    ];
    const result = removeMetric(metrics, { code: 'metric1', valueListCode: 'a' }, 2);
    expect(result).eqls([
      { code: 'metric1', valueListCode: 'a' },
      { code: 'metric2' },
      { code: 'metric1', valueListCode: 'a' },
    ]);
  });
});

describe('getChartCalculation', () => {
  const metrics = [
    { code: 'metric1', valueListCode: 'code1', name: 'Test 1' },
    { code: 'metric2' },
    { code: 'metric1', valueListCode: 'code1', name: 'Test 2' },
    { code: 'metric1', valueListCode: 'code1', name: 'Test 3' },
  ];
  const defaultUtrvBlueprintMin = {
    _id: 'testId',
    typeCode: 'testTypeCode',
    valueType: 'testValueType',
    type: 'testType',
  };
  const input = {
    metrics,
    variables: {
      a: { code: 'metric1', valueListCode: 'code1' },
      b: { code: 'metric2' },
    },
    questionsMap: new Map(
      metrics.map((metric) => [
        metric.code,
        { valueLabel: metric.code, ...defaultUtrvBlueprintMin, ...metric } as UniversalTrackerBlueprintMin,
      ])
    ),
  };
  const variablesMap = new Map(Object.entries(input.variables).map(([key, value]) => [value.code, key]));

  it('should return calculation for bar chart', () => {
    const barChartInput = {
      ...input,
      subType: ChartSubType.Bar,
    };

    const calculation = getChartCalculation({ ...barChartInput, chartType: InsightDashboardItemType.Chart });
    expect(calculation.type).toEqual(CalculationType.Formula);
    expect(calculation.values.length).toEqual(input.metrics.length);
    expect(calculation.headers).toBeDefined();
    calculation.values.forEach((value, index) => {
      const name = input.metrics[index].name || input.metrics[index].code;
      const code = input.metrics[index].code;
      expect(value.name).equal(name);
      expect(value.formula).equal(`{${variablesMap.get(code)}}`);
      expect(value.options?.style).equal(CHART_COLORS[index]);
    });
  });

  it('should return calculation for other charts', () => {
    const otherChartInput = {
      ...input,
      subType: ChartSubType.Line,
    };

    const calculation = getChartCalculation({ ...otherChartInput, chartType: InsightDashboardItemType.Chart });
    expect(calculation.type).toEqual(CalculationType.Formula);
    expect(calculation.values.length).toEqual(input.metrics.length);
    calculation.values.forEach((value, index) => {
      const name = input.metrics[index].name || input.metrics[index].code;
      const code = input.metrics[index].code;
      expect(value.name).equal(name);
      expect(value.formula).equal(`{${variablesMap.get(code)}}`);
      expect(value.options).toBe(undefined);
    });
  });
});

describe('getUniqueUtrVariables', () => {
  it('should return unique utr variables', () => {
    const testCases = [
      {
        input: [
          { code: 'metric1', valueListCode: 'a', name: 'Test 1' },
          { code: 'metric2' },
          { code: 'metric1', valueListCode: 'a', name: 'Test 2' },
        ],
        output: [{ code: 'metric1', valueListCode: 'a', name: 'Test 1' }, { code: 'metric2' }],
      },
      {
        input: [
          { code: 'metric1', valueListCode: 'a', name: 'Test 2' },
          { code: 'metric1', valueListCode: 'a', name: 'Test 3' },
        ],
        output: [{ code: 'metric1', valueListCode: 'a', name: 'Test 2' }],
      },
    ];

    testCases.forEach(({ input, output }) => {
      expect(getUniqueUtrVariables(input)).toStrictEqual(output);
    });
  });
});

describe('checkIsCustomTitle()', () => {
  describe('Adding chart', () => {
    const item = { type: InsightDashboardTempItemType.TempChart, subType: ChartSubType.SingleValue };
    const chartData = { metrics: [], title: undefined };
    const questionsMap: Map<string, UniversalTrackerBlueprintMin> = new Map();

    it('should return false if chart title is not edited', () => {
      expect(checkIsCustomTitle({ item, chartData: { ...chartData }, questionsMap })).toBe(false);
    });

    it('should return true if chart title is edited', () => {
      expect(checkIsCustomTitle({ item, chartData: { ...chartData, title: 'Custom title' }, questionsMap })).toBe(true);
    });
  });

  describe('Editing chart', () => {
    const item = { type: InsightDashboardItemType.Chart, _id: 'id' } as GridDashboardChartItem;
    const chartData = { metrics: [], title: undefined };

    it('should return false if metrics are not selected', () => {
      const variables = {};
      expect(checkIsCustomTitle({ item: { ...item, variables }, chartData, questionsMap: new Map() })).toBe(false);
    });

    const questionsMap = new Map([
      ['metric1', { code: 'metric1', valueLabel: 'Metric1' }],
      ['metric2', { code: 'metric2', valueLabel: 'Metric2' }],
    ]) as Map<string, UniversalTrackerBlueprintMin>;

    it('should compare chartData title with utr title using first metric only', () => {
      const variables = { a: { code: 'metric1' }, b: { code: 'metric2' } };
      const customTitleResult = checkIsCustomTitle({
        item: { ...item, variables },
        chartData: { ...chartData, title: 'Custom' },
        questionsMap,
      });
      expect(customTitleResult).toBe(true);

      const defaultTitleResult = checkIsCustomTitle({
        item: { ...item, variables },
        chartData: { ...chartData, title: 'Metric1' },
        questionsMap,
      });
      expect(defaultTitleResult).toBe(false);
    });
  });
});
