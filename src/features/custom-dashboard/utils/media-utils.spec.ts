import { UploadedDashboardFile } from '@api/insight-dashboards';
import {
  GridDashboardItem,
  GridDashboardMediaItem,
  InsightDashboardItemType,
  MediaFile,
} from '@g17eco/types/insight-custom-dashboard';
import { cloneFile, getDeletedDocumentIds, mapFilesToItems, getNewFilesToUpload } from './media-utils';

const generateFakeItem = (type: InsightDashboardItemType, _id: string = '1'): GridDashboardItem =>
  ({
    type,
    _id,
    gridSize: { x: 0, y: 0, w: 0, h: 0 },
  } as GridDashboardItem);

describe('getNewFilesToUpload', () => {
  it('should return an empty array if no new files are found', () => {
    const newItems: GridDashboardItem[] = [];
    const result = getNewFilesToUpload(newItems);
    expect(result).toEqual({});
  });

  it('should return an array of new files with originalFile to be uploaded', () => {
    const newItems = [
      generateFakeItem(InsightDashboardItemType.Text),
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '2'),
        files: [
          {
            originalFile: new File(['file1'], 'file1.jpg', { type: 'image/jpeg' }),
            documentId: '123',
          },
          {
            originalFile: new File(['file2'], 'file2.jpg', { type: 'image/jpeg' }),
          },
        ] as MediaFile[],
      },
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '3'),
        files: [
          {
            originalFile: new File(['file3'], 'file3.jpg', { type: 'image/jpeg' }),
          },
        ] as MediaFile[],
      },
    ];
    const result = getNewFilesToUpload(newItems);
    expect(result).toEqual({
      '2': [new File(['file2'], 'file2.jpg', { type: 'image/jpeg' })],
      '3': [new File(['file3'], 'file3.jpg', { type: 'image/jpeg' })],
    });
  });
});

describe('mapFilesToItems', () => {
  it('should return an empty array when there are no files or items', () => {
    const files: UploadedDashboardFile[] = [];
    const items: GridDashboardItem[] = [];

    const result = mapFilesToItems(files, items);

    expect(result).toEqual([]);
  });

  it('should return the items as is when there are files but no items', () => {
    const files: UploadedDashboardFile[] = [
      { name: 'file1', documentId: '1', itemId: '1' },
      { name: 'file2', documentId: '2', itemId: '2' },
    ];
    const items: GridDashboardItem[] = [];

    const result = mapFilesToItems(files, items);

    expect(result).toEqual([]);
  });

  it('should return the items as is when there are items but no files', () => {
    const files: UploadedDashboardFile[] = [];
    const items: GridDashboardItem[] = [
      { ...generateFakeItem(InsightDashboardItemType.Text) },
      { type: InsightDashboardItemType.Media, files: [] },
    ] as GridDashboardItem[];

    const result = mapFilesToItems(files, items);

    expect(result).toEqual(items);
  });

  it('should map files to items when there are both files and items', () => {
    const files: UploadedDashboardFile[] = [
      { name: 'file1', documentId: '1', itemId: '2' },
      { name: 'file2', documentId: '2', itemId: '2' },
    ];
    const items: GridDashboardItem[] = [
      { ...generateFakeItem(InsightDashboardItemType.Text) },
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '2'),
        files: [
          { name: 'file2', url: 'temp2', type: 'video/mp4', originalFile: { name: 'file2' }, ratio: 16 / 9 },
          { name: 'file1', url: 'temp1', type: 'image/png', originalFile: { name: 'file1' }, ratio: 1 },
        ],
      } as GridDashboardMediaItem,
    ];

    const result = mapFilesToItems(files, items);

    expect(result).toEqual([
      { ...generateFakeItem(InsightDashboardItemType.Text) },
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '2'),
        files: [
          { documentId: '2', ratio: 16 / 9 },
          { documentId: '1', ratio: 1 },
        ],
      },
    ]);
  });

  it('should not map files to items when files are uploaded', () => {
    const files: UploadedDashboardFile[] = [
      { name: 'file1', documentId: '1', itemId: '2' },
      { name: 'file2', documentId: '2', itemId: '2' },
    ];
    const items: GridDashboardItem[] = [
      { ...generateFakeItem(InsightDashboardItemType.Text) },
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '2'),
        files: [
          { name: 'file2', type: 'video/mp4', documentId: '2', ratio: 16 / 9 },
          { name: 'file1', type: 'image/png', originalFile: { name: 'file1' }, ratio: 1 },
        ],
      } as GridDashboardMediaItem,
    ];

    const result = mapFilesToItems(files, items);

    expect(result).toEqual([
      { ...generateFakeItem(InsightDashboardItemType.Text) },
      {
        ...generateFakeItem(InsightDashboardItemType.Media, '2'),
        files: [
          { documentId: '2', ratio: 16 / 9 },
          { documentId: '1', ratio: 1 },
        ],
      },
    ]);
  });
});

describe('getDeletedDocumentIds', () => {
  it('should return an empty array if both old and new items are empty', () => {
    const oldItems: GridDashboardItem[] = [];
    const newItems: GridDashboardItem[] = [];
    const deletedDocumentIds = getDeletedDocumentIds(oldItems, newItems);
    expect(deletedDocumentIds).toEqual([]);
  });

  it('should return an array of deleted document ids', () => {
    const oldItems = [
      { ...generateFakeItem(InsightDashboardItemType.Media), files: [{ documentId: 'doc1' }, { documentId: 'doc2' }] },
      { ...generateFakeItem(InsightDashboardItemType.Media, '2'), files: [{ documentId: 'doc3' }] },
    ] as GridDashboardItem[];
    const newItems = [
      { ...generateFakeItem(InsightDashboardItemType.Media), files: [{ documentId: 'doc1' }] },
      { ...generateFakeItem(InsightDashboardItemType.Media, '2'), files: [{ documentId: 'doc3' }] },
    ] as GridDashboardItem[];
    const deletedDocumentIds = getDeletedDocumentIds(oldItems, newItems);
    expect(deletedDocumentIds).toEqual(['doc2']);
  });
});

describe('cloneFile', () => {
  it('should create a new file with the specified custom name', () => {
    const originalFile = new File(['content'], 'original.txt', {
      type: 'text/plain',
      lastModified: new Date(2024, 5, 17).getTime()
    });

    const customName = 'custom.txt';
    const clonedFile = cloneFile(originalFile, customName);

    expect(clonedFile).toBeInstanceOf(File);
    expect(clonedFile.name).toBe(customName);
    expect(clonedFile.type).toBe(originalFile.type);
    expect(clonedFile.lastModified).toBe(originalFile.lastModified);
  });

  it('should retain the content of the original file', async () => {
    const originalFile = new File(['content'], 'original.txt', {
      type: 'text/plain',
      lastModified: new Date(2024, 5, 17).getTime()
    });

    const customName = 'custom.txt';
    const clonedFile = cloneFile(originalFile, customName);

    const readFileContent = (file: File) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsText(file);
      });
    };

    const content = await readFileContent(clonedFile);
    expect(content).toBe('content');
  });
});
