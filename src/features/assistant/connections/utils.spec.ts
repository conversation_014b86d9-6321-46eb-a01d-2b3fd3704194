import { CalculationGroupValueType, CalculationType, UniversalTrackerConnection } from '@g17eco/types/utrv-connections';
import { getUtrvConnections } from './utils';
import { IntegratedConnection, SurveyConnection } from './types';

describe('Connections utils', () => {
  describe('getUtrvConnections', () => {
    it('should return empty data if connections is empty', () => {
      expect(getUtrvConnections([])).toEqual({
        surveyIdToConnectionsMap: new Map(),
        integratedConnections: [],
        allConnections: [],
      });
    });

    it('should separate utrv connections into survey connections and integrated connections', () => {
      const connections: UniversalTrackerConnection[] = [
        {
          _id: 'surveyConnectionId',
          utrCode: 'utrCode',
          valueListCode: 'valueListCode',
          calculationGroups: [
            {
              _id: 'surveyCalculationGroupId',
              name: 'surveyCalculationGroupName',
              valueType: CalculationGroupValueType.Numeric,
              calculations: [
                {
                  _id: 'surveyCalculationId',
                  name: 'surveyCalculationName',
                  type: CalculationType.Direct,
                  direct: 'a',
                  variables: {
                    a: { code: 'surveyUtrCode', valueListCode: 'surveyValueListCode' },
                  },
                  data: [
                    {
                      surveyId: 'surveyId1',
                      value: 1,
                      variables: {
                        a: { value: 1, utrvId: 'utrvId1', unit: 'mt', numberScale: 'millions' },
                      },
                    },
                    {
                      surveyId: 'surveyId2',
                      value: 2,
                      variables: {
                        a: { value: 2, utrvId: 'utrvId2', unit: 'mt', numberScale: 'millions' },
                      },
                    },
                  ],
                  unit: 'mt',
                  numberScale: 'millions',
                },
              ],
            },
          ],
        },
        {
          _id: 'integratedConnectionId',
          utrCode: 'utrCode',
          valueListCode: 'valueListCode',
          calculationGroups: [
            {
              _id: 'integratedCalculationGroupId',
              name: 'integratedCalculationGroupName',
              valueType: CalculationGroupValueType.Numeric,
              calculations: [
                {
                  _id: 'integratedCalculationId',
                  name: 'integratedCalculationName',
                  type: CalculationType.Direct,
                  direct: 'a',
                  variables: {
                    a: {
                      code: 'integratedUtrCode',
                      valueListCode: 'integratedValueListCode',
                      integrationCode: 'green-project-tech',
                    },
                  },
                  data: [
                    {
                      value: 3,
                      variables: {
                        a: { value: 3, unit: 'mt', numberScale: 'millions' },
                      },
                    },
                  ],
                  unit: 'mt',
                  numberScale: 'millions',
                },
              ],
            },
          ],
        },
      ];

      const expectedSurveyIdToConnectionsMap: Map<string, SurveyConnection[]> = new Map([
        [
          'surveyId1',
          [
            {
              _id: 'surveyCalculationId',
              name: 'surveyCalculationName',
              type: CalculationType.Direct,
              utrCode: 'utrCode',
              valueListCode: 'valueListCode',
              direct: 'a',
              surveyId: 'surveyId1',
              value: 1,
              variables: {
                a: {
                  value: 1,
                  utrvId: 'utrvId1',
                  code: 'surveyUtrCode',
                  valueListCode: 'surveyValueListCode',
                  unit: 'mt',
                  numberScale: 'millions',
                },
              },
              unit: 'mt',
              numberScale: 'millions',
              groupValueType: CalculationGroupValueType.Numeric
            },
          ],
        ],
        [
          'surveyId2',
          [
            {
              _id: 'surveyCalculationId',
              name: 'surveyCalculationName',
              type: CalculationType.Direct,
              utrCode: 'utrCode',
              valueListCode: 'valueListCode',
              direct: 'a',
              surveyId: 'surveyId2',
              value: 2,
              variables: {
                a: {
                  value: 2,
                  utrvId: 'utrvId2',
                  code: 'surveyUtrCode',
                  valueListCode: 'surveyValueListCode',
                  unit: 'mt',
                  numberScale: 'millions',
                },
              },
              unit: 'mt',
              numberScale: 'millions',
              groupValueType: CalculationGroupValueType.Numeric
            },
          ],
        ],
      ]);
      const expectedIntegratedConnections: IntegratedConnection[] = [
        {
          _id: 'integratedCalculationId',
          name: 'integratedCalculationName',
          type: CalculationType.Direct,
          utrCode: 'utrCode',
          valueListCode: 'valueListCode',
          direct: 'a',
          value: 3,
          variables: {
            a: {
              value: 3,
              code: 'integratedUtrCode',
              valueListCode: 'integratedValueListCode',
              integrationCode: 'green-project-tech',
              unit: 'mt',
              numberScale: 'millions',
            },
          },
          unit: 'mt',
          numberScale: 'millions',
          groupValueType: CalculationGroupValueType.Numeric
        },
      ];
      const survey1Connections = expectedSurveyIdToConnectionsMap.get('surveyId1') ?? [];
      const survey2Connections = expectedSurveyIdToConnectionsMap.get('surveyId2') ?? [];
      const expected: ReturnType<typeof getUtrvConnections> = {
        surveyIdToConnectionsMap: expectedSurveyIdToConnectionsMap,
        integratedConnections: expectedIntegratedConnections,
        allConnections: [...survey1Connections, ...survey2Connections, ...expectedIntegratedConnections],
      };
      expect(getUtrvConnections(connections)).toEqual(expected);
    });
  });
});
