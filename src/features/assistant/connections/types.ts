import { UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import {
  Calculation,
  CalculationBase,
  CalculationGroupValueType,
  CalculationIntegrationUtr,
  CalculationUtr,
  NumericVariable,
  TextVariable,
  Variable,
} from '@g17eco/types/utrv-connections';
import { SelectedConnection } from './ConnectionContext';
import { Survey } from '@api/utrv';

export type NumericVariableData = Variable & NumericVariable;
export type TextVariableData = Variable & TextVariable;
export type VariableData = NumericVariableData | TextVariableData;

type BaseConnection<T> = Omit<T, 'data' | 'variables'> & {
  utrCode: string;
  valueListCode?: string;
  value: number | string;
  variables: Record<string, VariableData>;
};

// Apply Omit dynamically across all subtypes in Calculation
// Equal BaseConnection<Calculation> & { surveyId: string }
export type SurveyConnection = Calculation extends infer T
  ? T extends CalculationBase
    ? BaseConnection<T> & {
        surveyId: string;
        groupValueType: CalculationGroupValueType;
      }
    : never
  : never;
// Equal BaseConnection<Calculation>
export type IntegratedConnection = Calculation extends infer T
  ? T extends CalculationBase
    ? BaseConnection<T> & {
        groupValueType: CalculationGroupValueType.Numeric;
      }
    : never
  : never;

export type UtrvConnection = SurveyConnection | IntegratedConnection;

export interface ConnectionsGroupProps {
  utr: Pick<
    UniversalTrackerPlain,
    'valueType' | 'targetDirection' | 'valueValidation' | 'unitType' | 'unit' | 'alternatives'
  >;
  title: string;
  connections: SurveyConnection[] | IntegratedConnection[];
  displayDetails?: boolean;
  integrationUtrs: CalculationIntegrationUtr[];
  utrs: CalculationUtr[];
  selectedConnection?: SelectedConnection;
  onClickConnection: (connection: ConnectionsGroupProps['connections'][0]) => void;
  onPopulateInput: (connection: ConnectionsGroupProps['connections'][0]) => void;
  initiativeId?: string;
  surveyScope?: Survey['scope'];
}

export type ConnectionProps = Pick<
  ConnectionsGroupProps,
  | 'utr'
  | 'initiativeId'
  | 'displayDetails'
  | 'onClickConnection'
  | 'selectedConnection'
  | 'onPopulateInput'
  | 'integrationUtrs'
  | 'utrs'
  | 'surveyScope'
> & {
  connection: ConnectionsGroupProps['connections'][0];
  name?: string;
};
