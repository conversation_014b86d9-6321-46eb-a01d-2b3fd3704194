import React from 'react';
import { render, screen } from '@testing-library/react';
import { Connections } from './Connections';
import { useAppSelector } from '@reducers/index';
import { createUser, getCurrentUserState } from '@fixtures/user-factory';
import { mockResponseSecondaryConnections } from '@fixtures/calculation-groups-fixture';
import { GetSecondaryConnectionsResponse, useGetSecondaryConnectionsQuery } from '@api/utrv';
import { Mock } from 'vitest';
import { utrTableOne, utrTableOneUtrv } from '@fixtures/utr/utr-table-fixtures';

// Mock the useGetSecondaryConnectionsQuery hook
vi.mock('@api/utrv', () => ({
  useGetSecondaryConnectionsQuery: vi.fn(),
}));

// Mock the useAppSelector hook
vi.mock('@reducers/index', () => ({
  useAppSelector: vi.fn(),
}));

const connectionsGroupId = 'connections-group-id';
vi.mock('./ConnectionsGroup', () => ({
  ConnectionsGroup: ({ title, connections, onClickConnection, onPopulateInput }: any) => (
    <div data-testid={connectionsGroupId}>
      <h3>{title}</h3>
      {connections.map((conn: any) => (
        <button key={conn.id} onClick={() => onClickConnection(conn)}>
          {conn.valueListCode}
        </button>
      ))}
      <button onClick={() => onPopulateInput(connections[0])}>Populate</button>
    </div>
  ),
}));

const mockProps = {
  utr: utrTableOne,
  utrv: utrTableOneUtrv,
};
const currentUserState = getCurrentUserState(createUser({ isStaff: true }));

describe('Connections Component', () => {
  beforeEach(() => {
    // Set up default mock return values
    (useGetSecondaryConnectionsQuery as Mock).mockReturnValue({
      isFetching: false,
      isError: false,
      data: mockResponseSecondaryConnections,
    });

    (useAppSelector as Mock).mockReturnValue({
      loaded: true,
      data: {
        currentUser: currentUserState,
      },
    });
  });

  it('renders loading state initially', () => {
    const emptyConnectionsResponse: GetSecondaryConnectionsResponse = {
      ...mockResponseSecondaryConnections,
      connections: [],
    };
    (useGetSecondaryConnectionsQuery as Mock).mockReturnValue({
      isLoading: false,
      isError: false,
      data: emptyConnectionsResponse,
    });
    render(<Connections {...mockProps} />);
    expect(screen.getByText(/We looked, but this metric currently has no connections/i)).toBeInTheDocument();
  });

  it('should render connection groups and surveys when data is fetched', async () => {
    render(<Connections {...mockProps} />);
    expect(screen.getByText(/populate input field with selected answer/i)).toBeInTheDocument();
    expect(screen.getAllByTestId(connectionsGroupId).length).toEqual(1);
  });
});
