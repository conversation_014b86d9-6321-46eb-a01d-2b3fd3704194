@import '../../../css/variables';

$defaultBaseColor: var(--theme-NeutralsExtralight);
$defaultHighlightColor: var(--theme-NeutralsLight);

.question-assistant__ai {
  .badge {
    &.bg-success {
      background-color: $colorThemeSuccessExtralight !important;
      border: 1px solid $colorThemeSuccessLight !important;
      color: $colorThemeSuccessMedium !important;
    }

    &.bg-warning {
      background-color: $colorThemeWarningExtralight !important;
      border: 1px solid $colorThemeWarningLight !important;
      color: $colorThemeWarningMedium !important;
    }

    &.bg-secondary {
      background-color: $colorThemeBgMedium !important;
      border: 1px solid $colorThemeTextMedium !important;
      color: $colorThemeTextMedium !important;
    }
  }

  &__target {
    &--good {
      color: $colorThemeSuccessMedium;
    }

    &--bad {
      color: $colorThemeWarningMedium;
    }

    color: $colorThemeTextMedium;
  }

  @keyframes LoadingPlaceholder {
    0% {
      background-position: -200px 0;
    }

    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .loading-placeholder {
    display: block;
    background-color: $defaultBaseColor;
    background-image: linear-gradient(90deg,
        $defaultBaseColor,
        $defaultHighlightColor,
        $defaultBaseColor );
    background-size: 200px 100%;
    background-repeat: no-repeat;
    border: 1px solid var(--theme-BorderDefault);
    animation: LoadingPlaceholder 1.2s ease-in-out infinite;

    &.h-md {
      height: 1lh;
    }

    &.h-sm {
      height: 0.6875rem;
    }
  }
}
