import { render, screen } from '@testing-library/react';
import { NumberAnswer } from './BaseTypeAnswers';
import { UtrValueType } from '@g17eco/types/universalTracker';

describe('BaseTypeAnswers', () => {
  describe('<NumberAnswer />', () => {
    it('should render the text indicating no suggested answer', () => {
      render(
        <NumberAnswer
          inputAnswer={undefined}
          predictedAnswer={NaN}
          utr={{ valueType: UtrValueType.Number }}
          hasPreviousUtrvs={false}
        />
      );
      expect(screen.getByText('No answer can be suggested')).toBeInTheDocument();
    });
    it('should render the number if the suggested answer is an actual number', () => {
      render(
        <NumberAnswer
          inputAnswer={undefined}
          predictedAnswer={999}
          utr={{ valueType: UtrValueType.Number }}
          hasPreviousUtrvs={false}
        />
      );
      expect(screen.getByText('Answer Estimate:')).toBeInTheDocument();
      expect(screen.getByText('999')).toBeInTheDocument();
    });
    it('should prioritize rendering input answer if present', () => {
      render(
        <NumberAnswer
          inputAnswer={456}
          predictedAnswer={999}
          utr={{ valueType: UtrValueType.Number }}
          hasPreviousUtrvs={false}
        />
      );
      expect(screen.getByText('Answer:')).toBeInTheDocument();
      expect(screen.getByText('456')).toBeInTheDocument();
    })
  });
});
