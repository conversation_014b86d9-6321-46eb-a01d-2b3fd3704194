import { render, screen } from '@testing-library/react';
import { MultiInputAnswerData } from './MultiInputAnswer';
import { TableColumnType, UtrValueType } from '@g17eco/types/universalTracker';
import { faker } from '@faker-js/faker';
import { createUtr, createUtrv } from '@fixtures/utr/utrv-factory';

const multiAnswerInputDataProps = {
  column: 'col1',
  predictedAnswers: {
    col1: 'test',
    col2: 'test2',
  },
  hasPreviousUtrvs: false,
  isFetching: false,
  utr: createUtr('table/one', {
    valueType: UtrValueType.Table,
    valueValidation: {
      table: {
        columns: [
          {
            code: 'col1',
            name: 'Col 1',
            type: TableColumnType.Text,
          },
          {
            code: 'col2',
            name: 'Col 2',
            type: TableColumnType.Text,
          },
        ],
        validation: { maxRows: 1 },
      },
    },
  }),
  utrv: createUtrv(faker.database.mongodbObjectId()),
  table: {
    rows: [],
    editRowId: -1,
  },
};

describe('MultiInputAnswer', () => {
  describe('MultiIputAnswerData', () => {
    it('should render the text indicating no suggested answer', () => {
      render(<MultiInputAnswerData {...multiAnswerInputDataProps} utr={{ valueType: 'invalid-type' }} />);
      expect(screen.getByText('No answer can be suggested')).toBeInTheDocument();
    });
  });
});
