/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { Link } from 'react-router-dom';
import Dashboard, { DashboardSection } from '@components/dashboard';
import config from '../../config';
import { GenericState } from '../../slice/slice';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

interface NoOrganizationParams {
  rootInitiativesState: Pick<GenericState, 'loaded' | 'errored' | 'errorMessage'>
  appName: string;
}

/**
 * Receive loading state that can be CT/PT or any other app
 */
export const NoOrganization = ({ rootInitiativesState, appName }: NoOrganizationParams) => {

  const { errored, errorMessage, loaded } = rootInitiativesState;
  if (errored) {
    return (
      <Dashboard>
        <DashboardSection icon={'fa-danger'} title={appName}>
          <BasicAlert type={'danger'}>{errorMessage}</BasicAlert>
        </DashboardSection>
      </Dashboard>
    )
  }

  if (!loaded) {
    return (
      <Dashboard><LoadingPlaceholder height={500} /></Dashboard>
    )
  }

  return (
    <Dashboard>
      <DashboardSection icon={'fa-tasks'} title={`${appName}: No Organisation Found`}>
        <p>Sorry. We couldn’t find an organisation linked to your account under this version of {appName}.</p>
        <p>Please contact our <a href={`mailto:${config.emailSupport}`}>success team</a> to set up your organisation,
          or visit our <Link to={'/marketplace'}>marketplace</Link> to select a different application.</p>
      </DashboardSection>
    </Dashboard>
  )
}
