/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { CurrentUserData } from '@reducers/current-user';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { getFullName } from '@utils/user';
import { UserFeatureCode, UserPreferencesResponse } from '@g17eco/types/user';
import { UserApiKeyContainer } from '@features/user-api-keys';
import { hasUserFeatureFlag } from '@utils/user-features';
import { Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import NothingToSee from '../../images/nothing-to-see.svg';

interface Props {
  user: CurrentUserData;
  preferences: UserPreferencesResponse;
}

export const MySettings = ({ user, preferences }: Props) => {
  const fullName = getFullName(user);
  const hasApiKey = hasUserFeatureFlag(preferences, UserFeatureCode.UserApiKey);

  return (
    <Dashboard>
      <DashboardRow className={'mb-3'}>
        <Breadcrumbs breadcrumbs={[{ label: 'My Settings' }]} rootLabel='Profile' rootUrl={'/user-profile'} />
      </DashboardRow>

      <DashboardSectionTitle title='My settings'  />

      {hasApiKey ? (
        <UserApiKeyContainer user={user} />
      ) : (
        <DashboardSection>
          <h2 className='text-ThemeHeadingLight'>{fullName}</h2>
          <div className='text-center'>
            <img src={NothingToSee} alt='nothing to see' />
            <h1 className='text-ThemeTextPlaceholder'>Nothing to see here</h1>
          </div>
        </DashboardSection>
      )}
    </Dashboard>
  );
}
