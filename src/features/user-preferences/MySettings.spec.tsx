/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { afterEach, expect } from 'vitest';
import { MySettings } from './MySettings';
import { UserFeatureCode, UserPreferencesResponse } from '@g17eco/types/user';
import { userOne } from '@fixtures/user-factory';
import { getFullName } from '@utils/user';
import { renderWithProviders } from '@fixtures/utils';
import { simpleUserStore } from '@fixtures/redux-store';
import { httpGet } from '@fixtures/msw-fixtures';

describe('MySettings', () => {

  const server = setupServer(httpGet('/keys/personal', []))

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers();
  })
  afterAll(() => server.close())

  const mockPreferences: UserPreferencesResponse = {
    userId: userOne._id,
    featureFlags: [],
  };

  const renderMySettings = (preferences = mockPreferences) => {
    return renderWithProviders(
        <MySettings user={userOne} preferences={preferences}  />,
        {
          store: simpleUserStore,
          route: { path: '/user-profile', initialEntries: ['/user-profile'] }
        },
    );
  };

  it('should render breadcrumbs', () => {
    renderMySettings();

    expect(screen.getByText('My Settings')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
  });

  it('should show nothing to see message when user has no API key feature', () => {
    renderMySettings();

    expect(screen.getByText(getFullName(userOne))).toBeInTheDocument();
    expect(screen.getByText('Nothing to see here')).toBeInTheDocument();
    expect(screen.getByAltText('nothing to see')).toBeInTheDocument();
  });

  it('should render API key container when user has API key feature', async () => {
    renderMySettings({
      ...mockPreferences,
      featureFlags: [{ code: UserFeatureCode.UserApiKey, created: new Date().toISOString() }],
    });

    expect(screen.queryByText('Nothing to see here')).not.toBeInTheDocument();
    expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
    expect(await screen.findByText('API Keys')).toBeInTheDocument();
  });
});
