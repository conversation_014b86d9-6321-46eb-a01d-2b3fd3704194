/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react'
import './routeErrorBoundary.scss';
import { handleRouteError, showReportDialog } from '../../logger';
import { withRouter } from 'react-router-dom';
import config from '../../config';

class RouteErrorBoundary extends Component {

  state = {
    hasError: false,
    error: undefined,
  };

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidUpdate(prevProps) {
    if (this.state.hasError && prevProps.location.key !== this.props.location.key) {
      // Reset error on location change
      this.setState({ hasError: false, error: undefined });
    }
  }

  handleClick(e) {
    e.preventDefault();
    window.location.reload();
  }

  handleReport() {
    showReportDialog();
    window.open(config.reportURL, '_blank', '');
  }

  componentDidCatch(error, errorInfo) {
    handleRouteError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className='error-boundary-container'>
          <div className='section col-12 col-md-6'>
            <div>
              <p>
                Our development team has received an error message, and will investigate. If it is urgent, please report
                the issue or use the chat bot at the bottom of the page.
              </p>
            </div>
            <div className='text-center text-md-left'>
              <button type='button' className='btn btn-outline-secondary mt-2' onClick={this.handleClick}>
                Retry
              </button>
              <button type='button' className='ml-3 btn btn-secondary mt-2' onClick={this.handleReport}>
                Report issue
              </button>
            </div>
          </div>
        </div>
      );
    }
    return this.props.children
  }
}

export default withRouter(RouteErrorBoundary);
