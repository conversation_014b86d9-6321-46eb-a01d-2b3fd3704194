/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { Component } from 'react';

export class ErrorComponent extends Component {

  render() {
    return (
      <div className='loader-error'>
        <span className='material-icons'>error</span>
        Sorry, an error has occurred.

        <br /><br />
        <a href='#' onClick={(e) => {
          e.preventDefault();
          window.location.reload();
        }}>
          Retry?
        </a>

      </div>
    )
  }
}
