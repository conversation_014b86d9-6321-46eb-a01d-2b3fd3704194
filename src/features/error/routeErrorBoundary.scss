/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */
@import 'src/css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.error-boundary-container {
  margin-top: 10vh;
  @include media-breakpoint-up(md) {
    margin-top: 20vh;
  }
  max-width: $max-width-container;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  h1 {
    color: var(--theme-HeadingLight);
    font-size: 32px;
    letter-spacing: -2%;
    line-height: 38px;
  }
  p {
    color: var(--theme-HeadingLight);
    font-size: 16px;
    line-height: 24px;
  }
  button {
    min-width: 110px;
    min-height: 40px;
    font-size: 13px;
    line-height: 20px;
  }
  .section {
    flex: 1;
    padding-left: 30px;
    padding-right: 30px;
    @include media-breakpoint-up(md) {
      padding-left: 15px;
      padding-right: 15px;
    }
  }
}
