/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Component, ErrorInfo, ReactNode } from 'react';
import { handleRouteError } from '../../logger';

interface RecoveryErrorBoundaryProps {
  children: ReactNode;
  fallback: ReactNode;
}

interface RecoveryErrorBoundaryState {
  hasError: boolean;
}

export class RecoveryErrorBoundary extends Component<RecoveryErrorBoundaryProps, RecoveryErrorBoundaryState> {
  constructor(props: RecoveryErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): RecoveryErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidUpdate(prevProps: RecoveryErrorBoundaryProps) {
    // Check if children or fallback have changed
    if (prevProps.children !== this.props.children || prevProps.fallback !== this.props.fallback) {
      if (this.state.hasError) {
        this.setState({ hasError: false });
      }
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // You can also log the error to an error reporting service
    handleRouteError(error, { stack: errorInfo.componentStack });
    console.error('RecoveryErrorBoundary caught an error', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}
