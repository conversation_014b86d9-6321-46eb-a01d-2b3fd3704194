/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { CreateSetupFn, IntegrationProvider, ProviderSetupInfo } from '@g17eco/types/integration';
import { IntegrationProcessView } from './IntegrationProcessView';

interface Props extends ProviderSetupInfo {
  onCreate: CreateSetupFn;
}


const NoIntegrationView = ({ provider }: { provider: IntegrationProvider }) => {
  return (
    <div className='d-flex'>
      <h3>Integration {provider.name} not available</h3>
    </div>
  )
}


export const ProviderView = (props: Props) => {

  const { provider, integration, status, externalApp, onCreate } = props;

  return (
    <div>
      <div style={{ minHeight: '360px' }}>
        {integration ?
          <IntegrationProcessView externalApp={externalApp} onCreate={onCreate} status={status} provider={provider} integration={integration} /> :
          <NoIntegrationView provider={provider} />}
      </div>
    </div>

  )
}
