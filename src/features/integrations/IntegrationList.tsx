import { DashboardSection, DashboardSectionTitle } from '@components/dashboard';
import { IntegrationProvider, IntegrationProviderExtended } from '@g17eco/types/integration';
import { ProductCards } from '@routes/home/<USER>/partials/ProductCards';

export interface ProviderCard extends Pick<IntegrationProvider, 'logo' | 'color'> {
  buttons?: { text: string; onClick: (() => void) | React.MouseEventHandler<HTMLElement> }[];
}

export type ProviderCardExtended = ProviderCard &
  Pick<IntegrationProviderExtended, 'status'> & {
    onClick: () => void;
  };

interface Props {
  cards: (ProviderCard | ProviderCardExtended)[];
  title: string;
}

const classes = {
  img: 'w-100',
};

export const IntegrationList = ({ cards, title }: Props) => {
  return (
    <>
      <DashboardSectionTitle title={title} />
      {cards.length ? (
        <DashboardSection>
          <ProductCards cards={cards} classes={classes} />
        </DashboardSection>
      ) : (
        <DashboardSection className='text-center text-ThemeTextMedium'>
          There are no {title.toLowerCase()} to display.
        </DashboardSection>
      )}
    </>
  );
};
