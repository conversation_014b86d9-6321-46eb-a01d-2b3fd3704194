/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ReactNode } from 'react';
import { ProviderSetupInfoRequired } from '@g17eco/types/integration';
import { SetupView } from './setup/SetupView';
import { Button } from 'reactstrap';
import WorkingOnIt from '../../images/workingonit.gif';
import { BasicAlert } from '@g17eco/molecules/alert';


const BasicIntegrationView = (props: { children: ReactNode, icon?: ReactNode }) => {
  return (
    <div>
      {props.children}
      <div className={'text-center'}>
        {props.icon}
      </div>
    </div>
  )
}

export const IntegrationProcessView = (props: ProviderSetupInfoRequired) => {

  switch (props.status) {
    case 'active': {
      const login = props.externalApp?.login;
      return (
        <BasicIntegrationView>
          <h5>Active Connection is enabled</h5>
          <div className={'p-4 text-center'}>
            <i className='fa-solid text-success fa-8x fa-circle-check' />
          </div>

          {
            login ? (
              <div className={'text-center'}>
                <Button color={'primary'} size={'lg'} onClick={() => {
                  window.open(login.url, '_blank', '');
                }}>
                  {login.text ?? `Go to ${props.provider.name}`}
                </Button>
              </div>
            ) : null
          }

        </BasicIntegrationView>
      )
    }
    case 'error': {
      return (
        <BasicIntegrationView icon={<i className='fa-solid fa-2x fa-circle-check' />}>
          <BasicAlert type={'danger'}>
            An error have occured during the setup process. Please contact support.
          </BasicAlert>
        </BasicIntegrationView>
      )
    }
    case 'pending': {
      return (
        <BasicIntegrationView>
          <h4 className={'my-4'}>
            Your Integration is currently pending and will be notified once it is completed.
          </h4>
          <div className={'text-center p-sm-3'}>
            <div style={{ maxWidth: '500px' }} className={'d-inline-block'}>
              <img src={WorkingOnIt} alt='Work in Progress' />
            </div>
          </div>
        </BasicIntegrationView>
      )
    }
  }

  // Must be in setup for now
  if (!props.integration.requirements.questions.length) {
    return (
      <div className={'text-center'}>
        <Button color={'primary'} size={'lg'}>
          Create setup
        </Button>
      </div>
    )
  }

  return <SetupView {...props} />
}
