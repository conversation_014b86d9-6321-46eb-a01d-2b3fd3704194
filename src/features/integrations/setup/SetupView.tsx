/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useCallback, useMemo, useState } from 'react';
import { ProviderSetupInfoRequired, SetupUtrv } from '@g17eco/types/integration';
import { QuestionInformation } from '@components/survey/question/QuestionInformation';
import UniversalTracker from '@models/UniversalTracker';
import { QuestionInput } from '@components/question/QuestionInput';
import { SubmitButton } from '@components/button/SubmitButton';
import { InitialResettableQuestionState } from '@components/survey/question/questionInterfaces';
import './SetupView.scss';
import { isDataEmpty } from '@components/survey/question/questionUtil';
import { tableDataToView } from '@utils/valueDataTable';

export const SetupView = (props: Pick<ProviderSetupInfoRequired, 'integration' | 'onCreate'>) => {

  const { integration, onCreate } = props;

  // Assumption we have one question we can ask (table single row)
  const [firstQuestion] = integration.requirements.questions;

  const [answer, setAnswer] = useState<SetupUtrv>({
    utrCode: firstQuestion.code,
    value: undefined,
    valueData: undefined,
  });

  // Must only generate once as initial state to avoid infinite loop
  const { utr, utrv } = useMemo(() => {
    return {
      utrv: {
        utrCode: firstQuestion.code,
        value: undefined,
        valueData: undefined,
        status: 'created',
      },
      utr: new UniversalTracker(firstQuestion as any)
    }
  }, [firstQuestion]);

  const table = tableDataToView(answer.valueData);

  const isValid =
    answer.valueData &&
    !isDataEmpty({
      utr,
      displayCheckbox: {},
      valueData: answer.valueData,
      value: answer.value,
      table,
    });

  const onSubmit = async () => {
    if (isValid) {
      onCreate({ generatedAnswers: [answer] })
    }
  }

  const handleUpdate = useCallback((data: InitialResettableQuestionState) => {
    setAnswer((q) => ({ ...q, value: data.value, valueData: data.valueData }));
  }, []);

  return (
    <div className='setup-container'>
      <QuestionInformation
        classes={{ container: 'mt-3', valueLabel: 'text-lg text-ThemeHeadingMedium' }}
        utr={utr}
        alternativeCode={''}
      />

      <div className={'question-container'}>
        <QuestionInput
          key={firstQuestion.code}
          utrv={utrv}
          utr={utr}
          saving={false}
          handleUpdate={handleUpdate}
        />
      </div>

      <div className='pt-3 text-center'>
        <SubmitButton onClick={onSubmit} disabled={!isValid} color={'primary'} size={'lg'} className='fw-semibold py-2'>
          Set up
        </SubmitButton>
      </div>
    </div>
  );
}
