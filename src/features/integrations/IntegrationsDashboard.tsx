/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useListInitiativeIntegrationsQuery } from '@api/initiative-integrations';
import Dashboard from '@components/dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useCallback, useState, JSX } from 'react';
import { useHistory } from 'react-router-dom';
import { ProviderCardExtended, ProviderCard } from '@features/integrations';
import { ProviderIntegrationStatus } from '@g17eco/types/integration';
import { BasicAlert } from '@g17eco/molecules/alert';
import { useAppSelector } from '@reducers/index';
import { AdminBreadcrumbsProps } from '@g17eco/molecules/breadcrumbs';
import { IntegrationList } from './IntegrationList';
import { IntegrationModal } from './IntegrationModal';
import { createOnClickFromExternalUrl } from '@utils/click-handler';

interface Props {
  initiativeId: string;
  additionalCards: {
    active: ProviderCard[];
    available: ProviderCard[];
  };
  BreadCrumbsComponent: (props: AdminBreadcrumbsProps) => JSX.Element;
}

export const IntegrationsDashboard = ({ initiativeId, additionalCards, BreadCrumbsComponent }: Props) => {
  const history = useHistory();
  const canAccessIntegrations = useAppSelector(FeaturePermissions.canAccessAppIntegrations);
  const [code, setCode] = useState<string | undefined>();

  const { data, isLoading, error } = useListInitiativeIntegrationsQuery(initiativeId);

  const { allProviders = [], usedProviders = [] } = data ?? {};

  const onLinkClick = useCallback(
    ({ link, code }: { code: string; link?: string }) => {
      const analytics = getAnalytics();

      if (link) {
        analytics.track(AnalyticsEvents.IntegrationExternalView, { code, isExternal: true });
        return window.open(link);
      }
      analytics.track(AnalyticsEvents.IntegrationView, { code });
      return history.push(generateUrl(ROUTES.INTEGRATIONS_VIEW, { initiativeId, code }));
    },
    [history, initiativeId],
  );

  const usedProviderCards: ProviderCardExtended[] = usedProviders.map((provider) => {
    const onClick = () => onLinkClick({ link: provider.login?.url, code: provider.code });
    const buttons = [];

    if (provider.status === ProviderIntegrationStatus.Active) {
      buttons.push({ text: 'details', onClick: () => setCode(provider.code) }, { text: 'access', onClick });
    }

    return { ...provider, buttons, onClick };
  });

  const providerCards: ProviderCard[] = allProviders.map((provider) => {
    const buttons = [
      {
        text: 'info',
        onClick: createOnClickFromExternalUrl(ROUTES.EMISSIONS_CALCULATORS.path),
      },
    ];

    const isUsed = usedProviders.some((provider) =>
      [ProviderIntegrationStatus.Pending, ProviderIntegrationStatus.Active].includes(provider.status),
    );

    if (canAccessIntegrations && !isUsed) {
      buttons.unshift({ text: 'set up', onClick: () => onLinkClick({ code: provider.code }) });
    }
    return { ...provider, buttons };
  });

  if (isLoading) {
    return <Loader />;
  }

  const activeCards: ProviderCard[] = [...additionalCards.active, ...usedProviderCards];
  const availableCards: ProviderCard[] = [...additionalCards.available, ...providerCards];

  return (
    <Dashboard>
      <div className='pl-2 pb-4'>
        <BreadCrumbsComponent initiativeId={initiativeId} breadcrumbs={[{ label: 'App integrations' }]} />
      </div>
      {error ? <BasicAlert type='danger'>{'message' in error ? error.message : error}</BasicAlert> : null}
      <IntegrationList cards={activeCards} title={'Active app integrations'} />
      <IntegrationList cards={availableCards} title={'Available app integrations'} />
      <IntegrationModal
        code={code}
        initiativeId={initiativeId}
        closeModal={() => setCode(undefined)}
        handleLinkClick={onLinkClick}
      />
    </Dashboard>
  );
};
