/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { InitiativeData } from '@g17eco/types/initiative';
import { BlueprintContributions, ScopeQuestionGroup } from '@g17eco/types/survey';
import { QuestionContainerStateless } from '@components/survey/question/QuestionContainerStateless';
import SurveyQuestionList, { SurveyQuestionListProps } from '@components/survey-question-list/survey-question-list';
import { useState } from 'react';
import { SurveyActionData } from '@models/surveyData';
import { BulkActionToolbarProps } from '@components/survey-question-list/partials/BulkActionToolbar';
import { ColumnSimpleStatusIcon } from './partials/ColumnSimpleStatusIcon';
import { getColumnAnswerIndicator } from './partials/ColumnAnswerIndicator';
import { ColumnValue } from '@components/survey-question-list/partials/ColumnValue';
import { isMaterialityQuestionReadOnly } from './utils';
import { Column, Row } from '@g17eco/molecules/tracking-list';
import { ColumnTitle, ColumnTitleProps } from '@g17eco/molecules/survey-question-list';

const LOCKED_METRIC_MESSAGE =
  'Metric locked. These metrics have been answered during the assessments creation and cannot be updated. Please contact us for more information.';

interface ExpandingSurveyQuestionListProps extends Omit<SurveyQuestionListProps, 'goToQuestion' | 'surveyGroups'> {
  survey: SurveyActionData;
  surveyGroups: ScopeQuestionGroup[];
  handleReload?: () => void;
  Toolbar?: (props: BulkActionToolbarProps) => JSX.Element | null;
  disabledUTRs: string[];
}

const getMTColumnTitle = (tooltip: string) => (props: ColumnTitleProps) => {
  return <ColumnTitle {...props} tooltip={tooltip} />;
};

export const ExpandingSurveyQuestionList = (props: ExpandingSurveyQuestionListProps) => {
  const { survey, surveyGroups, Toolbar, disabledUTRs } = props;
  const [questionId, setExpandedRow] = useState<string | undefined>();

  const toggleExpanded = (questionId: string | undefined) => {
    setExpandedRow((currId) => (questionId === currId ? undefined : questionId));
  };

  const handleReload = async () => {
    setExpandedRow(undefined);
    props.handleReload?.();
  };

  const materiality: InitiativeData['materiality'] = {};
  const blueprint: BlueprintContributions = {};

  return (
    <SurveyQuestionList
      key={survey._id}
      {...props}
      surveyGroups={surveyGroups}
      goToQuestion={(questionId) => toggleExpanded(questionId)}

      allowBulkActions={!!Toolbar}
      bulkActionToolbar={(props) => (Toolbar ? <Toolbar {...props} surveyData={survey} /> : null)}
      columns={[ColumnSimpleStatusIcon, getMTColumnTitle(''), ColumnValue, getColumnAnswerIndicator(questionId)]}

      renderExpansionRow={(question) => {
        const utrvId = question.utrv?._id ?? '';
        const isReadOnly = isMaterialityQuestionReadOnly({ code: question.universalTracker.getCode() });

        if (utrvId === questionId) {
          // @TODO - this will appear multiple times if a UTRV appears multiple times in survey,
          // should be fixed but not a priority for Materiality Tracker
          return (
            <Row>
              <Column fill noWrap={false}>
                <div className='background-ThemeBgLight border-radius border border-ThemeBgDark'>
                  <QuestionContainerStateless
                    initiativeId={props.initiativeId}
                    surveyId={props.surveyId}
                    questionId={utrvId}
                    questionIndex={''}
                    survey={survey}
                    surveyGroups={surveyGroups}
                    materiality={materiality}
                    blueprint={blueprint}
                    searchIndex={undefined}
                    handleReload={handleReload}
                    config={{
                      enableFurtherExplanation: false,
                      enableComments: false,
                      enableQuestionSubtitle: false,
                      enableEvidence: false,
                      enableNANR: false,
                      enableMakePrivate: false,
                      enableAIAssistant: false
                    }}
                    isQuestionReadOnly={isReadOnly}
                  />
                </div>
              </Column>
            </Row>
          );
        }
        return null;
      }}
      getDisabledRowTooltip={(question) =>
        isMaterialityQuestionReadOnly({ code: question.universalTracker.getCode() }) ? LOCKED_METRIC_MESSAGE : undefined
      }
      disabledUTRs={disabledUTRs}
    />
  );
};
