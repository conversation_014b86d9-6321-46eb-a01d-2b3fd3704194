/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { Button } from 'reactstrap';
import { isMaterialityQuestionReadOnly } from '../utils';
import { Column } from '@g17eco/molecules/tracking-list';

export const getColumnAnswerIndicator =
  (questionId?: string) => (props: Pick<ColumnCommonProps, 'question' | 'handleGoToQuestion'>) => {
    const { question, handleGoToQuestion } = props;
    const iconClassName = questionId === question.utrv?._id ? 'fa-chevron-up' : 'fa-chevron-down';
    const isReadOnly = isMaterialityQuestionReadOnly({ code: question.universalTracker.getCode() });

    return (
      <Column className='text-center'>
        <Button disabled={isReadOnly} color='link' size='xs' onClick={handleGoToQuestion}>
          {isReadOnly ? 'Locked' : 'Answer'}
          <i className={`fal ${iconClassName} ms-2`} />
        </Button>
      </Column>
    );
  };
