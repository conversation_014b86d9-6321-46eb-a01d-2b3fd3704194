/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { SurveyModelMinimalUtrv } from '@models/surveyData';
import { getStatusText } from '@utils/universalTrackerValue';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { UtrvStatus } from '@constants/status';
import { isMaterialityQuestionReadOnly } from '../utils';
import { Column } from '@g17eco/molecules/tracking-list';

const getStatusIcon = (status: UtrvStatus): string => {
  switch (status) {
    case UtrvStatus.Updated:
    case UtrvStatus.Verified:
      return 'fas fa-circle-check text-ThemeSuccessMedium';
    case UtrvStatus.Created:
    case UtrvStatus.Rejected:
    default:
      return 'fal fa-circle';
  }
}
export const ColumnSimpleStatusIcon = (props: ColumnCommonProps) => {
  const { question } = props;
  if (!question.utrv) {
    return null;
  }

  const utrv = question.utrv;

  const getIcon = (utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'assuranceStatus' | 'lastUpdated'>) => {
    if (!utrv) {
      return {
        icon: '',
        onClick: undefined,
        disabled: true,
        className: 'fixed-width',
        tooltip: undefined,
      }
    }

    const defaultIcon = {
      icon: getStatusIcon(utrv.status as UtrvStatus),
      onClick: undefined,
      disabled: true,
      className: 'fixed-width',
      tooltip: isMaterialityQuestionReadOnly({ code: question.universalTracker.getCode() }) ? '' : getStatusText(utrv),
    }

    return {
      ...defaultIcon,
      disabled: false,
      className: `${utrv.status} fixed-width`,
    };
  }

  const statusIcon = getIcon(utrv);

  return (
    <Column className='text-center' disabled={statusIcon.disabled} onClick={statusIcon.onClick} tooltip={statusIcon.tooltip}>
      <i className={`fa ${statusIcon.icon} ${statusIcon.className}`} style={{ fontSize: '1.2rem' }}/>
    </Column>
  );
}
