/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import React, { useMemo, useState } from 'react';
import { DashboardSection } from '@components/dashboard';
import { UserApiKeyExtended } from '@g17eco/types/user-api-key';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { ConfirmationModal } from '@g17eco/molecules/confirm-modal';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Badge, Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { DATE, formatDateUTC } from '@utils/date';
import { useToggle } from '@hooks/useToggle';
import { UserApiKeyCreateForm } from '@features/user-api-keys/UserApiKeyCreateForm';
import { CurrentUserData } from '@reducers/current-user';
import { useCreateUserApiKeyMutation, useRevokeUserApiKeyMutation } from '@api/users';
import { QueryError } from '@components/query/QueryError';
import { ApiKeySuccess } from '@features/user-api-keys/ApiKeySuccess';
import { SubmitButton } from '@components/button/SubmitButton';


interface Props {
  list: UserApiKeyExtended[];
  user: CurrentUserData;
  maxActiveCount: number;
}

export const UserApiKeyList = ({ list, user, maxActiveCount }: Props) => {

  const [isOpen, toggle] = useToggle();
  const [revokeItem, setRevokeItem] = useState<UserApiKeyExtended | undefined>();
  const [createApiKey, { data, isSuccess, isError, error, reset }] = useCreateUserApiKeyMutation();
  const [revoke, revokeInfo] = useRevokeUserApiKeyMutation();

  const onModalClose = () => {
    reset();
    toggle();
  }

  const buttons = useMemo(() => {
    const activeKeys = list.filter((key) => !key.revokedDate);
    const isDisabled = activeKeys.length >= maxActiveCount;
    const tooltip = isDisabled ? `Maximum of ${maxActiveCount} keys are allowed` : 'Create API Key';
    return [
      <SimpleTooltip key={'create-api-key'} delay={{ show: 0, hide: 0 }} text={tooltip}>
        <Button
          data-testid={'create-api-key'}
          outline
          color='primary'
          className={'text-nowrap'}
          onClick={toggle}
          disabled={isDisabled}
        >
          <i className={'fa fa-plus mr-2'} /><span>Create Key</span>
        </Button>
      </SimpleTooltip>
    ];
  }, [list, maxActiveCount, toggle]);

  const columns: ColumnDef<UserApiKeyExtended>[] = [
    {
      header: 'Short Token',
      accessorKey: 'shortToken'
    },
    {
      header: 'Name',
      accessorKey: 'name',
    },
    {
      header: 'Company',
      accessorFn: (row) => row.initiative?.name ?? '-',
    },
    {
      header: 'Created',
      cell: ({ row }) => {
        return (
          <SimpleTooltip text={formatDateUTC(row.original.created, DATE.DEFAULT_SPACES_WITH_TIME)}>
            {formatDateUTC(row.original.created, DATE.HUMANIZE)}
          </SimpleTooltip>
        )
      },
    },
    {
      id: 'actions',
      header: 'Revoked',
      cell: ({ row }) => {
        if (row.original.revokedDate) {
          return (
              <Badge color='warning'>
                <SimpleTooltip text={formatDateUTC(row.original.revokedDate, DATE.DEFAULT_SPACES_WITH_TIME)}>
                  Revoked
                </SimpleTooltip>
              </Badge>
          );
        }
        return (
          <Button
            color={'link-secondary'}
            size={'sm'}
            outline
            onClick={() => setRevokeItem(row.original)}
          >
            <i className={'fa fa-trash text-danger'} />
          </Button>
        );
      },
    },
  ];

  return (
    <DashboardSection title='API Keys' icon='fa-key' buttons={buttons}>
      <Table columns={columns} data={list} />

      {isOpen ? (
        <Modal isOpen={isOpen} toggle={onModalClose}>
          <ModalHeader toggle={onModalClose}>Create Api Key</ModalHeader>
          <ModalBody>
            {isError ? (<QueryError error={error} type={'danger'} />) : null}
            {isSuccess ? (
              <ApiKeySuccess onAcknowledge={onModalClose} apiKeyToken={data.token} />
            ) : (
              <UserApiKeyCreateForm user={user} onClose={onModalClose} onSubmit={(data) => createApiKey(data)} />
            )}
          </ModalBody>
        </Modal>
      ) : null}

      {revokeItem ? (
        <ConfirmationModal
          title='Revoke API Key'
          content={(
            <div>
              {revokeInfo.isError ? <QueryError error={revokeInfo.error} type='danger' /> : null}
              Are you sure you want to revoke "{revokeItem.shortToken}" API key?
            </div>
          )}
          isOpen={true}
          handleCancel={() => setRevokeItem(undefined)}
          confirmButton={
            <SubmitButton color='warning' onClick={async () => {
              return revoke({ _id: revokeItem._id })
                .unwrap()
                .then(() => setRevokeItem(undefined))
            }}>
              Revoke
            </SubmitButton>
          }
        />
      ) : null}
    </DashboardSection>
  );
}
