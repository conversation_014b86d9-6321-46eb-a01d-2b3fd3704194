/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import './ApiKeySuccess.scss';
import { Button } from 'reactstrap';
import { CopyButton } from '@features/copy';
import { BasicAlert } from '@g17eco/molecules/alert';
import config from '../../config';

interface ApiKeySuccessProps {
  /** The generated API key to display once */
  apiKeyToken: string;
  onAcknowledge: () => void;
}

export const ApiKeySuccess = ({ onAcknowledge, apiKeyToken }: ApiKeySuccessProps) => {
  return (
    <div>
      <BasicAlert className={'success-key-alert'} type={'success'}>
        <div className='d-flex align-items-baseline'>
          <i className={'fa fa-check-circle mr-2 text-BrandGreen'} />
          <div className={'text-black'}>
            <strong>API key successfully generated</strong>
            <br />
            Your new API key can now be used to access the <a href={config.apiDocsUrl} target={'_blank'}>G17Eco Public API</a>.
            Please keep it safe and do not share it with others.
          </div>
        </div>
      </BasicAlert>
      <p>
        Please save this API key somewhere safe and accessible. For security
        reasons, <strong>you won't be able to view it again</strong> through your
        account. If you lose this secret key, you'll need to generate a new one.
      </p>

      <div className={'d-flex align-items-center mb-4'}>
        <div className={'text-sm me-2 border border-1 rounded-1 p-2 flex-grow-1'}>
          <div>{apiKeyToken}</div>
        </div>
        <CopyButton content={apiKeyToken} />
      </div>

      <div className={'mb-3 justify-content-end'}>
        <Button color='primary' onClick={onAcknowledge}>
          I have saved my API key
        </Button>
      </div>
    </div>
  );
};
