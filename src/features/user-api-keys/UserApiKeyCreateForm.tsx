/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ConnectionRole, CreateApiKeyData } from '@g17eco/types/user-api-key';
import React, { useMemo, useState } from 'react';
import { Button, Form, FormGroup, Input, Label } from 'reactstrap';
import { CurrentUserData } from '@reducers/current-user';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { useAppSelector } from '@reducers/index';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';

interface UserApiKeyCreateFormProps {
  onSubmit: (data: CreateApiKeyData) => void;
  user: CurrentUserData;
  onClose: () => void;
}

export const UserApiKeyCreateForm: React.FC<UserApiKeyCreateFormProps> = ({ onClose, onSubmit, user }) => {
  const [formData, setFormData] = useState<CreateApiKeyData>({
    name: '',
    initiativeId: '',
    // Only one we support for now
    roles: [ConnectionRole.DateEntry],
    scopes: []
  });

  // This is always loaded on the app start, safe to use in low level component
  const rootInitiativesState = useAppSelector((state) => state.rootInitiatives);
  const options = useMemo(() => {
    const allowedInitiativeIds = user.permissions.reduce((acc, p) => {
      if (InitiativePermissions.canManageRoles.some(role => p.permissions.includes(role))) {
        acc.push(p.initiativeId);
      }
      return acc;
    }, [] as string[]);

    return rootInitiativesState.data
      .filter(i => allowedInitiativeIds.includes(i._id))
      .map(i => ({ value: i._id, label: i.name }));
  }, [rootInitiativesState.data, user.permissions]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Form onSubmit={handleSubmit}>
      <FormGroup>
        <Label for='apiKeyName'>API Key Name</Label>
        <Input
          max={100}
          id='apiKeyName'
          required={true}
          type='text'
          value={formData.name}
          onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder='Enter a name for your API key'
        />
      </FormGroup>

      <FormGroup>
        <SelectFactory
          required={true}
          selectType={SelectTypes.SingleSelect}
          options={options}
          onChange={(option) => {
            setFormData(prev => ({ ...prev, initiativeId: option?.value ?? '' }));
          }}
          value={options.find(o => o.value === formData.initiativeId)}
        />
      </FormGroup>

      <div className='mt-3 d-flex justify-content-between'>
        <Button color='link-secondary' onClick={onClose}>Close</Button>
        <Button color='primary' type='submit' disabled={!formData.name || !formData.initiativeId}>
          Create API Key
        </Button>
      </div>

    </Form>
  );
};
