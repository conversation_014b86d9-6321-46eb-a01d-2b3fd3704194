/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { afterAll, afterEach, beforeAll, describe, expect, it } from 'vitest';
import userEvent from '@testing-library/user-event';
import { screen, waitForElementToBeRemoved } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { http } from 'msw';
import { UserApiKeyContainer } from './UserApiKeyContainer';
import { createExtendedApiKey, createUserApiKey, userApiKeyList } from '@fixtures/user-api-key-fixtures';
import { renderWithProviders } from '@fixtures/utils';
import { createUser, getCurrentUserState, userOne } from '@fixtures/user-factory';
import { ConnectionRole, CreateApiKeyData, CreateApiKeyResponse } from '@g17eco/types/user-api-key';
import { getUrl, ResponseError, ResponseSuccess } from '@fixtures/msw-fixtures';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { rootInitiativeOne, rootInitiativeTwo } from '@fixtures/initiative-factory';
import selectEvent from 'react-select-event';
import { UserRoles } from '@constants/user';


describe('UserApiKeyContainer', () => {

  const newToken = createUserApiKey({
    shortToken: 'paShortTokenThree',
    name: 'New Key',
    roles: [ConnectionRole.DateEntry],
  });

  const [firstKey] = userApiKeyList;
  const keysUrl = getUrl('/keys/personal');

  const errorMessage = 'Not allowed to create token for this company';

  const currentUser = createUser({
    _id: userOne._id,
    permissions: [
      { initiativeId: rootInitiativeOne._id, permissions: [UserRoles.Manager] },
      { initiativeId: rootInitiativeTwo._id, permissions: [UserRoles.Manager] },
    ],
  });

  const handlers = [
    http.get(keysUrl, async () => ResponseSuccess(userApiKeyList)),
    http.post<any, CreateApiKeyData>(keysUrl, async ({ request }) => {
      const data = await request.json()
      if (data.initiativeId === rootInitiativeTwo._id) {
        return ResponseError({ message: errorMessage, userMessage: errorMessage, status: 400 })
      }
      return ResponseSuccess({ apiKey: newToken, token: newToken.longTokenHash } satisfies CreateApiKeyResponse)}),
  ];

  const server = setupServer(...handlers);

  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  const createApiKeyModalBtn = 'create-api-key';

  const renderComponent = () => {

    renderWithProviders(<UserApiKeyContainer user={currentUser} />, {
      store: reduxFixtureStore({
        currentUser: getCurrentUserState(currentUser),
        rootInitiatives: { loaded: true, loading: false, data: [rootInitiativeOne, rootInitiativeTwo] },
      }),
    });
  };


  it('renders loading state initially', () => {
    renderComponent();
    expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
  });

  it('empty api key list should render table to create new api-key', async () => {
    server.use(http.get(keysUrl, async () => ResponseSuccess([])));

    renderComponent();
    const createButton = await screen.findByTestId(createApiKeyModalBtn);
    expect(createButton).toBeInTheDocument();
    expect(createButton).not.toBeDisabled();
  });

  it('displays API keys list after loading', async () => {
    renderComponent();
    expect(await screen.findByText(firstKey.shortToken)).toBeInTheDocument();
  });

  it('displays error state when API fails', async () => {
    const message = 'user-error-message';
    server.use(
      http.get(keysUrl, async () => ResponseError({
        message: message,
        userMessage: message,
        status: 400,
      })),
    );
    renderComponent();
    expect(await screen.findByText(message)).toBeInTheDocument();
  });

  describe('API create', () => {

    const createSetup = async () => {
      renderComponent();
      const createButton = await screen.findByTestId(createApiKeyModalBtn);
      expect(createButton).toBeInTheDocument();

      await userEvent.click(createButton);
      const submitBtn = screen.getByRole('button', { name: /create api key/i });
      expect(submitBtn).toBeInTheDocument();
      expect(submitBtn).toBeDisabled();

      const nameInput = screen.getByLabelText(/name/i);
      expect(nameInput).toBeInTheDocument();
      await userEvent.type(nameInput, newToken.name);
      return submitBtn;
    }

    it('allows creating new API key', async () => {
      const submitBtn = await createSetup();

      const dropdown = screen.getByRole('combobox');
      expect(dropdown).toBeInTheDocument();
      await selectEvent.select(dropdown, rootInitiativeOne.name)

      expect(submitBtn).not.toBeDisabled();
      await userEvent.click(submitBtn);
      expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
    });

    it('allows creating new API key on empty list and see the code', async () => {
      server.use(http.get(keysUrl, async () => ResponseSuccess([])));
      server.use(http.get(keysUrl, async () => ResponseSuccess([newToken])));
      const submitBtn = await createSetup();
      await selectEvent.select(screen.getByRole('combobox'), rootInitiativeOne.name)

      expect(submitBtn).not.toBeDisabled();
      await userEvent.click(submitBtn);

      // Check if the new key is displayed
      expect(await screen.findByText(newToken.longTokenHash)).toBeVisible();

      // Close the modal
      const saveBtn = await screen.findByRole('button', { name: /API key/i });
      expect(saveBtn).toBeVisible();
      expect(saveBtn).not.toBeDisabled();
      await userEvent.click(saveBtn);

      // Check if the new key is no longer displayed
      expect(screen.queryByText(newToken.longTokenHash)).not.toBeInTheDocument();
      // Row is in the table
      expect(await screen.findByText(newToken.shortToken)).toBeInTheDocument();

      // Open modal again
      await userEvent.click(await screen.findByTestId(createApiKeyModalBtn));

      // Form is reset
      const nameInput = screen.getByLabelText(/name/i);
      expect(nameInput).toHaveValue('');
    });

    it('should display creating error from API', async () => {
      const submitBtn = await createSetup();

      const dropdown = screen.getByRole('combobox');
      expect(dropdown).toBeInTheDocument();
      await selectEvent.select(dropdown, rootInitiativeTwo.name)

      expect(submitBtn).not.toBeDisabled();
      await userEvent.click(submitBtn);

      expect(await screen.findByText(errorMessage)).toBeInTheDocument();
    });
  })

  it('respects max key limit', async () => {
    const manyKeys = Array(6).fill(null).map((_, index) => {
      return createExtendedApiKey({ name: `Index ${index}` });
    });
    server.use(
      http.get(keysUrl, () => ResponseSuccess(manyKeys))
    );

    renderComponent();
    const createButton = await screen.findByTestId(createApiKeyModalBtn);
    expect(createButton).toBeDisabled();

    const tooltip = screen.queryByText('Maximum of 5 keys are allowed');
    expect(tooltip).not.toBeInTheDocument();
  });
});
