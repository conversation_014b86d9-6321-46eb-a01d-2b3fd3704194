/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useCallback } from 'react';
import { useGetUserApiKeysQuery } from '@api/users';
import { QueryWrapper } from '@components/query/QueryWrapper';
import { UserApiKeyList } from '@features/user-api-keys/UserApiKeyList';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { CurrentUserData } from '@reducers/current-user';
import { UserApiKeyExtended } from '@g17eco/types/user-api-key';

const emptyList: UserApiKeyExtended[] = [];

export const UserApiKeyContainer = ({ user }: { user: CurrentUserData }) => {

  const apiListQuery = useGetUserApiKeysQuery();

  const renderView = useCallback((list?: UserApiKeyExtended[]) => {
    // Ideally we could get the max key from API side
    return <UserApiKeyList key={user._id} maxActiveCount={5} user={user} list={list ?? emptyList} />
  }, [user]);

  return (
    <div>
      <QueryWrapper
        query={apiListQuery}
        onLoading={() => <LoadingPlaceholder height={500} />}
        onSuccess={renderView}
        onNoData={renderView}
      />
    </div>
  );
}
