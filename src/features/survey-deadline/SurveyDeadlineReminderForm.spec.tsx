import { fireEvent, screen } from '@testing-library/react';
import {
  SurveyDeadlineReminderForm,
  ERROR_MESSAGE,
  SurveyDeadlineReminderFormProps
} from './SurveyDeadlineReminderForm';
import type { DatePickerProps } from '@g17eco/molecules/date-picker';
import { renderWithProviders } from '@fixtures/utils';
import { configureStore } from '@reduxjs/toolkit';
import currentUser from '../../reducers/current-user';
import { DATE, formatDate, getFutureIsoString, subtractDate } from '@utils/date';
import { createUser, getCurrentUserState } from '@fixtures/user-factory';
import { vi } from 'vitest';
import { simpleUserStore } from '@fixtures/redux-store';
import { FormScheduledDate } from '@models/surveyData';

const datePickerId = 'date-picker';
// mock DatePicker
vi.mock('@g17eco/molecules/date-picker', () => ({
  __esModule: true,
  DatePicker: ({ date, callbackChange }: DatePickerProps) => (
    <button type='button' data-testid={datePickerId} onClick={() => callbackChange(date)}>
      {date ? date.toJSON().slice(0, 10) : ''}
    </button>
  ),
}));

const basicAlertId = 'basic-alert';
// mock BasicAlert
vi.mock('@g17eco/molecules/alert', () => ({
  __esModule: true,
  BasicAlert: ({ children }: { children: JSX.Element }) => <div data-testid={basicAlertId}>{children}</div>,
}));

describe('<SurveyDeadlineReminderForm />', () => {

  interface DataProviderItem {
    testcase: string;
    props: Pick<SurveyDeadlineReminderFormProps, 'deadlineDate' | 'scheduledDates'>;
    input: {
      reminder: number;
      uncheckedCheckbox?: boolean;
    };
    expected: {
      checkbox?: boolean;
      deadlineDate: string;
      updateFormCalledWith: undefined |
        { code: 'deadlineDate'; value: string | undefined } |
        { code: 'scheduledDates', value: FormScheduledDate[] };
    };
    error: string | undefined;
  }

  const dataProvider: DataProviderItem[] = [
    {
      testcase: `Show ${ERROR_MESSAGE.LIMITED} when reminder is less then tomorrow`,
      props: {
        deadlineDate: getFutureIsoString(3),
        scheduledDates: [{ date: getFutureIsoString(2), status: 'updated' }],
      },
      input: {
        reminder: 3,
      },
      expected: {
        checkbox: true,
        deadlineDate: formatDate(getFutureIsoString(3), DATE.DATE_PICKER),
        updateFormCalledWith: undefined,
      },
      error: ERROR_MESSAGE.LIMITED,
    },
    {
      testcase: 'Not show any error when correct deadline and scheduled date',
      props: {
        deadlineDate: getFutureIsoString(6),
      },
      input: {
        reminder: 2,
      },
      expected: {
        checkbox: true,
        deadlineDate: formatDate(getFutureIsoString(6), DATE.DATE_PICKER),
        updateFormCalledWith: { code: 'scheduledDates', value: expect.anything() },
      },
      error: undefined,
    },
    {
      testcase: 'Unchecked survey deadline',
      props: {
        deadlineDate: getFutureIsoString(8),
        scheduledDates: [{ date: getFutureIsoString(4), status: 'updated' }],
      },
      input: {
        reminder: 2,
        uncheckedCheckbox: true,
      },
      expected: {
        checkbox: true,
        deadlineDate: formatDate(getFutureIsoString(8), DATE.DATE_PICKER),
        updateFormCalledWith: { code: 'deadlineDate', value: undefined },
      },
      error: undefined,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  dataProvider.forEach((dp) => {
    it(dp.testcase, () => {
      const mockUpdateForm = vi.fn();
      const { container } = renderWithProviders(
        <SurveyDeadlineReminderForm
          {...dp.props}
          updateForm={mockUpdateForm}
        />,
        {
          store: configureStore({
            reducer: { currentUser },
            preloadedState: {
              currentUser: getCurrentUserState(createUser({ isStaff: true })),
            },
          }),
        },
      );

      const checkbox = screen.getByTestId('survey-deadline-checkbox');
      if (checkbox) {
        if (dp.expected.checkbox) {
          expect(checkbox).toBeChecked();
        } else {
          expect(checkbox).not.toBeChecked();
        }

        if (dp.input.uncheckedCheckbox) {
          fireEvent.click(checkbox);
          expect(mockUpdateForm).toHaveBeenLastCalledWith(expect.objectContaining(dp.expected.updateFormCalledWith));
          return;
        }
      }

      const calendarTrigger = screen.getByTestId(datePickerId);
      if (calendarTrigger) {
        fireEvent.click(calendarTrigger);
        expect(calendarTrigger.textContent).toEqual(dp.expected.deadlineDate);
      }

      const reminderInput = container.querySelector('.deadline-reminder-date');
      if (reminderInput) {
        fireEvent.change(reminderInput, { target: { value: dp.input.reminder } });
        if (dp.expected.updateFormCalledWith) {
          expect(mockUpdateForm).toHaveBeenLastCalledWith(expect.objectContaining(dp.expected.updateFormCalledWith));
        }
      }

      if (dp.error) {
        expect(screen.getByTestId(basicAlertId)).toHaveTextContent(dp.error);
      }
    });
  });

  it('[GU-5878] should set status to update on change', () => {
    const mockUpdateForm = vi.fn();
    const deadline = getFutureIsoString(3);
    const scheduledDate = {
      date: getFutureIsoString(2),
      idempotencyKey: 'idempotencyKey',
      status: 'original'
    } satisfies FormScheduledDate;

    renderWithProviders(
      <SurveyDeadlineReminderForm
        scheduledDates={[scheduledDate]}
        deadlineDate={deadline}
        updateForm={mockUpdateForm}
      />,
      { store: simpleUserStore },
    );

    const reminderInput = screen.getByTestId('survey-deadline-reminder-input');
    const daysBeforeDeadline = 2;
    fireEvent.change(reminderInput, { target: { value: daysBeforeDeadline } });

    expect(mockUpdateForm).toHaveBeenLastCalledWith({
      code: 'scheduledDates',
      value: [{
        ...scheduledDate,
        date: subtractDate(deadline, daysBeforeDeadline, 'day').toISOString(),
        status: 'updated'
      }]
    });
  });

  describe('Add reminder button', () => {
    it('should be disabled when no deadline date is set', () => {
      const mockUpdateForm = vi.fn();

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={undefined}
          scheduledDates={[]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const addButton = screen.getByTestId('survey-deadline-add-reminder-btn');
      expect(addButton).toBeDisabled();
    });

    it('should add new empty scheduled date when clicked', () => {
      const mockUpdateForm = vi.fn();
      const deadline = getFutureIsoString(3);

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={deadline}
          scheduledDates={[]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const addButton = screen.getByTestId('survey-deadline-add-reminder-btn');
      fireEvent.click(addButton);

      expect(mockUpdateForm).toHaveBeenCalledWith({
        code: 'scheduledDates',
        value: [{
          date: '',
          status: 'updated'
        }]
      });
    });

    it('should preserve existing scheduled dates when adding new one', () => {
      const mockUpdateForm = vi.fn();
      const deadline = getFutureIsoString(3);
      const existingScheduledDate = {
        date: getFutureIsoString(2),
        status: 'updated'
      } satisfies FormScheduledDate;

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={deadline}
          scheduledDates={[existingScheduledDate]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const addButton = screen.getByTestId('survey-deadline-add-reminder-btn');
      fireEvent.click(addButton);

      expect(mockUpdateForm).toHaveBeenCalledWith({
        code: 'scheduledDates',
        value: [
          existingScheduledDate,
          {
            date: '',
            status: 'updated'
          }
        ]
      });
    });
  });

  describe('Invalid reminder dates handling', () => {
    it('should clear invalid reminder date when removing a scheduled date', () => {
      const mockUpdateForm = vi.fn();
      const deadline = getFutureIsoString(3);
      const scheduledDate = {
        date: '',
        status: 'updated'
      } satisfies FormScheduledDate;

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={deadline}
          scheduledDates={[scheduledDate]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const removeButton = screen.getByTestId('survey-deadline-remove-reminder-btn');
      fireEvent.click(removeButton);

      expect(mockUpdateForm).toHaveBeenCalledWith({
        code: 'scheduledDates',
        value: []
      });
    });

    it('should mark reminder input as invalid when included in invalidReminderDates', () => {
      const mockUpdateForm = vi.fn();
      const deadline = getFutureIsoString(3);
      const scheduledDate = {
        date: getFutureIsoString(4),
        status: 'updated'
      } satisfies FormScheduledDate;

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={deadline}
          scheduledDates={[scheduledDate]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const reminderInput = screen.getByTestId('survey-deadline-reminder-input');
      expect(reminderInput).toHaveClass('is-invalid');
      expect(reminderInput).toHaveAttribute('aria-invalid', 'true');
    });

    it('should clear invalid reminder date when updating a scheduled date', () => {
      const mockUpdateForm = vi.fn();
      const deadline = getFutureIsoString(3);
      const scheduledDate = {
        date: '',
        status: 'updated'
      } satisfies FormScheduledDate;

      renderWithProviders(
        <SurveyDeadlineReminderForm
          deadlineDate={deadline}
          scheduledDates={[scheduledDate]}
          updateForm={mockUpdateForm}
        />,
        { store: simpleUserStore },
      );

      const reminderInput = screen.getByTestId('survey-deadline-reminder-input');
      fireEvent.change(reminderInput, { target: { value: 2 } });

      expect(mockUpdateForm).toHaveBeenCalledWith({
        code: 'scheduledDates',
        value: [{
          date: expect.any(String),
          status: 'updated'
        }]
      });
    });
  });
});
