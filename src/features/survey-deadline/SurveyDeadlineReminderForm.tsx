/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Button, FormGroup, Input, Label } from 'reactstrap';
import { updateFn } from '@g17eco/molecules/form';
import { DatePicker } from '@g17eco/molecules/date-picker';
import { getDate, getDateTime, getFutureDate, isBefore, subtractDate } from '@utils/date';
import { FormScheduledDate } from '@models/surveyData';
import { SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { NotificationReminderForm } from './NotificationReminderForm';
import { isValidScheduledDate } from '@components/survey-configuration/utils';

export const ERROR_MESSAGE = {
  LIMITED: 'Reminder earliest day should be tomorrow',
};

export interface SurveyDeadlineReminderFormProps {
  deadlineDate?: string;
  scheduledDates?: FormScheduledDate[];
  updateForm: updateFn;
}

export const SurveyDeadlineReminderForm = (props: SurveyDeadlineReminderFormProps) => {
  const { deadlineDate, scheduledDates = [], updateForm } = props;
  const [hasDeadline, setHasDeadline] = useState(!!deadlineDate);
  const [errorMessage, setErrorMessage] = useState('');

  const minReminderDate = getDateTime(getFutureDate(1), { hour: 0, minute: 0, second: 0 });

  const handleCheck = (event: React.FormEvent<HTMLInputElement>) => {
    const checked = event.currentTarget.checked;
    if (!checked) {
      updateForm({ code: 'deadlineDate', value: undefined });
    }
    setHasDeadline(checked);
    setErrorMessage('');
  };

  const handleChangeDeadline = (date: Date | undefined) => {
    updateForm({ code: 'deadlineDate', value: date ? getDateTime(date).toISOString() : date });
  };

  const initEmptyScheduledDate = () => {
    if (!deadlineDate) {
      return;
    }
    updateForm({
      code: 'scheduledDates',
      value: [
        ...scheduledDates,
        {
          date: '',
          status: 'updated',
        },
      ],
    });
  };

  const handleChangeSchedule = (event: React.FormEvent<HTMLInputElement>, index: number) => {
    const value = event.currentTarget.value;
    const updateScheduledDate = value
      ? subtractDate(deadlineDate ?? new Date(), Number(value), 'day', false)
      : getDate(deadlineDate).toDate();

    if (isBefore(minReminderDate, updateScheduledDate)) {
      setErrorMessage(ERROR_MESSAGE.LIMITED);
    } else {
      setErrorMessage('');
    }
    updateForm({
      code: 'scheduledDates',
      value: [
        ...scheduledDates.slice(0, index),
        {
          ...scheduledDates[index],
          date: updateScheduledDate.toISOString(),
          status: 'updated',
        },
        ...scheduledDates.slice(index + 1),
      ],
    });
  };

  const handleRemoveSchedule = (index: number) => {
    updateForm({
      code: 'scheduledDates',
      value: [...scheduledDates.slice(0, index), ...scheduledDates.slice(index + 1)],
    });
  };

  return (
    <div className='deadline-container'>
      <FormGroup>
        <Input
          id='survey-deadline'
          data-testid='survey-deadline-checkbox'
          type='checkbox'
          className='mr-2'
          checked={hasDeadline}
          onChange={handleCheck}
        />
        <Label check htmlFor='survey-deadline'>
          Send reminder email/notification to all delegated users ahead of the {SURVEY.SINGULAR} deadline (includes both
          contributors and verifiers)
        </Label>
      </FormGroup>
      <DatePicker
        date={deadlineDate ? new Date(deadlineDate) : undefined}
        callbackChange={handleChangeDeadline}
        disabled={!hasDeadline}
        minDate={minReminderDate}
      />
      {scheduledDates.map((scheduledDate, index) => (
        <div key={`scheduled-date-${index}`}>
          <NotificationReminderForm
            deadlineDate={deadlineDate}
            scheduledDate={scheduledDate}
            handleChangeSchedule={(e) => handleChangeSchedule(e, index)}
            handleRemoveSchedule={() => handleRemoveSchedule(index)}
            isInvalid={!!scheduledDate.date && !isValidScheduledDate({ deadlineDate, scheduledDate })}
          />
        </div>
      ))}
      <Button
        color='link'
        data-testid='survey-deadline-add-reminder-btn'
        disabled={!deadlineDate}
        className='text-sm px-2 py-1 mt-2'
        onClick={initEmptyScheduledDate}
      >
        <i className='fal fa-plus text-ThemeAccentMedium text-sm mr-1' />
        <span className='text-ThemeAccentMedium'>Add reminder</span>
      </Button>
      {errorMessage && (
        <BasicAlert type='danger' className='mt-2'>
          {errorMessage}
        </BasicAlert>
      )}
    </div>
  );
};
