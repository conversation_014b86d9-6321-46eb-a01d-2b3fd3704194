import { getDiff } from '@utils/date';
import { FormScheduledDate } from '@models/surveyData';
import { Input } from 'reactstrap';
import IconButton from '@components/button/IconButton';

interface Props {
  deadlineDate?: string;
  scheduledDate?: FormScheduledDate;
  handleChangeSchedule: (event: React.FormEvent<HTMLInputElement>) => void;
  handleRemoveSchedule?: () => void;
  isInvalid?: boolean;
}

export const NotificationReminderForm = (props: Props) => {
  const { deadlineDate, scheduledDate, handleChangeSchedule, handleRemoveSchedule, isInvalid } = props;
  const reminderDays = deadlineDate && scheduledDate?.date ? getDiff(deadlineDate, scheduledDate.date, 'day') : '';
  return (
    <div className='d-inline-flex align-items-center mt-3'>
      <span className='me-2'>Send notification reminder</span>
      <Input
        type='number'
        data-testid='survey-deadline-reminder-input'
        className='deadline-reminder-date py-0 px-1 text-md'
        min={0}
        value={reminderDays}
        onChange={handleChangeSchedule}
        disabled={!deadlineDate}
        invalid={isInvalid}
      />
      <span className='ms-2'>days before deadline</span>
      {handleRemoveSchedule ? (
        <IconButton
          data-testid='survey-deadline-remove-reminder-btn'
          color='danger'
          icon='fal fa-xmark'
          onClick={handleRemoveSchedule}
          className='border-0 ms-1'
        />
      ) : null}
    </div>
  );
};
