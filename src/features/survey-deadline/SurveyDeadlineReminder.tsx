/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React from 'react';
import { SurveyDeadlineReminderForm, SurveyDeadlineReminderFormProps } from './SurveyDeadlineReminderForm';
import { getDate } from '@utils/date';
import { CollapseGroup } from '@g17eco/molecules/collapse-panel';
import './SurveyDeadlineReminder.scss';

export const SurveyDeadlineReminderCollapse = (props: SurveyDeadlineReminderFormProps) => {
  return (
    <CollapseGroup initialValue={!props.deadlineDate} trigger={<h5>Deadline reminder notification</h5>}>
      <SurveyDeadlineReminderForm key={getDate(props.deadlineDate).toISOString()} {...props} />
    </CollapseGroup>
  );
};
