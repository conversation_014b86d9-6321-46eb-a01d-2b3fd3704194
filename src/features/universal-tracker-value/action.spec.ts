/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { describe, expect, it } from 'vitest';
import { ActionCheck, canEditUtrv } from '@features/universal-tracker-value/action';
import { UtrvStatus } from '@constants/status';

describe('canEditUtrv function', () => {

  type DataProviderItem  = Partial<ActionCheck> & { expected: boolean };

  const dataProvider: DataProviderItem[] = [
    {
      status: undefined,
      expected: false
    },
    {
      status: UtrvStatus.Created,
      expected: false
    },
    {
      status: UtrvStatus.Created,
      canVerify: true,
      expected: false
    },
    {
      status: UtrvStatus.Created,
      canVerify: true,
      canContribute: true,
      expected: true
    },
    {
      canVerify: true,
      status: UtrvStatus.Verified,
      expected: true
    },
    {
      canVerify: true,
      canContribute: true,
      status:  UtrvStatus.Verified,
      expected: true
    },
    {
      canVerify: true,
      canContribute: true,
      status:  UtrvStatus.Created,
      expected: true
    },
  ];

  dataProvider.forEach(({ canVerify = false, canContribute = false , status, expected }) => {
    it(`should return ${expected} for canVerify: ${canVerify}, canContribute: ${canContribute}, status: ${status}`, () => {
      const result = canEditUtrv({ canVerify, canContribute, status });
      expect(result).toBe(expected);
    });
  })
});
