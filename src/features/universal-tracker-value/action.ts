/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { UtrvStatus } from '@constants/status';

export interface ActionCheck {
  canVerify: boolean;
  canContribute: boolean;
  status: string | undefined;
}

export function canEditUtrv({ canVerify, canContribute, status }: ActionCheck) {
  if (!status) {
    return false;
  }

  if (UtrvStatus.Created === status) {
    // Only created status is limited to contributor
    return canContribute;
  }

  // verified => reject, update
  // rejected => verify, update
  // updated => verify, update
  // which means it's always possible to updated,
  // unless it's created if you have one of the roles
  return canContribute || canVerify;
}
