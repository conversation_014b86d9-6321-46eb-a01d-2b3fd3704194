@import '../../../css/variables';

.modal .surveys-editing.modal-dialog .modal-content .modal-header {
  padding: 25px;
}

.surveys-editing {
  &.modal-dialog {
    max-width: 740px;

    .modal-body {
      padding: 13px 25px 0 25px;
    }
  }

  .table-component-container {
    border: 1px solid var(--theme-BgDisabled);
    padding: 7px;
    max-height: 400px;
    overflow: scroll;

    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    &::-webkit-scrollbar {
      display: none;
    }

    &.table-unresponsive .g17-table {
      .g17-tbody {
        .g17-tr:first-child {
          .g17-td {
            padding-top: 14px !important;
          }
        }
      }

      .g17-tbody .g17-tr .g17-td {
        padding: 4px 8px!important;
      }

      .g17-thead .g17-tr .g17-th {
        padding: 10px 8px;
        border: none;
        text-align: right;

        &:nth-child(1), &:nth-child(2), &:nth-child(8) {
          text-align: center;
        }

        &:nth-child(3) {
          text-align: left;
        }
      }
    }
  }

  &__table {
    margin-top: 13px;

    .form-check-input {
      position: relative;
      margin: 0;
    }

    .btn-xs {
      font-size: 0.6rem;
      padding: 0;
    }
  }

  & &__mark-btn {
    padding: 8px 16px;
    font-weight: 700;
  }
}
