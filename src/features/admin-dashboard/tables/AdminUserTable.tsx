/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { createRef, useEffect, useState } from 'react';
import { DATE, formatDate } from '@utils/date';
import { Button } from 'reactstrap';
import { getFullName } from '@utils/user';
import { naturalSort } from '@utils/index';
import { UserMessageModal } from '@components/message-modal/MessageModal';
import { DelegationStats, UserWithDelegation, UserWithStats } from '@api/initiative-stats';
import { SerializedError } from '@reduxjs/toolkit';
import { AdminUserDropdown, UserDropdownItem } from '../../../apps/company-tracker/components/admin-dashboard/AdminUserDropdown';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useAppSelector } from '@reducers/index';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table, ColumnDef } from '@g17eco/molecules/table';
import { SortingFn } from '@tanstack/react-table';
import { DISABLED_DELEGATION_MESSAGE } from '@constants/user';

type UserRow = UserWithDelegation;
interface AdminUserTableParams {
  initiativeId: string;
  highlightUserId?: string | null;
  onUserClick?: (user: UserRow) => void;
  users: UserWithDelegation[];
  error?: {
    message: string;
    name: string;
  } | SerializedError;
}

const statusSortAsc = (prop: keyof DelegationStats): SortingFn<UserRow> => (a, b): number => {
  const ap = a.original.delegationStats?.[prop] ?? -1;
  const bp = b.original.delegationStats?.[prop] ?? -1;

  if (ap < bp) return -1;
  if (ap > bp) return 1;
  return 0;
}

const highlightClass = 'row-highlight-blink';
const removeClass: React.MouseEventHandler<HTMLDivElement> = (e) => e.currentTarget.classList.remove(highlightClass);

export const AdminUserTable = (props: AdminUserTableParams) => {

  const { initiativeId, onUserClick, users, error, highlightUserId } = props;
  const [showUserMessage, setShowUserMessage] = useState<string | undefined>(undefined);
  const [selectedId, setSelectedId] = useState<string | undefined>(undefined);
  const canAccessBulkDelegation = useAppSelector(FeaturePermissions.canAccessBulkDelegation);
  const [lastScrollUserId, setLastScrollUserId] = useState<string>('');
  const history = useHistory();

  const userRowRef = createRef<HTMLDivElement>();
  useEffect(() => {
    if (userRowRef.current && highlightUserId && lastScrollUserId !== highlightUserId) {
      userRowRef.current.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
      });
      // Ensure we do not do it forever
      setLastScrollUserId(highlightUserId);
    }
  }, [userRowRef, highlightUserId, lastScrollUserId]);

  if (error && error.message) {
    return <BasicAlert type={'danger'}>{error.message}</BasicAlert>
  }

  const handleToggle = (id: string) => {
    if (selectedId === id) {
      setSelectedId(undefined);
      return;
    }
    setSelectedId(id);
  }

  const getDropdownItems = (row: UserWithStats): UserDropdownItem[] => {
    const buttons: UserDropdownItem[] = [
      {
        label: 'Message User',
        clickHandler: () => setShowUserMessage(row._id),
      },
    ];

    if (canAccessBulkDelegation) {
      buttons.push({
        label: 'Delegate to user',
        clickHandler: () =>
          history.push(generateUrl(ROUTES.SUBSIDIARY_USER_DELEGATION, { initiativeId, userId: row._id })),
        disabledMessage: row.delegateDisabled ? DISABLED_DELEGATION_MESSAGE : undefined,
      })
    }

    return buttons;
  };

  const columns: ColumnDef<UserRow>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      sortingFn: (a, b) => {
        return naturalSort(getFullName(a.original), getFullName(b.original));
      },
      cell: ({ row }) => {
        const data = row.original;
        const isHighlighted = data._id === highlightUserId;
        const tooltip = (
          <div key={`${data._id}`}>
            <strong>{getFullName(data)}</strong>
            <div>{data.email}</div>
          </div>
        );

        return (
          <div
            className={isHighlighted ? highlightClass : ''}
            onMouseEnter={removeClass}
            ref={isHighlighted ? userRowRef : undefined}
          >
            {onUserClick ? (
              <div className='d-flex'>
                <SimpleTooltip text={tooltip}>
                  <Button
                    color={'link'}
                    onClick={() => onUserClick(data)}
                    className={'text-truncate'}
                    style={{ display: 'block' }}
                  >
                    {getFullName(data)}
                  </Button>
                </SimpleTooltip>
                {data.isStaff ? (
                  <SimpleTooltip text='Staff user. This user is not visible to non-staff.'>
                    <i className='fa fa-user-ninja text-ThemeDangerMedium' />
                  </SimpleTooltip>
                ) : null}
              </div>
            ) : (
              <span className={'text-truncate'} style={{ display: 'block' }}>
                {getFullName(data)}
              </span>
            )}
          </div>
        );
      },
    },
    {
      header: 'Last Online',
      accessorFn: (row) => formatDate(row.lastLogin ?? '', DATE.MONTH_YEAR_SHORT),
      sortingFn: (a, b) => naturalSort(a.original.lastLogin ?? '', b.original.lastLogin ?? ''),
    },
    {
      header: 'Contributor',
      meta: {
        cellProps: { style: { width: 20 } },
      },
      accessorFn: (row) => row.delegationStats?.contributor || '-',
      sortingFn: statusSortAsc('contributor'),
    },
    {
      header: 'Answered',
      meta: {
        cellProps: { style: { width: 20 } },
      },
      accessorFn: (row) => row.delegationStats?.updated || '-',
      sortingFn: statusSortAsc('updated'),
    },
    {
      header: 'Verifier',
      meta: {
        cellProps: { style: { width: 20 } },
      },
      accessorFn: (row) => row.delegationStats?.verifier || '-',
      sortingFn: statusSortAsc('verifier'),
    },
    {
      header: 'Verified',
      meta: {
        cellProps: { style: { width: 20 } },
      },
      accessorFn: (row) => row.delegationStats?.verified || '-',
      sortingFn: statusSortAsc('verified'),
    },
    {
      id: 'actions',
      header: '',
      meta: {
        cellProps: {
          style: {
            borderRight: 0,
            textAlign: 'center',
            width: 40,
          },
        },
      },
      cell: ({ row }) => {
        const data = row.original;
        return (
          <AdminUserDropdown
            isOpen={data._id === selectedId}
            toggle={() => handleToggle(data._id)}
            dropdownItems={getDropdownItems(data)}
          />
        );
      },
    },
  ];

  return (
    <>
      <Table
        responsive
        pageSize={10}
        columns={columns}
        data={users}
        sortBy={[{ id: 'name', desc: false }]}
        noData={<>No results</>}
      />
      {showUserMessage ? (
        <UserMessageModal users={users} userIds={[showUserMessage]} toggle={() => setShowUserMessage(undefined)} />
      ) : null}
    </>
  );
};
