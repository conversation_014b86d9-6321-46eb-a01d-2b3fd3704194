/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import { SurveyModelMinData } from '@models/surveyData';
import { NoteInstructionsEditorViewer } from './NoteInstructionsEditorViewer';
import { RichTextEditorContainer } from '@features/rich-text-editor';

interface Props extends Pick<SurveyModelMinData, 'noteInstructionsEditorState' | 'noteInstructions'> {
  className?: string;
}

export const NoteInstructionsViewer = (props: Props) => {

  if (props.noteInstructionsEditorState) {
    return (
      <RichTextEditorContainer>
        <NoteInstructionsEditorViewer
          editorState={props.noteInstructionsEditorState}
          className={'ml-5 pl-2'}
        />
      </RichTextEditorContainer>
    );
  }

  if (props.noteInstructions) {
    return <p className='ml-5 pl-2'>{props.noteInstructions}</p>
  }

  return null;
}
