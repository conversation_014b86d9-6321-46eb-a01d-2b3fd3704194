/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Modal, ModalBody, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap';
import NoteInstructionsImage from '../../images/note-instructions.png';
import { useToggle } from '@hooks/useToggle';
import { QUESTION, SURVEY } from '@constants/terminology';
import { SurveyNoteInstructionsProps } from './types';
import { $createParagraphNode, $createTextNode, $getRoot } from 'lexical';
import { RichTextEditor, useGuardEditorState } from '@features/rich-text-editor';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export const SurveyNoteInstructionsForm = (props: SurveyNoteInstructionsProps) => {
  const { canSetNoteInstructions, noteInstructions, updateForm, disabled = false, noteInstructionsEditorState } = props;
  const [isOpen, toggle] = useToggle(false);

  const { editor, getGuardEditorState } = useGuardEditorState();

  useEffect(() => {
    if (noteInstructionsEditorState) {
      editor.setEditorState(getGuardEditorState(noteInstructionsEditorState));
      return;
    }

    if (noteInstructions) {
      editor.update(() => {
        // Get the RootNode from the EditorState
        const root = $getRoot();
        // Create a new ParagraphNode
        const paragraphNode = $createParagraphNode();
        paragraphNode.append($createTextNode(noteInstructions));
        root.append(paragraphNode);
      });
      return;
    }
    // We rely on the passed in key to handle or refresh state,
    // we only want to init once as editor is uncontrolled component
    // @TODO refactor state init to support state init and editor init at the same time
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editor]);

  useEffect(() => {
    editor.setEditable(canSetNoteInstructions && !disabled);
  }, [disabled, canSetNoteInstructions, editor]);

  return (
    <>
      <p className='mt-2'>Add your own company specific instructions/message to:</p>
      <div className='d-flex flex-row  justify-content-between'>
        <p>Further Explanation/Notes</p>

        <Button onClick={toggle} className='d-flex btn-link text-ThemeTextMedium text-sm'>
          View where text will appear <i className='fa-light fa-arrow-up-right ml-2 text-md' />
        </Button>
      </div>
      <div>
        <SimpleTooltip text={!canSetNoteInstructions ? 'This feature is not available on your current plan.' : ''}>
          <RichTextEditor
            disabled={disabled || !canSetNoteInstructions}
            placeholder={
              !canSetNoteInstructions
                ? 'This feature is not available on your current plan.'
                : `Enter your instructions or message here. It will appear on all ${QUESTION.PLURAL} in your ${SURVEY.SINGULAR}`
            }
            handleChange={({ editorState }) => {
              updateForm({ code: 'noteInstructionsEditorState', value: editorState.toJSON() });
            }}
          />
        </SimpleTooltip>
      </div>
      <Modal isOpen={isOpen} toggle={toggle} backdrop='static'>
        <ModalHeader toggle={toggle}>Custom Instructions</ModalHeader>
        <ModalBody>
          <p>
            Your custom instructions will appear on all {QUESTION.PLURAL} in your {SURVEY.SINGULAR} here:
          </p>
          <img src={NoteInstructionsImage} alt='note instructions' />
        </ModalBody>
        <ModalFooter>
          <Button color='primary' className='mr-3' onClick={toggle}>
            Dismiss
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};
