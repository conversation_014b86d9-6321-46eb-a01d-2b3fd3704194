/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import { SerializedEditorState } from 'lexical';
import { ReadonlyRichTextEditor, useGuardEditorState } from '../rich-text-editor';

interface Props {
  className?: string;
  editorState: SerializedEditorState;
}

export const NoteInstructionsEditorViewer = (props: Props) => {

  const { editorState, className = '' } = props;
  const { guardEditorState } = useGuardEditorState(editorState);

  return (
    <div className={`note-instruction-editor-viewer ${className}`} >
      <ReadonlyRichTextEditor
        editorState={guardEditorState} />
    </div>
  )
}
