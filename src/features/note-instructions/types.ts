/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { SerializedEditorState } from 'lexical/LexicalEditorState';
import { updateFn } from '@g17eco/molecules/form';

export interface SurveyNoteInstructionsProps {
  noteInstructions: string | undefined;
  noteInstructionsEditorState: SerializedEditorState | undefined;
  updateForm: updateFn;
  disabled?: boolean
  /** Key must be set to reset the whole component **/
  editorId: string
  canSetNoteInstructions: boolean;
}
