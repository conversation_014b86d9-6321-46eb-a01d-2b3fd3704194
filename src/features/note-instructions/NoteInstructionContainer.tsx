/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import { SurveyNoteInstructionsProps } from './types';
import { SurveyNoteInstructionsCollapse } from './SurveyNoteInstructions';
import { RichTextEditorContainer } from '@features/rich-text-editor';

export const NoteInstructionCollapseContainer = (props: SurveyNoteInstructionsProps) => {
  return (
    <RichTextEditorContainer key={props.editorId}>
      <SurveyNoteInstructionsCollapse {...props} />
    </RichTextEditorContainer>
  );
};
