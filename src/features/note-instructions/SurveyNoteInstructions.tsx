/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */
import { SurveyNoteInstructionsProps } from './types';
import { SurveyNoteInstructionsForm } from './SurveyNoteInstructionsForm';
import { CollapseGroup } from '@g17eco/molecules/collapse-panel';

export const SurveyNoteInstructions = (props: SurveyNoteInstructionsProps) => {
  return (
    <>
      <h5 className='mt-5 question-title-container text-ThemeAccentExtradark'>Custom Instructions</h5>
      <SurveyNoteInstructionsForm {...props} />
    </>
  );
};

export const SurveyNoteInstructionsCollapse = (props: SurveyNoteInstructionsProps) => {
  return (
    <CollapseGroup trigger={<h5>Further explanation custom instructions</h5>}>
      <SurveyNoteInstructionsForm {...props} />
    </CollapseGroup>
  );
};
