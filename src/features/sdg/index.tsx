/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { getSDGTitleByCode, getSdgUtrCode, sdgMap } from '@constants/sdg-data';
import { SDG_CODES } from '@constants/utr';
import SDGIcon from '@components/sdg-icon/sdg-icon';
import { Option } from '@g17eco/molecules/select/SelectFactory';

const getSdgOption = (sdgGoal: number | string, text: string) => {
  return (
    <span>
      <SDGIcon height='26px' code={sdgGoal} className='mr-1' />
      <span>{text}</span>
    </span>
  );
}

export const getSDGTargetOptions = (sdgGoalCode: string | undefined): Option<string>[] => {
  if (!sdgGoalCode) {
    return [];
  }

  const sdgGoal = sdgMap.find((goal) => goal.code === sdgGoalCode);

  if (!sdgGoal) {
    return [];
  }

  const sdgText = getSDGTitleByCode(sdgGoalCode)
  return [
    {
      value: getSdgUtrCode(sdgGoalCode),
      searchString: sdgText,
      label: getSdgOption(sdgGoalCode, sdgText),
    },
    // Exclude non-contributing targets, like 2.b, 8a etc.
    ...sdgGoal.targets.filter(t => !isNaN(Number(t.code))).map(({ code }) => {
      const text = getSDGTitleByCode(code);
      return ({
        value: getSdgUtrCode(code),
        searchString: text,
        label: getSdgOption(code, text),
      });
    }),
  ];
}


export const getSDGOptions = (): Option<string>[] => {
  return SDG_CODES.map((sdgGoal) => {
    const text = getSDGTitleByCode(String(sdgGoal));
    return {
      value: String(sdgGoal),
      searchString: text,
      label: getSdgOption(sdgGoal, text),
    };
  });
}
