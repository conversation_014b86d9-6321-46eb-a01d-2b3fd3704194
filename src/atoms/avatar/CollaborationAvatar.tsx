/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import React from 'react';
import classnames from 'classnames';

interface LetterAvatarProps {
  name: string;
  size?: number;
  className?: string;
  /** URL to the profile image */
  profile?: string;
  style?: React.CSSProperties;

  /** Default Avatar Letters to show when name is empty */
  defaultText?: string;
}

function stringToColor(string: string) {
  let hash = 0;
  let i;

  for (i = 0; i < string.length; i += 1) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }

  let color = '#';

  for (i = 0; i < 3; i += 1) {
    const value = (hash >> (i * 8)) & 0xff;
    color += `00${value.toString(16)}`.slice(-2);
  }

  return color;
}

export const CollaborationAvatar: React.FC<LetterAvatarProps> = (props) => {
  const { name, profile, className = '', size = 40, style, defaultText = 'U' } = props;

  const avatarStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    width: size,
    height: size,
    fontSize: size / 2,
    backgroundColor: profile ? 'transparent' : 'white',
};

  const ratio = 0.8;
  const innerStyle:  React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    width: size * ratio,
    height: size * ratio,
    fontSize: size / 2 * ratio,
    backgroundColor: stringToColor(name),
    color: '#fff',
    ...style,
  };

  const [firstName = '', surname = ''] = name.split(' ');
  const text = `${firstName[0] ?? ''}${surname[0] ?? ''}`.trim() || defaultText;

  return (
    <div className={classnames(className, 'shadow-md-alt')} style={avatarStyle}>
      {profile ?
        <img src={profile} alt={name} style={{ width: '100%', height: '100%', borderRadius: '50%' }} /> :
        <span style={innerStyle}>{text}</span>
      }
    </div>
  );
};
