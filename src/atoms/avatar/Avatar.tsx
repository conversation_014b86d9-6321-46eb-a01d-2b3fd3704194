/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import './styles.scss';

const defaultUser = (type?: string) => {
  if (type === 'organization') {
    return (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        className={`avatar-blank ${type}`} fill='#000000'
        viewBox='0 0 512 512'>
        <path d='M475.115 163.781L336 252.309v-68.28c0-18.916-20.931-30.399-36.885-20.248L160 252.309V56c0-13.255-10.745-24-24-24H24C10.745 32 0 42.745 0 56v400c0 13.255 10.745 24 24 24h464c13.255 0 24-10.745 24-24V184.029c0-18.917-20.931-30.399-36.885-20.248z'></path>
      </svg>
    );
  }

  return <svg
    className='avatar-blank' fill='#000000'
    height='24'
    viewBox='0 0 24 24' width='24'
    xmlns='http://www.w3.org/2000/svg'>
    <path
      d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z' />
    <path d='M0 0h24v24H0z' fill='none' />
  </svg>;
};

interface AvatarProps {
  children?: JSX.Element;
  width: string;
  className?: string;
  type?: string;
  style?: object;
}

export const Avatar = (props: AvatarProps) => {

  const { children, width, className = 'avatar', type = 'user', style } = props;

  return (
    <div style={{ width: width, ...style }} className={className}>
      <div className='aspect-ratio-1-1'>
        {children ? children : defaultUser(type)}
      </div>
    </div>
  )
};
