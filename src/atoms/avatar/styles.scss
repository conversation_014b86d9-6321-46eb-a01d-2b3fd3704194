/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import '../../css/variables';

.avatar {
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    background-color: var(--theme-NeutralsLight);
    display: inline-block;
    vertical-align: bottom;
}

.avatar-blank {
    fill: var(--theme-TextWhite);
    position: absolute;
    overflow: hidden;
    top:50%;
    margin-top:15%;
    left:50%;
    transform: translate3d(-50%,-50%,0);
    min-height:110%;
    min-width:110%;

  &.organization {
    margin-top: 0;
    min-width: 50%;
    fill: var(--theme-ColourBlack);
  }
}
