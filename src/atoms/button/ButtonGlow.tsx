/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import { Button, ButtonProps } from 'reactstrap';
import './styles.scss';
import classNames from 'classnames';

interface Props extends Omit<ButtonProps, 'className'> {
  classes?: {
    wrapper?: string;
    button?: string;
  };
}

export const ButtonGlow = (props: Props) => {
  return (
    <div className={classNames('button-glow-wrapper', props.classes?.wrapper)}>
      <div className='animated-border-box-glow' />
      <div className='animated-border-box'>
        <Button {...props} color={props.color} className={classNames(props.classes?.button)}>
          <span className='button-internal'>{props.children}</span>
        </Button>
      </div>
    </div>
  );
};
