/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

@import "src/css/variables";

.button-gradient.btn:not(.disabled) {
  .button-internal,
  i,
  &:after {
    background-clip: text !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
  }

  &.btn-outline-gradient {
    &:not(:hover) {
      .button-internal,
      i,
      &:after {
        background: map-get($buttonColours, "gradient", "outline", "default", "text");
      }
    }
    &:hover {
      .button-internal,
      i,
      &:after {
        background: map-get($buttonColours, "gradient", "outline", "hover", "text");
      }
    }
  }

  &:not(.btn-outline-gradient) {
    &:not(:hover) {
      .button-internal,
      i,
      &:after {
        background: map-get($buttonColours, "gradient", "solid", "default", "text");
      }
    }
    &:hover {
      .button-internal,
      i,
      &:after {
        background: map-get($buttonColours, "gradient", "solid", "hover", "text");
      }
    }
  }

  &:before {
    box-shadow: 0 0 0 4px #fc5c7d32; // Hack as there is no gradient support on box-shadow?
  }
}

.button-glow-wrapper {
  position: relative;

  .animated-border-box,
  .animated-border-box-glow {
    position: relative;
    overflow: hidden;
    z-index: 0;
    border-radius: 4px;
    padding: 2px
  }

  .animated-border-box-glow {
    overflow: hidden;
    filter: blur(20px);
  }

  .animated-border-box:before,
  .animated-border-box-glow:before {
    content: '';
    z-index: -2;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(0deg);
    position: absolute;
    width: 99999px;
    height: 99999px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-image: conic-gradient(rgba(0, 0, 0, 0), #1976ed, rgba(0, 0, 0, 0) 25%);
    animation: rotate 4s linear infinite;
  }

  .animated-border-box:after {
    content: '';
    position: absolute;
    z-index: -1;
    /* border width */
    left: 2px;
    top: 2px;
    /* double the px from the border width left */
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    background: white;
    border-radius: 4px;
  }
}

@keyframes rotate {
  100% {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}
