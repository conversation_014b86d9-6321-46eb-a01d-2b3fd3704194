/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import { Button, ButtonProps } from 'reactstrap';
import './styles.scss';

type ButtonGradientProps = Omit<ButtonProps, 'color'>;

export const ButtonGradient = (props: ButtonGradientProps) => {

  return (
    <Button
      {...props}
      color='gradient'
      className={`button-gradient ${props.className ?? ''}`}
    >
      <span className='button-internal'>
        {props.children}
      </span>
    </Button>
  );
}
