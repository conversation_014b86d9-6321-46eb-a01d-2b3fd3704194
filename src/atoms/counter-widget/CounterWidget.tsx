/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import './CounterWidget.scss';
import React from 'react';

interface CounterWidgetProps {
  count: number;
  appearance?: 'dark',
  size?: 'sm' | 'md' | 'lg'

}

export const CounterWidget = ({ count, size = 'md', appearance = 'dark' }: CounterWidgetProps) => {
  return (
    <div className={`appearance-${appearance} size-${size} counter-widget`}>
      {count}
    </div>
  )
}
