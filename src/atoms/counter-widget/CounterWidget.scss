/*!
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

@import '../../css/functions';
@import '../../css/helpers';

.counter-widget {
  text-align: center;

  // Default .dark appearance
  background-color: var(--theme-BgMedium);
  border: 1px solid var(--theme-IconDark);
  border-radius: $border-radius-sm;

  &.size-sm {
    @extend .p-1;
    @extend .text-sm;
    @extend .me-1;
    @extend .ms-1;
    min-width: map-get($spacing, 4);
  }

  &.size-md {
    @extend .me-2;
    @extend .ms-2;
    @extend .p-2;
    @extend .text-md;
    @extend .text-strong;
    // Changing the min with for
    min-width: map-get($spacing, 6);
  }

  &.size-lg {
    @extend .p-3;
    @extend .me-3;
    @extend .ms-3;
    @extend .text-lg;
    @extend .text-strong;
    min-width: map-get($spacing, 7);
  }
}
