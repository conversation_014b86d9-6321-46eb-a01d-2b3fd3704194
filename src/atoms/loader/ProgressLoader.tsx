/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */
import classNames from 'classnames';
import './styles.scss';

interface ProgressLoaderProps {
  fixed?: boolean;
  className?: string;
}

export const ProgressLoader = ({ fixed, className }: ProgressLoaderProps) => {
  return (
    <div
      className={classNames(
        fixed ? 'progress-loader-fixed' : 'position-relative d-flex justify-content-center align-items-center px-3',
        className
      )}
    >
      <div className='dot-typing' />
    </div>
  );
};
