/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { CSSProperties } from 'react';
import LogoIcon from '@g17eco/images/logo-icon.png';

interface Styles {
  loader: CSSProperties;
  image: CSSProperties;
}

interface SizeStyles {
  sm: Styles,
  md: Styles,
  lg: Styles,
}

const sizeStyles: SizeStyles = {
  sm: {
    loader: {
      margin: '-25px 0 0 -25px',
      width: '75px',
      height: '75px'
    },
    image: {
      margin: '-50px 0 0 0',
      width: '25px'
    }
  },
  md: {
    loader: {
      margin: '-50px 0 0 -50px',
      width: '100px',
      height: '100px'
    },
    image: {
      margin: '-75px 0 0 -25px',
      width: '50px'
    }
  },
  lg: {
    loader: {
      margin: '-75px 0 0 -75px',
      width: '150px',
      height: '150px'
    },
    image: {
      margin: '-125px 0 0 -50px',
      width: '100px'
    }
  },
}

const relativeWrapperStyles = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate3d(-50%,-50%,0)',
  zIndex: 10,
} as const;

interface LoaderProps {
  relative?: boolean;
  size?: keyof SizeStyles;
}

export const Loader = ({ relative, size }: LoaderProps) => {

  const { loader, image } = size && size in sizeStyles ? sizeStyles[size] : sizeStyles.md;

  return (
    <div data-testid='loader' style={relative ? {} : relativeWrapperStyles}>
      <div id='loader' style={loader} />
      <img id='loader-img' style={image} src={LogoIcon} alt='logo-icon' />
    </div>
  )
}
