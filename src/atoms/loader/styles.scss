.progress-loader-fixed {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.dot-typing {
  position: relative;
  left: -9999px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: var(--theme-ChartDark);
  color: var(--theme-ChartDark);
  box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  animation: dot-typing 1.5s infinite linear;
}

@keyframes dot-typing {
  0% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
  16.667% {
    box-shadow: 9984px -10px 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
  33.333% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
  50% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px -10px 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
  66.667% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
  83.333% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px -10px 0 0 var(--theme-ChartMedium);
  }
  100% {
    box-shadow: 9984px 0 0 0 var(--theme-ChartDark), 9999px 0 0 0 var(--theme-ChartLight), 10014px 0 0 0 var(--theme-ChartMedium);
  }
}