/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import './BasicContainer.scss';
import { ReactNode } from 'react';

interface BasicContainerProps {
  appearance?: 'info',
  size?: 'sm' | 'md' | 'lg'
  children: ReactNode;
  mb?: number;
}

/**
 * Basic Container element, for now just support a single appearance
 * Could be expanded to modify border and border appearance.
 */
export const BasicContainer = ({ children, size = 'md', appearance = 'info', mb = 0 }: BasicContainerProps) => {
  return (
    <div className={`basic-container appearance-${appearance} size-${size} mb-${mb}`}>
      {children}
    </div>
  )
}
