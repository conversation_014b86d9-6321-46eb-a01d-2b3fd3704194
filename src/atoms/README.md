## ATOMS
* Re-usable presentational items
* The smallest building blocks
* Very simple and little logic
* No hooks or API calls
* All data must be provided through props

## MOLECULES
* Re-usable presentational items
* They wrap one or multiple atoms
* Relatively simple and limited logic
* No hooks or API calls
* All data must be provided through props

## COMPONENTS / FEATURES
* Re-usable presentational items
* They wrap one or multiple molecules
* Can contain complex logic
* Data can come from hooks or API
