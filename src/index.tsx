/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */
import { createRoot } from 'react-dom/client';
import App from './App';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import * as logger from './logger';
import { ReduxStore } from './reducers/index';
import { getAnalytics } from './services/analytics/AnalyticsService';
import { CompatRouter } from 'react-router-dom-v5-compat';

const analytics = getAnalytics();
analytics.page();
logger.init();

const container = document.getElementById('root');
const root = createRoot(container!);
root.render(
  <BrowserRouter>
    <CompatRouter>
      <Provider store={ReduxStore}>
        <App />
      </Provider>
    </CompatRouter>
  </BrowserRouter>
);
