/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';

interface Props {
  toggleFilters: () => void;
  showExtendedFilters?: boolean;
  label?: string;
  reverse?: boolean;
}

export const FilterToggle = ({
  toggleFilters,
  showExtendedFilters = false,
  label = 'Advanced filters',
  reverse = false,
}: Props) => {
  const icon = <i className={showExtendedFilters ? 'fa fa-filter' : 'fal fa-filter'}></i>;
  const content = reverse ? (
    <>
      {icon} {label}
    </>
  ) : (
    <>
      {label} {icon}
    </>
  );

  return (
    <Button color='link-secondary' size='sm' onClick={() => toggleFilters()} className='p-0'>
      {content}
    </Button>
  );
};
