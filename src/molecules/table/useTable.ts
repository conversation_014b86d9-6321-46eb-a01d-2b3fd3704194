/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import {
  ChangeEventHandler,
  KeyboardEventHandler,
  useCallback,
  useEffect,
  useState,
} from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
} from '@tanstack/react-table';
import { TableProps } from './Table';

export const useTable = <T extends object = Record<string, unknown>>(
  params: Pick<TableProps<T>, 'columns' | 'data' | 'pageSize' | 'sortBy'>
) => {
  const { columns, data, pageSize = 20, sortBy } = params;
  const [currentPage, setCurrentPage] = useState<number>(1);

  const columnPinning = columns.reduce((acc, col) => {
    const { id, meta = {} } = col;
    if (id && 'sticky' in meta) {
      if (meta.sticky === 'left') {
        acc.left.push(id)
      }
      if (meta.sticky === 'right') {
        acc.right.push(id)
      }
    }

    return acc;
  }, { left: [] as string[], right: [] as string[] });

  const {
    getHeaderGroups,
    getRowModel,
    getCanPreviousPage,
    getCanNextPage,
    getPageOptions,
    getPageCount,
    setPageIndex,
    setPageSize,
    getState,
  } = useReactTable({
    columns,
    data,
    enableSortingRemoval: false,
    autoResetPageIndex: false, // Don't go back to Page 1 if data changes
    initialState: {
      pagination: {
        pageSize: pageSize,
      },
      sorting: sortBy,
      columnPinning
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const getPageIndex = useCallback(
    () => getState().pagination.pageIndex,
    [getState]
  );

  useEffect(() => {
    // reset current page if the data is out of current view
    if (data.length <= getPageIndex() * pageSize) {
      setPageIndex(0);
    }
    setPageSize(pageSize);
  }, [data, pageSize, setPageSize, getPageIndex, setPageIndex]);

  const changePage = (page: number) => {
    setPageIndex(page);
  };

  const isValidPage = (value: number) => {
    return (
      value && Math.round(value) > 0 && Math.round(value) <= getPageCount()
    );
  };

  const handleChangeCurrentPage: ChangeEventHandler<HTMLInputElement> = (e) => {
    const { value } = e.currentTarget;
    if (isNaN(Number(value))) {
      return;
    }
    setCurrentPage(Math.round(Number(value)));
  };

  const handleMovePage = (page: number) => {
    if (isValidPage(page)) {
      setCurrentPage(page);
      changePage(page - 1);
    } else {
      // reset to current page when invalid page
      setCurrentPage(getPageIndex() + 1);
      changePage(getPageIndex());
    }
  };

  const handleKeyUpPage: KeyboardEventHandler<HTMLInputElement> = (e) => {
    if (e.key === 'Enter') {
      handleMovePage(currentPage);
    }
  };

  return {
    getHeaderGroups,
    getRowModel,
    getCanPreviousPage,
    getCanNextPage,
    getPageOptions,
    getPageCount,
    getPageIndex,
    setPageIndex,
    setPageSize,
    getState,
    currentPage,
    handleChangeCurrentPage,
    handleMovePage,
    handleKeyUpPage,
  };
};
