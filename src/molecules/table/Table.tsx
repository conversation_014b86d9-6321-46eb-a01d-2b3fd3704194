/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ColumnDef as DefaultColumnDef, flexRender, RowData, SortDirection } from '@tanstack/react-table';
import { Button, Input } from 'reactstrap';
import './styles.scss';
import classNames from 'classnames';
import { useTable } from './useTable';
import { getCommonPinningStyles } from './utils';

/**
 * Briefly the updates:
 *
 * Regarding, Column definitions:
 *  - accessor was renamed to either accessorKey or accessorFn
 *  - lowercase Header, Cell, Footer
 *  - All disable* column options were renamed to enable* column options.
 *    (e.g. disableSortBy is now enableSorting, disableGroupBy is now enableGrouping, etc.)
 *  - sortType was updated to sortingFn
 *
 *  Update details: https://tanstack.com/table/v8/docs/guide/migrating
 */

/**
 * Condition for a sticky columns table
 *
 * TableProps => sticky: true
 *
 * ColumnDef {
 *   id: string, (required)
 *   meta: {
 *     headerProps: {
 *       style: {
 *         left: ... | right: ...,
 *         width: ...,
 *         minWidth: ...
 *       }
 *     },
 *     cellProps: {
 *       style: {
 *         left: ... | right: ...,
 *         width: ...,
 *         minWidth: ...
 *       }
 *     }
 *   },
 *   sticky: 'left' | 'right'
 * }
 */
interface CustomMeta {
  headerProps?: {
    className?: string;
    style?: CSSKeywordValue;
    sortTooltip?: string;
  };
  cellProps?: {
    className?: string;
    style?: CSSKeywordValue;
  };
  sticky?: 'left' | 'right';
}

const iconMap = {
  asc: <i className='fal fa-caret-down ml-1 text-ThemeTextDark' />,
  desc: <i className='fal fa-caret-up ml-1 text-ThemeTextDark' />,
  default: <i className='fal fa-caret-down ml-1 text-ThemeTextPlaceholder sort-icon-placeholder' />,
}

export type ColumnDef<TData extends RowData> = DefaultColumnDef<TData>;

export interface TableProps<T extends object = Record<string, unknown>> {
  columns: ColumnDef<T>[];
  data: T[];
  pageSize?: number;
  className?: string;
  showRowCount?: boolean;
  showHeader?: boolean;
  noData?: string | JSX.Element;
  responsive?: boolean;
  sortBy?: { id: string; desc: boolean }[];
  sticky?: boolean;
  style?: Record<string, any>;
}

export const Table = <T extends object = Record<string, unknown>>(props: TableProps<T>) => {
  const {
    data,
    pageSize = 20,
    className = '',
    showRowCount = false,
    showHeader = true,
    responsive = false,
    noData,
    sticky,
    style,
  } = props;

  const {
    getHeaderGroups,
    getRowModel,
    getCanPreviousPage,
    getCanNextPage,
    getPageOptions,
    getPageCount,
    getPageIndex,
    currentPage,
    handleChangeCurrentPage,
    handleMovePage,
    handleKeyUpPage,
  } = useTable<T>(props);

  if (data.length === 0 && noData) {
    return <div className={classNames('table-component-container', className)}>{noData}</div>;
  }

  const getSortIcon = ({ canSort, isSorted }: { canSort: boolean; isSorted: false | SortDirection }) => {
    if (!canSort) {
      return null;
    }

    const sortIcon = isSorted && isSorted in iconMap ? iconMap[isSorted] : iconMap.default;
    return <span>{sortIcon}</span>
  };

  return (
    <div className='table-component-wrapper'>
      <div
        className={classNames(
          'table-component-container',
          responsive ? 'table-responsive' : 'table-unresponsive',
          { 'sticky-table': sticky },
          className
        )}
        style={style}
      >
        <div className='g17-table'>
          {showHeader ? (
            <div className='g17-thead'>
              {getHeaderGroups().map((headerGroup) => (
                <div className='g17-tr' key={headerGroup.id}>
                  {showRowCount && <div className='g17-tr rowCountCol'></div>}
                  {headerGroup.headers.map((header) => {
                    const { column } = header;
                    const meta = column.columnDef.meta as CustomMeta;
                    const canSort = header.column.getCanSort();
                    const isSorted = header.column.getIsSorted();

                    return (
                      <div
                        key={header.id}
                        title={meta?.headerProps?.sortTooltip}
                        className={classNames(
                          'g17-th',
                          { 'cursor-pointer select-none': canSort, sticky: meta?.sticky },
                          meta?.headerProps?.className
                        )}
                        onClick={header.column.getToggleSortingHandler()}
                        //IMPORTANT: This is where sticky columns work!
                        style={{ ...getCommonPinningStyles(column), ...meta?.headerProps?.style }}
                      >
                        <span>{flexRender(header.column.columnDef.header, header.getContext())}</span>
                        {getSortIcon({ canSort, isSorted })}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          ) : (
            <></>
          )}
          <div className='g17-tbody'>
            {getRowModel().rows.map((row, i) => {
              return (
                <div className='g17-tr' key={row.id}>
                  {showRowCount && <div className='g17-td rowCountCol'>{`${getPageIndex() * pageSize + i + 1}.`}</div>}
                  {row.getVisibleCells().map((cell) => {
                    const { column } = cell;
                    const meta = column.columnDef.meta as CustomMeta;
                    return (
                      <div
                        key={cell.id}
                        className={classNames('g17-td', { sticky: meta?.sticky }, meta?.cellProps?.className)}
                        //IMPORTANT: This is where sticky columns!
                        style={{ ...getCommonPinningStyles(column), ...meta?.cellProps?.style }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className='pagination align-items-center mt-2 py-2'>
        {getPageCount() > 1 ? (
          <div className='d-flex align-items-center'>
            <Button size='sm' className='mr-2' onClick={() => handleMovePage(1)} disabled={!getCanPreviousPage()}>
              <i className='fa fa-fast-backward' />
            </Button>
            <Button
              size='sm'
              className='m-0'
              onClick={() => handleMovePage(currentPage - 1)}
              disabled={!getCanPreviousPage()}
            >
              <i className='fa fa-caret-left' />
            </Button>
            <span className='ms-4 me-1'>Page</span>{' '}
            <Input
              className='d-inline-block pagination-input text-center rounded-1'
              name='currentPage'
              value={currentPage}
              onChange={handleChangeCurrentPage}
              onKeyUp={handleKeyUpPage}
              onBlur={() => handleMovePage(currentPage)}
            />{' '}
            <span className='ms-1 me-4'>of {getPageOptions().length}</span>
            <Button
              size='sm'
              className='m-0'
              onClick={() => handleMovePage(currentPage + 1)}
              disabled={!getCanNextPage()}
            >
              <i className='fa fa-caret-right' />
            </Button>
            <Button
              size='sm'
              className='ml-2'
              onClick={() => handleMovePage(getPageCount())}
              disabled={!getCanNextPage()}
            >
              <i className='fa fa-fast-forward' />
            </Button>
          </div>
        ) : null}
      </div>
    </div>
  );
};
