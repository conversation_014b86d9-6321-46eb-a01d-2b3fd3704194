/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

@import '../../css/functions';

.table-component-container {
  overflow-y: hidden;

  &.table-unresponsive {
    .g17-table {
      display: table !important;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0px;
      padding-right: 1px;

      .g17-thead {
        display: table-header-group !important;

        .g17-tr {
          min-height: 40px;
          background-color: var(--theme-BgToolbar);
          color: var(--theme-HeadingLight);
          border-spacing: 9px;

          .g17-th {
            padding: 9px 12px;
            font-weight: bold;
            border-left: 1px solid var(--theme-NeutralsLight);
            position: relative;
            white-space: nowrap;

            &:first-of-type {
              border-top-left-radius: $borderRadius;
              border-bottom-left-radius: $borderRadius;
              border-left: 1px solid transparent;
            }

            &:last-of-type {
              border-top-right-radius: $borderRadius;
              border-bottom-right-radius: $borderRadius;
            }

            i {
              width: 1rem;
            }
          }
        }
      }

      .g17-tbody {
        display: table-row-group !important;
        border: none;
      }

      .g17-tr {
        display: table-row !important;
        border: none;
        padding-top: 0rem;
        margin-bottom: 0rem;
        flex-direction: unset;
        flex-wrap: unset;
      }

      .g17-th,
      .g17-td {
        display: table-cell !important;
        border: 1px solid transparent;
        vertical-align: middle;

        &.rowCountCol {
          width: 1px;
          line-height: 2rem;
          white-space: nowrap;
          text-align: right;
          padding-right: 0.5rem;
        }

        .badge {
          &:hover {
            cursor: default;
            filter: brightness(105%);
          }
        }
      }

      .g17-tbody {
        .g17-tr {
          .g17-td {
            padding: 3px 0px 3px 12px !important;
          }
        }
      }
    }
  }

  &.table-responsive {
    .g17-table {
      .g17-thead {
        display: none;
      }

      .g17-tbody {
        border-bottom: 1px solid var(--theme-NeutralsLight);
        display: block;
      }

      .g17-tr {
        display: flex !important;
        border-top: 1px solid var(--theme-NeutralsLight);
        padding-top: 1rem;
        margin-bottom: 1rem;
        flex-direction: row !important;
        flex-wrap: wrap !important;
        align-items: flex-start !important;
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important;
      }

      .g17-th,
      .g17-td {
        display: block;
        white-space: break-spaces;
        margin-top: auto;
        min-height: 2rem;

        &.row-break-1 {
          flex-grow: 1;
          width: 100%;
        }

        &.row-break-0 {
          width: unset !important;
        }

        &.rowCountCol {
          line-height: 2rem;
          white-space: nowrap;
          text-align: right;
          padding-right: 1rem;
        }

        .badge {
          &:hover {
            cursor: default;
            filter: brightness(105%);
          }
        }
      }
    }

    @include media-breakpoint-up(sm) {
      .row-sm-break-1 {
        flex-grow: 1;
        width: 100%;
      }

      .row-sm-break-0 {
        width: unset !important;
      }
    }

    @include media-breakpoint-up(md) {
      .row-md-break-1 {
        flex-grow: 1;
        width: 100%;
      }

      .row-md-break-0 {
        width: unset !important;
      }
    }

    @include media-breakpoint-up(lg) {
      .row-lg-break-1 {
        flex-grow: 1;
        width: 100%;
      }

      .row-lg-break-0 {
        width: unset !important;
      }
    }

    @include media-breakpoint-up(lg) {
      .g17-table {
        display: table !important;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0px;

        .g17-thead {
          display: table-header-group !important;

          .g17-tr {
            min-height: 40px;
            background-color: var(--theme-BgToolbar);
            color: var(--theme-HeadingLight);
            border-spacing: 9px;

            .g17-th {
              padding: 9px 12px;
              font-weight: bold;
              border-left: 1px solid var(--theme-NeutralsLight);
              position: relative;
              white-space: nowrap;

              &:first-of-type {
                border-top-left-radius: $borderRadius;
                border-bottom-left-radius: $borderRadius;
                border-left: 1px solid transparent;
              }

              &:last-of-type {
                border-top-right-radius: $borderRadius;
                border-bottom-right-radius: $borderRadius;
              }

              i {
                width: 1rem;
              }
            }
          }
        }

        .g17-tbody {
          display: table-row-group !important;
          border: none;
        }

        .g17-tr {
          display: table-row !important;
          border: none;
          padding-top: 0rem;
          margin-bottom: 0rem;
          flex-direction: unset;
          flex-wrap: unset;
        }

        .g17-th,
        .g17-td {
          display: table-cell !important;
          border: 1px solid transparent;
          vertical-align: middle;
        }

        .g17-tbody {
          .g17-tr {
            .g17-td {
              padding: 3px 0px 3px 12px !important;
            }
          }
        }
      }
    }
  }

  &.table-responsive,
  &.table-unresponsive {
    .g17-th.col-hidden,
    .g17-td.col-hidden {
      display: none !important;
    }

    @include media-breakpoint-down(sm) {
      .g17-th.col-sm-hidden,
      .g17-td.col-sm-hidden {
        display: none !important;
      }
    }

    @include media-breakpoint-down(md) {
      .g17-th.col-md-hidden,
      .g17-td.col-md-hidden {
        display: none !important;
      }
    }

    @include media-breakpoint-down(lg) {
      .g17-th.col-lg-hidden,
      .g17-td.col-lg-hidden {
        display: none !important;
      }
    }

    @include media-breakpoint-down(xl) {
      .g17-th.col-xl-hidden,
      .g17-td.col-xl-hidden {
        display: none !important;
      }
    }

    @include media-breakpoint-up(xxl) {
      .g17-th.col-xxl-table-cell,
      .g17-td.col-xxl-table-cell {
        display: table-cell !important;
      }
    }

    .g17-th {
      .sort-icon-placeholder {
        visibility: hidden;
      }

      &:hover .sort-icon-placeholder {
        visibility: visible;
      }
    }
  }

  &.sticky-table {
    overflow-x: scroll !important;

    .g17-table {
      /* box-shadow and borders will not work with positon: sticky otherwise */
      border-collapse: separate !important;
      border-spacing: 0;

      .g17-thead {
        .g17-th.sticky {
          background-color: var(--theme-BgToolbar);
        }
      }

      .g17-tbody {
        .g17-td.sticky {
          background-color: white;
        }
      }
    }
  }
}

.table-component-wrapper {
  .pagination {
    margin-top: 0.5rem;
    position: relative;
    text-align: center;

    & > div {
      margin: 0px auto;
    }

    span {
      font-size: 1rem;
      color: var(--theme-TextMedium);
      line-height: 2rem;
      margin: 0px 20px;
    }

    button {
      line-height: 2rem;
      margin: 0px 2px;
      padding: 0px 6px;
      width: auto;
      background-color: var(--theme-BgToolbar);
      color: var(--theme-HeadingLight);

      &:hover {
        background-color: var(--theme-AccentMedium);
        color: var(--theme-TextWhite);
      }

      &.disabled {
        background-color: transparent !important;
        color: var(--theme-TextDark) !important;
      }

      i {
        width: 2rem;
      }
    }

    &-input {
      width: 32px;
      padding: 3px 4px;
    }
  }
}
