/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { FormGroup, FormText, Input, Label } from 'reactstrap';
import {
  getTableColumnTypeNames,
  TableColumnType,
  UniversalTrackerPlain,
  UtrValueType,
} from '@g17eco/types/universalTracker';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import G17Client from '@services/G17Client';
import { Loader } from '@g17eco/atoms/loader';
import { useRouteMatch } from 'react-router-dom';
import { MetricType } from '@g17eco/types/custom-report';
import { UTRSuggestion } from '@components/search/UTRSearch';

interface Props {
  utr: UTRSuggestion;
  setValueListCode: (value: string) => void;
  selectedValueListCode?: string;
  setColumnCode: (value: string) => void;
  selectedColumnCode?: string;
  allowedUtrValueTypes: UtrValueType[];
  allowedColumnValueTypes: TableColumnType[];
}

interface UrlParams {
  metricType?: MetricType;
}

export const ValueListSelection = (props: Props) => {
  const {
    utr,
    setValueListCode,
    selectedValueListCode,
    setColumnCode,
    selectedColumnCode,
    allowedUtrValueTypes,
    allowedColumnValueTypes,
  } = props;

  const thisId = useMemo(() => Math.random().toString(36).substr(2, 9), []);

  const [utrDetails, setUtrDetails] = React.useState<UniversalTrackerPlain>();

  const isTable = utr.valueType === UtrValueType.Table;

  React.useEffect(() => {
    setUtrDetails(undefined);
    G17Client.loadUniversalTracker(utr._id).then(setUtrDetails);
  }, [utr._id, setUtrDetails]);

  const valueListOptions = useMemo(() => {
    if (!utrDetails || utrDetails._id !== utr._id) {
      return;
    }
    if (isTable) {
      if (selectedColumnCode) {
        const column = utr.valueValidation?.table?.columns.find((col) => col.code === selectedColumnCode);
        if (column && column.listId) {
          const list = utrDetails.tableColumnValueListOptions?.find((l) => l._id === column.listId);
          if (list) {
            return list.options;
          }
        }
      }
      return;
    }
    return utr.valueListOptions?.options ?? utr.valueValidation?.valueList?.custom;
  }, [isTable, selectedColumnCode, utr, utrDetails]);

  let selectedColumnType;

  const tableColumnsOptions = utr.valueValidation?.table?.columns.map((c) => {
    const isAllowed = allowedColumnValueTypes.includes(c.type);
    if (c.code === selectedColumnCode) {
      selectedColumnType = c.type;
    }

    const typeString = !isAllowed ? 'Not Supported' : getTableColumnTypeNames(c.type, Boolean(c.listId), c);
    return {
      value: c.code,
      label: `${c.name} (${typeString})`,
      isDisabled: !isAllowed,
    };
  });

  const isValidTable = isTable && tableColumnsOptions;
  const isValidValueList = !isTable && valueListOptions;

  const isAllowedType = allowedUtrValueTypes.includes(utr.valueType);
  const isAllowedColumn = selectedColumnType && allowedColumnValueTypes.includes(selectedColumnType);
  const isAllowed = isAllowedType && (!isTable || isAllowedColumn);
  const viewType = useRouteMatch<UrlParams>().params?.metricType;
  const canDisplayAmountOptions = valueListOptions && viewType !== MetricType.Text;

  if (!isAllowed && !isValidTable && !isValidValueList) {
    return null;
  }

  if (!utrDetails) {
    return <Loader />;
  }

  return (
    <>
      {isTable ? (
        <FormGroup tag='fieldset' className='mt-3'>
          <div className='d-flex justify-content-between flex-wrap'>
            <Label className='strong'>Which column are you interested in:</Label>
            <FormText className='text-right'>
              (permitted columns: {allowedColumnValueTypes.map((t) => getTableColumnTypeNames(t)).join(', ')})
            </FormText>
          </div>
          <div className='mx-1'>
            <SelectFactory
              selectType={SelectTypes.SingleSelect}
              placeholder={'Please select the table column'}
              options={tableColumnsOptions}
              value={tableColumnsOptions?.find((col) => col.value === selectedColumnCode)}
              onChange={(col) => setColumnCode(col?.value ?? '')}
            />
          </div>
        </FormGroup>
      ) : null}
      {canDisplayAmountOptions ? (
        <FormGroup tag='fieldset' className='mt-3'>
          <Label className='strong'>Are you interested in the amount of:</Label>
          <div className='mx-1'>
            {valueListOptions?.map((vl) => (
              <FormGroup key={`valueList-radio--${thisId}-${vl.code}`} check>
                <Input
                  type='radio'
                  name={`valueList-radio-${thisId}`}
                  defaultChecked={vl.code === selectedValueListCode}
                  onClick={() => setValueListCode(vl.code)}
                />
                <Label check>{vl.name} answers</Label>
              </FormGroup>
            ))}
          </div>
        </FormGroup>
      ) : null}
    </>
  );
};
