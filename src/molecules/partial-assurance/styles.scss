.partial-assurance {
  &-wrapper {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    transform: translateX(100%);
    display: flex;
    align-items: center;
    padding-left: 6px;
  }

  &-checkbox {
    &:checked {
      background-color: var(--theme-AccentMedium) !important;
    }
  }
}

.partially-assured-icon {
  background: linear-gradient(90deg, var(--theme-SuccessMedium) 0%, var(--theme-SuccessMedium) 50%, var(--theme-TextLight) 50%, var(--theme-TextLight) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
