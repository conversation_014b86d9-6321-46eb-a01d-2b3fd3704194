/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { SearchBox } from './SearchBox';

interface Props {
  onTextChange: (value: string) => void;
  defaultText?: string;
  placeholder?: string;
  wait?: number;
  classNames?: {
    wrapper?: string;
    input?: string;
  };
}

export const DebouncedSearchBox = (props: Props) => {
  const { defaultText = '', onTextChange, placeholder = '', wait = 500, classNames } = props;

  const [searchTerm, setSearchTerm] = useState<string>(defaultText);

  const debounced = useDebouncedCallback(onTextChange, wait);

  const handleTextChange = (e: React.FormEvent<HTMLInputElement>) => {
    setSearchTerm(e.currentTarget.value);
    debounced(e.currentTarget.value);
  };

  return (
    <SearchBox
      classNames={classNames}
      handleOnChange={handleTextChange}
      searchText={searchTerm}
      placeholder={placeholder}
    />
  );
};
