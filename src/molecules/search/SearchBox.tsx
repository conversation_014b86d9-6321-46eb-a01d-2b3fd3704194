/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { Input } from 'reactstrap';

interface SearchBoxOptionsInterface {
  searchText?: string;
  placeholder?: string;
  handleOnChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  classNames?: {
    wrapper?: string;
    input?: string;
  };
}
export const SearchBox = (props: SearchBoxOptionsInterface) => {
  const { handleOnChange, searchText = '', placeholder = 'Search', classNames = {} } = props;

  return (
    <div className={`search-box ${classNames.wrapper ?? ''}`}>
      <Input
        placeholder={placeholder}
        onChange={handleOnChange}
        value={searchText}
        className={`border-ThemeBorderDefault text-md ${classNames.input ?? ''}`}
      />
    </div>
  );
};
