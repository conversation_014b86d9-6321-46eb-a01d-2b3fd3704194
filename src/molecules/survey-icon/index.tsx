/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { SurveySummary, SurveyType } from '@g17eco/types/survey';
import { QUESTION } from '@constants/terminology';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import CompletedReport from '@g17eco/images/completed-report.svg';

export const SurveyIcon = ({ survey }: { survey: Pick<SurveySummary, 'type' | 'status' | 'completedDate'> }) => {
  if (survey.type === SurveyType.AutoAggregation) {
    return <span className='px-2'></span>;
  }

  if (survey.completedDate) {
    return (
      <SimpleTooltip text='This report is completed'>
        <img alt='This report is completed' src={CompletedReport} width={16} />
      </SimpleTooltip>
    );
  }

  const { created, updated, verified, rejected } = survey.status;
  const isCombinedReport = survey.type === SurveyType.Aggregation;
  const isAllVerified = verified > 0 && created === 0 && updated === 0 && rejected === 0;
  const hasRejected = rejected > 0;
  const inProgress = survey.status.updated > 0 || survey.status.verified > 0;

  const icon = (() => {
    if (isCombinedReport) {
      return 'fal fa-object-exclude';
    }
    if (isAllVerified) {
      return 'fal fa-circle-check';
    }
    return 'far fa-circle';
  })();

  const color = (() => {
    if (isCombinedReport) {
      return 'text-ThemeIconDark';
    }
    if (isAllVerified) {
      return 'text-ThemeSuccessMedium';
    }
    if (hasRejected) {
      return 'text-ThemeDangerLight';
    }
    if (inProgress) {
      return 'text-ThemeIconDark';
    }
    return 'text-ThemeIconSecondary';
  })();

  const tooltip = (() => {
    switch (true) {
      case isCombinedReport:
        return 'This is a combined report';
      case isAllVerified:
        return `This report has all ${QUESTION.PLURAL} verified`;
      case hasRejected:
        return `This report has rejected ${QUESTION.PLURAL} that need to be reviewed`;
      case inProgress:
        return 'This report is in progress';
      default:
        return 'This report has not started';
    }
  })();

  return (
    <SimpleTooltip text={tooltip}>
      <i className={`${icon} ${color} fs-5`} />
    </SimpleTooltip>
  );
};
