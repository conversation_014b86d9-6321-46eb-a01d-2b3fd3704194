/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { Column } from '../tracking-list';

export interface ColumnTitleProps
  extends Pick<ColumnCommonProps, 'question' | 'alternativeCode' | 'preferredAltCodes'> {
  handleGoToQuestion?: () => void;
  tooltip?: string;
}

const getTitle = (p: ColumnTitleProps) => {
  const { question, alternativeCode, preferredAltCodes } = p;
  const universalTracker = question.universalTracker;

  if (Array.isArray(preferredAltCodes)) {
    for (const code of preferredAltCodes) {
      if (universalTracker.hasAlternativeInfo(code)) {
        return universalTracker.getName(code)
      }
    }
  }

  return universalTracker.getName(alternativeCode);
}

export const ColumnTitle = (props: ColumnTitleProps) => {
  const { tooltip } = props;
  const fullTitle = getTitle(props);
  const displayTooltip = tooltip ?? fullTitle;

  return (
    <Column className='pl-2' fill truncateTwoline tooltip={displayTooltip} onClick={props.handleGoToQuestion}>
      {fullTitle}
    </Column>
  );
}
