/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { UtrvCommentCount } from '../../api/utrv-comments';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { Column } from '../tracking-list';
import { SimpleTooltip } from '../simple-tooltip';

interface Props extends Pick<ColumnCommonProps, 'question'> {
  utrvsCommentCount?: UtrvCommentCount[];
}

const hasUtrvComments = (utrvsCommentCount: UtrvCommentCount[] | undefined, utrvId: string | undefined) => {
  if (!utrvsCommentCount || !utrvsCommentCount.length || !utrvId) {
    return false;
  }
  const commentCount = utrvsCommentCount.find((item) => item.utrvId === utrvId)?.commentCount ?? 0;

  return Boolean(commentCount);
};

export const ColumnCommentFlag = (props: Props) => {
  const { question, utrvsCommentCount } = props;

  const hasComments = hasUtrvComments(utrvsCommentCount, question.utrv?._id);

  if (!hasComments) {
    return null;
  }

  return (
    <Column className={'d-none d-md-inline-block commentFlagCol text-ThemeTextPlaceholder ps-0 pe-1'}>
      <SimpleTooltip text='New comment'>
        <i className='fal fa-message-lines me-1' />
      </SimpleTooltip>
    </Column>
  );
};
