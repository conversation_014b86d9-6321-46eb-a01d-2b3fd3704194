interface Props {
  icon: string;
  header: string;
  content: string;
}

export const FeatureIntro = ({ icon, header, content }: Props) => {
  return (
    <div className='d-flex justify-content-between align-items-center gap-3'>
      <i className={`fal ${icon} text-ThemeIconDark fa-3x`} />
      <div>
        <h6 className='text-ThemeHeadingDark'>{header}</h6>
        <p>{content}</p>
      </div>
    </div>
  );
};
