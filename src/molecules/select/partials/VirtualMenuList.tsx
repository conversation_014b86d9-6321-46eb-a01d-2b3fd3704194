/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { components } from 'react-select';
import { FixedSizeList } from 'react-window';

const itemSize = 44;
export const VirtualMenuList: typeof components.MenuList = (props) => {
  const { children, maxHeight } = props;
  const childrenArray = Array.isArray(children) ? children : [children];

  return (
    <FixedSizeList height={maxHeight} width='100%' itemCount={childrenArray.length} itemSize={itemSize}>
      {({ index, style }) => (
        <div className='p-0 truncateTwoline' style={style}>
          {childrenArray[index]}
        </div>
      )}
    </FixedSizeList>
  );
};
