/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';

export interface RadioOptions {
  formCheckClass?: string;
  groupCode: string;
  value: string | undefined;
  formGroupClassName?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  options: {
    code: string;
    name: string;
    disabled?: boolean;
    tooltip?: string;
  }[];
}

export const RadioGroup = ({
  groupCode,
  value,
  disabled,
  options = [],
  onChange = () => {},
  formGroupClassName = '',
}: RadioOptions) => {
  return (
    <div>
      {options.map((o) => {
        const id = `${groupCode}-${o.code}`;
        const isDisabled = disabled || o.disabled;
        const isSelected = o.code === value;
        const className = `form-check-label ${isDisabled && !isSelected ? 'text-ThemeTextPlaceholder' : ''}`;
        return (
          <SimpleTooltip text={o.tooltip} key={id}>
            <FormGroup check key={id} className={formGroupClassName}>
              <Input
                type='radio'
                disabled={isDisabled}
                name={groupCode}
                checked={isSelected}
                onChange={(e) => onChange(e)}
                id={id}
                value={o.code}
              />

              <Label check className={className} htmlFor={id}>
                {o.name}
              </Label>
            </FormGroup>
          </SimpleTooltip>
        );
      })}
    </div>
  );
};
