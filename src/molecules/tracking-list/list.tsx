/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { CSSProperties } from 'react';
import { SimpleTooltip } from '../simple-tooltip';
import './style.scss';
import classNames from 'classnames';

export interface TrackingListProps {
  tooltip?: string | JSX.Element;
  className?: string;
  style?: CSSProperties;
  children?: React.ReactNode;
  border?: boolean;
}

const List = (props: TrackingListProps) => {
  const id = props.tooltip ? Math.random().toString(36).substr(2, 9) : undefined;

  if (!props.children) {
    return null;
  }

  const className = classNames({
    'trackingListContainer': true,
    'd-flex flex-column': true,
    'no-border': props.border === false,
    [props.className ?? '']: props.className
  });
  const el = <div className={className} style={props.style} >
    {props.children}
  </div>;

  if (props.tooltip) {
    return <SimpleTooltip
      id={id}
      text={props.tooltip}
      component={el} />
  }

  return el;
}

export default List;
