/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { CSSProperties, ForwardRefRenderFunction, useMemo } from 'react';
import { SimpleTooltip } from '../simple-tooltip';

export interface TrackingListRowProps {
  tooltip?: string | JSX.Element;
  className?: string;
  onClick?: null | (() => void);
  highlightColour?: string;
  style?: CSSProperties;
  children?: React.ReactNode;
  header?: boolean;
  onMouseEnter?: React.MouseEventHandler<HTMLDivElement>;
}

const Row: ForwardRefRenderFunction<HTMLDivElement, TrackingListRowProps> = (props, ref) => {

  const className = useMemo(() => {
    let className = 'trackingListRow d-flex align-items-stretch';
    if (props.header) {
      className = `${className} header`;
    }
    if (props.className) {
      className = `${className} ${props.className}`;
    }
    if (props.onClick) {
      className = `${className} clickable`;
    }
    if (props.highlightColour) {
      className = `${className} highlight highlight-${props.highlightColour}`;
    }
    return className;
  }, [props]);

  const onClick = props.onClick ?? undefined;

  const divProps = {
    ...props,
    ref,
    className,
    onClick,
  }

  if (props.tooltip) {
    return <SimpleTooltip text={props.tooltip}>
      <div {...divProps}>{props.children}</div>
    </SimpleTooltip>
  }

  return <div {...divProps}>{props.children}</div>
}

export default React.forwardRef(Row);
