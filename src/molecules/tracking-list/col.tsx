/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { CSSProperties } from 'react';
import { SimpleTooltip } from '../simple-tooltip';

export interface TrackingListColumnProps {
  tooltip?: string | JSX.Element;
  className?: string;
  onClick?: (e?: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  style?: CSSProperties;
  stretch?: boolean;
  fill?: boolean;
  noWrap?: boolean;
  disabled?: boolean;
  truncate?: boolean;
  truncateTwoline?: boolean;
  children?: React.ReactNode;
}

const Col = (props: TrackingListColumnProps) => {
  const { onClick, disabled, noWrap = true } = props;

  const getClassName = () => {
    let className = 'trackingListColumn align-self-center align-self-stretch';
    if (props.className) {
      className = `${className} ${props.className}`;
    }
    if (props.onClick) {
      className = `${className} clickable`;
    }
    if (props.stretch) {
      className = `${className} flex-grow-1`;
    }
    if (props.fill) {
      className = `${className} flex-fill`;
    }
    if (props.truncate) {
      className = `${className} truncate`;
    }
    if (props.truncateTwoline) {
      className = `${className} truncateTwoline`;
    }
    if (noWrap) {
      className = `${className} nowrap`;
    }
    if (props.disabled) {
      className = `${className} disabled`;
    }
    return className;
  }

  const el = <div className={getClassName()} onClick={disabled ? undefined : onClick} style={props.style} >
    {props.children}
  </div>;

  if (props.tooltip) {
    return <SimpleTooltip text={props.tooltip} component={el} />
  }

  return el;

}

export default Col;
