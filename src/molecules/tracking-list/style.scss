/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "../../css/variables";

.trackingListContainer {
  border: 1px solid var(--theme-NeutralsLight);
  border-radius: $borderRadius;

  &.no-border {
    border: none;
  }

  .clickable:hover {
    background-color: var(--theme-AccentExtralight);

    .trackingListColumn {
      cursor: pointer;
    }
    .link-like {
      text-decoration: underline !important;
      color: var(--theme-AccentMedium) !important;
    }
  }

  .hover-highlight:hover {
    background-color: var(--theme-AccentExtralight);
  }

  .trackingListRow {
    background-color: var(--theme-TextWhite);
    border: 0px;
    line-height: 25px;

    &:not(header) {
      &:first-child {
        padding-top: 0.3rem;
        border-top-left-radius: $borderRadius;
        border-top-right-radius: $borderRadius;
      }
      &:last-child {
        padding-bottom: 0.3rem;
        border-bottom-left-radius: $borderRadius;
        border-bottom-right-radius: $borderRadius;
      }
    }

    &.highlight {
      border-left: $borderRadius solid var(--theme-NeutralsLight);
      &.highlight-primary {
        border-left: $borderRadius solid var(--theme-AccentMedium);
      }
      &.highlight-success {
        border-left: $borderRadius solid var(--theme-SuccessMedium);
      }
      &.highlight-danger {
        border-left: $borderRadius solid var(--theme-DangerMedium);
      }
    }

    .trackingListColumn {
      padding: 0.3rem 0.6rem;
      margin: auto;

      &.nowrap {
        white-space: nowrap;
      }

      &.clickable:hover {
        cursor: pointer;
        i {
          filter: brightness(85%);
        }
      }
      &.truncate {
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &.truncateTwoline {
        // Apply line-clamp to truncate text on the second line for the browsers that support it(most modern browsers) and fall back to 1 line for older.
        @supports (-webkit-line-clamp: 2) {
          white-space: normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 1.5rem !important;
          overflow: hidden;
        }
      }
      &.disabled {
        &:hover {
          cursor: default;
          text-decoration: none;
          color: initial !important;
        }
        i {
          color: var(--theme-NeutralsLight) !important;
        }
      }
    }
  }
}

.question-list__wrapper {
  .trackingListDivider {
    padding: 0px;
    width: 1px;
    margin: 6px 0px;
    border-left: 1px solid var(--theme-NeutralsLight);
  }
}

