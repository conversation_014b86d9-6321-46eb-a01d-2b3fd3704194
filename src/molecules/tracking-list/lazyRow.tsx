/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { ForwardRefRenderFunction, Ref } from 'react';
import Row, { TrackingListRowProps } from './row';
import handleViewport from 'react-in-viewport';
import { LoadingPlaceholder } from '../loader-container';

interface ViewportInterface {
  inViewport: boolean;
  forwardedRef: Ref<HTMLDivElement>;
  enterCount: number;
  leaveCount: number;
  forceShow?: boolean;
}

const RowWrapper: ForwardRefRenderFunction<HTMLDivElement, TrackingListRowProps & ViewportInterface> = (props) => {
  const { enterCount, forwardedRef, inViewport, forceShow, leaveCount, ...origProps } = props;

  const shouldShow = forceShow || enterCount > 0;

  if (!shouldShow) {
    return (
      <div className='dont_translate' ref={forwardedRef}>
        <LoadingPlaceholder height={40} style={{ backgroundColor: 'white' }} />
      </div>
    );
  }

  return <Row {...origProps} ref={forwardedRef} />;
};

export const LazyRow = handleViewport(RowWrapper, /** options: {}, config: {} **/);
