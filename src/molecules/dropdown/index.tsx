import classNames from 'classnames';
import { DropdownItem, DropdownMenu, DropdownToggle, UncontrolledDropdown } from 'reactstrap';

interface BaseDropdownClasses {
  container?: string;
  dropdownToggle?: string;
  dropdownMenu?: string;
}

export interface BaseItem {
  key: string;
  content: React.ReactNode;
}

interface Props<T extends BaseItem> {
  selectedItem?: T;
  items: T[] | { [key: string]: { header: React.ReactNode; items: T[] } };
  toggleContent: React.ReactNode;
  onClickItem?: (item: T) => void;
  disabled?: boolean;
  classes?: BaseDropdownClasses;
}

export const BaseDropdown = <T extends BaseItem>(props: Props<T>) => {
  const { selectedItem, items, toggleContent, onClickItem = () => {}, disabled, classes } = props;
  return (
    <UncontrolledDropdown>
      <DropdownToggle
        className={classNames('text-truncate', classes?.dropdownToggle)}
        style={{ maxWidth: '350px' }}
        color='transparent'
        disabled={disabled}
      >
        {toggleContent}
      </DropdownToggle>
      <DropdownMenu end className={classNames(classes?.dropdownMenu)}>
        {Array.isArray(items)
          ? items.map((item: T, i: number) => (
              <DropdownItem
                key={`${item.key}-${i}`}
                active={selectedItem?.key === item.key}
                className='text-truncate'
                onClick={() => onClickItem(item)}
              >
                {item.content}
              </DropdownItem>
            ))
          : Object.values(items).map(({ header, items }, i) => (
              <>
                <DropdownItem header>{header}</DropdownItem>
                {items.map((item: T, i: number) => (
                  <DropdownItem
                    key={`${item.key}-${i}`}
                    active={selectedItem?.key === item.key}
                    className='text-truncate'
                    onClick={() => onClickItem(item)}
                  >
                    {item.content}
                  </DropdownItem>
                ))}
                <DropdownItem divider />
              </>
            ))}
      </DropdownMenu>
    </UncontrolledDropdown>
  );
};
