/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { JSXElementConstructor } from 'react';
import './styles.scss';

export interface BasicAlertParams {
  message?: string | JSXElementConstructor<any>;
  type: 'success' | 'danger' | 'warning' | 'info' | 'primary' | 'secondary'
  hide?: boolean,
  children: any,
  className?: string
}

export const BasicAlert = ({ children, hide, type = 'success', className = '' }: BasicAlertParams) => {

  if (!children || hide) {
    return null;
  }

  return (
    <div className={`alert alert-${type} ${className}`}>
      {children}
    </div>
  );
}
