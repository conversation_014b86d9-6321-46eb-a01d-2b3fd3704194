/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { JSXElementConstructor } from 'react';
import { Button } from 'reactstrap';
import './styles.scss';

export interface BasicAlertParams {
  message?: string | JSXElementConstructor<any>;
  type: 'success' | 'danger' | 'warning' | 'info' | 'secondary'
  hide?: boolean,
  children: any,
  className?: string,
  handleClose?: () => void,
}

export const BannerAlert = ({ children, hide, type = 'success', className = '', handleClose }: BasicAlertParams) => {

  if (!children || hide) {
    return null;
  }

  return (
    <div className={`alert-${type} ${className} alert--page banner--alert position-absolute background-ThemeAccentExtralight`}>
      <div className='alert--page__info flex-fill text-ThemeAccentDark d-flex justify-content-center align-items-center'>{children}</div>
      {handleClose ? (
        <div>
          <Button color='link' className='p-0' onClick={handleClose}>
            <i className='fa fa-times' />
          </Button>
        </div>
      ) : null}
    </div>
  );
}
