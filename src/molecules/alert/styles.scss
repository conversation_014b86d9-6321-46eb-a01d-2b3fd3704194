@import '../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

.alert--page {
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  z-index: 3;
  border: 1px solid $colorThemeBgDark;
  border-radius: 4px;
  padding: 10px;
  font-size: 0.8rem;
  line-height: 1.2rem;
  .alert--page__info {
    gap: 16px;
    font-weight: 500;
  }

  &.banner--alert {
    z-index: $zindex-fixed; // Above the sticky header, bellow modal -modal-backdrop
  }
}
