/*!
 * Copyright (c) 2023. World Wide Generation Ltd
 */

@import "src/css/variables";

$downloadCardWidth: 230px;

.download-card {

  .download-actions {
    margin: 2rem 0;
    button {
      text-align: left;
      .fas {
        &:not(.fa-lg) {
          font-size: 1.5rem;
        }
      }
    }
    .download-card__heading {
      font-size: 1rem;
      line-height: 1rem;
      color: var(--theme-TextDark);
    }
  }

  .download-details {
    border: 1px solid var(--theme-NeutralsLight);
    border-radius: $borderRadius;
    box-shadow: 0 3px 10px var(--theme-NeutralsLight);
    width: $downloadCardWidth;
    height: 300px;
    .download-details-internal {
      padding: 2rem;
      text-align: center;
      height: 85%;
      white-space: pre-wrap;
    }
    .download-item-name {
      white-space: pre-wrap;
    }
    .counter {
      font-size: 0.8rem;
      color: $gray-600;
      .verified {
        color: var(--theme-AccentExtradark);
      }
    }

    .doc-btn {
      padding: 0.5rem 1rem;
      .span-style {
        margin-right: 0rem;
      }
    }
    .buy-button {
      padding: 0.5rem 1rem;
    }
  }
  aside.download-actions {
    width: $downloadCardWidth;
    text-align: center;
  }
}
