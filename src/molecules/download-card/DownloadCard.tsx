/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React from 'react';
import './DownloadCard.scss'

interface Item {
  code: string;
  name: string;
  src?: string;
  icon?: string;
}

interface CardProps {
  item: Item;
  disabledText?: string;
  children?: React.JSX.Element;
  footer?: React.JSX.Element;
  aside?: boolean;
}

export const DownloadCard = (props: CardProps) => {
  const {
    aside = false,
    disabledText = '',
    item,
    children,
    footer
  } = props;

  return (
    <div className={`download-card ${aside ? 'd-flex flex-wrap align-self-baseline flex-fill' : ''}`}>
      <section className='download-details d-flex flex-column'>
        <div className='download-details-internal'>
          <div className='mb-auto text-center'>
            {item.src ? <img src={item.src} alt={item.name} width='70px' height='70px' /> : null}
            {item.icon ? <i className={`mt-1 fa ${item.icon} fa-5x text-ThemeAccentExtradark`} /> : null}
            <div className='strong mt-4 text-center download-item-name'>{item.name}</div>
          </div>
          <div className='mt-5'>
            {children}
          </div>
        </div>
        {footer}
      </section>

      {disabledText ? <aside className={'download-actions d-flex flex-column mt-3 py-0 ml-2 pl-5 text-label'}>
        {disabledText}
      </aside> : null}
    </div>
  );
};
