/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { Collapse } from 'reactstrap';

interface CollapseContentProps {
  children: React.ReactNode;
  isOpen?: boolean;
  className?: string;
}

export const CollapseContent = (props: CollapseContentProps) => {
  const { children, isOpen } = props;

  return (
    <Collapse isOpen={isOpen} {...props}>
      {children}
    </Collapse>
  );
};
