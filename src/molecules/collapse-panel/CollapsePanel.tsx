/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Fragment, useState } from 'react';
import { CollapseButton } from './CollapseButton';
import './styles.scss';

interface CollapsePanelProps {
  collapsed?: boolean;
  children: JSX.Element | JSX.Element[];
  className?: string;
  onCollapse?: (collapsed: boolean) => void;
}

export const CollapsePanel = (props: CollapsePanelProps) => {
  const children = Array.isArray(props.children) ? props.children : [props.children];
  const [collapsed, setCollapsed] = useState<boolean | undefined>(undefined);

  // if local state has changed, use that, or else go to props and assume default is open
  const isOpen = typeof collapsed === 'boolean' ? !collapsed : props.collapsed !== true;

  const toggle = () => {
    setCollapsed(isOpen);
    props.onCollapse?.(isOpen);
  };

  return (
    <div className={props.className ?? ''}>
      {children.length &&
        children.map((child, i) => (
          <Fragment key={i}>
            {child.type === CollapseButton
              ? React.cloneElement(child, {
                  isOpen: isOpen,
                  onClick: () => toggle(),
                })
              : React.cloneElement(child, {
                  isOpen: isOpen,
                })}
          </Fragment>
        ))}
    </div>
  );
};
