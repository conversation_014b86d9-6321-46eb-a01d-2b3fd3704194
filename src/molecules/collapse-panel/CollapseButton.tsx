/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import classNames from 'classnames';
import React, { MouseEventHandler } from 'react';
import { Button } from 'reactstrap';

type Classes = {
  wrapper?: string;
  icon?: string;
  content?: string;
};

interface CollapseButtonProps {
  children: JSX.Element;
  isOpen?: boolean;
  classes?: Classes;
  onClick?: () => void;
  disabled?: boolean;
}

export const CollapseButton = (props: CollapseButtonProps) => {
  const { children, isOpen = false, classes, onClick = () => {}, disabled = false } = props;

  const handleClick: MouseEventHandler<HTMLElement> = (e) => {
    e.preventDefault();
    if (disabled) {
      return;
    }
    onClick();
  };

  return (
    <div className={classNames('collapse-button d-flex align-items-center', classes?.wrapper, { disabled: disabled })}>
      <Button
        color='link'
        disabled={disabled}
        onClick={handleClick}
        className={classNames(classes?.icon, { open: isOpen })}
      >
        <i className='fa fa-plus text-ThemeAccentDark' />
      </Button>
      <div className={classNames('flex-fill pt-1', classes?.content)} onClick={handleClick}>
        {children}
      </div>
    </div>
  );
};
