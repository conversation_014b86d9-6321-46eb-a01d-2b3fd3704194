/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "../../css/variables";

.collapse-button {
  &:hover:not(.disabled) {
    cursor: pointer;
  }

  button {
    color: var(--theme-AccentExtradark) !important;
    padding: 0rem 0.5rem !important;
    height: 1.7rem;
    line-height: 1;
    vertical-align: baseline;

    &:active,
    &:hover {
      color: var(--theme-AccentMedium) !important;
    }
    &:disabled {
      background-color: initial !important;
      opacity: initial !important;
    }

    &.open {
      > i {
        transform: rotate(180deg);
        &::before {
          content: "\f068";
        }
      }
    }

    > i {
      transition: $transitionTime;
      line-height: 1.4rem;
      height: 1.2rem;
    }
  }
}

.collapse-group {
  .collapse-button {
    border: 1px solid transparent;

    &:hover {
      background-color: var(--theme-BgMedium);
      border-color: var(--theme-BorderDefault);
      border-radius: 4px;
    }
  }

  .editor-container {
    background-color: var(--theme-BgExtralight);
  }
}
