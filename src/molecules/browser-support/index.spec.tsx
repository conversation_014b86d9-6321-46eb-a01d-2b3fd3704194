import { render, screen } from '@testing-library/react'
import { BrowserSupport } from './index';

describe('<BrowserSupport />', () => {
  const minProps = {
    appComponent: <div />,
    children: {},
    className: '',
    showBoth: false,
    showDownloadLinks: true,
    style: {},
    config: {},
    unsupportedComponent: () => {}
  };

  it('render a div without exploding (<div><div /></div>)', () => {
    render(<BrowserSupport {...minProps} />);
    expect(screen.queryAllByRole('generic').length).toEqual(2);
  });
});
