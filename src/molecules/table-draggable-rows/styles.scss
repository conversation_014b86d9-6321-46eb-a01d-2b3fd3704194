/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

@import "src/css/variables";

$rowHeight: 42px;
$highlightBorderHeight: 1px;

.table-draggable-rows {
  max-height: 80vh;
  overflow: scroll;

  .tdr-table {
    display: table;
    width: 100%;

    .tdr-thead {
      display: table-header-group;
    }

    .tdr-tbody {
      position: relative;
      display: table-row-group;
    }

    .tdr-tr {
      display: table-row;
      border-radius: $borderRadius;
    }

    .tdr-td {
      display: table-cell;
      padding: 0.5rem;
    }

    .tdr-spacer {
      background-color: var(--theme-NeutralsLight);
      width: 100%;
      height: calc($rowHeight + $highlightBorderHeight);

      .tdr-td {
        border-top: 1px dashed var(--theme-NeutralsExtradark);
        border-bottom: 1px dashed var(--theme-NeutralsExtradark);

        &:first-child {
          border-left: 1px dashed var(--theme-NeutralsExtradark);
        }

        &:last-child {
          border-right: 1px dashed var(--theme-NeutralsExtradark);
        }
      }
    }
  }

  .tdr-tbody .tdr-tr {
    &.react-draggable-dragging {
      pointer-events: none;
      cursor: ns-resize;
      z-index: 2;
      background-color: var(--theme-AccentMedium);
      color: var(--theme-TextWhite);
      display: table;
      position: absolute;
      width: 100%;

      i {
        color: var(--theme-TextWhite) !important;
      }
    }

    .tdr-td {
      &.col-grip {
        width: $rowHeight;
        text-align: center;

        i {
          cursor: ns-resize;
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.table-draggable-rows.light-theme {
  .tdr-thead .tdr-tr {
    background-color: var(--theme-BgToolbar);
  }

  .tdr-tbody .tdr-tr {
    &:hover {
      background-color: var(--theme-AccentExtralight);

      .tdr-td {
        border-bottom: $highlightBorderHeight solid var(--theme-AccentExtradark);
      }
    }

    .tdr-td {
      border-bottom: $highlightBorderHeight solid white;
    }
  }
}

.table-draggable-rows.default-theme {
  padding: 0;

  .tdr-thead .tdr-tr {
    color: var(--theme-TextWhite);
    background-color: var(--theme-AccentDark);
    font-weight: 700;

    .tdr-td {
      border-right: $highlightBorderHeight solid var(--theme-BorderDefault);
      border-bottom: $highlightBorderHeight solid var(--theme-BorderDefault);
    }
  }

  .tdr-tbody .tdr-tr {
    &:hover {
      background-color: var(--theme-AccentExtralight);

      .tdr-td {
        border-bottom: $highlightBorderHeight solid var(--theme-AccentExtradark);
      }
    }

    &.active {
      background-color: lighten(#ffc107, 40) !important;

      .tdr-td {
        border-top: $highlightBorderHeight solid black;
        border-bottom: $highlightBorderHeight solid black;

        &:first-child {
          border-left: $highlightBorderHeight solid black;
        }

        &:last-child {
          border-right: $highlightBorderHeight solid black;
        }
      }
    }

    .tdr-td {
      border-right: $highlightBorderHeight solid var(--theme-BorderDefault);
      border-bottom: $highlightBorderHeight solid var(--theme-BorderDefault);
    }
  }
}