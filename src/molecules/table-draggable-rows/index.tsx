/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { createRef, Fragment, RefObject, useEffect, useState } from 'react';
import Draggable, { DraggableEvent, DraggableEventHandler } from 'react-draggable';
import './styles.scss';
import classNames from 'classnames';
import { SimpleTooltip } from '../simple-tooltip';

export type InputRow = {
  id: string;
  cols: (string | JSX.Element)[];
  disabled?: boolean;
  active?: boolean;
};

type Row = InputRow & {
  ref: RefObject<HTMLTableRowElement>;
};

export interface TableDraggableColumn {
  header: string | JSX.Element;
  className?: string;
}

interface TableDraggableRowsProps {
  columns: TableDraggableColumn[];
  data: InputRow[];
  handleUpdate: (orderedIds: string[]) => void;
  classes?: {
    wrapper?: string;
  };
  disabled?: boolean;
  disabledMessage?: string | JSX.Element;
}

const Spacer = (props: { cols: number }) => (
  <div className='tdr-tr tdr-spacer'>
    <div className='tdr-td' />
    {[...Array(props.cols)].map((col, i) => (
      <div key={`spacer-${i}`} className='tdr-td' />
    ))}
  </div>
);

export const TableDraggableRows = (props: TableDraggableRowsProps) => {
  const {
    data,
    columns,
    handleUpdate,
    classes = { wrapper: 'light-theme' },
    disabled = false,
    disabledMessage = '',
  } = props;
  const [rows, setRows] = useState<Row[]>([]);
  const [isDragging, setIsDragging] = useState<number | undefined>();

  useEffect(() => {
    setRows(
      data.map((r) => ({
        ...r,
        ref: createRef<HTMLTableRowElement>(),
      }))
    );
  }, [data]);

  const onStop: DraggableEventHandler = (e, position) => {
    setIsDragging(undefined);
    const reSortedRows = [...rows];
    reSortedRows.sort((a, b) => {
      const aTop = a.ref.current?.getBoundingClientRect().top ?? 0;
      const bTop = b.ref.current?.getBoundingClientRect().top ?? 0;
      return aTop > bTop ? 1 : aTop < bTop ? -1 : 0;
    });
    handleUpdate(reSortedRows.map((row) => row.id));
  };

  const onDrag = (e: DraggableEvent, i: number) => {
    if (isDragging === undefined || isDragging !== i) {
      setIsDragging(i);
    }
  };

  return (
    <div className={classNames('table-draggable-rows', classes.wrapper)}>
      <div className='tdr-table'>
        <div className='tdr-thead'>
          <div className='tdr-tr'>
            <div className='tdr-td col-grip'></div>
            {columns.map((column, i) => (
              <div key={`col-${i}`} className={`tdr-td fw-bold ${column.className ?? ''}`}>
                {column.header}
              </div>
            ))}
          </div>
        </div>
        <div className='tdr-tbody'>
          {rows.map((row, i) => (
            <Fragment key={`draggable-${i}`}>
              <Draggable
                handle={'.col-grip'}
                axis='y'
                onStop={onStop}
                onStart={(e) => onDrag(e, i)}
                position={{ x: 0, y: 0 }}
                disabled={disabled}
              >
                <div className={classNames('tdr-tr', { active: row.active })} ref={row.ref}>
                  <div className={classNames('tdr-td col-grip', { 'pe-none': row.disabled })}>
                    <SimpleTooltip text={disabled ? disabledMessage : ''}>
                      <i
                        className={`fa-solid fa-grip-dots-vertical fs-5 text-ThemeIconSecondary ${
                          disabled ? 'disabled' : ''
                        }`}
                      />
                    </SimpleTooltip>
                  </div>
                  {row.cols.map((col, j) => (
                    <div key={`draggable-${i}-${j}`} className={`tdr-td ${columns[j]?.className ?? ''}`}>
                      {col}
                    </div>
                  ))}
                </div>
              </Draggable>
              {isDragging !== undefined && isDragging === i ? <Spacer cols={columns.length} /> : null}
            </Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};
