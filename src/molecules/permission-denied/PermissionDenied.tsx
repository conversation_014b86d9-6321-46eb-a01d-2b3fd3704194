/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import Dashboard, { DashboardSection } from '@components/dashboard';

interface Props {
  title?: string;
  subtitle?: string;
}

export const PermissionDenied = (props: Props) => {

  const {
    title = 'Permission Denied',
    subtitle = 'You do not have permission to access this',
  } = props;

  return (
    <Dashboard>
      <DashboardSection icon='fa-exclamation-triangle' title={title} subtitle={subtitle} />
    </Dashboard>
  )
}
