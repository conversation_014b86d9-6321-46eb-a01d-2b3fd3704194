/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */



import React, { CSSProperties, JSXElementConstructor, ReactNode } from 'react';
import './loadingPlaceholder.scss';

export interface LoadingPlaceholderProps {
  count?: number;
  duration?: number;
  width?: string | number;
  wrapper?: JSXElementConstructor<any>;
  height?: string | number;
  circle?: boolean;
  style?: CSSProperties;
  className?: string;
  isLoading?: boolean;
  children?: ReactNode;
  testId?: string;
}

export function LoadingPlaceholder(props: LoadingPlaceholderProps) {

  const {
    count = 1,
    width,
    wrapper: Wrapper,
    height,
    circle = false,
    style: customStyle,
    className: customClassName,
    isLoading,
    testId = 'loading-placeholder',
  } = props;

  if (isLoading === false) {
    return <>{props.children}</>;
  }

  const elements = [];

  for (let i = 0; i < count; i++) {
    const style = {
      width,
      height,
      borderRadius: width && height && circle ? '50%' : undefined,
    };
    const className = 'loading-placeholder-item ' + (customClassName ?? '');

    elements.push(
      <span key={i} className={className} style={{ ...customStyle, ...style, }}>
        &zwnj;
      </span>
    );
  }

  return (
    <span data-testid={testId} className={'loading-placeholder-container'}>
      {Wrapper
        ? elements.map((element, i) => (
          <Wrapper key={i}>
            {element}
            &zwnj;
          </Wrapper>
        ))
        : elements}
    </span>
  );
}
