/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import React, { useRef } from 'react';
import { LoadingPlaceholder, LoadingPlaceholderProps } from './LoadingPlaceholder';
import { BasicAlert } from '../alert';

interface StateSlice {
  loaded: boolean,
  errored?: boolean,
  errorMessage?: string,
}

interface LoaderContainerProps extends LoadingPlaceholderProps {
  required: StateSlice[];
  optional?: StateSlice[];
}

const isLoading = (s: StateSlice) => !s.loaded;

export const LoaderContainer = ({ required, optional, children, ...placeholderProps }: LoaderContainerProps) => {

  const wrapperElement = useRef<HTMLDivElement | null>(null);


  if (required.some(isLoading) || optional?.some(isLoading)) {
    const height = wrapperElement.current?.clientHeight ?? placeholderProps.height;
    return <LoadingPlaceholder {...placeholderProps} height={height} />
  }

  for (const r of required) {
    if (r.errored) {
      return <BasicAlert type='danger'>{r.errorMessage}</BasicAlert>
    }
  }

  return <div ref={wrapperElement}>{children}</div>
}
