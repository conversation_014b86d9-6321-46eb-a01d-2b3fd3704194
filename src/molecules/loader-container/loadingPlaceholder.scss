/*!
 * Copyright (c) 2020. World Wide Generation Ltd
 */
 @import "src/css/variables";

$defaultBaseColor: var(--theme-BgExtralight);
$defaultHighlightColor: var(--theme-BgMedium);

@keyframes LoadingPlaceholder {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-placeholder-container {
  display: block;
  width: 100%;
  font-size: 0px; // For some reason this is causing small container to display incorrecly
  .loading-placeholder-item {
    background-color: $defaultBaseColor;
    background-image: linear-gradient(90deg,
      $defaultBaseColor,
      $defaultHighlightColor,
      $defaultBaseColor
    );
    background-size: 200px 100%;
    background-repeat: no-repeat;
    border: 1px solid var(--theme-BorderDefault);
    border-radius: 4px;
    display: inline-block;
    line-height: 1;
    width: 100%;
    margin-bottom: 3px;
    animation: LoadingPlaceholder 1.2s ease-in-out infinite;
  }
}

