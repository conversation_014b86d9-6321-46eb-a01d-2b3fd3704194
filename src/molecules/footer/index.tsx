/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Link, useHistory } from 'react-router-dom';
import { footerRoutes } from '../../constants/footerRoutes';
import { generateUrl } from '../../routes/util';

const onClick = () => window.scrollTo(0, 0);
const currentYear = new Date().getFullYear();

interface FooterProps {
  showDisclaimer?: boolean;
  className?: string;
}

export const Footer = (props: FooterProps) => {
  const { showDisclaimer = false } = props;
  const history = useHistory();

  return (
    <footer>
      <div className={`${props.className ?? ''}`}>
        {footerRoutes.map((route) => {
          const { label, id } = route;
          const breadcrumbTitle = 'Go back to previous page';
          const returnParams = new URLSearchParams(history.location.search);
          returnParams.delete('returnUrl');
          returnParams.delete('breadcrumbTitle');

          const searchParams = new URLSearchParams();
          searchParams.append('returnUrl', `${history.location.pathname}${returnParams.toString()}`);
          searchParams.append('breadcrumbTitle', breadcrumbTitle);
          return (
            <Link key={`footer_${id}`} to={`${generateUrl(route)}?${searchParams.toString()}`} onClick={onClick}>
              {label}
            </Link>
          );
        })}
      </div>
      {showDisclaimer ? (
        <div>
          <p>
            <strong>Disclaimer</strong>: The information displayed on this website is for general information purposes
            only. While we endeavour to keep all information on our website up to date and accurate, we make no
            representation or warranty, express or implied, about the completeness, accuracy or reliability of any such
            information or any of the related graphics. Any reliance, therefore, that you place on this information is
            done so strictly at your own risk. Under no circumstances will we be liable to you for any loss or damage of
            whatsoever nature you may suffer arising from, or in connection with your use of this website.
          </p>
        </div>
      ) : null}
      <div>
        <span className='mr-2'>Copyright © {currentYear} WWG</span>|<span className='mx-2'>ISO27001 Certified</span>|
        <span className='ml-2'>GRI Certified Software and Tools Program</span>
        <span className='ml-2'>
          <img
            height='30'
            src='https://wwg-cdn.s3.eu-west-2.amazonaws.com/i/wwg-gri-certified-colour-6Jul20.png'
            alt='GRI Certified Software and Tools Program'
            title='GRI Certified Software and Tools Program'
          />
        </span>
      </div>
    </footer>
  );
};
