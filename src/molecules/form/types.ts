/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


export type updateFn = (update: { code: string, value: any }) => void

export interface BaseOptions {
  onChange: updateFn;
  disabled?: boolean;
  options: {
    instructions?: string;
    code: string;
    name: string;
    disabled?: boolean;
    selected?: boolean;
    tooltip?: string;
  }[];
}

export interface CheckboxProps extends BaseOptions {
  groupCode: string;
  form: { [key: string]: any },
}

export interface ConfigFormData {
  delegation?: boolean;
  settings?: boolean;
  scope?: boolean;
  hasData?: boolean;
  metricStatus?: boolean;
  month: number | string;
  year: number | string;
}
