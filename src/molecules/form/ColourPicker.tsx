/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { ColourPickerPopup } from './ColourPickerPopup';

interface ColourPickerProps {
  defaultColour?: string;
  handleChange: (colourHex: string) => void;
}

export const DEFAULT_GROUP_COLOR = '#9BCDEF';

export const ColourPicker = ({ defaultColour = DEFAULT_GROUP_COLOR, handleChange }: ColourPickerProps) => {
  const [isOpen, setOpen] = React.useState(false);
  const [currentColour, setColour] = React.useState<string>(defaultColour);

  const onChange = (colour: string) => {
    setColour(colour);
    handleChange(colour);
  };

  const toggle = () => setOpen(!isOpen);

  const styleColor = {
    width: '36px',
    height: '14px',
    borderRadius: '2px',
    background: currentColour,
  };
  const styleSwatch = {
    padding: '5px',
    background: isOpen ? '#ccc' : '#fff',
    borderRadius: '1px',
    boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
    display: 'inline-block',
    cursor: 'pointer',
  };
  const stylePopover: any = {
    position: 'absolute',
    zIndex: '2',
  };
  const styleCover: any = {
    position: 'fixed',
    top: '0px',
    right: '0px',
    bottom: '0px',
    left: '0px',
  };

  return (
    <>
      <div style={styleSwatch} onClick={toggle}>
        <div style={styleColor} />
      </div>
      {isOpen ? (
        <div style={stylePopover}>
          <div style={styleCover} onClick={toggle} />
          <ColourPickerPopup currentColour={currentColour} onChange={onChange} />
        </div>
      ) : null}
    </>
  );
};
