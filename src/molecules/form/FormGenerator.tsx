/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { FormGroup, Input, Label, FormFeedback } from 'reactstrap';
import React, { ReactElement } from 'react';
import type { InputType } from 'reactstrap/types/lib/Input';
import { ColourPicker } from './ColourPicker';
import { OnChangeValue } from 'react-select';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export enum ExtendedInputType {
  'colour-picker'
}

export type Validation = { valid?: boolean, message: string };

export interface FieldProps<T extends FieldsForm = FieldsForm> {
  parentCode?: string;
  code: string;
  type: InputType | ExtendedInputType;
  label: string;
  htmlLabel?: ReactElement;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  requiredCallback?: (form: T) => boolean;
  disabledCallback?: (form: T) => boolean;
  options?: Option<string>[];
  multiple?: boolean;
  inputFilter?: (v: any) => any;
  isValid?: (v: T) => Validation;
  min?: number;
  max?: number;
  classes?: {
    label: string;
    inputWrapper?: string;
  },
  tooltip?: string;
  note?: string;
  testId?: string;
}

export interface FieldsForm {
  [key: string]: any
}

interface FormGeneratorParams<T extends FieldsForm> {
  uniqueKey?: string;
  fields: FieldProps<T>[];
  form: T;
  updateForm: (e: React.ChangeEvent<{ name: string, value: string | number | undefined }>) => void
}

interface FormInputParams<T extends FieldsForm> {
  uniqueKey?: string;
  field: FieldProps<T>;
  form: T;
  updateForm: (e: React.ChangeEvent<{ name: string, value: string | number | undefined }>) => void;
  className?: string;
}

const FormInput = <T extends FieldsForm, >(props: FormInputParams<T>) => {
  const { field, form, updateForm, uniqueKey, className } = props;

  const {
    parentCode,
    code,
    label,
    inputFilter,
    placeholder,
    required,
    disabled,
    options,
    type,
    multiple,
    requiredCallback,
    disabledCallback,
    min,
    max,
    isValid,
  } = field;

  const isRequired = requiredCallback ? requiredCallback(form) : required;
  const isDisabled = disabledCallback ? disabledCallback(form) : !!disabled;

  const data = parentCode ? form[parentCode][code] : form[code];
  const value = inputFilter ? inputFilter(data) : data;
  const validation: Validation = isValid ? isValid(form) : { message: '' };

  if (type === ExtendedInputType['colour-picker']) {
    const handleColour = (colour: string) => {
      updateForm({
        target: {
          name: code,
          value: colour
        }
      } as any);
    }
    return <div>
      <ColourPicker defaultColour={value} handleChange={handleColour} />
    </div>
  }

  if (type === 'select') {
    const reactSelectOnChange = (option: OnChangeValue<Option | null, false>) => {
      if (!option) {
        return
      }
      updateForm({
        target: {
          name: code,
          value: option.value
        }
      } as any);
    }

    return (
      <SelectFactory
        selectType={SelectTypes.SingleSelect}
        id={`${uniqueKey ?? 'input'}_${code}`}
        placeholder={placeholder ?? label}
        name={code}
        onChange={reactSelectOnChange}
        value={options?.find((o) => o?.value === value)}
        isSearchable={true}
        options={options}
        isDisabled={isDisabled}
      />
    );
  }

  if (type === 'radio') {
    const reactRadioOnChange = (name: string, value: string | number | undefined) => {
      updateForm({
        target: {
          name, value
        }
      } as any);
    }

    return (
      <div className={className}>
        {options?.map(o => (
          <FormGroup check key={`fgo-${o.value}`}>
            <Input
              type='radio'
              disabled={isDisabled}
              name={code}
              value={o.value ?? ''}
              onChange={() => reactRadioOnChange(code, o.value)}
              defaultChecked={value === o.value}
            />
            <Label check>
              {o.label}
            </Label>
          </FormGroup>
        ))}
      </div>
    );
  }

  const isInvalid = validation.valid !== undefined ? !validation.valid : undefined;
  return (
    <>
      <Input
        id={`${uniqueKey ?? 'input'}_${code}`}
        placeholder={placeholder ?? label}
        name={code}
        required={isRequired}
        autoComplete='off'
        disabled={isDisabled}
        type={type}
        valid={validation.valid}
        invalid={isInvalid}
        min={min}
        max={max}
        onChange={updateForm}
        value={value ?? ''}
        multiple={!!multiple}
      />
      <FormFeedback valid={validation.valid}>{validation.message}</FormFeedback>
    </>
  );
}

const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
};

export const FormGenerator = <T extends FieldsForm, >(props: FormGeneratorParams<T>) => {
  const { fields, form } = props;

  return (
    <form autoComplete='new-password' onSubmit={handleSubmit}>
      {form['errorMessage'] ? <div className='alert alert-danger'>{form['errorMessage']}</div> : <></>}
      {fields.map(field => {
        const { code, label, htmlLabel, required, requiredCallback, classes, tooltip, note, testId } = field;
        const isRequired = requiredCallback ? requiredCallback(form) : required;
        return (
          <FormGroup key={code} data-testid={testId}>
            <Label for={code} className={classes?.label}>
              {htmlLabel ?? label}
              {isRequired ? ' *' : ''}
              {tooltip ? (
                <SimpleTooltip className='ml-2' text={tooltip}>
                  <i className='fa-light fa fa-circle-info' color='secondary' />
                </SimpleTooltip>
              ) : null}
            </Label>
            <FormInput field={field} {...props} className={classes?.inputWrapper}/>
            {note ? <p className={'text-xs text-ThemeDangerExtradark'}>{note}</p> : null}
          </FormGroup>
        );
      })}
    </form>
  );
}
