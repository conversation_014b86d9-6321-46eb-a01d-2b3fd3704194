/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

@import "../../css/variables";

$spacing: 3px;
$colourWidth: 100px;
$colourHeight: 30px;

.colour-picker-popup {
  position: absolute;
  z-index: 2;
  width: 224px;
  background-color: var(--theme-TextWhite);
  border: 1px solid var(--theme-BgDisabled);
  border-radius: 4px;
  padding: 5px;
  line-height: 0px;
  .colour-option {
    cursor: pointer;
    margin: $spacing;
    display: inline-block;
    width: $colourWidth;
    height: $colourHeight;
    border-radius: 4px;
    border: 2px solid white;
    &:hover {
      border: 2px solid var(--theme-AccentMedium);
    }
    &.selected {
      border: 2px solid var(--theme-TextPlaceholder);
    }
  }
}
