/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { FormGroup, Input, Label } from 'reactstrap';
import { BaseOptions } from './types';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export interface RadioOptions extends BaseOptions {
  formCheckClass?: string;
  groupCode: string;
  value: any;
  className?: string;
  formGroupClassName?: string;
}

export const RadioInput = ({
  groupCode,
  value,
  disabled,
  options = [],
  onChange = () => {},
  className = '',
  formGroupClassName = '',
}: RadioOptions) => (
  <FormGroup tag='fieldset' className={className}>
    {options.map((o) => {
      const id = `${groupCode}-${o.code}`;
      const isDisabled = disabled || o.disabled;
      const isSelected = o.selected || o.code === value;
      const className = `form-check-label ${isDisabled && !isSelected ? 'text-ThemeTextPlaceholder' : ''}`;
      return (
        <SimpleTooltip text={o.tooltip} key={id}>
          <FormGroup check key={id} className={formGroupClassName}>
            <Input
              type='radio'
              disabled={isDisabled}
              name={groupCode}
              checked={isSelected}
              onChange={(e) => onChange({ code: groupCode, value: e.target.value })}
              id={id}
              value={o.code}
            />

            <Label check className={className} htmlFor={id}>
              {o.name}
            </Label>
            {o.instructions ? (
              <Label check className={className} htmlFor={id}>
                {o.instructions}
              </Label>
            ) : null}
          </FormGroup>
        </SimpleTooltip>
      );
    })}
  </FormGroup>
);
