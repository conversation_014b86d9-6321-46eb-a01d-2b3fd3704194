/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import './ColourPickerPopup.scss';

interface ColourPickerPopupProps {
  currentColour: string;
  onChange: (colourHex: string) => void;
}

export const safeColours = [
  '#9BCDEF', '#F1D572',
  '#00D4C7', '#F1AF72',
  '#00D405', '#F17272',
  '#B1F172', '#F1729D',
  '#E7F172', '#D072F1'
];

export const ColourPickerPopup = ({ currentColour, onChange }: ColourPickerPopupProps) => {
  return (
    <div className='colour-picker-popup'>
      {safeColours.map(colour => (
        <div
          key={colour}
          className={`colour-option ${currentColour === colour ? 'selected' : ''}`}
          style={{ backgroundColor: colour }}
          onClick={() => onChange(colour)}
        />
      ))}
    </div>
  )
};
