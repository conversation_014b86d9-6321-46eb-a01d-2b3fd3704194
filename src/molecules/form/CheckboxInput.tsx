/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { FormGroup, Input, Label } from 'reactstrap';
import { CheckboxProps } from './types';

export const CheckboxInput = ({ groupCode, form = {}, disabled, options = [], onChange = () => {} }: CheckboxProps) => (
  <div className={'form-radio-input'}>
    {options.map((o) => {
      const id = `${groupCode}-${o.code}`;
      const isDisabled = disabled || o.disabled;
      const className = `form-check-label ${isDisabled ? 'text-ThemeTextPlaceholder' : ''}`;
      return (
        <div className={'mb-2'} key={id}>
          <FormGroup check>
            <Input
              type={'checkbox'}
              disabled={isDisabled}
              name={groupCode}
              checked={Boolean(form[o.code])}
              onChange={(e) =>
                onChange({
                  code: o.code,
                  value: e.target.checked,
                })
              }
              id={id}
              value={o.code}
            />
            <Label check className={`${className}`} htmlFor={id}>
              {o.name}
            </Label>
            <Label check className={`d-block text-ThemeTextLight ${className}`} htmlFor={id}>
              {o.instructions}
            </Label>
          </FormGroup>
        </div>
      );
    })}
  </div>
);
