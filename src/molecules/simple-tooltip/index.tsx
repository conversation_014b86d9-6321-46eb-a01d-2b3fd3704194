/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Button, TooltipProps, UncontrolledTooltip } from 'reactstrap';

type Text = string | JSX.Element;

const renderComponent = (Component: JSX.Element | undefined, id: string, text?: Text) => {
  if (Component) {
    return <Component.type {...Component.props} id={id} />
  }

  return <Button className='mr-1' color='secondary' id={id}>
    {text}
  </Button>
};

type SimpleTooltipProps = Omit<TooltipProps, 'target'> & {
  component?: JSX.Element | undefined;
  children?: React.ReactNode;
  text?: Text;
}

export const SimpleTooltip = (props: SimpleTooltipProps) => {
  const { text, component, className, children, delay, trigger = 'hover', ...rest } = props;

  const [stateId] = useState(Math.random().toString(36).substr(2, 9));
  const defaultDelay = delay || { show: 200, hide: 0 };

  const tooltipId = 'Tooltip-' + stateId;

  return (
    <>
      {component ? renderComponent(component, tooltipId, text) : null}
      {children ? (
        text || className ? (
          <span id={tooltipId} className={className}>
            {children}
          </span>
        ) : (
          <>{children}</>
        )
      ) : null}
      {text ? (
        <UncontrolledTooltip
          {...rest}
          className={'weglot-translate'}
          trigger={trigger}
          target={tooltipId}
          delay={defaultDelay}
        >
          {text}
        </UncontrolledTooltip>
      ) : null}
    </>
  );
}
