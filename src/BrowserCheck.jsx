/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { BrowserSupport } from '@g17eco/molecules/browser-support';

export const warnBrowserVersions = {
  chrome: '46',
  edge: '12',
  firefox: '19.5',
  ie: '12',
  opera: '37',
  safari: '10.2',
}

const minBrowserVersions = {
  ie: '11',
}

export default function BrowserCheck(Component) {
  class BrowserCheckComponent extends React.Component {
    state = {
      browser: {}
    }

    handleScanBrowser = data => this.setState({ browser: data })

    render() {
      return <BrowserSupport
        config={minBrowserVersions}
        showDownloadLinks={true} // this will show suggested download links for user's browser
        appComponent={<Component />}
        showBoth={false} // this will show both unsupported and your app component
      />
    }

    renderUnsupportedMessage() {
      return <div>Unsupported</div>;
    }
  }

  return BrowserCheckComponent
}
