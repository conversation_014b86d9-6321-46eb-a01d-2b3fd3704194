/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */
import reducer, { portfolioSlice } from './portfolioSlice'
import { describe } from 'vitest';
import { createPortfolio } from '@fixtures/portfolio-factory';
import { Portfolio } from '@g17eco/types/portfolio';

describe('portfolioSlice reducer', () => {

  const getDefaultState = (pt?: Portfolio) => {
    return {
      data: pt,
      id: pt?._id,
      errorMessage: '',
      errored: false,
      loaded: false,
      loading: false,
    };
  }
  test('should return the initial state', () => {
    const defaultState = getDefaultState();
    expect(reducer(undefined, { type: 'unknown' })).toEqual(defaultState)
  });

  test('should update portfolio', () => {
    const pt = createPortfolio();
    const [first] = pt.scorecard.children;
    const defaultState = getDefaultState(pt);
    const weight = 3;
    const action = portfolioSlice.actions.updateWeighting({
      portfolioId: pt._id,
      holdingId: first.initiativeId,
      weight: weight,
    });
    const state = reducer(defaultState, action);
    expect(state.data?.scorecard.children[0].weight).toEqual(weight);
  });

  test('should load portfolio', () => {
    const pt = createPortfolio();
    const defaultState = getDefaultState();
    const weight = 2;
    const action = portfolioSlice.actions.loaded(pt);
    const state = reducer(defaultState, action);
    expect(state.data?.scorecard.children[0].weight).toEqual(weight);
  });

  test('should handle error', () => {
    const defaultState = getDefaultState();
    const msg = 'something went wrong';
    const action = portfolioSlice.actions.handleError(msg);
    const state = reducer(defaultState, action);
    expect(state.errorMessage).toEqual(msg);
    expect(state.errored).toEqual(true);
  });
})
