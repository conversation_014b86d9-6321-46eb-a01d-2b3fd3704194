import { getUtrNumberValue } from './universalTracker';

describe('getUtrNumberValue function', () => {
  const testCase = [
    {
      input: { value: undefined, decimal: undefined, hasValueChanged: true },
      expected: '',
    },
    {
      input: { value: undefined, decimal: 2, hasValueChanged: false, fallback: 0 },
      expected: 0,
    },
    {
      input: { value: '', decimal: 2, hasValueChanged: true },
      expected: '',
    },
    {
      input: { value: 'string', decimal: 2, hasValueChanged: true, fallback: '4' },
      expected: '4',
    },
    {
      input: { value: '03', decimal: undefined, hasValueChanged: true, fallback: '4' },
      expected: '03',
    },
    {
      input: { value: '03', decimal: 2, hasValueChanged: true, fallback: '4' },
      expected: '03',
    },
    {
      input: { value: '03', decimal: 2, hasValueChanged: false },
      expected: '03',
    },
    {
      input: { value: 24, decimal: 3, hasValueChanged: false, fallback: '4.932' },
      expected: '24.000',
    },
    {
      input: { value: '12.345', decimal: 1 },
      expected: '12.345',
    },
    {
      input: { value: '12.345', decimal: 1, hasValueChanged: true },
      expected: '12.345',
    },
    {
      input: { value: 24.35, decimal: 4, hasValueChanged: true },
      expected: 24.35,
    },
    {
      input: { value: '12.345', decimal: 4 },
      expected: '12.345',
    },
    {
      input: { value: 24.2468, decimal: 3, hasValueChanged: false },
      expected: '24.247',
    },
    {
      input: { value: 24.35, decimal: 4, hasValueChanged: false },
      expected: '24.3500',
    }
  ];

  testCase.forEach(({ input: { value, decimal, hasValueChanged, fallback }, expected }) => {
    it(`value: ${value}, decimal: ${decimal}, hasValueChanged: ${hasValueChanged}, expected: '${expected}'`, () => {
      const decimalNumber = getUtrNumberValue({ value, decimal, hasValueChanged, fallback });
      expect(decimalNumber).toEqual(expected);
    });
  });
});
