import { sortByDateAndId } from './sort';

describe('utils/sort', () => {
  describe('sortByDateAndId', () => {
    it('should sort desc by date', () => {
      const items = [
        { date: '2023-06-20T10:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
        { date: '2023-06-20T11:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
      ];

      expect(items.sort(sortByDateAndId)).toEqual([
        { date: '2023-06-20T11:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
        { date: '2023-06-20T10:13:28.132Z', _id: '64917bc80504d3351f9802cc' },
      ]);
    });
    it('should fallback to sort desc by _id when date are equal', () => {
      const date = '2023-06-20T10:13:28.132Z';
      const items = [
        { date, _id: '64917bc80504d3351f9802cb' },
        { date, _id: '64917bc80504d3351f9802cc' },
      ];

      expect(items.sort(sortByDateAndId)).toEqual([
        { date, _id: '64917bc80504d3351f9802cc' },
        { date, _id: '64917bc80504d3351f9802cb' },
      ]);
    });
  });
});
