import { extractPageNumbersFromReferences, isValidPdfUrl } from './pdf';

describe('pdf utils', () => {
  describe('extractPageNumbersFromReferences', () => {
    it('should return the input if it is already a number string', () => {
      const references = '12';
      const result = extractPageNumbersFromReferences(references);
      expect(result).toEqual(12);
    });

    it('should return undefined when no page numbers are found', () => {
      const references = 'Test page reference';
      const result = extractPageNumbersFromReferences(references);
      expect(result).toEqual(undefined);
    });

    it('should return the first page number', () => {
      const references = 'Page 1, Figure 2.1, Page 2';
      const result = extractPageNumbersFromReferences(references);
      expect(result).toEqual(1);
    });

    it('should ignore non-numeric page numbers', () => {
      const references = 'Page foo, Page 3';
      const result = extractPageNumbersFromReferences(references);
      expect(result).toEqual(3);
    });

    it('should handle multiple occurrences of the same page number', () => {
      const references = 'Page 1, Page 2, Page 1';
      const result = extractPageNumbersFromReferences(references);
      expect(result).toEqual(1);
    });
  });

  describe('isValidPdfUrl', () => {
    const testCases = [
      {
        name: 'an undefined value',
        input: undefined,
        output: false,
      },
      {
        name: 'an empty string',
        input: '',
        output: false,
      },
      {
        name: 'a non-PDF URL',
        input: 'https://example.com/document.txt',
        output: false,
      },
      {
        name: 'a URL with no extension',
        input: 'https://example.com/document',
        output: false,
      },
      {
        name: 'a URL with an uppercase extension',
        input: 'https://example.com/document.PDF',
        output: false,
      },
      {
        name: 'a URL with no protocol',
        input: 'example.com/document.pdf',
        output: true,
      },
      {
        name: 'an invalid PDF URL with wrong query symbol',
        input: 'https://example.com/document.pdf?page=1',
        output: false,
      },
      {
        name: 'a valid PDF URL with correct query params',
        input: 'https://example.com/document.pdf#page=1&zoom=50',
        output: true,
      },
    ];

    testCases.forEach(({ input, output, name }) => {
      it(`should return ${output} for ${name}`, () => {
        expect(isValidPdfUrl(input)).toEqual(output);
      });
    });
  });
});
