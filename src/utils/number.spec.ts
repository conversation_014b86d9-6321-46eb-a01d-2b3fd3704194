import { isNumeric, getDecimalNumber, getDecimalAsNumber, roundTo } from './number';

describe('isNumeric function', () => {
  it('should be numeric value', () => {
    const validValues = [
      0.1,
      '1',
      '-1',
      1,
      -1,
      0,
      -0,
      '0',
      '-0',
      2e2,
      1e23,
      1.1,
      -0.1,
      '0.1',
      '2e2',
      '1e23',
      '-0.1',
      ' 898',
      '080',
    ];
    validValues.forEach((value) => expect(isNumeric(value)).toBe(true));
  });
  it('should not be numeric value', () => {
    const invalidValues = [
      '9BX46B6A',
      "+''",
      '',
      '-0,1',
      [],
      '123a',
      'a',
      'NaN',

      1e10000,
      undefined,
      null,
      NaN,
      Infinity,
      () => {},
    ];
    invalidValues.forEach((value) => expect(isNumeric(value)).toBe(false));
  });
});

describe('getDecimalNumber function', () => {
  const testCase = [
    {
      input: { value: undefined, decimal: undefined },
      expected: '',
    },
    {
      input: { value: null, decimal: 2 },
      expected: '',
    },
    {
      input: { value: '', decimal: 3 },
      expected: '',
    },
    {
      input: { value: 'string', decimal: 1 },
      expected: '',
    },
    {
      input: { value: '03', decimal: undefined },
      expected: '3',
    },
    {
      input: { value: '03', decimal: 2 },
      expected: '3.00',
    },
    {
      input: { value: 24, decimal: 3 },
      expected: '24.000',
    },
    {
      input: { value: '12.345', decimal: 1 },
      expected: '12.3',
    },
    {
      input: { value: '12.345', decimal: 4 },
      expected: '12.3450',
    },
  ];

  testCase.forEach(({ input: { value, decimal }, expected }) => {
    it(`value: ${value}, decimal: ${decimal}`, () => {
      const decimalNumber = getDecimalNumber(value, decimal);
      expect(typeof decimalNumber).toEqual('string');
      expect(decimalNumber).toEqual(expected);
    });
  });
});

describe('getDecimalAsNumber function', () => {
  const testCase = [
    {
      input: { value: undefined, decimal: undefined },
      expected: '',
      expectedType: 'string',
    },
    {
      input: { value: null, decimal: 2 },
      expected: '',
      expectedType: 'string',
    },
    {
      input: { value: '', decimal: 3 },
      expected: '',
      expectedType: 'string',
    },
    {
      input: { value: '12.345', decimal: 4, asNumber: true },
      expected: 12.345,
      expectedType: 'number',
    },
    {
      input: { value: '03', decimal: 2, asNumber: true },
      expected: 3.0,
      expectedType: 'number',
    },
    {
      input: { value: 24, decimal: 3, asNumber: true },
      expected: 24.0,
      expectedType: 'number',
    },
  ];

  testCase.forEach(({ input: { value, decimal }, expected, expectedType }) => {
    it(`value: ${value}, decimal: ${decimal}`, () => {
      const decimalNumber = getDecimalAsNumber(value, decimal);
      expect(typeof decimalNumber).toEqual(expectedType);
      expect(decimalNumber).toEqual(expected);
    });
  });
});

describe('roundTo function', () => {
  it('should round to default decimal places', () => {
    expect(roundTo(12.345)).toBe(12.35);
  });

  it('should round to specified decimal places', () => {
    expect(roundTo(12.345, 1)).toBe(12.3);
  });

  it('should not round with non-integer decimal places', () => {
    expect(roundTo(12.345, 1.5)).toBe(12.345);
  });

  it('should not round with negative decimal places', () => {
    expect(roundTo(12.345, -1)).toBe(12.345);
  });

  it('should return non-finite values unchanged', () => {
    expect(roundTo(NaN)).toBe(NaN);
    expect(roundTo(Infinity)).toBe(Infinity);
  });
});
