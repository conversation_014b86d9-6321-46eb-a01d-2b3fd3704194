import { StockExchange } from '@g17eco/types/stock-exchange-settings';

export const categorizeStockExchangesByLocation = (
  stockExchanges: StockExchange[],
  location: string[],
): { popularStockExchanges: StockExchange[]; otherStockExchanges: StockExchange[] } => {
  return stockExchanges.reduce(
    (result, stockExchange) => {
      const isPopular = (stockExchange.popularCountryCodes ?? []).some((item) => location.includes(item));
      if (isPopular) {
        result.popularStockExchanges.push(stockExchange);
        return result;
      }
      result.otherStockExchanges.push(stockExchange);
      return result;
    },
    { popularStockExchanges: [] as StockExchange[], otherStockExchanges: [] as StockExchange[] },
  );
};
