import { isArraysEqual, replaceItem } from './array';

describe('utils/array', () => {
  describe('replaceItem', () => {
    it('should replace an item in an array', () => {
      const items = [1, 2, 3];
      const newItem = 4;
      const index = 1;
      const result = replaceItem(items, newItem, index);
      expect(result).toEqual([1, 4, 3]);
    });
  });
  describe('isArraysEqual', () => {
    it('should return true if arrays are equal', () => {
      const testCases: [(string | number)[], (string | number)[], boolean][] = [
        [[], [], true],
        [[1, 2], [1, 2], true],
        [['a', 'b'], ['a', 'b'], true],
        [[1, 2], [1], false],
        [['a', 'b'], ['a'], false],
        [[0, 0], [0, 1], false],
      ];

      testCases.forEach(([arr1, arr2, expected]) => {
        expect(isArraysEqual(arr1, arr2)).toBe(expected);
      });
    });
  });
});
