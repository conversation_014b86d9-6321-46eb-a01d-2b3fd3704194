/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { SerializedEditorState } from 'lexical/LexicalEditorState';
import { SerializedLexicalNode } from 'lexical';

interface ChangeProps {
  current: SerializedEditorState | undefined;
  previous: SerializedEditorState | undefined;
}

type ChildTypes = SerializedLexicalNode & { text?: string }

interface RecursiveCheckParams {
  currentChild: ChildTypes[];
  prevChild:  ChildTypes[];
  depth: number
}

const maxDepth = 3;

/**
 * Might need to add additional properties, but does work quite well with not too many nested items
 */
const recursiveCheck = ({currentChild, prevChild, depth }: RecursiveCheckParams) => {

  if (depth > maxDepth) {
    return false;
  }

  for (const [index, child] of currentChild.entries()) {
    const prev = prevChild[index];
    if (!prev) {
      return true;
    }

    if (child.type !== prev.type) {
      return true
    }

    if (child.version !== prev.version) {
      return true
    }

    if (child.text !== prev.text) {
      return true
    }

    if ('children' in child && Array.isArray(child.children) && 'children' in prev && Array.isArray(prev.children)) {
      if (child.children?.length !== prev.children?.length) {
        return true;
      }

      if (recursiveCheck({ currentChild: child.children, prevChild: prev.children, depth: depth + 1 })) {
        return true;
      }
    }
  }

  return false;
}

export const hasEditorStateChanged = ({ previous, current }: ChangeProps): boolean => {

  if (!current || !previous) {
    // If one of them are defined, then it changed
    return Boolean(current || previous);
  }

  if (current.root.type !== previous.root.type) {
    return true;
  }

  const currentChild = current.root.children;
  const prevChild = previous.root.children;
  if (currentChild.length !== prevChild.length) {
    return true;
  }

  return recursiveCheck({ currentChild, prevChild, depth: 0 });
}
