/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { AppCode } from '@g17eco/types/app';
import { ObType, OnboardingToken } from '../types/onboarding';
import { Country } from '@g17eco/types/common';

/**
 * Check if onboarding have a valid setup for assurance portfolios.
 *
 * stakeholder => [assurancePortfolioIdOne, assurancePortfolioIdTwo]
 *   stakeholder role will be converted to "user" role for each assurance portfolio
 * verifier => [assurancePortfolioIdThree]
 *   verifier role will be converted to "admin" role for each assurance portfolio
 */
export function hasAssuranceConfig(onboardingToken: Partial<OnboardingToken> | OnboardingToken) {
  if (!onboardingToken.assuranceStakeholders) {
    return false;
  }

  // stakeholder = 'user' role, verifier = 'admin' role
  const { stakeholder = [], verifier = [] } = onboardingToken.assuranceStakeholders;

  return stakeholder.length > 0 || verifier.length > 0;
}

export function isMaterialityTrackerOnboarding(onboardingToken: Partial<OnboardingToken> | OnboardingToken) {
  return onboardingToken.appConfig?.code === AppCode.MaterialityTracker;
}

export function isAssuranceTrackerOnboarding(onboardingToken: Partial<OnboardingToken> | OnboardingToken) {
  return onboardingToken.type === ObType.Organization;
}

export const getOnboardingCountries = (countries: Country[] = []) => {
  const countryList = countries.map((c) => ({ value: c.code, label: c.name }));
  const topCountries = [
    { label: 'United Kingdom of Great Britain and Northern Ireland', value: 'GB' },
    { value: 'US', label: 'United States of America' },
  ];
  const filteredCountryCodes = topCountries.map((c) => c.value);
  const filteredTopCountries = countryList.filter((c) => !filteredCountryCodes.includes(c.value));
  return [...topCountries, ...filteredTopCountries];
};
