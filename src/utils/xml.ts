/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


// A list of invalid XML characters can be found at:
// * https://www.w3.org/TR/xml/#sec-common-syn
// * http://www-01.ibm.com/support/docview.wss?uid=swg21514211&aid=1

import stripAnsi from 'strip-ansi';

// \uD800-\uDFFF cause emoji like 🌴 to be removed
const BAD_JUJU = /[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007f-\u0084\u0086-\u009f\uD800-\uDFFF\uFDD0-\uFDFF\uFFFF\uC008]/g;
export function xmlSanitizer(input: string | number, replacement = '') {
  if (typeof input === 'number') {
    return String(input);
  }

  if (!input) {
    return input;
  }
  return stripAnsi(input.replace(BAD_JUJU, replacement));
}




