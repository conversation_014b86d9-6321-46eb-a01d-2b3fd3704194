/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

export const isTooShort = (password: string, minLength: number) => password.length < minLength;

export const isPasswordValid = (password: string): boolean => {
  if (isTooShort(password, 10)) {
    return false;
  }

  const hasUppercase = password.match(/[A-Z]/);
  const hasLowercase = password.match(/[a-z]/);
  const hasNumber = password.match(/[0-9]/);
  const hasSymbol = password.match(/.[0-9A-Za-z]/);
  return Boolean(hasUppercase && hasLowercase && hasNumber && hasSymbol);
}
