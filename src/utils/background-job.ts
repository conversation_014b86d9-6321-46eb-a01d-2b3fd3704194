import { BackgroundJob, JobStatus, Task, TaskStatus } from '@g17eco/types/background-jobs';

export const finalStatuses = [JobStatus.Completed, JobStatus.Error, JobStatus.Deleted];
export const isFinishedJob = (job: Pick<BackgroundJob, 'status'>) => finalStatuses.includes(job.status);

export const isProcessingJob = (job: Pick<BackgroundJob, 'status'>) => !finalStatuses.includes(job.status);

export const canRetryJob = (job: Pick<BackgroundJob, 'status'> & { tasks: Pick<Task, 'status'>[] }) => {
  const isPending = job.status === JobStatus.Pending;
  const hasFailedTask = job.tasks.some((t) => t.status === TaskStatus.Error);
  return isPending && hasFailedTask;
};
