import { sortCustomGroupQuestions } from './metricGroup';
import { ScopeQuestionOptionalValue } from '@g17eco/types/surveyScope';
import UniversalTracker from '@models/UniversalTracker';
import { ScopeGroupData } from '@g17eco/types/survey';
import { CustomMetricOrderType } from '@g17eco/types/metricGroup';
import { defaultUniversalTrackerFields } from '@fixtures/questions-fixture';

const utr1 = new UniversalTracker({
  ...defaultUniversalTrackerFields,
  _id: 'utr1',
  name: 'Question 1',
  typeCode: 'A',
});

const utr2 = new UniversalTracker({
  ...defaultUniversalTrackerFields,
  _id: 'utr2',
  name: 'Question 2',
  typeCode: 'B',
});

const utr3 = new UniversalTracker({
  ...defaultUniversalTrackerFields,
  _id: 'utr3',
  name: 'Question 3',
  typeCode: 'C',
});

describe('sortCustomGroupQuestions', () => {
  const mockQuestions: Pick<ScopeQuestionOptionalValue, 'name' | 'universalTracker'>[] = [
    {
      name: 'Question 2',
      universalTracker: utr2,
    },
    {
      name: 'Question 3',
      universalTracker: utr3,
    },
    {
      name: 'Question 1',
      universalTracker: utr1,
    },
  ];

  const sortedByCustomGroup: ScopeGroupData = {
    title: 'Group 1',
    preferredAltCodes: ['A', 'B', 'C'],
    metricsOrder: { orderType: CustomMetricOrderType.Custom },
    universalTrackerIds: ['utr3', 'utr2', 'utr1'],
  };

  const sortedByNameGroup: ScopeGroupData = {
    title: 'Group 1',
    preferredAltCodes: ['A', 'B', 'C'],
    metricsOrder: { orderType: CustomMetricOrderType.Name },
  };

  it('should sort questions by name when metricsOrder.orderType is Name', () => {
    const sortedQuestions = sortCustomGroupQuestions(mockQuestions, sortedByNameGroup);
    expect(sortedQuestions).toEqual([
      {
        name: 'Question 1',
        universalTracker: utr1,
      },
      {
        name: 'Question 2',
        universalTracker: utr2,
      },
      {
        name: 'Question 3',
        universalTracker: utr3,
      },
    ]);
  });

  it('should sort questions by typeCode when metricsOrder.orderType is Custom', () => {
    const sortedQuestions = sortCustomGroupQuestions(mockQuestions, sortedByCustomGroup);
    expect(sortedQuestions).toEqual([
      {
        name: 'Question 3',
        universalTracker: utr3,
      },
      {
        name: 'Question 2',
        universalTracker: utr2,
      },
      {
        name: 'Question 1',
        universalTracker: utr1,
      },
    ]);
  });

  it('should return the original questions when universalTrackerIds is not provided and orderType is Custom', () => {
    const sortedQuestions = sortCustomGroupQuestions(mockQuestions, {
      ...sortedByCustomGroup,
      universalTrackerIds: [],
    });
    expect(sortedQuestions).toEqual(mockQuestions);
  });

  it('should sort questions with invalid universalTrackerIds and orderType is Custom', () => {
    const sortedQuestions = sortCustomGroupQuestions(
      mockQuestions,
      {
        ...sortedByCustomGroup,
        universalTrackerIds: ['utr1', 'utr4', 'utr3'],
      },
    );
    expect(sortedQuestions).toEqual([
      { name: 'Question 1', universalTracker: utr1 },
      { name: 'Question 3', universalTracker: utr3 },
      { name: 'Question 2', universalTracker: utr2 },
    ]);
  });
});
