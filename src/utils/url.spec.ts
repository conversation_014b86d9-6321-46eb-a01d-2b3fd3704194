import { isValidUrl } from './url';

describe('url utils', () => {
  describe('isValidUrl', () => {
    const testCases = [
      {
        name: 'an empty string',
        input: '',
        output: false,
      },
      {
        name: 'invalid URL (missing protocol)',
        input: 'example.com',
        output: false,
      },
      {
        name: 'valid URL (with www)',
        input: 'http://www.example.com',
        output: true,
      },
      {
        name: 'valid URL (without www)',
        input: 'http://example.com',
        output: true,
      },
      {
        name: 'valid URL (with https protocol)',
        input: 'https://example.com',
        output: true,
      },
      {
        name: 'valid URL (with special characters)',
        input: 'http://example.com/path?query#fragment',
        output: true,
      },
      {
        name: 'very long URL',
        input: 'http://example.com/very/long/path/with/many/segments',
        output: true,
      },
    ];
    testCases.forEach(({ input, output, name }) => {
      it(`should return ${output} for ${name}`, () => {
        expect(isValidUrl(input)).toEqual(output);
      });
    });
  });
});
