/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { AppCode, AppConfig } from '@g17eco/types/app';
import { RootInitiativeData } from '@g17eco/types/initiative';


// ////////////////////////////////////////////////////
//
// File for resolving AppCode related things in a single place
// Should be easy to introduce new AppCode and do the changes in maximum 2 files
//
// ////////////////////////////////////////////////////


/**
 * @TODO: Can we drop and rely on productCode or validProductCodes?
 * These are used to determine if we can redirect to CT with appConfig
 * should probably be called tiers or similar.
 */
export const companyTrackerAppCodes = [
  AppCode.CompanyTracker,
  AppCode.CompanyTrackerPro,
  AppCode.CompanyTrackerEnterprise,
  AppCode.CompanyTrackerStarter,
  AppCode.SGXESGenome,
  AppCode.TOLI,
  AppCode.WWG,
];

export const getAppTypeLabel = (appCode?: AppCode | ''): string | undefined=> {
  switch (appCode) {
    case AppCode.CompanyTrackerStarter:
      return 'COMPANY TRACKER STARTER'
    case AppCode.CompanyTrackerPro:
      return 'COMPANY TRACKER PRO';
    case AppCode.CompanyTrackerEnterprise:
      return 'COMPANY TRACKER ENTERPRISE';
    case AppCode.SGXESGenome:
      return 'SGX ESGENOME';
    case AppCode.TOLI:
      return 'TOLI';
    case AppCode.WWG:
      return 'WWG';
    case AppCode.CompanyTracker:
      return 'COMPANY TRACKER';
    default:
      return;
  }
}

const getBadgeText = (code?: AppCode) => {
  switch (code) {
    case AppCode.CompanyTrackerPro:
      return 'Pro';
    case AppCode.CompanyTrackerEnterprise:
      return 'Enterprise';
    case AppCode.CompanyTrackerStarter:
      return 'Starter';
    case AppCode.TOLI:
      return 'TOLI';
    case AppCode.WWG:
      return 'WWG';
    default:
      return undefined;
  }
}

interface AppBadgeProps {
  appConfig?: AppConfig;
  isStaffOrganization?: boolean;
  isDemo?: boolean;
}

export const getAppBadgeCodes = ({ appConfig, isStaffOrganization, isDemo }: AppBadgeProps): string[] => {
  const codes: string[] = [];
  const tierBadgeLabel = getBadgeText(appConfig?.code);
  if (tierBadgeLabel) {
    codes.push(tierBadgeLabel);
  }

  if (isStaffOrganization || isDemo) {
    codes.push('Demo');
  }
  return codes;
}

export enum OnboardingPath {
  CompanyTrackerStarter = 'company-tracker-starter',
  CompanyTracker = 'company-tracker',
  CompanyTrackerPro = 'company-tracker-pro',
  CompanyTrackerEnterprise = 'company-tracker-enterprise',
  WWG = 'wwg',
  TOLI = 'toli',
  SGX = 'sgx-esgenome',
  MaterialityTracker = 'materiality-tracker',
}

/**
 * Onboarding route paths needs to be aligned with API
 * @TODO Is it time to drop these and let API decide what is valid?
 */
export const ctBrandedRoutes = [
  OnboardingPath.CompanyTrackerStarter,
  OnboardingPath.CompanyTracker,
  OnboardingPath.CompanyTrackerPro,
  OnboardingPath.CompanyTrackerEnterprise,
  OnboardingPath.WWG,
  OnboardingPath.TOLI,
];

export const getOnboardingRoutes = () => {
  return [
    // CT Branded Routes
    ...ctBrandedRoutes,
    OnboardingPath.SGX,
    OnboardingPath.MaterialityTracker,
  ]
}

export const isMaterialityTracker = (company: Pick<RootInitiativeData, 'appConfigCode' | 'appConfig'>) => {
  const appConfigCode = company.appConfigCode || company.appConfig?.code;
  return appConfigCode === AppCode.MaterialityTracker;
};
