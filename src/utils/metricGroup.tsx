/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { DEFAULT_GROUP_COLOR } from '@g17eco/molecules/form';
import { AccessType, CustomMetricOrderType, MetricGroup } from '../types/metricGroup';
import { naturalSort } from '.';
import { UniversalTrackerBlueprintMin, UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import { getName, getTypeCode, hasFramework, hasStandard } from './universalTracker';
import { ScopeQuestion, ScopeQuestionOptionalValue } from '@g17eco/types/surveyScope';
import { ScopeGroupData } from '@g17eco/types/survey';

export const getGroupLabel = (group: MetricGroup) => {
  if (group.accessType === AccessType.Inherited) {
    return `Inherited: ${group.groupName}`;
  }

  if (group.accessType === AccessType.Assigned && group.initiative) {
    return `${group.initiative.name} - ${group.groupName}`;
  }

  return group.groupName;
};

export const getCustomGroupOptions = (metricGroups: MetricGroup[]) =>
  metricGroups
    .map((group) => {
      return {
        label: (
          <div className='d-flex align-items-center'>
            {group.groupData?.icon ? (
              <img src={group.groupData.icon} alt={group.groupName} width={26} className='mr-2' />
            ) : (
              <i
                className='fa fa-circle mr-2'
                style={{ color: group.groupData?.colour ?? DEFAULT_GROUP_COLOR, fontSize: '26px' }}
              ></i>
            )}
            <span>CM: {group.groupName}</span>
          </div>
        ),
        searchString: group.groupName,
        value: group._id,
        scopeType: 'custom' as 'custom',
      };
    })
    .sort((a, b) => naturalSort(a.searchString, b.searchString));

const objectIdLength = 24;

export const filterMetricGroups = ({
  utrId,
  metricGroupId,
  metricGroups = [],
}: {
  utrId: string;
  metricGroupId: string;
  metricGroups?: Pick<MetricGroup, '_id' | 'universalTrackers'>[];
}) => {
  if (metricGroupId.length !== objectIdLength) {
    return false;
  }
  const metricGroup = metricGroups.find((g) => g._id === metricGroupId);
  return metricGroup?.universalTrackers?.includes(utrId);
};

export const isMatchSelectedPacks = ({
  utr,
  packs,
  metricGroups,
}: {
  utr: UniversalTrackerBlueprintMin;
  packs: string[];
  metricGroups: MetricGroup[];
}) => {
  if (packs.length === 0) {
    return true;
  }
  for (const pack of packs) {
    if (hasStandard(utr, pack) || hasFramework(utr, pack)) {
      return true;
    }
    if (filterMetricGroups({ utrId: utr._id, metricGroupId: pack, metricGroups })) {
      return true;
    }
  }
  return false;
};

export function sortCustomGroupQuestions<
  T extends Pick<ScopeQuestionOptionalValue, 'name' | 'universalTracker'> = ScopeQuestion
>(
  questions: T[] | undefined,
  group: Pick<ScopeGroupData, 'preferredAltCodes' | 'metricsOrder' | 'universalTrackerIds'>
): T[] {
  const { preferredAltCodes, metricsOrder, universalTrackerIds } = group;
  if (!questions?.length) {
    return [];
  }

  if (!metricsOrder || metricsOrder.orderType === CustomMetricOrderType.Name) {
    if (preferredAltCodes && preferredAltCodes.length > 0) {
      return questions.sort((a, b) => {
        return naturalSort(
          a.universalTracker.getBestName(preferredAltCodes),
          b.universalTracker.getBestName(preferredAltCodes)
        );
      });
    }
    // William thinks this is the same, but Kes is scared
    return questions.sort((a, b) => naturalSort(a.name, b.name));
  }

  const { orderType } = metricsOrder;
  if (orderType === CustomMetricOrderType.Custom) {
    if (!universalTrackerIds?.length) {
      return questions;
    }
    // universalTrackerIds is the latest list of utrIds of metric group
    // questions are not synced with universalTrackerIds when users update the metric group scope
    // Use universalTrackerIds to sort the current questions
    const sortedQuestions = questions.slice().sort((a, b) => {
      const indexA = universalTrackerIds.indexOf(a.universalTracker.getId());
      const indexB = universalTrackerIds.indexOf(b.universalTracker.getId());
      // If an item is not in utrIds, assign it a high value to push it to the end
      return (indexA !== -1 ? indexA : Infinity) - (indexB !== -1 ? indexB : Infinity);
    });

    return sortedQuestions;
  }

  // Must be sort by typeCode
  if (preferredAltCodes && preferredAltCodes.length > 0) {
    return questions.sort((a, b) => {
      return naturalSort(
        a.universalTracker.getBestTypeCode(preferredAltCodes),
        b.universalTracker.getBestTypeCode(preferredAltCodes)
      );
    });
  }
  return questions.sort((a, b) => naturalSort(a.universalTracker.getTypeCode(), b.universalTracker.getTypeCode()));
}

export function sortCustomGroupQuestionPlains<T extends UniversalTrackerPlain>(
  questions: T[] | undefined,
  group: Pick<ScopeGroupData, 'preferredAltCodes' | 'metricsOrder' | 'universalTrackerIds'>
): T[] {
  const { preferredAltCodes, metricsOrder, universalTrackerIds } = group;
  if (!questions?.length) {
    return [];
  }

  if (!metricsOrder || metricsOrder.orderType === CustomMetricOrderType.Name) {
    if (preferredAltCodes && preferredAltCodes.length > 0) {
      return questions.sort((a, b) => {
        return naturalSort(
          getName(a, preferredAltCodes),
          getName(b, preferredAltCodes)
        );
      });
    }
    // William thinks this is the same, but Kes is scared
    return questions.sort((a, b) => naturalSort(a.name, b.name));
  }

  const { orderType } = metricsOrder;
  if (orderType === CustomMetricOrderType.Custom) {
    if (!universalTrackerIds?.length) {
      return questions;
    }
    // universalTrackerIds is the latest list of utrIds of metric group
    // questions are not synced with universalTrackerIds when users update the metric group scope
    // Use universalTrackerIds to sort the current questions
    const sortedQuestions = questions.slice().sort((a, b) => {
      const indexA = universalTrackerIds.indexOf(a._id);
      const indexB = universalTrackerIds.indexOf(b._id);
      // If an item is not in utrIds, assign it a high value to push it to the end
      return (indexA !== -1 ? indexA : Infinity) - (indexB !== -1 ? indexB : Infinity);
    });

    return sortedQuestions;
  }

  // Must be sort by typeCode
  if (preferredAltCodes && preferredAltCodes.length > 0) {
    return questions.sort((a, b) => {
      return naturalSort(
        getTypeCode(a, preferredAltCodes),
        getTypeCode(b, preferredAltCodes)
      );
    });
  }
  return questions.sort((a, b) => naturalSort(a.typeCode, b.typeCode));
}