/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

// eslint-disable-next-line no-restricted-imports
import { IRunOptions, TextRun } from 'docx';
import { xmlSanitizer } from '@utils/xml';

export class SafeTextRun extends TextRun {
  public constructor(options: IRunOptions | string | number) {
    if (typeof options === 'string' || typeof options === 'number') {
      super(xmlSanitizer(options));
      return;
    }

    super({
      ...options,
      text: options.text ? xmlSanitizer(options.text) : options.text
    });
  }

  public getRootNode() {
    return this.root;
  }

  public getTextValue() {
    return this.getRootNode().find((item) => item.rootKey === 'w:t')?.root[1];
  }
}
