/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */



import { SafeTextRun } from '@utils/docx/SafeTextRun';

describe('SafeTextRun', () => {
  [
    ['', ''],
    ['test\x00', 'test'],
    ['control de\x02vice', 'control device'],
    ['test', 'test'],
    [
      'control de\u0002vice accurate control of us\u0002age and energy consumption. \n\nUWX:\n•',
      'control device accurate control of usage and energy consumption. \n\nUWX:\n•'
    ],
    // Regex escape on \uD800-\uDFFF cause emoji like 🌴 to be removed
    ['control de\x02vice 🌴', 'control device '],
    // Language specific characters should remain
    ['你好', '你好'],
    ['مرحب', 'مرحب'],
    ['Xin Chào Việt Nam', 'Xin Chào Việt Nam'],
    ['Quoi de neuf ?', 'Quoi de neuf ?'],
    // Table rows can have numeric values as well, when using import sheets
    [9, '9'],
  ].forEach(([xml, expected]) => {
    it(`should return "${expected}" for "${xml}"`, () => {
      expect(new SafeTextRun(xml).getTextValue()).eq(expected);
    });
  })
});
