/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { addDate, getDiff } from './date';

interface ProcessBatchParams<DataType = unknown, Result = unknown> {
  data: DataType[];
  processFn: (item: DataType) => Promise<Result>;
  onBatchComplete?: (results: Result[], count: number, total: number) => void;
  batchSize?: number;
}

interface RateLimitProcessing<T, R> extends ProcessBatchParams<T, R> {
  batchRateLimit: {
    /** Must wait min number of units for the next batch to start */
    minWaitTime: number;
    unit: 'second' | 'millisecond' | 'minute';
  };
  onRateLimit?: (timeToWaitInMs: number) => void;
}

const sleep = (duration: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, duration);
  });
}

export const processInBatches = async <T, N>(props: ProcessBatchParams<T, N> | RateLimitProcessing<T, N>) => {
  const { data, processFn, onBatchComplete, batchSize = 10 } = props;

  if (!data.length) {
    return [];
  }

  // Copy the data so we can safely modify it
  const dataCopy = [...data];
  const totalResults: N[] = [];
  const totalBatches = Math.ceil(dataCopy.length / batchSize);

  while (dataCopy.length > 0) {
    const timeNow = Date.now();
    const batch = dataCopy.splice(0, batchSize);
    const results = await Promise.all(batch.map(processFn));
    totalResults.push(...results);
    onBatchComplete?.(results, totalResults.length, totalBatches);

    // We still have something to process and we have rate-limit limit set
    if (dataCopy.length > 0 && 'batchRateLimit' in props && props.batchRateLimit) {
      const shouldWaitTillDate = addDate(timeNow, props.batchRateLimit.minWaitTime, props.batchRateLimit.unit);
      // Future date, minus now, how long until we reach future date?
      const timeToWaitInMs = getDiff(shouldWaitTillDate, Date.now(), 'milliseconds');
      if (timeToWaitInMs) {
        props.onRateLimit?.(timeToWaitInMs);
        await sleep(timeToWaitInMs);
      }
    }
  }

  return totalResults;
};
