/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { tryCalculation } from './formula';

describe('tryCalculation', () => {
  const fallback = 'fallback value';
  const infinityFallback = 'fallback infinity value';

  const testCases = [
    { input: { formula: '4', variables: { a: 7 }, fallback }, output: 4 },
    { input: { formula: '{a}', variables: { a: 7 }, fallback }, output: 7 },
    { input: { formula: '{b}', variables: { b: 2 }, fallback }, output: 2 },
    { input: { formula: '{a}+{b}', variables: { a: 6, b: 4 }, fallback }, output: 10 },
    { input: { formula: '{a}+{b}+{c}-{d}', variables: { a: 6, b: 4, c: 20, d: 10 }, fallback }, output: 20 },
    { input: { formula: '{a}-{b}', variables: { a: 6, b: 4 }, fallback }, output: 2 },
    { input: { formula: '{a}-{b}', variables: { a: 4, b: 6 }, fallback }, output: -2 },
    { input: { formula: '{a}*{b}', variables: { a: 0, b: 100 }, fallback }, output: 0 },
    { input: { formula: '{a}*{b}', variables: { a: 2, b: 6 }, fallback }, output: 12 },
    { input: { formula: '100*{a}/{b}', variables: { a: 3, b: 5 }, fallback }, output: 60 },
    { input: { formula: '{a}/{b}', variables: { a: 0, b: 6 }, fallback }, output: 0 },
    { input: { formula: '{a}/{b}', variables: { a: 34, b: 0 }, fallback }, output: 0 },
    { input: { formula: '{a}/{b}', variables: { a: 3, b: 3 }, fallback }, output: 1 },
    { input: { formula: '{c}/{d}', variables: { a: 3, b: 4 }, fallback }, output: fallback },
    { input: { formula: '{c}/{d}', variables: { c: NaN, d: 4 }, fallback, infinityFallback }, output: infinityFallback },
  ];

  testCases.forEach(({ input, output }) => {
    const { formula, variables } = input;
    it(`formula: ${formula}, variables: { ${Object.entries(variables)
      .map(([k, v]) => `${k}: ${v}`)
      .join(', ')} }`, () => {
      const result = tryCalculation(input);
      expect(result).equal(output);
    });
  });
});
