/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { processInBatches } from './batch';
import * as dataFn from './date';
import { vi } from 'vitest';

describe('processInBatches', () => {
  const data = ['item1', 'item2', 'item3', 'item4', 'item5'];
  const batchSize = 2;

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should handle empty array', async () => {
    const onBatchComplete = vi.fn();

    const result = await processInBatches({
      data: [],
      processFn: async () => 'processed',
      onBatchComplete,
      batchSize,
    });

    expect(result).to.eql([]);
    expect(onBatchComplete).toHaveBeenCalledTimes(0);
  });

  it('should process data in batches', async () => {
    const processFn = () => {
      return Promise.resolve('processed');
    };

    const onBatchComplete = vi.fn();
    const getDiffInUnitSpy = vi.spyOn(dataFn, 'getDiff');

    const result = await processInBatches({
      data,
      processFn,
      onBatchComplete,
      batchSize,
    });

    expect(result).to.have.lengthOf(data.length);
    expect(result).to.eql(data.map(() => 'processed'));
    expect(onBatchComplete).toHaveBeenCalledTimes(Math.ceil(data.length / batchSize));
    expect(getDiffInUnitSpy).not.toHaveBeenCalled();
  });

  it('should process data with rate-limit', async () => {
    const onRateLimit = vi.fn();

    // 5 items, 2 items per batch, 3 batches
    // 2ms per batch diff, but must wait 10ms => 30ms min
    const dataList = Array.from({ length: 7 }, (_, i) => i);
    const startTime = performance.now();
    const result = await processInBatches({
      data: dataList,
      processFn: async () => 'processed',
      batchSize: 2,
      batchRateLimit: {
        minWaitTime: 10,
        unit: 'millisecond',
      },
      onRateLimit,
    });

    const endTime = performance.now();
    expect(result).to.have.lengthOf(dataList.length);
    expect(result).to.eql(dataList.map(() => 'processed'));

    // Only last three batches should be rate-limited
    expect(onRateLimit).to.toHaveBeenCalledTimes(3);
    expect(endTime - startTime).to.be.greaterThan(30);
  });

  it('should not call onRateLimit', async () => {
    const onRateLimit = vi.fn();

    // 5 items, 2 items per batch, 3 batches
    // 2ms per batch diff, but must wait 10ms
    vi.spyOn(dataFn, 'getDiff').mockReturnValue(0).mockReturnValue(0);

    const result = await processInBatches({
      data,
      processFn: async () => 'processed',
      batchSize: 2,
      batchRateLimit: {
        minWaitTime: 10,
        unit: 'millisecond',
      },
      onRateLimit,
    });

    expect(result).to.have.lengthOf(data.length);
    expect(result).to.eql(data.map(() => 'processed'));
    expect(onRateLimit).not.toHaveBeenCalled();
  });
});
