/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import {
  DisplayOption,
  DownloadSettingsType,
  DownloadUtrvStatus,
  getDownloadDisplayOptions,
} from '@components/downloads/util/downloadReportHandler';
import { VisibilityStatus } from '@g17eco/types/download';
import { PackOption } from '@constants/standards-frameworks';
import { getScopeGroupByScope } from '@utils/survey-scope';
import { ScopeGroups } from '@models/surveyData';
import { sdgMap } from '@constants/sdg-data';

//
// Lots of custom functions and constants
// that are not quite clear where it should live yet
// @TODO refactor and move to a better place
//

export const otherReportCard = {
  code: 'others',
  name: 'Other aligned reports',
  icon: 'fal fa-folder-download text-ThemeIconSecondary',
};

export const getDownloadMultiScope = (scope?: string, scopeType?: PackOption['scopeType']) => {

  const defaultScope = {
    [ScopeGroups.Sdg]: [],
    [ScopeGroups.Standards]: [],
    [ScopeGroups.Frameworks]: [],
    [ScopeGroups.Custom]: [],
  };
  if (!scope) {
    return defaultScope;
  }

  if (scope === 'sdg') {
    return {
      ...defaultScope,
      sdg: sdgMap.map(goal => goal.code),
    };
  }

  const scopeGroup = getScopeGroupByScope(scope, scopeType);

  return {
    ...defaultScope,
    [scopeGroup]: [scope],
  };
};

export type DownloadSettingsChange =
  | { key: 'scope'; value: string[] }
  | {
  key: 'status' | 'assuranceStatus' | 'privacy' | 'display';
  value: string;
};

export const handleSettingsChange = (
  change: DownloadSettingsChange,
  setDownloadSettings: (settings: DownloadSettingsType) => void,
  downloadSettings: DownloadSettingsType
) => {
  switch (change.key) {
    case 'privacy':
      setDownloadSettings({ ...downloadSettings, visibility: change.value as VisibilityStatus });
      return;
    case 'status': {
      const statuses = change.value as DownloadUtrvStatus;
      setDownloadSettings({ ...downloadSettings, status: statuses, assuranceStatus: undefined });
      return;
    }
    case 'assuranceStatus': {
      const assuranceStatus = change.value as DownloadUtrvStatus;
      setDownloadSettings({ ...downloadSettings, status: undefined, assuranceStatus });
      return;
    }
    case 'display': {
      setDownloadSettings({ ...downloadSettings, ...getDownloadDisplayOptions(change.value as DisplayOption) });
      return;
    }
    case 'scope': {
      setDownloadSettings({
        ...downloadSettings,
        selectedScopes: downloadSettings.selectedScopes.map((item) => ({
          ...item,
          checked: change.value.includes(item.code),
        })),
      });
      return;
    }
  }
};
