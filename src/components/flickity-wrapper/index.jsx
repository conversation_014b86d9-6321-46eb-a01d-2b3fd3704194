/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { createPortal } from 'react-dom';
import imagesloaded from 'imagesloaded';
import Flickity from 'flickity';

export class FlickityWrapper extends Component {
  state = {
    flickityReady: false,
  };

  carousel = null;
  flkty = null;
  _isMounted = true;

  componentDidUpdate(prevProps, prevState) {
    const {
      children,
      options: { draggable, initialIndex },
      reloadOnUpdate,
    } = this.props;
    const { flickityReady } = this.state;
    if (reloadOnUpdate || (!prevState.flickityReady && flickityReady)) {
      this.flkty.deactivate();
      this.flkty.selectedIndex = initialIndex || 0;
      this.flkty.options.draggable =
        draggable === undefined
          ? children
          ? children.length > 1
          : false
          : draggable;
      this.flkty.activate();
    } else {
      this.flkty.reloadCells();
    }
  }

  componentDidMount() {
    const { disableImagesLoaded, flickityRef, options } = this.props;
    const carousel = this.carousel;
    this.flkty = new Flickity(carousel, {
      ...options,
      on: {
        dragStart: function () {
          this.slider.childNodes.forEach((slide) => (slide.style.pointerEvents = 'none'));
        },
        dragEnd: function () {
          this.slider.childNodes.forEach((slide) => (slide.style.pointerEvents = 'all'));
        },
      },
    });

    const setFlickityToReady = () => {
      if (this._isMounted) {
        this.setState({ flickityReady: true });
      }
    }

    if (disableImagesLoaded) {
      setFlickityToReady();
    } else {
      imagesloaded(carousel, setFlickityToReady);
    }

    if (flickityRef) {
      flickityRef(this.flkty);
    }
  }

  componentWillUnmount() {
      this._isMounted = false;
  }

  renderPortal() {
    if (!this.carousel) return null;
    const mountNode = this.carousel.querySelector('.flickity-slider');
    if (mountNode) return createPortal(this.props.children, mountNode);
  }

  render() {
    return React.createElement(
      this.props.elementType,
      {
        className: this.props.className,
        ref: c => {
          this.carousel = c;
        },
      },
      this.props.static ? this.props.children : this.renderPortal()
    );
  }
}

FlickityWrapper.propTypes = {
  children: PropTypes.array,
  className: PropTypes.string,
  disableImagesLoaded: PropTypes.bool,
  elementType: PropTypes.string,
  flickityRef: PropTypes.func,
  options: PropTypes.object,
  reloadOnUpdate: PropTypes.bool,
  static: PropTypes.bool,
};

FlickityWrapper.defaultProps = {
  className: '',
  disableImagesLoaded: false,
  elementType: 'div',
  options: {},
  reloadOnUpdate: false,
  static: false,
};
