/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { loadUserActions } from '../../actions/userTracker';
import { Loader } from '@g17eco/atoms/loader';
import UniversalTrackerActionContainer from './container';
import './styles.scss';

class UserActions extends PureComponent {

  componentDidMount() {
    if (!this.props.userActionsState.loaded) {
      this.props.loadUserActions();
    }
  }

  render() {
    const { userActionsState } = this.props;

    if (userActionsState.errored) {
      return <div className='user-actions-component'><h1>Unable to load outstanding updates</h1></div>;
    }

    if (!userActionsState.loaded) {
      return (<Loader />);
    }

    const { actions } = userActionsState.data;

    return this.renderActions(actions.utrs);
  }

  renderActions(utrvs) {

    const totalCount = utrvs.length;
    if (totalCount === 0) {
      return <div className='user-actions-component'>
        <h1>You have no outstanding actions to complete</h1>
      </div>
    }

    const utrvGroups = {};
    utrvs.forEach((utrv) => {
      if (!utrv.universalTracker) {
        return;
      }
      const key = utrv.initiativeId + '-' + utrv.universalTrackerId;
      if (!utrvGroups[key]) {
        utrvGroups[key] = [];
      }
      utrvGroups[key].push(utrv);
    });

    return (
      <div className='user-actions-component px-2'>
        {Object.keys(utrvGroups).map((key, i) =>
          <div className='section mb-2' key={i}>
            <UniversalTrackerActionContainer universalTrackerValues={utrvGroups[key]} />
          </div>
        )}
      </div>
    )
  }
}

const mapStateToProps = (state) => ({
  userActionsState: state.userTrackers,
});

const mapDispatchToProps = ({
  loadUserActions,
});

export default connect(mapStateToProps, mapDispatchToProps)(UserActions);
