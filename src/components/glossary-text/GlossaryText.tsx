/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { findAll } from 'highlight-words-core';
import { GlossaryState } from '../survey/question/questionInterfaces';
import { nl2br } from '../../utils';
import { RecoveryErrorBoundary } from '../../features/error/RecoveryErrorBoundary';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface GlossaryTextProps {
  text: string;
  glossary: GlossaryState;
}

export const GlossaryText = (props: GlossaryTextProps) => {
  const { text, glossary } = props;

  const searchWords = Object.keys(glossary ?? {});

  const chunks = findAll({
    caseSensitive: false,
    searchWords,
    textToHighlight: text,
  });

  return (
    <RecoveryErrorBoundary fallback={text}>
      {chunks.map((chunk, i) => {
        const { end, highlight, start } = chunk;
        const replacedText = text.substring(start, end);
        if (!highlight) {
          // These are <div /> that behave like <span /> so that weglot doesn't see them as a
          // single sentence and cause virtual-DOM errors
          return <div className='d-inline' key={`${i}-${replacedText}`}>{nl2br(replacedText)}</div>
        }

        const glossaryKey = replacedText.toLowerCase();
        const glossaryText = glossary?.[glossaryKey]?.html ?? '';

        const definitionText = (
          <div
            dangerouslySetInnerHTML={{ __html: glossaryText }}
            className='text-left'
          />
        );
        return (
          <span className='' key={`${i}-${replacedText}`}>
            <SimpleTooltip text={definitionText}>{nl2br(replacedText)}</SimpleTooltip>
          </span>
        );
      })}
    </RecoveryErrorBoundary>
  );
};
