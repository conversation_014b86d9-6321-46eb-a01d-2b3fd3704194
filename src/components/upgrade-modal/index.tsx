/*
 * Copyright (c) 2019-2023. World Wide Generation Ltd
 */

import { closeUpgradeModal } from '../../actions/upgradeModal';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { UserInterestModal } from '@components/user-interest-modal';
import React from 'react';


export const UpgradeModal = () => {
  const dispatch = useAppDispatch();
  const toggle = () => dispatch(closeUpgradeModal());
  const upgradeModal = useAppSelector(state => state.upgradeModal);

  // Not used anymore, as all downloads are allowed. Align with existing form.
  return <UserInterestModal title={'Upgrade Required'} isOpen={upgradeModal.isOpen} toggle={toggle} />;
}
