/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { getDiff, getDuration } from '../../utils/date';
import './styles.scss';

class Countdown extends Component {
  constructor() {
    super();
    this.state = {
      units: [
        { moment: 'years', label: 'Year' },
        { moment: 'months', label: 'Month' },
        { moment: 'days', label: 'Day' },
        { moment: 'hours', label: 'Hour' },
        { moment: 'minutes', label: 'Minute' },
        { moment: 'seconds', label: 'Second' }
      ]
    }

    this.endDate = new Date('2030-01-01T00:00:00.000Z');
  }

  componentDidMount() {
    this.setDiff();
    this.interval = setInterval(() => {
      !this.unmounted && this.setDiff();
    }, 1000)
  }

  componentWillUnmount() {
    this.unmounted = true;
    clearTimeout(this.interval);
  }

  setDiff() {
    const units = [...this.state.units];
    const diffVar = getDiff(this.endDate);
    const diff = getDuration(diffVar);

    // $d is an internal Day.js structure
    units.forEach(unit => {
      unit.diff = diff.$d[unit.moment];
    })

    this.setState({ units: units });
  }

  render() {
    const { units } = this.state;

    return (
      <div className='counter-wrapper'>
        <span className='title'>Countdown to 2030</span>
        <div className='row g-0'>
          {units.map((unit, i) => {
            return (
              <div key={i} className='col'>
                <span className='dont_translate'>
                  {unit.diff}
                </span>
                {unit.diff === 1 ? unit.label : unit.label + 's'}
              </div>
            )
          })}
        </div>
      </div>
    )

  }

}

export default Countdown;
