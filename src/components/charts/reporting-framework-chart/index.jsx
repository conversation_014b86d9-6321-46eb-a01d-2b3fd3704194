/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import * as d3 from 'd3';

const getColour = (sdgGoal) => {
  const colours = [
    '#f1002f',
    '#d99e3c',
    '#219b52',
    '#c81035',
    '#f83631',
    '#00aed9',
    '#ffb437',
    '#921036',
    '#f56a2f',
    '#e60080',
    '#ff993a',
    '#d48a37',
    '#497744',
    '#007db8',
    '#3bb056',
    '#005586',
    '#0c3866'
  ];
  return colours[sdgGoal - 1];
}

class ReportingFrameworkChart extends Component {

  state = {
    width: 968,
    height: 968,
  };

  svg = undefined;
  containerRef = React.createRef();
  d3objects = {};

  updateDimensions = () => {
    const current = this.containerRef.current;
    if (!current) {
      return;
    }
    this.setState({ width: current.clientWidth, height: current.clientWidth }, () => {
      this.plotChart('graph', this.props.data);
    });
  }

  componentDidMount() {
    window.addEventListener('resize', this.updateDimensions);
    this.updateDimensions();
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateDimensions);
  }

  componentDidUpdate(prevProps) {
    const dataHasChanged = prevProps.data !== this.props.data;
    if (dataHasChanged) {
      this.plotChart('graph', this.props.data);
    }
    if (this.d3objects) {
      if (prevProps.code !== this.props.code) {
        const p = this.getSelected(this.d3objects.dataHierarchy, this.props.code)
        this.d3objects.handleRootChange(p);
      }
    }
  }

  render() {
    return <div id='graph' ref={this.containerRef} ></div>
  }

  getSelected = (dataHierarchy, code) => {
    if (!code) {
      return dataHierarchy;
    }

    let selectedRoot = undefined;
    const recursiveFindBySDGCode = (data, code) => {
      if (data.data.code === code) {
        selectedRoot = data;
        return true;
      }
      if (!data.children) {
        selectedRoot = undefined;
        return false;
      }

      return data.children.some((c) => {
        return recursiveFindBySDGCode(c, code);
      });
    }
    recursiveFindBySDGCode(dataHierarchy, this.props.code);

    return selectedRoot;
  }

  setSelected = (p, notifyParent = false) => {
    this.props.handleSetSelected(p);
  }

  plotChart(chartElementId, data) {

    const _this = this;

    const size = Math.min(this.state.width, this.state.height);
    const radius = size / 6;
    _this.d3objects.dataHierarchy = d3.hierarchy(data).sum(d => d.value);
    const func = d3.partition().size([2 * Math.PI, _this.d3objects.dataHierarchy.height + 1]);
    const root = func(_this.d3objects.dataHierarchy);

    root.each(d => d.current = d);

    if (this.svg) {
      this.svg.remove();
    }

    this.svg = d3.select('#' + chartElementId).append('svg')
      .style('width', '100%')
      .style('height', '100%')
      .style('font', '12px Inter');

    _this.d3objects.g = this.svg.append('g')
      .attr('transform', `translate(${size / 2},${size / 2})`);

    _this.d3objects.arc = d3.arc()
      .startAngle(d => d.x0)
      .endAngle(d => d.x1)
      .padAngle(d => Math.min((d.x1 - d.x0) / 2, 0.005))
      .padRadius(radius * 1.5)
      .innerRadius(d => d.y0 * radius)
      .outerRadius(d => Math.max(d.y0 * radius, d.y1 * radius - 1));

    _this.d3objects.arcVisible = (d) => {
      return d.y1 <= 3 && d.y0 >= 0 && d.x1 > d.x0;
    }

    _this.d3objects.labelVisible = (d) => {
      return d.y1 <= 3 && d.y0 >= 1 && (d.y1 - d.y0) * (d.x1 - d.x0) > 0.03;
    }

    _this.d3objects.labelTransform = (d) => {
      const x = (d.x0 + d.x1) / 2 * 180 / Math.PI;
      const y = (d.y0 + d.y1) / 2 * radius;
      return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
    }

    function mouseover(event, d) {
      const opacity = _this.d3objects.arcVisible(d.current) ? 1 : 0;
      d3.select(this).attr('fill-opacity', opacity);
    }

    function mouseout(event, d) {
      const opacity = _this.d3objects.arcVisible(d.current) ? (d.children ? 0.6 : 0.4) : 0;
      d3.select(this).attr('fill-opacity', opacity);
    }

    _this.d3objects.path = this.d3objects.g.append('g')
      .selectAll('path')
      .data(root.descendants().slice(1))
      .join('path')
      .attr('fill', d => { while (d.depth > 1) d = d.parent; return getColour(d.data.code); })
      .attr('fill-opacity', d => _this.d3objects.arcVisible(d.current) ? (d.children ? 0.6 : 0.4) : 0)
      .attr('cursor', d => _this.d3objects.arcVisible(d.current) ? 'pointer' : '')
      .attr('d', d => _this.d3objects.arc(d.current))
      .on('mouseover', mouseover)
      .on('mouseout', mouseout)
      .on('click', (event, d) => _this.setSelected(d));

    _this.d3objects.path.append('title')
      .text(d => `${d.ancestors().map(d => d.data.name).reverse().join(' / ')}`);

    _this.d3objects.label = _this.d3objects.g.append('g')
      .attr('pointer-events', 'none')
      .attr('text-anchor', 'middle')
      .style('user-select', 'none')
      .selectAll('text')
      .data(root.descendants().slice(1))
      .join('text')
      .attr('dy', '0.35em')
      .attr('fill-opacity', d => +_this.d3objects.labelVisible(d.current))
      .attr('transform', d => _this.d3objects.labelTransform(d.current))
      .text(d => d.data.name);

    function centerMouseOver() {
      _this.d3objects.centerCircle.attr('fill-opacity', 0.6);
    }
    function centerMouseOut() {
      _this.d3objects.centerCircle.attr('fill-opacity', 0)
    }

    _this.d3objects.centerCircle = _this.d3objects.g.append('circle')
      .datum(root)
      .attr('r', radius - 2)
      .attr('fill', '#ffffff')
      .attr('fill-opacity', 0)
      .attr('pointer-events', 'all')
      .style('cursor', 'pointer')
      .on('mouseover', centerMouseOver)
      .on('mouseout', centerMouseOut)
      .on('click', (event, d) => _this.setSelected(d));

    _this.d3objects.centerText = _this.d3objects.g.append('text')
      .datum(root)
      .attr('pointer-events', 'none')
      .attr('dy', '0.5em')
      .style('text-anchor', 'middle')
      .style('font', '32px Inter')
      .text(d => d.data.name);

    _this.d3objects.handleRootChange = (p) => {

      if (!p || !_this.d3objects.arcVisible(p.current)) {
        return;
      }

      _this.d3objects.centerCircle.datum(p.parent || root);
      _this.d3objects.centerText.text(p.data.name);

      root.each(d => d.target = {
        x0: Math.max(0, Math.min(1, (d.x0 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
        x1: Math.max(0, Math.min(1, (d.x1 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
        y0: Math.max(0, d.y0 - p.depth),
        y1: Math.max(0, d.y1 - p.depth)
      });

      const t = _this.d3objects.g.transition().duration(500);

      // Transition the data on all arcs, even the ones that aren’t visible,
      // so that if this transition is interrupted, entering arcs will start
      // the next transition from the desired position.
      _this.d3objects.path.transition(t)
        .tween('data', d => {
          const i = d3.interpolate(d.current, d.target);
          return t => d.current = i(t);
        })
        .filter(function (d) {
          return +this.getAttribute('fill-opacity') || _this.d3objects.arcVisible(d.target);
        })
        .attr('fill-opacity', d => _this.d3objects.arcVisible(d.target) ? (d.children ? 0.6 : 0.4) : 0)
        .attr('cursor', d => _this.d3objects.arcVisible(d.target) ? 'pointer' : '')
        .attrTween('d', d => () => _this.d3objects.arc(d.current));

      _this.d3objects.label.filter(function (d) {
        return +this.getAttribute('fill-opacity') || _this.d3objects.labelVisible(d.target);
      }).transition(t)
        .attr('fill-opacity', d => +_this.d3objects.labelVisible(d.target))
        .attrTween('transform', d => () => _this.d3objects.labelTransform(d.current));
    }

    if (this.props.code) {
      _this.d3objects.handleRootChange(this.getSelected(_this.d3objects.dataHierarchy, this.props.code));
    }

    return this.svg.node();
  }
}

export default ReportingFrameworkChart;
