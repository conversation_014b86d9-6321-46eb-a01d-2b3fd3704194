/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */


import { useAppSelector } from '../../../reducers';
import { FeaturePermissions } from '../../../services/permissions/FeaturePermissions';
import { useCallback } from 'react';
import { loggerMessage } from '../../../logger';

interface Props {
  initiativeId: string;
}

export const useScopePacksFeature = ({ initiativeId }: Props) => {

  const scopePacksFeature = useAppSelector(FeaturePermissions.getScopePackFeature);
  const isRestricted = scopePacksFeature.access === 'custom' && scopePacksFeature.restricted;
  const scope = 'scope' in scopePacksFeature ? scopePacksFeature.scope : { standards: [], frameworks: [] };
  const onUpgrade = useCallback(async () => {
    // Does nothing for now, and is not needed until upgrade flow allow
    loggerMessage(`Trying to upgrade ${initiativeId}, not supported yet.`)
  }, [initiativeId]);


  return {
    isRestricted,
    scope,
    onUpgrade,
  }
}
