/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */


import React from 'react';
import { getGroup } from '@g17eco/core';
import { SubmitButton } from '../../button/SubmitButton';
import RequestDemoModal from '../../request-demo-modal';
import { useToggle } from '../../../hooks/useToggle';


interface UpgradeProps {
  isRestricted: boolean | undefined;
  onUpgrade: () => Promise<void>;
}

export const UpgradeScopePack = (props: UpgradeProps) => {

  const [isOpen, toggle] = useToggle(false)

  if (!props.isRestricted) {
    return null;
  }

  const logos: { code: string, type: 'standards' | 'frameworks' }[] = [
    { type: 'standards', code: 'issb' },
    { type: 'frameworks', code: 'ctl' },
    { type: 'standards', code: 'sasb' },
    { type: 'standards', code: 'tcfd_standard' },
  ];

  // Current upgrade just show contact-us modal, later on use props.onUpgrade
  const onUpgrade = async () => {
    toggle()
  }

  return (
    <div className={'background-ThemeBgMedium text-center py-5 px-7'}>
      <div className={'text-ThemeTextMedium'}>
        Upgrade your plan to get global sustainability standards at your <br/>
        fingertips, including ISSB, TCFD, SASB and many more.
      </div>
      <div className={'d-flex align-items-center py-5 justify-content-center'}>
        {logos.reduce((acc, { code, type }) => {
          const group = getGroup(type, code);
          if (group) {
            acc.push(<img key={code} className={'mx-2'} width={40} src={group.src} alt={group.shortName}/>);
          }
          return acc;
        }, [] as React.JSX.Element[])}
      </div>

      <div>
        <SubmitButton size={'lg'} onClick={onUpgrade}>
          Upgrade to access global standards
        </SubmitButton>
      </div>
      <RequestDemoModal isOpen={isOpen} title={'Contact us'} toggle={toggle} />
    </div>
  );
};
