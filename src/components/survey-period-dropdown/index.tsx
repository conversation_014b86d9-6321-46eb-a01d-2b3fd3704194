/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { DataPeriods } from '@g17eco/types/universalTracker';
import { PeriodOption, getSurveyPeriodOptions } from './utils';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';

export interface PeriodDropdownProps<T extends PeriodOption = DataPeriods> {
  disabled?: boolean;
  availablePeriods: T[];
  period?: T;
  setPeriod?: (period: T) => void;
  hasAllOption?: boolean;
  styleProps?: {
    dropdown?: string;
    isFlexibleSize?: boolean;
    isTransparent?: boolean;
  };
}

export const SurveyPeriodDropdown = <T extends PeriodOption = DataPeriods>(props: PeriodDropdownProps<T>) => {
  const {
    disabled = false,
    availablePeriods,
    period = DataPeriods.Yearly,
    setPeriod,
    hasAllOption = false,
    styleProps,
  } = props;

  if (availablePeriods.length === 0) {
    return null;
  }

  const isDisabled = (p: T) => {
    return period === p || !availablePeriods.includes(p);
  };

  const options = getSurveyPeriodOptions<T>(hasAllOption);

  return (
    <SelectFactory
      selectType={SelectTypes.SingleSelect}
      options={options}
      value={options.find(({ value }) => value === period)}
      onChange={(op) => setPeriod?.(op?.value ?? ('all' as T))}
      isOptionDisabled={(op) => isDisabled(op?.value as T)}
      isDisabled={disabled}
      className={styleProps?.dropdown}
      isFlexibleSize={styleProps?.isFlexibleSize}
      isTransparent={styleProps?.isTransparent}
      isSearchable={false}
    />
  );
};
