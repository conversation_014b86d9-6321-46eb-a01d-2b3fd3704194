/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { <PERSON><PERSON>, ModalBody, Modal<PERSON><PERSON>er, ModalHeader, Button } from 'reactstrap';
import { UTR } from '../../constants/labels';
import FileGrid from './file-grid';
import { EvidenceLink } from '../../utils/evidence';

export class EvidenceModal extends React.Component {

  renderHistoryEvidenceFiles = (history, documents, zIndex = 10) => {
    if (!history || !history.evidence) {
      return '';
    }

    const files = history.evidence.map(id => documents.find(d => d._id === id));

    return <div className='mt-0 mb-4'>
        <FileGrid zIndex={zIndex} files={files}/>
      </div>
  };

  renderHistoryEvidenceLinks = (history, zIndex = 10) => {
    if (!history || !history.evidenceLinks) {
      return '';
    }

    return (
      <div className='mt-0 mb-4'>
        {history.evidenceLinks.map((link) => (
          <div>
            <EvidenceLink link={link} />
          </div>
        ))}
      </div>
    );
  };

  render() {
    const { history, isOpen, documents, toggle } = this.props;
    const zIndex = this.docModal ? this.docModal.props.zIndex + 1 : undefined;

    return (
      <Modal ref={(modal) => (this.docModal = modal)} isOpen={isOpen} toggle={toggle} backdrop='static'>
        <ModalHeader toggle={toggle}>{UTR.FILES}</ModalHeader>
        <ModalBody>
          <div className='fileList'>{this.renderHistoryEvidenceFiles(history, documents, zIndex)}</div>

          <div className='fileList'>{this.renderHistoryEvidenceLinks(history)}</div>
        </ModalBody>
        <ModalFooter>
          <Button color='secondary' onClick={toggle}>Close</Button>
        </ModalFooter>
      </Modal>
    );
  }
}
