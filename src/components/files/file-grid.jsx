/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { Component } from 'react'
import { Button } from 'reactstrap';
import { apiClient } from '../../actions/api';
import './file-grid.scss';
import { EvidenceLink } from '../../utils/evidence';
import config from '../../config';
import { CLOSED_PHOTO_INDEX, G17Lightbox } from '@components/lightbox';

class FileGrid extends Component {

  state = {
    photoIndex: CLOSED_PHOTO_INDEX,
    displayGeoModal: false,
  };

  fileDownload = (e, id, url) => {
    e.preventDefault();
    if (url) {
      window.open(url, '_blank', '');
      return;
    }
    return apiClient.get('documents/' + id).then((response) => {
      const body = response.data;
      if (body.success) {
        window.open(body.data.url, '_blank', '');
      }
    }).catch(console.log)
  };

  handleLightbox(e, idx) {
    e.preventDefault();
    this.setState({
      photoIndex: idx
    });
  }

  handleReset() {
    this.setState({
      photoIndex: CLOSED_PHOTO_INDEX,
    });
  }

  render() {
    const { files } = this.props;

    const filteredFiles = files.filter(f => typeof f === 'object');

    const getFileType = (mimetype) => {
      const res = mimetype.split('/');
      if (Array.isArray(res)) {
        return res[0];
      }
      return mimetype;
    };

    const links = filteredFiles.filter((file) => file.type === 'link');
    const images = filteredFiles.filter((file) => file.type !== 'link' && getFileType(file.metadata.mimetype) === 'image');
    const documents = filteredFiles.filter((file) => file.type !== 'link' && getFileType(file.metadata.mimetype) !== 'image');

    return (
      <div className='filesComponent'>
        {this.renderImages(images)}
        {this.renderDocuments(documents)}
        {this.renderLinks(links)}
      </div>
    );
  }

  renderLinks(links) {
    return (
      <div>
        {links.map((link, idx) => {
          return (
            <div key={'link-' + idx} className='file text-truncate' title={link.path}>
              <i className={`fa fas fa-${link.public ? 'link' : 'lock'} ml-1 mr-2 text-primary`} />
              {<EvidenceLink link={link.path} />}
            </div>
          );
        })}
      </div>
    );
  }

  renderDocuments(documents) {
    return (
      <div>
        {documents.map((document, idx) => {
          return (
            <div key={'document-' + idx} className='row mt-1'>
              <div className='col file' style={{ cursor: 'pointer' }} onClick={(e) => this.fileDownload(e, document._id, document.url)}>
                <i className='fa fas fa-file ml-1 mr-1 text-primary' /> <u>{document.metadata.name}</u>
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  renderImages(photos) {
    const { photoIndex } = this.state;
    const slides = photos.map((photo, idx) => {
      return {
        src: photo.url,
        title: photo.title,
        description: this.renderGoogleMap(photos[idx], idx),
      }
    });

    return (
      <div className='container'>
        <div className='row mediaTileContainer'>
          {photos.map((photo, idx) => {
            return (
              <div key={'photo-' + idx} className='pl-0 mediaTile col-lg-2 col-md-3 col-12 align-self-center m-1'>
                <div className='img-thumbnail' onClick={(e) => this.handleLightbox(e, idx)}>
                  <div className='mediaTileImage' style={{ backgroundImage: 'url(' + photo.url + ')' }}></div>
                </div>
              </div>
            );
          })}
        </div>
        <G17Lightbox
          on={{
            view: ({ index }) => this.setState({ displayGeoModal: false, photoIndex: index }),
          }}
          hasCaptions
          slides={slides}
          photoIndex={photoIndex}
          handleReset={() => this.handleReset()}
        />
      </div>
    );
  }

  renderGoogleMap = (photo, idx) => {
    if (!photo.metadata || !photo.metadata.exif || !photo.metadata.exif.GPSLatitude || !photo.metadata.exif.GPSLongitude) {
      return <Button disabled={true}>No GPS location detected</Button>;
    }

    const { displayGeoModal } = this.state;
    if (!displayGeoModal) {
      return <Button onClick={(e) => this.toggleGeoModal(idx)}><i className='fas fa-lg fa-map-marker-alt mr-1' />View Location Map</Button>;
    }

    const latLong = `${photo.metadata.exif.GPSLatitude},${photo.metadata.exif.GPSLongitude}`;
    return <div className='mapContainer'>
      <Button className='closeButton' color='danger' onClick={(e) => this.toggleGeoModal(idx)}  ><i className='fa fas fa-times' /></Button>
      <img alt='Geo location'
        src={`https://maps.googleapis.com/maps/api/staticmap?zoom=16&key=${config.googleMapsKey}&size=600x300&markers=${latLong}`} />
    </div>
  }

  toggleGeoModal = (index) => {
    this.setState({ displayGeoModal: !this.state.displayGeoModal, photoIndex: index });
  }
}

export default FileGrid;
