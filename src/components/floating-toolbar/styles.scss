/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

$doubleHeight: 6rem;
$height: 4rem;

.floating-toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24px;

  margin: 0 auto;
  z-index: 1000;

  border: 1px solid var(--theme-BgDark);
  background-color: var(--theme-BgMedium);
  box-shadow: 0px 16px 24px 0px rgba(38, 40, 43, 0.1), 0px 0px 1px 0px rgba(38, 40, 43, 0.05);
  border-radius: 1rem;
  text-align: center;

  width: fit-content;

  max-height: $doubleHeight;
  height: $height;
  overflow-y: visible;
  pointer-events: all;

  opacity: 0;
  transition: all 500ms;

  &.toolbar--show {
    opacity: 1;
    animation: 500ms linear 0s 1 slideOpen normal none;
  }

  &.toolbar--hide {
    height: 0;
    animation: 500ms linear 0s 1 slideClose normal none;
  }
}
