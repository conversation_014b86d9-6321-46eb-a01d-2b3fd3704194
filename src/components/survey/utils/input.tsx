import UniversalTracker from '../../../model/UniversalTracker';
import { isDefined } from '../../../utils';
import { hasNumericInput, hasSimpleNumericColumnType, isNumericValueType, getUtrDecimal, isMultiRowTableType } from '@utils/universalTracker';
import { BaseInputProps, ErrorMessageType } from '../form/input/InputProps';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { TableDataInfo, ValidationItem } from '../question/questionInterfaces';
import { UniversalTrackerPlain, UtrValueType } from '@g17eco/types/universalTracker';
import { InputColumn } from '../form/input/table/InputInterface';
import { formatNumberWithDecimal, getDecimalPattern } from '@utils/number';
import { isNumericString } from '@utils/string';
import { AggregationColumn, CurrentInputData, UtrvVariation, UtrvVariationWarning } from '@g17eco/types/question';
import { UnitConfig } from '@models/surveyData';
import { getUnitDesc, UnitTypes } from '@utils/units';
import { getConvertedValue } from '@utils/universalTrackerValue';
import { DATE, formatDateUTC } from '@utils/date';

const DECIMAL_EXAMPLE = '83';
const DECIMAL_STRING = '12345';

interface InputValueRecord {
  [key: string]: InputColumn['value'];
}

const isValidValueWithDecimal = ({
  utr,
  value,
  name,
}: {
  utr: Pick<UniversalTracker, 'getValueType' | 'getValueValidation'>;
  value: InputValueRecord[string];
  name: string;
}) => {
  if (!value) {
    return true;
  }

  if (!hasNumericInput(utr)) {
    return true;
  }

  const decimal = getUtrDecimal(utr, name);

  if (decimal === undefined) {
    return true;
  }

  const pattern = getDecimalPattern(decimal);
  const decimalRegExp = new RegExp(pattern);
  return decimalRegExp.test(value.toString());
};

export const generateDecimalErrorMessage = ({
  utr,
  input,
}: {
  utr: Pick<UniversalTracker, 'getId' | 'getValueType' | 'getValueValidation'>;
  input: InputValueRecord | undefined;
}) => {
  if (!input) {
    return;
  }

  return Object.keys(input).reduce((acc, name) => {
    if (isValidValueWithDecimal({ utr, value: input[name], name })) {
      acc[name] = '';
    } else {
      acc[name] = generateDecimalHelperText(utr, name);
    }
    return acc;
  }, {} as ErrorMessageType);
};

export const generateDecimalExample = (decimal: number) => {
  if (decimal === 0) {
    return DECIMAL_EXAMPLE;
  }

  const fractionalPart = DECIMAL_STRING.substring(0, decimal);

  return DECIMAL_EXAMPLE.concat('.').concat(fractionalPart);
};

export const generateDecimalHelperText = (
  utr: Pick<UniversalTracker, 'getValueType' | 'getValueValidation'> | undefined,
  inputName?: string
) => {
  const decimal = getUtrDecimal(utr, inputName);
  return isDefined(decimal) ? `Requirement: ${decimal} decimal places (example ${generateDecimalExample(decimal)})` : '';
};

const handleUnitAndNumberScaleWarnings = (
  props: Pick<BaseInputProps, 'initiativeUtr' | 'unit' | 'numberScale'> & {
    columnCode?: string;
  }
) => {
  const unitWarning = getUnitHelperText(props);
  if (unitWarning) {
    return unitWarning;
  }
  return getNumberScaleHelperText(props);
};

export const getInputWarningMessage = (
  props: Pick<BaseInputProps, 'initiativeUtr' | 'unit' | 'numberScale'> & {
    utr: Pick<UniversalTracker, 'getValueType'> | undefined;
  }
) => {
  const { utr } = props;
  if (!utr || !isNumericValueType(utr.getValueType())) {
    return;
  }
  return handleUnitAndNumberScaleWarnings(props);
};

export const getTableInputColumnWarningMessage = ({
  initiativeUtr,
  inputColumn,
}: {
  initiativeUtr: InitiativeUniversalTracker | undefined;
  inputColumn: InputColumn;
}) => {
  const { code, unit, numberScale } = inputColumn;
  return handleUnitAndNumberScaleWarnings({ initiativeUtr, columnCode: code, unit, numberScale });
};

const getUnitHelperText = (
  props: Pick<BaseInputProps, 'initiativeUtr' | 'unit'> & { columnCode?: string }
) => {
  const { unit, initiativeUtr, columnCode } = props;

  if (!initiativeUtr) {
    return;
  }
  const { unitInput, unitLocked } = columnCode
    ? initiativeUtr.valueValidation?.table?.columns.find((col) => col.code === columnCode) ?? {}
    : initiativeUtr;
  if (!unitInput || !unitLocked || unit === unitInput) {
    return;
  }
  return (
    <div>
      Warning: New submissions require the unit <strong>{unitInput}</strong>
    </div>
  );
};

const getNumberScaleHelperText = (
  props: Pick<BaseInputProps, 'initiativeUtr' | 'numberScale'> & { columnCode?: string }
) => {
  const { numberScale, initiativeUtr, columnCode } = props;

  if (!initiativeUtr) {
    return;
  }
  const { numberScaleInput, numberScaleLocked } = columnCode
    ? initiativeUtr.valueValidation?.table?.columns.find((col) => col.code === columnCode) ?? {}
    : initiativeUtr;
  if (!numberScaleInput || !numberScaleLocked || numberScale === numberScaleInput) {
    return;
  }

  // Allow for single number scale undefined value, as this is the default value
  if (numberScaleInput === 'single' && numberScale === undefined) {
    return;
  }

  return (
    <div>
      Warning: New submissions require the number scale <strong>{numberScaleInput}</strong>
    </div>
  );
};


interface ValidationItems {
  inputColumns: InputColumn[];
  initiativeUtr: InitiativeUniversalTracker | undefined;
  utr: UniversalTracker;
  variationWarnings?: UtrvVariationWarning[];
}

export const getRowValidationItems = ({ inputColumns, initiativeUtr, utr, variationWarnings = [] }: ValidationItems) => {
  return inputColumns.reduce<ValidationItem[]>((acc, inputColumn) => {
    if (inputColumn.value === undefined) {
      return acc;
    }

    const warningMessage = getTableInputColumnWarningMessage({
      initiativeUtr,
      inputColumn
    });
    if (warningMessage) {
      // Technically warning for unit/numberScale blocks updates, so this should be error.
      acc.push({ columnCode: inputColumn.code, type: 'error', value: warningMessage, });
    }

    if (isNumericString(inputColumn.value)) {
      // There could be an issue where data is string[],
      // but still have a decimal requirement. Not handling that case.
      // @TODO: should re-write generateDecimalErrorMessage with more specific implementation
      const decimalErrors = generateDecimalErrorMessage({
        utr,
        input: { [inputColumn.code]: inputColumn.value },
      });

      const decimalError = decimalErrors?.[inputColumn.code];
      if (decimalError) {
        // These are now blocking submission, therefore error.
        acc.push({ columnCode: inputColumn.code, type: 'error', value: decimalError });
      }
      const variationWarning = findMultiRowTableVariationWarning({ variationWarnings, inputColumns, inputColumn });
      if (variationWarning) {
        acc.push({
          columnCode: inputColumn.code,
          type: 'warning',
          value: generateInputVariationWarningMessage(variationWarning),
        });
      }
    }

    return acc;
  }, []);
}

const findMultiRowTableVariationWarning = ({
  variationWarnings,
  inputColumns,
  inputColumn,
}: {
  variationWarnings: UtrvVariationWarning[];
  inputColumns: InputColumn[];
  inputColumn: InputColumn;
}) => {
  return variationWarnings.find(
    ({ valueListCode, aggregationColumns }) =>
      valueListCode === inputColumn.code &&
      (!aggregationColumns ||
        aggregationColumns.every(
          (aggregationCol) =>
            inputColumns.find((col) => col.code === aggregationCol.code)?.value === aggregationCol.value,
        )),
  );
}; 

export const checkHasInputWarningMessage = (props: {
  utr: UniversalTracker | undefined;
  initiativeUtr: InitiativeUniversalTracker | undefined;
  table: TableDataInfo;
  unit: string | undefined;
  numberScale: string | undefined;
}) => {
  const { utr, table, initiativeUtr } = props;
  switch (utr?.getValueType()) {
    case UtrValueType.Number:
    case UtrValueType.Percentage:
    case UtrValueType.NumericValueList:
      return !!getInputWarningMessage(props);
    case UtrValueType.Table: {
      if (!hasSimpleNumericColumnType({ valueValidation: utr.getValueValidation() }) || table.rows.length === 0) {
        return false;
      }

      const blockingTypes = ['error'];
      return table.rows.some((row) => {
          const validationItems = getRowValidationItems({
            inputColumns: row.data,
            initiativeUtr,
            utr,
          });

          return validationItems.some((item) => blockingTypes.includes(item.type));
        }
      );
    }
    default:
      return false;
  }
}

interface VariationMessageProps {
  utr: Pick<UniversalTrackerPlain, 'unitType' | 'valueType'> | undefined;
  unitConfig?: UnitConfig;
  utrvVariations: UtrvVariation[] | undefined;
  currentInputData: CurrentInputData;
}

const formatUtrvValue = ({
  value,
  unit,
  unitType,
  numberScale,
  unitConfig,
}: {
  value: number | undefined;
  unit?: string;
  unitType?: string;
  numberScale?: string;
  unitConfig?: UnitConfig;
}) => {
  if (value === undefined) {
    return '';
  }
  let formattedValue = String(formatNumberWithDecimal({ value }));

  if (numberScale) {
    formattedValue += ` ${numberScale}`;
  }
  const unitDesc = getUnitDesc({ unit, unitType, unitConfig });
  
  if (unitDesc) {
    formattedValue += ` ${unitDesc}`;
  }
  return formattedValue;
};

interface GetVariationInputProps {
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'unitType' | 'valueValidation'>;
  currentInputData: CurrentInputData;
  valueListCode?: string;
  aggregationColumns?: AggregationColumn[];
}

const getVariationInputValue = ({ utr, currentInputData, valueListCode, aggregationColumns }: GetVariationInputProps) => {
  const { value, unit, numberScale, valueData } = currentInputData;
  switch (utr.valueType) {
    case UtrValueType.Number:
    case UtrValueType.Percentage:
      return { value, unit, numberScale };
    case UtrValueType.NumericValueList:
      return { value: valueListCode ? valueData?.data?.[valueListCode] : undefined, unit, numberScale };
    case UtrValueType.Table: {
      const column = utr.valueValidation?.table?.columns.find((col) => col.code === valueListCode);
      const rowData = getTableRowData({ utr, currentInputData, aggregationColumns }) ?? [];
      const columnData = rowData.find((col) => col.code === valueListCode);
      return {
        value: columnData?.value,
        unit: columnData?.unit ?? column?.unit,
        numberScale: columnData?.numberScale ?? column?.numberScale,
      };
    }
    default:
      return {};
  }
};

const getTableRowData = ({ utr, currentInputData, aggregationColumns }: GetVariationInputProps) => {
  const { table, aggregatedTableData } = currentInputData;
  // single row table only have 1 row
  if (!isMultiRowTableType(utr)) {
    return table.rows[0]?.data;
  }
  if (!aggregationColumns || !aggregatedTableData) {
    return;
  }
  return aggregatedTableData.find((row) =>
    aggregationColumns.every((aggregatedCol) => {
      const aggregatedInputCol = row.find((col) => col.code === aggregatedCol.code);
      return aggregatedCol.code === aggregatedInputCol?.code && aggregatedCol.value === aggregatedInputCol?.value;
    }),
  );
};

const checkIsInVariationRange = ({
  utr,
  currentInputData,
  baselineInputData,
  valueListCode,
}: {
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'unitType'>;
  currentInputData: CurrentInputData;
  baselineInputData: UtrvVariation['details'];
  valueListCode?: string;
}) => {
  const { value, unit, numberScale } = getVariationInputValue({
    utr,
    currentInputData,
    valueListCode,
    aggregationColumns: baselineInputData.aggregationColumns,
  });
  
  if (value === undefined) {
    return true;
  }

  const convertedInputValue = getConvertedValue({
    value,
    unit,
    numberScale,
    isCurrency: utr.unitType === UnitTypes.currency,
    defaultUnit: baselineInputData.unit,
    // fallback to 'single' if we already answered with overridden number scale
    defaultNumberScale: numberScale && !baselineInputData.numberScale ? 'single' : baselineInputData.numberScale,
  });

  if (isNumericString(convertedInputValue)) {
    const num = Number(convertedInputValue);
    return num >= baselineInputData.min && num <= baselineInputData.max;
  }

  return true;
}

const processUtrvVariation = (
  props: {
    utr: Pick<UniversalTrackerPlain, 'unitType' | 'valueType' | 'valueValidation'>;
    utrvVariation: UtrvVariation;
    unitConfig?: UnitConfig;
    currentInputData: CurrentInputData;
  },
): UtrvVariationWarning | undefined => {
  const { utr, unitConfig, utrvVariation, currentInputData } = props;
  const valueListCode = utrvVariation.valueListCode;
  const isInRange = checkIsInVariationRange({
    utr,
    currentInputData,
    baselineInputData: utrvVariation.details,
    valueListCode,
  });

  if (isInRange) {
    return;
  }
  
  const input = getVariationInputValue({ utr, currentInputData, valueListCode });

  const unitType =
    utr.valueType === UtrValueType.Table
      ? utr.valueValidation?.table?.columns.find((col) => col.code === valueListCode)?.unitType
      : utr.unitType;

  const formattedCurrentInput = formatUtrvValue({
    value: input.value,
    unit: input.unit,
    numberScale: input.numberScale,
    unitConfig,
    unitType,
  });

  const { baseline, unit, numberScale, effectiveDate, aggregationColumns } = utrvVariation.details;
  const formattedBaseline = formatUtrvValue({
    value: baseline,
    unitType,
    unit,
    numberScale,
  });

  return {
    valueListCode,
    formattedBaseline,
    formattedCurrentInput,
    variance: utrvVariation.max ?? utrvVariation.min,
    dataSource: utrvVariation.dataSource,
    baselineReportingDate: formatDateUTC(effectiveDate, DATE.MONTH_YEAR),
    confirmationRequired: utrvVariation.confirmationRequired,
    aggregationColumns,
  };
};

export const getUtrvVariationWarnings = (props: VariationMessageProps) => {
  const { utr, utrvVariations = [], unitConfig, currentInputData } = props;

  if (!utr || !utrvVariations.length) {
    return [];
  }

  return utrvVariations.reduce<UtrvVariationWarning[]>((acc, utrvVariation) => {
    const data = processUtrvVariation({ utr, utrvVariation, unitConfig, currentInputData });
    if (data) {
      acc.push(data);
    }
    return acc;
  }, []);
}

export const generateInputVariationWarningMessage = (props: UtrvVariationWarning) => {
  const { variance, formattedBaseline, baselineReportingDate } = props;
  return (
    `The value entered falls outside of the ${variance}% variance limit set by the admin. ` +
    `The baseline value for this metric was ${formattedBaseline} set in ${baselineReportingDate}.`
  );
};
