/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Toggle } from '@g17eco/molecules/toggle';
import { QuestionStatusBar } from './QuestionStatusBar';
import config from '../../../../config';

interface Props {
  questionId: string;
  isDraft: boolean | undefined;
  isUserStaff: boolean;
  status: string;
  questionIndex: string
  handleGoToQuestion: (params: { id: string; index: number; changeDraftTo: boolean }) => void;

}

export const getDraftConfig = (props: Props) => {

  const {
    questionId,
    status,
    isDraft = false,
    handleGoToQuestion,
    isUserStaff,
    questionIndex,
  } = props;

  if (!config.features.draftMode) {
    return { addons: null, isDraftEnabled: false };
  }

  // Still allow to turn off draft mode, even if you are not staff
  const displayDraftView = isUserStaff || isDraft;

  return {
    addons: displayDraftView ? [
      <Toggle
        key={`${questionId}-draft-toggle`}
        className={{
          form: 'd-flex align-items-center pl-0',
          preLabel: 'text-md me-2',
          label: 'text-md ms-2',
        }}
        checked={!isDraft}
        onChange={() => {
          handleGoToQuestion({ id: questionId, index: Number(questionIndex), changeDraftTo: !isDraft });
        }}
        preLabel='Draft'
        label='Live'
      />,
      <QuestionStatusBar key={`${questionId}-question-status-bar`} isDraft={isDraft} status={status} />
    ]: null,
    isDraftEnabled: isDraft
  }
}
