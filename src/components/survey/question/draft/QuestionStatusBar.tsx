/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import classNames from 'classnames';
import React from 'react';
import { UtrvStatus } from '@constants/status';

const getStatus = (status: string) => {
  switch (status) {
    case UtrvStatus.Created:
      return {
        text: 'UNANSWERED',
        classes: 'd-none'
      };
    case UtrvStatus.Updated:
      return {
        text: 'AWAITING VERIFICATION',
        classes: 'border-ThemeAccentExtradark text-ThemeAccentExtradark'
      };
    case UtrvStatus.Rejected:
      return {
        text: 'REJECTED',
        classes: 'background-ThemeDangerDark text-white border-0'
      };
    case UtrvStatus.Verified:
      return {
        text: 'VERIFIED',
        classes: 'background-ThemeSuccessDark text-white border-0'
      };
    case 'draft':
    default:
      return {
        text: 'DRAFT',
        classes: 'background-ThemeBorderDefault text-ThemeTextMedium border-0'
      };
  }
}

export const QuestionStatusBar = ({ isDraft, status }: { isDraft: boolean, status: string }) => {
  const { text, classes } = getStatus(isDraft ? 'draft' : status);
  const classname = classNames('ms-3 py-3 flex-grow-1 fw-bold border border-1 rounded-1 text-center', classes);

  return <div className={classname}>{text}</div>;
}
