/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import { formatHumaniseDate } from '@utils/date';
import { Button } from 'reactstrap';

const sleep = (duration: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, duration);
  });
}
/**
 * DraftButton - Fake autosave button to represent saving state.
 *
 * It should eventually be replaced or linked to websocket provider to track
 * when saves actually happen.
 */

interface Props {
  disabled?: boolean;
}

export const DraftButton = (props: Props) => {

  const [autoSaveTs, setAutoSaveTs] = useState<number | undefined>();
  const [counter, setCounter] = useState<number>(0);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const seconds = Math.round(((autoSaveTs ?? 0) - (performance.timeOrigin + performance.now())) / 1000);

  useEffect(() => {
    const timeout = setTimeout(() => setAutoSaveTs(Date.now()), 10000);
    return () => clearTimeout(timeout);
  }, [autoSaveTs]);

  useEffect(() => {
    // Used to keep the time stamp up to date
    const id = setTimeout(() => {
      setCounter(counter + 1);
    }, 1000);
    return () => clearTimeout(id);
  }, [counter]);

  const onSave = async () => {
    setIsSaving(true);
    await sleep(1000);
    setAutoSaveTs(Date.now());
    setIsSaving(false);
  }


  const justNow = seconds > -3;
  return (
    <>
      {autoSaveTs ? <span
        className='me-2 text-ThemeTextLight'>autosaved {justNow ? 'just now' : formatHumaniseDate(autoSaveTs)}</span> : null}
      <Button color='secondary' disabled={isSaving || justNow || props.disabled} onClick={onSave}>
        Save draft
      </Button>
    </>
  );
};
