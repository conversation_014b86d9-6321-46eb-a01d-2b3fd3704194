/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Button, PopoverBody, PopoverHeader, UncontrolledPopover, UncontrolledPopoverProps } from 'reactstrap';
import React, { useRef } from 'react';

export const MappingPopoverButton = (props: Pick<UncontrolledPopoverProps, 'title' | 'children' | 'placement'>) => {
  const { title, children, placement = 'bottom-end' } = props;
  const ref = useRef<HTMLButtonElement>(null);
  return (
    <div className={'d-flex'}>
      <Button innerRef={ref} type='button' color='link-secondary' outline={true}>
        <i className='fa-light fa-tag' />
      </Button>
      <UncontrolledPopover
        hideArrow color={'primary'}
        popperClassName={'shadow-lg border-ThemeAccentExtradark'}
        trigger={'legacy'}
        target={ref}
        placement={placement}>
        <PopoverHeader>{title}</PopoverHeader>
        <PopoverBody>{children}</PopoverBody>
      </UncontrolledPopover>
    </div>
  )
}
