/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { UniversalTrackerBase } from '@models/UniversalTracker';
import { Addon } from '@components/survey/form/input/InputProps';
import React, { ReactNode } from 'react';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { skipToken } from '@reduxjs/toolkit/query';
import { useGetUniversalTrackerMappingQuery } from '@api/universal-trackers';
import { MappingView } from '@components/survey/question/mapping/MappingView';
import { MappingPopoverButton } from '@components/survey/question/mapping/MappingPopoverButton';
import { UtrMappingResponse } from '@g17eco/types/utr-external-mapping';


interface MappingHookResult {
  addons: Addon[],
  modal?: ReactNode,
}

interface UseMappingAddonsParams {
  utr: UniversalTrackerBase | undefined;
  initiativeId: string;
  enabled?: boolean;
}

interface GenerateAddons {
  utr: UniversalTrackerBase;
  data: UtrMappingResponse;
}

const handleTable = ({ utr, data }: GenerateAddons): MappingHookResult => {
  const columns = utr.valueValidation?.table?.columns ?? [];
  const addons = columns.reduce((acc, column) => {
    const mapping = data.mappings.find((m) => {
      return m.utrCode === utr.code && m.valueListCode === column.code;
    });

    if (mapping) {
      acc.push({
        code: column.code,
        rowIndex: 0,
        element: (
          <MappingPopoverButton>
            <MappingView code={column.code} mapping={mapping} />
          </MappingPopoverButton>
        ),
      })
    }
    return acc;
  }, [] as Addon[]);

  return { addons }
}

export function useMappingAddons({ utr, initiativeId, enabled }: UseMappingAddonsParams): MappingHookResult {

  const { data, isLoading, isError, isSuccess } = useGetUniversalTrackerMappingQuery(utr?._id && enabled ? {
    utrId: utr._id,
    initiativeId,
  } : skipToken);

  // Load mapping information for utr
  if (!utr || !enabled || isLoading || isError || !isSuccess) {
    return { addons: [] }
  }

  switch (utr.valueType) {
    // We are currently only deal with tables for CSRD, so we can ignore the rest
    case UtrValueType.Table:
      return handleTable({utr : utr, data : data});
    default:
      return { addons: [] };
  }
}
