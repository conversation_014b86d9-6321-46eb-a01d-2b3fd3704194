/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ExternalMappingItem } from '@g17eco/types/utr-external-mapping';
import React from 'react';

/**
 * It gets created from provided text converting ExternalMappingItem['references']
 * [key: string, value: string][] => { [key]: [value] };
 * for easier access and display only properties we care about in compact view
 */
interface ReferenceObject {
  Number?: string;
  Name?: string;
  Paragraph?: string;
  Subparagraph?: string;
  Section?: string;
  URI?: string;
  ReferenceType?: 'main';
  MandatoryDatapoint?: string;

  [key: string]: string | undefined;
}

type ReferenceViewerParams = Pick<ExternalMappingItem, 'references'> & {
  view?: 'compact' | 'all';
};

export const ReferenceViewer = (props: ReferenceViewerParams) => {

  const { references, view = 'compact' } = props;
  if (!references) {
    return null;
  }

  if (view === 'all') {
    return (
      <div>
        {references.map((row, index) => {
          return (
            <div key={index} className={'d-flex flex-column'}>
              {row.map(([label, value], index) => {
                if (['Name'].includes(label)) {
                  return null;
                }
                return (
                  <div key={index}>
                    <strong>{label}</strong>:{value}
                  </div>
                )
              })}
            </div>
          )
        })}
      </div>
    );
  }

  const referenceObjects = references.map((row) => {
    return row.reduce((acc, [label, value]) => {
      acc[label] = value;
      return acc;
    }, {} as ReferenceObject);
  }, [] as ReferenceObject[]);

  const renderItem = (label: string, value: string | undefined) => {
    if (!value) {
      return null;
    }

    return (
      <div key={label || value} className={'me-2'}>
        {label ? (<><strong>{label}</strong>:</>) : null}
        {value}
      </div>
    )
  }

  return (
    <div>
      {referenceObjects.map((reference, index) => {
        return (
          <div key={index} className={'d-flex flex-column'}>
            {renderItem('Section', reference.Section)}
            {renderItem('Paragraph', [reference.Paragraph, reference.Subparagraph].filter(Boolean).join(' - '))}
            {reference.URI ? <a href={reference.URI} target={'_blank'}>{reference.URI}</a> : null}
            {renderItem('', reference.MandatoryDatapoint)}
          </div>
        )
      })}
    </div>
  );
}
