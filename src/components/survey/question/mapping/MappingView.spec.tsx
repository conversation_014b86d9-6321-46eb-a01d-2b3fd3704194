import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MappingView } from './MappingView';
import { ExternalMappingItem } from '@g17eco/types/utr-external-mapping';

describe('MappingView', () => {
  const mockMapping: ExternalMappingItem = {
    type: 'csrd',
    utrCode: 'utr-test-code',
    name: 'Test Name',
    mappingCode: 'Test Code',
    references: [
      [
        ['Section', 'SBM-3']
      ]
    ]
  };

  it('renders text view by default', () => {
    render(<MappingView code={mockMapping.utrCode} mapping={mockMapping} />);
    expect(screen.getByText('Test Name')).toBeInTheDocument();
  });

  it('renders tag view when tag button is clicked', () => {
    render(<MappingView code={mockMapping.utrCode} mapping={mockMapping} />);
    fireEvent.click(screen.getByTestId('mapping-icon-button-tag'));
    expect(screen.getByText('Test Code')).toBeInTheDocument();
  });

  it('renders info view when info button is clicked', () => {
    render(<MappingView code={mockMapping.utrCode} mapping={mockMapping} />);
    fireEvent.click(screen.getByTestId('mapping-icon-button-info'));
    expect(screen.getByText('Section')).toBeInTheDocument();
  });
});
