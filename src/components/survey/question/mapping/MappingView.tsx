/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ExternalMappingItem } from '@g17eco/types/utr-external-mapping';
import React, { useState } from 'react';
import { ReferenceViewer } from '@components/survey/question/mapping/ReferenceViewer';
import { ButtonGroup } from 'reactstrap';
import IconButton from '@components/button/IconButton';

interface MappingViewParams {
  code: string,
  mapping: ExternalMappingItem
}

export const MappingView = (props: MappingViewParams) => {

  const [view, setView] = useState<'text' | 'tag' | 'info'>('text');
  const { name, mappingCode } = props.mapping;

  const viewContent = () => {
    switch (view) {
      case 'text':
        return name;
      case 'tag':
        return mappingCode;
      case 'info': {
        return <ReferenceViewer references={props.mapping.references} />
      }
    }
  }

  const buttons = [
    {
      icon: 'fa-light fa-text-size',
      view: 'text',

    },
    {
      icon: 'fa-light fa-tag',
      view: 'tag',
    },
    {
      icon: 'fa-light fa-info',
      view: 'info',
    },
  ] as const

  return (
    <div className={'d-flex flex-row'}>
      <ButtonGroup className={'me-2 align-self-center d-inline'} vertical>
        {buttons.map((button) => {
          const active = button.view === view;
          return (
            <IconButton
              key={button.icon}
              data-testid={`mapping-icon-button-${button.view}`}
              color={active ? 'primary' : 'primary'}
              outline={!active}
              active={active}
              onClick={() => setView(button.view)}
              icon={button.icon}
            />
          )
        })}
      </ButtonGroup>
      <div
        style={{ minWidth: '200px' }}
        className={'w-100 border border-radius  border-2 p-2 text-break'}>
        {viewContent()}
      </div>
    </div>
  )
}
