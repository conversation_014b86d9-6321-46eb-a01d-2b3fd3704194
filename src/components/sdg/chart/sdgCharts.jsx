/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import ReactDOMServer from 'react-dom/server';
import { Chart } from 'react-google-charts';
import { Loader } from '@g17eco/atoms/loader';

export const SdgStackedColumnChart = (data, goalTicks, questionTicks, handleClick) => {

  const options = {
    legend: 'none',
    bars: 'vertical',
    bar: { groupWidth: '95%' },
    isStacked: true,
    height: 500,
    chartArea: {
      backgroundColor: '#f5f5f0',
      left: 45,
      right: 0,
      top: 10,
      bottom: 0
    },
    tooltip: { isHtml: true, trigger: 'visible' },
    vAxis: {
      backgroundColor: '#ff0011',
      minValue: 0,
      position: 'top',
      textPosition: 'right',
      label: 'none',
      textStyle: {
        color: '#3e3e3e',
      },
      gridlines: {
        color: 'transparent'
      },
      baselineColor: 'transparent'
    },
    hAxis: {
      gridlines: {
        color: 'transparent'
      },
      baselineColor: 'transparent'
    },
    vAxes: {
      0: {
        ticks: goalTicks,
        gridlines: {
          color: '#a1a3a4'
        },
        viewWindow: {
          max: 100,
          min: 0
        },
      }
    }
  };

  if (questionTicks) {
    // create a new entry at beginning of array and assign to second axis to force it to display
    data[0].push(''); // header
    data[1].push(0); // data
    options.series = [];

    const countOfSeries = data[1].reduce((acc, cur) => !isNaN(cur) ? acc += 1 : acc, 0);
    options.series[countOfSeries - 2] = {
      targetAxisIndex: 1
    };
    options.vAxes[1] = {
      ticks: questionTicks,
      gridlines: {
        color: 'lightgrey'
      }
    }
  }

  const chartEvents = [
    {
      eventName: 'select',
      callback: ({ chartWrapper }) => {
        const [selected] = chartWrapper.getChart().getSelection();
        const { column, row } = selected;
        handleClick(data[row][column]);
      }
    }
  ];

  return <Chart
    chartType='ColumnChart'
    data={data}
    loader={<Loader />}
    options={options}
    width='100%'
    style={{ position: 'relative' }}
    chartEvents={chartEvents}
  />
};

const renderTooltip = (inScope, sdgCode, value = '') => {
  let el;

  if (inScope) {
    el = <div className='p-2' style={{ minWidth: '200px' }}>
      <strong>SDG Target {sdgCode}</strong>
      <div>Target Contribution: {value}%</div>
      <div>Target in Scope</div>
    </div>;
  } else {
    el = <div className='p-2' style={{ minWidth: '200px' }}>
      <strong>SDG Target {sdgCode}</strong>
      <div>Target not in Scope</div>
    </div>;
  }
  return ReactDOMServer.renderToString(el);
}

export const calculateChartData = (targets, colour) => {

  let cumulativeHeight = 0;
  const goalTicks = [];
  const questionTicks = [];
  const header = [''];
  let data = [0];

  const styleOne = 'color: ' + colour + '; opacity:0.8';
  const styleTwo = 'color: ' + colour + '; opacity:0.3';
  const styleDisabled = 'opacity: 0';
  const segmentHeight = 100 / targets.length;

  targets.forEach((target) => {
    const targetName = target.sdgCode;
    goalTicks.push({ v: cumulativeHeight, f: targetName });

    if (!target.inScope) {
      const tooltip = renderTooltip(target.inScope, target.sdgCode);
      cumulativeHeight += segmentHeight;

      header.push(targetName);
      header.push({ role: 'style' });
      header.push({ type: 'string', role: 'tooltip', p: { html: true } });
      data.push(segmentHeight);
      data.push(styleDisabled);
      data.push(tooltip);
    } else {
      const targetActual = Math.round(target.actual || 0);
      const targetValue = Math.min(Math.max(0, targetActual), 100);
      const valueHeight = targetValue / 100 * segmentHeight;
      const remainderHeight = (100 - targetValue) / 100 * segmentHeight;
      const tooltip = renderTooltip(target.inScope, target.sdgCode, targetActual);
      cumulativeHeight += segmentHeight;

      header.push(targetName);
      header.push({ role: 'style' });
      header.push({ type: 'string', role: 'tooltip', p: { html: true } });
      data.push(valueHeight);
      data.push(styleOne);
      data.push(tooltip);

      header.push(targetName);
      header.push({ role: 'style' });
      header.push({ type: 'string', role: 'tooltip', p: { html: true } });
      data.push(remainderHeight);
      data.push(styleTwo);
      data.push(tooltip);
    }
  });

  goalTicks.push({ v: 100, f: '' });
  questionTicks.push({ v: 1, f: '' });
  data = [header, [...data]];

  return { data, goalTicks, questionTicks };
};
