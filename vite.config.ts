/// <reference types="vitest" />
import { coverageConfigDefaults } from 'vitest/config'
import { defineConfig, loadEnv, UserConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgrPlugin from 'vite-plugin-svgr';
import checker from 'vite-plugin-checker';
import { ViteEjsPlugin } from 'vite-plugin-ejs';
import path from 'path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import { generateCSP } from './src/cors';
import oxlintPlugin from 'vite-plugin-oxlint'
import { defaultExclude } from 'vitest/dist/config';

function getCheckerPlugin(isDevMode: boolean) {
  // Only run ESLint development mode, other modes will be handled by CI/CD
  if (isDevMode) {
    return checker({
      overlay: { initialIsOpen: false },
      typescript: true,
      eslint: {
        // This should be aligned with the `lint` script in package.json
        useFlatConfig: true,
        lintCommand: 'eslint src --quiet',
        dev: {
          logLevel: ['warning', 'error'],
        },
      }
    });
  }

  // Still run typescript checker in production mode, but not eslint
  // as that currently consumes too much memory when running in a single process
  return checker({ typescript: true, eslint: false });
}

function getReleaseName(env: Record<string, string>) {
  if (typeof env.BITBUCKET_BRANCH !== 'string') {
    return { name: undefined, build: undefined };
  }

  const branch = env.BITBUCKET_BRANCH
    // Remove release name prefix, to have semver number
    .replace('release/', '')
    // Need to ensure no forward slashes in the release name
    .replace(/\//g, '');

  const packageName = 'www'
  const build = env.BITBUCKET_BUILD_NUMBER || '0';

  // Semantic Versioning: package@version or package@version+build
  return {
    name: `${packageName}@${branch}+${build}`,
    buildNumber: build,
  };
}

export const resolveConfig: UserConfig['resolve'] = {
  alias: {
    '@g17eco/atoms': path.resolve(__dirname, './src/atoms'),
    '@g17eco/molecules': path.resolve(__dirname, './src/molecules'),
    '@g17eco/images': path.resolve(__dirname, './src/images'),
    '@g17eco/slices': path.resolve(__dirname, './src/slice'),
    '@components': path.resolve(__dirname, './src/components'),
    '@features': path.resolve(__dirname, './src/features'),
    '@constants': path.resolve(__dirname, './src/constants'),
    '@services': path.resolve(__dirname, './src/services'),
    '@utils': path.resolve(__dirname, './src/utils'),
    '@g17eco/types': path.resolve(__dirname, './src/types'),
    '@fixtures': path.resolve(__dirname, './src/__fixtures__'),
    '@browser': path.resolve(__dirname, './src/browser'),
    '@routes': path.resolve(__dirname, './src/routes'),
    '@api': path.resolve(__dirname, './src/api'),
    '@hooks': path.resolve(__dirname, './src/hooks'),
    '@actions': path.resolve(__dirname, './src/actions'),
    '@models': path.resolve(__dirname, './src/model'),
    '@reducers': path.resolve(__dirname, './src/reducers'),
    '@selectors': path.resolve(__dirname, './src/selectors'),
    '@apps': path.resolve(__dirname, './src/apps'),
  },
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const port = Number(env.PORT || 4001);
  const isDevMode = mode === 'development';
  const isTestMode = mode === 'test';

  const { name: releaseName, buildNumber } = getReleaseName(env);
  return {
    test: {
      name: 'unit',
      globals: true,
      globalSetup: './globalSetup.ts',
      environment: 'jsdom',
      setupFiles: ['./src/setupTests.ts', './tests/setupTests.ts'],
      testTimeout: Number(env.REACT_APP_VITE_TEST_TIMEOUT ?? 5000),
      server: {
        deps: {
          inline: ['@sentry/react', '@lexical/react'],
        }
      },
      exclude: [
        ...defaultExclude,
        'src/**/*.browser.{spec,test}.ts[x]',
        'tests/**',
      ],
      coverage: {
        provider: 'v8',
        reporter: ['lcov', 'html', 'json', 'clover'],
        include: [
          'src/**/*.ts',
          'src/**/*.tsx',
          'src/**/*.js',
          'src/**/*.jsx',
        ],
        exclude: [
          'node_modules/',
          'src/setupTests.ts',
          'integration-test/**',
          'deploy/**',
          ...coverageConfigDefaults.exclude,
        ],
      },
    },
    plugins: [
      react(),
      isTestMode ? undefined : getCheckerPlugin(isDevMode),
      isTestMode ? undefined : oxlintPlugin({
        path: 'src',
        params: '--import-plugin',
        allow: ['correctness'],
      }),
      viteTsconfigPaths(),
      svgrPlugin(),
      ViteEjsPlugin({
        robots: env.REACT_APP_ROBOTS || 'noindex',
        internationalization: env.REACT_APP_INTERNATIONALIZATION || '0',
      }),
      isTestMode ? undefined : sentryVitePlugin({
        // Need to split our main js file (~8Mb) into smaller chunks
        // using dynamic imports or increase allowed memory to be (5GB+)
        disable: isDevMode,
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: process.env.SENTRY_ORG || 'world-wide-generation',
        project: process.env.SENTRY_PROJECT || 'react-g17-eco',
        release: {
          name: releaseName,
          dist: buildNumber,
          deploy: {
            env: mode,
            name: releaseName || `${mode}-unknown`,
          }
        },
        telemetry: false,
      }),
    ],
    build: {
      outDir: 'build',
      sourcemap: true, // Source map generation must be turned on
    },
    optimizeDeps: {
      include: ['@g17eco/convert-units'],
    },
    server: {
      open: true,
      port: port,
      cors: {
        allowedHeaders: ['Content-Security-Policy'],
      },
      headers: {
        'Content-Security-Policy': generateCSP(port, env.REACT_APP_API_SERVER, env.REACT_APP_WEBSOCKET_URL),
      },
    },
    resolve: resolveConfig,
    envPrefix: 'REACT_APP',
    // slick-carousel raises error because it uses @charset: UTF-8
    // so this setting is to disable the error. should refactor slick-carousel and remove this.
    css: {
      preprocessorOptions: {
        scss: {
          charset: false,
        },
        less: {
          charset: false,
        },
      },
      /** @ts-expect-error Not in the definition? can be removed? */
      charset: false,
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },
    define: {
      'import.meta.env.REACT_APP_RELEASE_NAME': JSON.stringify(releaseName),
    },
  } satisfies UserConfig;
});
